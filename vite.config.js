import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.jsx',
                'resources/js/order.jsx',
                'resources/js/customer-ipad.jsx',
                'resources/js/kitchen.jsx',
                'resources/js/mobile-ordering.jsx',
                'resources/js/customer-ipad.jsx',
                'resources/js/employee-order.jsx',
                'resources/js/manager-order.jsx',
                'resources/js/meal-ticket.jsx'
            ],
            refresh: true,
        }),
        react({
            jsxRuntime: 'automatic',
            include: /\.(js|jsx|ts|tsx)$/,
            exclude: /node_modules/,
        })
    ],
});
