<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\PermissionController;

Route::middleware(['route.permission'])->prefix('admin')->name('admin.')->group(function () {
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// User Management
Route::resource('users', UserController::class);
Route::post('users/{user}/assign-role', [UserController::class, 'assignRole'])->name('users.roles.assign');
Route::delete('users/{user}/revoke-role/{role}', [UserController::class, 'revokeRole'])->name('users.roles.revoke');
Route::post('users/{user}/assign-permission', [UserController::class, 'assignPermission'])->name('users.permissions.assign');
Route::delete('users/{user}/revoke-permission/{permission}', [UserController::class, 'revokePermission'])->name('users.permissions.revoke');

Route::post('permissions/sync-routes', [PermissionController::class, 'syncRoutes'])->name('permissions.sync-routes');
Route::get('permissions/sync-status', [PermissionController::class, 'syncStatus'])->name('permissions.sync-status');
Route::post('permissions/cleanup', [PermissionController::class, 'cleanupPermissions'])->name('permissions.cleanup');
// Role Management
Route::resource('roles', RoleController::class);

// Permission Management
Route::resource('permissions', PermissionController::class);
});
