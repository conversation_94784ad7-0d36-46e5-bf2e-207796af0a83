<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DateLockController;
use App\Http\Controllers\Admin\DiscountCodeController;
use App\Http\Controllers\Admin\FoodCategoryController;
use App\Http\Controllers\Admin\FoodController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\ProfileController;
use App\Http\Controllers\Admin\TableController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\VietQrController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\OrderItemController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['route.permission'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Users Management
    Route::resource('users', UserController::class);
    Route::get('/reception', function () {
        return view('reception');
    })->name('orderFood');

    // Profile Management
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::delete('/profile/avatar', [ProfileController::class, 'removeAvatar'])->name('profile.remove-avatar');

    Route::resource('foods', FoodController::class);
    Route::patch('foods/{food}/toggle-status', [FoodController::class, 'toggleStatus'])->name('foods.toggle-status');

    // Food Categories
    Route::resource('food-categories', FoodCategoryController::class);
    Route::patch('food-categories/{food_category}/toggle-status', [FoodCategoryController::class, 'toggleStatus'])->name('food-categories.toggle-status');

    // Discount Codes
    Route::resource('discount-codes', DiscountCodeController::class);

    // Date Locks
    Route::resource('date-locks', DateLockController::class);
    Route::get('date-locks-bulk/create', [DateLockController::class, 'bulkCreate'])->name('date-locks.bulk-create');
    Route::post('date-locks-bulk', [DateLockController::class, 'bulkStore'])->name('date-locks.bulk-store');

    // Tables Management
    Route::resource('tables', TableController::class);

    // Orders Management
    Route::resource('orders', OrderController::class);
    Route::patch('orders/{order}/status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::get('orders/{order}/payment', [OrderController::class, 'payment'])->name('orders.payment');
    Route::post('orders/{order}/payment', [OrderController::class, 'processPayment'])->name('orders.process-payment');
    Route::patch('orders/{order}/cancel', [OrderController::class, 'cancel'])->name('orders.cancel');
    Route::post('orders/{order}/generate-qr', [OrderController::class, 'generateQrCode'])->name('orders.generate-qr');
    Route::patch('/order-items/{orderItem}/update', [OrderItemController::class, 'update'])->name('order-items.update');
    Route::post('/order-items/bulk-update', [OrderItemController::class, 'bulkUpdate'])->name('order-items.bulk-update');

    Route::resource('settings', SettingController::class);
    Route::put('settings-bulk-update', [SettingController::class, 'bulkUpdate'])
        ->name('settings.bulk-update');


    Route::resource('vietqr', VietQrController::class);
    Route::post('vietqr/preview', [VietQrController::class, 'preview'])
        ->name('vietqr.preview');
});
