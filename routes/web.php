<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PWAController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/dashboard/data', [DashboardController::class, 'getData'])->name('dashboard.data');
Route::get('/', function () {
    return view('welcome');
});

// Customer Interface Routes
Route::prefix('customer')->name('customer.')->group(function () {
    Route::get('/scan', [App\Http\Controllers\CustomerController::class, 'scanQR'])->name('scan');
    Route::get('/order/{table}', [App\Http\Controllers\CustomerController::class, 'orderInterface'])->name('order');
    Route::get('/order-react/{order}', [App\Http\Controllers\CustomerController::class, 'orderReactInterface'])->name('order-react');
    Route::get('/table/{table}/qr', [App\Http\Controllers\CustomerController::class, 'showQRCode'])->name('qr');
    Route::get('/ipad/{order}', [App\Http\Controllers\CustomerController::class, 'ipadInterface'])->name('ipad');
    Route::get('/eat-ticket/{orderRoomId}', [App\Http\Controllers\CustomerController::class, 'eatTicket'])->name('ticket');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});
Route::get('orders/{order}/print', [\App\Http\Controllers\Admin\OrderController::class, 'print'])->name('orders.print');

// Customer routes for QR code access
Route::get('/order/{table}', function ($tableCode) {
    $table = \App\Models\Table::where('code', $tableCode)
        ->with(['currentOrder.orderItems.food', 'currentOrder.customer'])
        ->firstOrFail();

    return view('customer.order', compact('table'));
})->name('customer.order');

// Customer menu routes
Route::get('/menu/{table}', [App\Http\Controllers\Customer\MenuController::class, 'index'])->name('customer.menu');

// Customer order tracking route
Route::get('/track/{order}', function ($orderNumber) {
    $order = \App\Models\Order::where('order_number', $orderNumber)
        ->with(['table', 'customer', 'orderItems.food'])
        ->firstOrFail();

    return view('customer.track', compact('order'));
})->name('customer.order.track');

Route::get('/order', function () {
    return view('order');
});

Route::get('/kitchen', function () {
    return view('kitchen');
})->name('kitchen');

Route::get('logs', [\Rap2hpoutre\LaravelLogViewer\LogViewerController::class, 'index']);

require __DIR__.'/auth.php';
require __DIR__.'/admin.php';
require __DIR__.'/permission.php';
require __DIR__.'/pwa.php';

