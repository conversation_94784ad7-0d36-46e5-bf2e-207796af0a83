<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});
Broadcast::channel('kitchen', function ($user) {
    return $user && in_array($user->role, ['admin', 'chef', 'kitchen_staff']);
});

Broadcast::channel('restaurant', function ($user) {
    return $user && in_array($user->role, ['admin', 'manager', 'waiter', 'chef', 'kitchen_staff']);
});

Broadcast::channel('table.{tableId}', function ($user, $tableId) {
    return $user && ($user->role === 'admin' || $user->role === 'manager');
});
