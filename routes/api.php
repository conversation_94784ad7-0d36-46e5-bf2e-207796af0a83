<?php

use App\Http\Controllers\Api\MenuApiController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\KitchenController;
use App\Http\Controllers\LoudSpeakerController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::prefix('orders')->group(function () {
    Route::get('/', [OrderController::class, 'index']);
    Route::post('/store', [OrderController::class, 'store']);

    Route::get('/current', [App\Http\Controllers\CustomerController::class, 'getRecentOrders']);
    Route::post('/{order}/checkin', [App\Http\Controllers\CustomerController::class, 'checkIn']);
    Route::post('/{order}/checkout', [App\Http\Controllers\CustomerController::class, 'checkOut']);
    Route::post('/{order}/payment', [OrderController::class, 'payment']);
    Route::get('/{order}/status', [App\Http\Controllers\CustomerController::class, 'getOrderStatus']);
    Route::get('/{order}/getQrPayment', [OrderController::class, 'getQrPayment']);
    Route::post('/{order}/merge-tables', [OrderController::class, 'mergeTables']);
    Route::get('/{order}/items', [OrderController::class, 'getOrderItems']);
    Route::patch('/items/{itemId}', [OrderController::class, 'updateOrderItem']);
    Route::delete('/items/{itemId}', [OrderController::class, 'removeOrderItem']);
    Route::post('/{order}/confirm', [OrderController::class, 'confirm']);
    Route::post('/{order}/request-payment', [OrderController::class, 'requestPayment']);
    Route::get('/{order}/details', [OrderController::class, 'getOrderDetails']);
    Route::post('/', [App\Http\Controllers\CustomerController::class, 'createOrder']);
});
Route::get('/foods', [App\Http\Controllers\Admin\FoodController::class, 'apiIndex']);
Route::get('/reception-foods', [\App\Http\Controllers\Api\FoodItemController::class, 'getReceptionFoods']);
Route::post('/reception-foods/update-status', [\App\Http\Controllers\Api\FoodItemController::class, 'updateStatus']);

Route::prefix('kitchen')->group(function () {
    Route::get('/orderFoods', [KitchenController::class, 'getOrderFoods']);
    Route::patch('/foods/updateStatus', [KitchenController::class, 'updateOrderItemStatus']);
    Route::patch('/foods/{orderItem}/status', [KitchenController::class, 'updateOrderStatus']);
});
Route::prefix('customer')->group(function () {
    Route::get('/menu/{table}', [App\Http\Controllers\Customer\MenuController::class, 'getMenu']);
    Route::post('/order/{table}', [App\Http\Controllers\Customer\MenuController::class, 'submitOrder']);
    Route::get('/order-status/{table}', [App\Http\Controllers\Customer\MenuController::class, 'getOrderStatus']);
});

Route::prefix('menu')->group(function () {
    Route::get('/category/{categoryId}', [MenuApiController::class, 'getMenuByCategory']);
    Route::get('/search', [MenuApiController::class, 'searchMenu']);
    Route::get('/{orderId?}', [MenuApiController::class, 'getMenu']);
});
Route::get('/customers/search', [CustomerController::class, 'search'])->name('api.customers.search');
Route::get('/customers/by-phone', [CustomerController::class, 'getByPhone'])->name('api.customers.by-phone');

// api for ad.quangia prefix v1
Route::prefix('v1')->group(function () {
    Route::get('/menu', [MenuApiController::class, 'getMenuforBreakfastTicket']);
    Route::post('/order/create-by-breakfast-ticket', [MenuApiController::class, 'createByBreakfastTicket']);
    Route::get('/order/get-order-by-breakfast-ticket', [MenuApiController::class, 'getOrderByBreakfastTicket']);
});
Route::get('/mp3', [LoudSpeakerController::class, 'mp3']);

Route::prefix('public')->group(function () {
    Route::get('/meal-tickets/data', [\App\Http\Controllers\PublicMealTicketController::class, 'getData']);
    Route::post('/meal-tickets/use', [\App\Http\Controllers\PublicMealTicketController::class, 'use']);
});



