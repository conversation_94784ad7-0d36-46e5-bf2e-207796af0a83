<?php

use App\Http\Controllers\PWAController;
use Illuminate\Support\Facades\Route;

Route::get('/manager/order/{device}', function ($device) {
    return view('orderIpad', compact('device'));
})->where('device', '[a-zA-Z0-9]+')->name('order.device');

Route::get('/meal-tickets', [\App\Http\Controllers\PublicMealTicketController::class, 'index'])
    ->name('meal-tickets.index');

Route::get('/manager/reception/order-food/{device}', function ($device) {
    return view('reception-employee', compact('device'));
})->where('device', '[a-zA-Z0-9]+')->name('reception-employee.device');

Route::get('/manifest/{app}/{device}', [PWAController::class, 'manifestForDevice'])
    ->name('pwa.manifest.device')
    ->where(['app' => '[a-z-]+', 'device' => '[a-z0-9]+']);
// PWA Routes
Route::get('/manifest/{app?}', [PWAController::class, 'manifest'])
    ->name('pwa.manifest')
    ->where('app', '[a-z-]+');


// Service Worker routes
Route::get('/sw-{app}.js', [PWAController::class, 'serviceWorker'])
    ->name('pwa.service-worker')
    ->where('app', '[a-z-]+');

// Default service worker (for backward compatibility)
Route::get('/sw.js', function() {
    return app(PWAController::class)->serviceWorker(request(), 'default');
})->name('pwa.service-worker.default');

// PWA Config API (for frontend)
Route::get('/pwa-config/{app?}', [PWAController::class, 'getAppConfig'])
    ->name('pwa.config')
    ->where('app', '[a-z-]+');

// Offline fallback page
Route::get('/offline', function () {
    return view('pwa.offline');
})->name('pwa.offline');
