<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('eating_tickets', function (Blueprint $table) {
            $table->id();
            $table->integer('quantity')->default(0);
            $table->integer('price')->nullable();
            $table->unsignedInteger('order_id')->nullable();
            $table->unsignedInteger('creator_id')->nullable();
            $table->timestamps(); // Creates created_at and updated_at
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('eating_tickets');
    }
};
