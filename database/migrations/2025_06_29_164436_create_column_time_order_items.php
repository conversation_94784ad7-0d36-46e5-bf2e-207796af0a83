<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->timestamp('cooking_started_at')->nullable()->comment('Thời gian bắt đầu nấu');
            $table->timestamp('cooking_completed_at')->nullable()->comment('Thời gian nấu xong');
            $table->timestamp('completed_at')->nullable()->comment('Thời gian hoàn thành món');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn([
                'cooking_started_at',
                'cooking_completed_at',
                'completed_at',
            ]);
        });
    }
};
