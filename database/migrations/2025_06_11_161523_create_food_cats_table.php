<?php
// Migration 1: create_food_cats_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('food_cats', function (Blueprint $table) {
            $table->id();
            $table->string('name', 150);
            $table->tinyInteger('status')->default(0)->comment('0: active, 1: inactive');
            $table->integer('image_id')->nullable()->comment('Ảnh danh mục');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('food_cats');
    }
};
