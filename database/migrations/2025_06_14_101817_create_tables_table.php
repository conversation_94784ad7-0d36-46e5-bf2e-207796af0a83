<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tables', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100); // Tên bàn (VD: Bàn 1, Bàn VIP A)
            $table->string('code', 50)->unique(); // Mã bàn duy nhất cho QR
            $table->integer('capacity')->default(4); // Số chỗ ngồi
            $table->text('description')->nullable(); // Mô tả bàn
            $table->string('location', 100)->nullable(); // Vị trí (VD: Tầng 1, Khu A)
            $table->tinyInteger('status')->default(0)->comment('0: available, 1: occupied, 2: reserved, 3: maintenance');
            $table->string('qr_code')->nullable(); // Đường dẫn file QR code
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tables');
    }
};
