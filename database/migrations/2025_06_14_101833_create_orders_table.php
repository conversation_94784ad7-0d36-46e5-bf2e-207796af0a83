<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number', 50)->unique(); // Mã đơn hàng
            $table->foreignId('table_id')->nullable()->constrained('tables')->onDelete('set null'); // Bàn
            $table->foreignId('customer_id')->nullable()->constrained('customers')->onDelete('set null'); // Khách hàng
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null'); // Nhân viên tạo đơn
            $table->tinyInteger('status')->default(0)->comment('0: pending, 1: confirmed, 2: preparing, 3: ready, 4: completed, 5: cancelled');
            $table->integer('subtotal')->default(0); // Tổng tiền trước giảm giá
            $table->integer('discount_amount')->default(0); // Số tiền giảm giá
            $table->string('discount_code', 100)->nullable(); // Mã giảm giá đã sử dụng
            $table->integer('total_amount')->default(0); // Tổng tiền sau giảm giá
            $table->tinyInteger('payment_status')->default(0)->comment('0: unpaid, 1: paid, 2: refunded');
            $table->string('payment_method', 50)->nullable(); // Phương thức thanh toán
            $table->text('notes')->nullable(); // Ghi chú đơn hàng
            $table->timestamp('order_time')->nullable(); // Thời gian đặt hàng
            $table->timestamp('completed_at')->nullable(); // Thời gian hoàn thành
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
