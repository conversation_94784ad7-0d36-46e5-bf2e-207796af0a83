<?php
// Migration 4: create_food_date_locks_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('food_date_locks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('food_id')->constrained('foods')->onDelete('cascade');
            $table->date('date');
            $table->timestamps();

            $table->unique(['food_id', 'date']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('food_date_locks');
    }
};
