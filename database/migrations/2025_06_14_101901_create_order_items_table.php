<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade'); // Đơn hàng
            $table->foreignId('food_id')->constrained('foods')->onDelete('cascade'); // Món ăn
            $table->integer('quantity')->default(1); // Số lượng
            $table->integer('unit_price'); // Giá đơn vị tại thời điểm đặt
            $table->integer('total_price'); // Tổng giá (quantity * unit_price)
            $table->text('notes')->nullable(); // Ghi chú cho món ăn
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
