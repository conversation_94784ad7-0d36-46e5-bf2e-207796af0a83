<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('food_cats', function (Blueprint $table) {
            $table->boolean('hide_price')->default(false)->comment('Không hiện giá khi khách xem');
            $table->boolean('breakfast_with_room')->default(false)->comment('Ăn sáng kèm phòng - hiển thị ở link vé ăn sáng');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('food_cats', function (Blueprint $table) {
            $table->dropColumn(['hide_price', 'breakfast_with_room']);
        });
    }
};
