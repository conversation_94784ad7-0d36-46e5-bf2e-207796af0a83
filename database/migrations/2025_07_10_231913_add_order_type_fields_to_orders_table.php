<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add customer type and service type fields
            $table->enum('customer_type', ['individual', 'breakfast-room'])
                ->after('customer_id')
                ->comment('Loại khách hàng: individual=Khách lẻ, breakfast-room=Ăn sáng kèm đặt phòng');

            $table->enum('service_type', ['ala-carte', 'buffet'])
                ->after('customer_type')
                ->comment('<PERSON><PERSON><PERSON> thức phục vụ: buffet=Buffet, ala-carte=Gọi món lẻ');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['customer_type', 'service_type']);
        });
    }
};
