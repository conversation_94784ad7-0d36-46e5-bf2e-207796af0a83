<?php
// Migration 3: create_food_discount_codes_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('food_discount_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code', 100)->unique();
            $table->datetime('start_time');
            $table->datetime('expire_time');
            $table->tinyInteger('type')->comment('1: <PERSON><PERSON><PERSON><PERSON> theo %, 2: <PERSON><PERSON><PERSON>m theo số tiền cụ thể');
            $table->float('discount');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('food_discount_codes');
    }
};
