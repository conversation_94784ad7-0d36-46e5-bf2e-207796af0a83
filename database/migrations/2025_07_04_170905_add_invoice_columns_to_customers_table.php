<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInvoiceColumnsToCustomersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('tax_code')->nullable()->after('id');
            $table->string('company_name')->nullable()->after('tax_code');
            $table->text('company_address')->nullable()->after('company_name');
            $table->string('invoice_email')->nullable()->after('company_address');
            $table->string('buyer_name')->nullable()->after('invoice_email');
            $table->string('buyer_phone')->nullable()->after('buyer_name');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn([
                'tax_code',
                'company_name',
                'company_address',
                'invoice_email',
                'buyer_name',
                'buyer_phone'
            ]);
        });
    }
}
