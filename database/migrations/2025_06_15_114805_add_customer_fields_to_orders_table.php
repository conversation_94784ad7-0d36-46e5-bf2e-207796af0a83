<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->timestamp('checked_in_at')->nullable()->after('notes');
            $table->timestamp('checked_out_at')->nullable()->after('checked_in_at');
            $table->string('customer_status')->default('pending')->after('checked_out_at'); // pending, checked_in, checked_out
            $table->string('qr_code_url')->nullable()->after('customer_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['checked_in_at', 'checked_out_at', 'customer_status', 'qr_code_url']);
        });
    }
};
