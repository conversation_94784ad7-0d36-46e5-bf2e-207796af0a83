<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('eating_ticket_history', function (Blueprint $table) {
            $table->id(); // Creates unsigned auto-increment primary key
            $table->integer('order_id');
            $table->integer('quantity');
            $table->string('image_id')->nullable();
            $table->timestamps(); // Creates created_at and updated_at with auto-update

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('eating_ticket_history');
    }
};
