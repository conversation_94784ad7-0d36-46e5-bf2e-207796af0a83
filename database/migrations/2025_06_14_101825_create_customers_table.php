<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100); // Tên khách hàng
            $table->string('phone', 20)->nullable(); // Số điện thoại
            $table->string('email', 100)->nullable(); // Email
            $table->text('address')->nullable(); // Địa chỉ
            $table->text('notes')->nullable(); // <PERSON>hi chú
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
