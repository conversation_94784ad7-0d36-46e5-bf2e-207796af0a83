
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('viet_qr_configs', function (Blueprint $table) {
            $table->id();
            $table->string('bank_code')->default('970416');
            $table->string('account_number');
            $table->string('account_name');
            $table->string('template')->default('zahTi5g');
            $table->string('order_prefix')->default('ORDER');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('viet_qr_configs');
    }
};
