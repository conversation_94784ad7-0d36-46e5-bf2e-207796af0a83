<?php
// Migration 2: create_foods_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('foods', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);
            $table->tinyInteger('status')->default(0)->comment('0:active, 1:inactive');
            $table->text('description');
            $table->integer('price');
            $table->integer('image_id')->nullable();
            $table->foreignId('cat_id')->nullable()->constrained('food_cats')->onDelete('set null');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('foods');
    }
};
