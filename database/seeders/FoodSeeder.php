<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Food;
use App\Models\FoodCat;

class FoodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories first
        $categories = [
            ['name' => 'Món chính', 'status' => 0],
            ['name' => 'Khai vị', 'status' => 0],
            ['name' => 'Tráng miệng', 'status' => 0],
            ['name' => 'Đồ uống', 'status' => 0],
        ];

        foreach ($categories as $categoryData) {
            FoodCat::firstOrCreate(['name' => $categoryData['name']], $categoryData);
        }

        // Get categories
        $mainCategory = FoodCat::where('name', 'Món chính')->first();
        $appetizerCategory = FoodCat::where('name', 'Khai vị')->first();
        $dessertCategory = FoodCat::where('name', 'Tráng miệng')->first();
        $drinkCategory = FoodCat::where('name', 'Đồ uống')->first();

        // Create foods
        $foods = [
            // Món chính
            ['name' => 'Phở Bò Tái', 'description' => 'Phở bò tái truyền thống', 'price' => 65000, 'cat_id' => $mainCategory->id, 'status' => 0],
            ['name' => 'Bún Bò Huế', 'description' => 'Bún bò Huế cay nồng', 'price' => 70000, 'cat_id' => $mainCategory->id, 'status' => 0],
            ['name' => 'Cơm Tấm Sườn Nướng', 'description' => 'Cơm tấm sườn nướng thơm ngon', 'price' => 75000, 'cat_id' => $mainCategory->id, 'status' => 0],
            ['name' => 'Bánh Mì Thịt Nướng', 'description' => 'Bánh mì thịt nướng Sài Gòn', 'price' => 35000, 'cat_id' => $mainCategory->id, 'status' => 0],
            ['name' => 'Mì Quảng', 'description' => 'Mì Quảng đặc sản miền Trung', 'price' => 68000, 'cat_id' => $mainCategory->id, 'status' => 0],

            // Khai vị
            ['name' => 'Gỏi Cuốn Tôm Thịt', 'description' => 'Gỏi cuốn tôm thịt tươi ngon', 'price' => 45000, 'cat_id' => $appetizerCategory->id, 'status' => 0],
            ['name' => 'Nem Nướng Nha Trang', 'description' => 'Nem nướng Nha Trang đặc biệt', 'price' => 55000, 'cat_id' => $appetizerCategory->id, 'status' => 0],
            ['name' => 'Chả Cá Lã Vọng', 'description' => 'Chả cá Lã Vọng truyền thống', 'price' => 85000, 'cat_id' => $appetizerCategory->id, 'status' => 0],

            // Tráng miệng
            ['name' => 'Chè Ba Màu', 'description' => 'Chè ba màu mát lạnh', 'price' => 25000, 'cat_id' => $dessertCategory->id, 'status' => 0],
            ['name' => 'Bánh Flan', 'description' => 'Bánh flan caramen', 'price' => 30000, 'cat_id' => $dessertCategory->id, 'status' => 0],
            ['name' => 'Kem Dừa', 'description' => 'Kem dừa tươi mát', 'price' => 35000, 'cat_id' => $dessertCategory->id, 'status' => 0],

            // Đồ uống
            ['name' => 'Cà Phê Sữa Đá', 'description' => 'Cà phê sữa đá truyền thống', 'price' => 25000, 'cat_id' => $drinkCategory->id, 'status' => 0],
            ['name' => 'Trà Đá', 'description' => 'Trà đá miễn phí', 'price' => 0, 'cat_id' => $drinkCategory->id, 'status' => 0],
            ['name' => 'Nước Dừa Tươi', 'description' => 'Nước dừa tươi mát lạnh', 'price' => 30000, 'cat_id' => $drinkCategory->id, 'status' => 0],
            ['name' => 'Sinh Tố Bơ', 'description' => 'Sinh tố bơ sáp béo ngậy', 'price' => 40000, 'cat_id' => $drinkCategory->id, 'status' => 0],
            ['name' => 'Bia Sài Gòn', 'description' => 'Bia Sài Gòn lạnh', 'price' => 20000, 'cat_id' => $drinkCategory->id, 'status' => 0],
        ];

        foreach ($foods as $foodData) {
            Food::firstOrCreate(['name' => $foodData['name']], $foodData);
        }
    }
}
