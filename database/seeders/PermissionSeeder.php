<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        $permissions = [
            'admin.dashboard',
            // User Management
            'admin.users.index',
            'admin.users.create',
            'admin.users.store',
            'admin.users.show',
            'admin.users.edit',
            'admin.users.update',
            'admin.users.destroy',

            // Role Management
            'admin.roles.index',
            'admin.roles.create',
            'admin.roles.store',
            'admin.roles.show',
            'admin.roles.edit',
            'admin.roles.update',
            'admin.roles.destroy',

            // Permission Management
            'admin.permissions.index',
            'admin.permissions.create',
            'admin.permissions.store',
            'admin.permissions.show',
            'admin.permissions.edit',
            'admin.permissions.update',
            'admin.permissions.destroy',

            // User Role & Permission Assignment
            'admin.users.roles.assign',
            'admin.users.roles.revoke',
            'admin.users.permissions.assign',
            'admin.users.permissions.revoke',

            'admin.permissions.index',
            'admin.permissions.show',
            'admin.permissions.sync-routes',
            'admin.permissions.sync-status',
            'admin.permissions.cleanup',
        ];
        foreach ($permissions as $permission) {
            try {
                Permission::create(['name' => $permission]);
            }catch (\Exception $exception){
                Log::channel('stderr')->info($exception->getMessage());
            }
        }

        $superAdminRole = Role::create(['name' => 'Super Admin']);
        $adminRole = Role::create(['name' => 'Admin']);
        $managerRole = Role::create(['name' => 'Manager']);
        $userRole = Role::create(['name' => 'User']);

        // Gán permissions cho roles
        $superAdminRole->givePermissionTo(Permission::all());
        $adminRole->givePermissionTo([
            'admin.users.index',
            'admin.users.create',
            'admin.users.store',
            'admin.users.show',
            'admin.users.edit',
            'admin.users.update',
            'admin.users.destroy',
            'admin.roles.index',
            'admin.roles.show',
        ]);
        $managerRole->givePermissionTo([
            'admin.users.index',
            'admin.users.show',
            'admin.users.edit',
            'admin.users.update',
        ]);

        // Tạo user mặc định
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $superAdmin->assignRole('Super Admin');

        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $admin->assignRole('Admin');

    }
}
