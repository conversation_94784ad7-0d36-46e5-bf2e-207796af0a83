<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Table;
use Illuminate\Support\Str;

class TableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tables = [
            ['name' => 'Bàn 1', 'capacity' => 4, 'location' => 'Tầng 1', 'description' => 'Bàn gần cửa sổ'],
            ['name' => 'Bàn 2', 'capacity' => 4, 'location' => 'Tầng 1', 'description' => 'Bàn ở giữa'],
            ['name' => 'Bàn 3', 'capacity' => 6, 'location' => 'Tầng 1', 'description' => 'Bàn lớn cho gia đình'],
            ['name' => 'Bàn 4', 'capacity' => 2, 'location' => 'Tầng 1', 'description' => 'Bàn đôi lãng mạn'],
            ['name' => 'Bàn VIP A', 'capacity' => 8, 'location' => 'Tầng 2', 'description' => 'Phòng VIP riêng tư'],
            ['name' => 'Bàn VIP B', 'capacity' => 10, 'location' => 'Tầng 2', 'description' => 'Phòng VIP lớn'],
            ['name' => 'Bàn 7', 'capacity' => 4, 'location' => 'Sân thượng', 'description' => 'Bàn ngoài trời'],
            ['name' => 'Bàn 8', 'capacity' => 4, 'location' => 'Sân thượng', 'description' => 'Bàn view đẹp'],
        ];

        foreach ($tables as $tableData) {
            // Generate unique code
            do {
                $code = 'TBL-' . strtoupper(Str::random(6));
            } while (Table::where('code', $code)->exists());

            $tableData['code'] = $code;
            $tableData['status'] = 0; // Available

            Table::create($tableData);
        }
    }
}
