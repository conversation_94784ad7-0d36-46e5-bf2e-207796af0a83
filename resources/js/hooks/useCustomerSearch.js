// src/hooks/useCustomerSearch.js
import { useState, useCallback } from 'react';
import { orderService } from '../services/orderService';
import dayjs from 'dayjs';

export const useCustomerSearch = () => {
    const [customerSuggestions, setCustomerSuggestions] = useState([]);
    const [customerInfo, setCustomerInfo] = useState(null);
    const [searchTimeout, setSearchTimeout] = useState(null);

    const searchCustomers = useCallback(async (query) => {
        if (query.length < 3) {
            setCustomerSuggestions([]);
            return;
        }

        try {
            const customers = await orderService.searchCustomers(query);
            const suggestions = customers.map(customer => ({
                value: customer.phone,
                label: `${customer.name} - ${customer.phone}${customer.total_orders ? ` (${customer.total_orders} đơn hàng)` : ''}`,
                customer: customer
            }));
            setCustomerSuggestions(suggestions);
        } catch (error) {
            console.error('Error searching customers:', error);
            setCustomerSuggestions([]);
        }
    }, []);

    const handlePhoneSearch = useCallback((value) => {
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        const timeout = setTimeout(() => {
            searchCustomers(value);
        }, 300);

        setSearchTimeout(timeout);
    }, [searchCustomers, searchTimeout]);

    const handleCustomerSelect = useCallback((value, option, form) => {
        if (option?.customer) {
            const customer = option.customer;

            // Parse birthday safely
            let birthdayValue = null;
            if (customer.birthday) {
                try {
                    const birthdayDate = dayjs(customer.birthday);
                    if (birthdayDate.isValid()) {
                        birthdayValue = birthdayDate;
                    }
                } catch (error) {
                    console.warn('Invalid birthday format:', customer.birthday);
                    birthdayValue = null;
                }
            }

            // Fill form with customer data
            if (form && form.setFieldsValue) {
                form.setFieldsValue({
                    customer_phone: customer.phone,
                    customer_name: customer.name,
                    customer_email: customer.email || '',
                    customer_birthday: birthdayValue,
                    customer_address: customer.address || ''
                });
            }

            setCustomerInfo(customer);
        }
    }, []);

    const resetCustomerData = useCallback(() => {
        setCustomerSuggestions([]);
        setCustomerInfo(null);
        if (searchTimeout) {
            clearTimeout(searchTimeout);
            setSearchTimeout(null);
        }
    }, [searchTimeout]);

    return {
        customerSuggestions,
        customerInfo,
        handlePhoneSearch,
        handleCustomerSelect,
        resetCustomerData
    };
};
