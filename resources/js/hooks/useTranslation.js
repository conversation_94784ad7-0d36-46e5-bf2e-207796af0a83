import { useState, useEffect, createContext, useContext } from 'react';
import { locales, defaultLocale, getLocale, setLocale } from '../locales/index.js';

// Create translation context
const TranslationContext = createContext();

// Translation provider component
export const TranslationProvider = ({ children }) => {
  const [currentLocale, setCurrentLocale] = useState(getLocale());

  const changeLocale = (locale) => {
    if (setLocale(locale)) {
      setCurrentLocale(locale);
      // Trigger a page reload to update Ant Design locale
      window.location.reload();
    }
  };

  const t = (key, fallback = key) => {
    const translation = locales[currentLocale]?.[key];
    return translation || locales[defaultLocale]?.[key] || fallback;
  };

  const value = {
    locale: currentLocale,
    changeLocale,
    t
  };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
};

// Hook to use translation
export const useTranslation = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
};
