import { useState, useEffect } from 'react';
import { notification } from 'antd';
import { orderService } from '../services/orderService';
import { mapOrderFromAPI, mapTableFromAPI } from '../utils/mappers';

export const useOrders = () => {
    const [orders, setOrders] = useState([]);
    const [tables, setTables] = useState([]);
    const [loading, setLoading] = useState(false);

    const loadData = async () => {
        setLoading(true);
        try {
            const data = await orderService.getOrders();
            setOrders(data.orders.map(mapOrderFromAPI));
            setTables(data.tables.map(mapTableFromAPI));

            notification.success({
                message: 'Thành công',
                description: `Đã tải ${data.orders.length} orders và ${data.tables.length} bàn từ API`,
                placement: 'topRight'
            });
        } catch (error) {
            console.error('Error loading data:', error);
            notification.error({
                message: 'Lỗi',
                description: '<PERSON>h<PERSON>ng thể tải dữ liệu từ API',
                placement: 'topRight'
            });
        } finally {
            setLoading(false);
        }
    };

    const refreshOrders = async () => {
        await loadData();
        notification.success({
            message: 'Thành công',
            description: 'Đã làm mới dữ liệu!'
        });
    };

    useEffect(() => {
        loadData();
        const interval = setInterval(refreshOrders, 60000);
        return () => clearInterval(interval);
    }, []);

    return {
        orders,
        tables,
        loading,
        setOrders,
        setTables,
        loadData,
        refreshOrders
    };
};
