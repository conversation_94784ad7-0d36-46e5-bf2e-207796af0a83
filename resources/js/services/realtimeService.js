
import Pusher from 'pusher-js';
import { notification } from 'antd';

window.Pusher = Pusher;

class RealtimeService {
    constructor() {
        this.echo = null;
        this.kitchenChannel = null;
        this.restaurantChannel = null;
        this.listeners = new Map();
        this.soundEnabled = JSON.parse(localStorage.getItem('kitchen_sound_enabled') ?? 'true');
        this.init();
    }

    init() {
        this.connectToChannels();
    }

    connectToChannels() {
        // Kitchen channel for kitchen staff
        this.kitchenChannel = this.echo.channel('kitchen');

        // Restaurant channel for general updates
        this.restaurantChannel = this.echo.channel('restaurant');

        this.setupChannelListeners();
    }

    setupChannelListeners() {
        // New order received
        this.kitchenChannel.listen('order.new', (event) => {
            console.log('New order received:', event);

            this.playNotificationSound('new-order');

            notification.info({
                message: '🆕 Đơn hàng mới',
                description: `${event.order.order_number} - ${event.order.table_name}`,
                placement: 'topRight',
                duration: 5,
                className: 'animate-bounce'
            });

            this.notifyListeners('new-order', event);
        });

        // Order status updated
        this.kitchenChannel.listen('order.status.updated', (event) => {
            console.log('Order status updated:', event);

            this.playNotificationSound('status-update');

            const statusMessages = {
                1: '⏳ Chờ chế biến',
                2: '🔥 Đang chế biến',
                3: '✅ Sẵn sàng',
                4: '🎉 Hoàn thành'
            };

            notification.success({
                message: 'Cập nhật đơn hàng',
                description: `${event.order.order_number} → ${statusMessages[event.order.status]}`,
                placement: 'topRight',
                duration: 3
            });

            this.notifyListeners('order-updated', event);
        });

        // Kitchen notifications
        this.kitchenChannel.listen('kitchen.notification', (event) => {
            console.log('Kitchen notification:', event);

            const notificationTypes = {
                'chef_assigned': { type: 'info', icon: '👨‍🍳' },
                'order_ready': { type: 'success', icon: '🔔', sound: 'order-ready' },
                'bulk_update': { type: 'info', icon: '📋' },
                'priority_alert': { type: 'warning', icon: '⚠️', sound: 'priority-alert' }
            };

            const config = notificationTypes[event.type] || { type: 'info', icon: '📢' };

            if (config.sound) {
                this.playNotificationSound(config.sound);
            }

            notification[config.type]({
                message: `${config.icon} ${event.message}`,
                description: event.data?.details || '',
                placement: 'topRight',
                duration: event.type === 'order_ready' ? 10 : 4
            });

            this.notifyListeners('kitchen-notification', event);
        });

        // Connection status
        this.echo.connector.pusher.connection.bind('connected', () => {
            console.log('✅ Real-time connection established');
            this.notifyListeners('connection-status', { status: 'connected' });
        });

        this.echo.connector.pusher.connection.bind('disconnected', () => {
            console.log('❌ Real-time connection lost');
            this.notifyListeners('connection-status', { status: 'disconnected' });

            notification.warning({
                message: 'Mất kết nối real-time',
                description: 'Đang thử kết nối lại...',
                placement: 'topRight'
            });
        });

        this.echo.connector.pusher.connection.bind('reconnected', () => {
            console.log('🔄 Real-time connection restored');
            this.notifyListeners('connection-status', { status: 'reconnected' });

            notification.success({
                message: 'Đã khôi phục kết nối real-time',
                placement: 'topRight'
            });
        });
    }

    // Event listener management
    addEventListener(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }
        this.listeners.get(event).add(callback);

        return () => {
            this.listeners.get(event)?.delete(callback);
        };
    }

    notifyListeners(event, data) {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            eventListeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    // Sound management
    playNotificationSound(soundType) {
        if (!this.soundEnabled) return;

        const sounds = {
            'new-order': '/sounds/new-order.mp3',
            'order-ready': '/sounds/order-ready.mp3',
            'status-update': '/sounds/status-update.mp3',
            'priority-alert': '/sounds/priority-alert.mp3'
        };

        const soundFile = sounds[soundType];
        if (soundFile) {
            const audio = new Audio(soundFile);
            audio.volume = 0.7;
            audio.play().catch(error => {
                console.log('Could not play sound:', error);
            });
        }
    }

    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        localStorage.setItem('kitchen_sound_enabled', JSON.stringify(this.soundEnabled));
        return this.soundEnabled;
    }

    isSoundEnabled() {
        return this.soundEnabled;
    }

    // Manual API calls
    async updateOrderStatus(orderId, status, chefId = null, notes = null) {
        try {
            const response = await fetch(`/api/kitchen/orders/${orderId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                },
                body: JSON.stringify({
                    status,
                    chef_id: chefId,
                    notes
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update order status');
            }

            return await response.json();
        } catch (error) {
            console.error('Error updating order status:', error);
            notification.error({
                message: 'Lỗi cập nhật',
                description: 'Không thể cập nhật trạng thái đơn hàng',
                placement: 'topRight'
            });
            throw error;
        }
    }

    async assignChef(orderId, chefId) {
        try {
            const response = await fetch(`/api/kitchen/orders/${orderId}/assign-chef`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                },
                body: JSON.stringify({ chef_id: chefId })
            });

            if (!response.ok) {
                throw new Error('Failed to assign chef');
            }

            return await response.json();
        } catch (error) {
            console.error('Error assigning chef:', error);
            notification.error({
                message: 'Lỗi phân công',
                description: 'Không thể phân công đầu bếp',
                placement: 'topRight'
            });
            throw error;
        }
    }

    async markOrderReady(orderId) {
        try {
            const response = await fetch(`/api/kitchen/orders/${orderId}/ready`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                }
            });

            if (!response.ok) {
                throw new Error('Failed to mark order as ready');
            }

            return await response.json();
        } catch (error) {
            console.error('Error marking order ready:', error);
            notification.error({
                message: 'Lỗi cập nhật',
                description: 'Không thể đánh dấu đơn hàng sẵn sàng',
                placement: 'topRight'
            });
            throw error;
        }
    }

    // Cleanup
    disconnect() {
        if (this.kitchenChannel) {
            this.echo.leaveChannel('kitchen');
        }
        if (this.restaurantChannel) {
            this.echo.leaveChannel('restaurant');
        }
        if (this.echo) {
            this.echo.disconnect();
        }
        this.listeners.clear();
    }
}

// Singleton instance
export default new RealtimeService();
