const API_BASE_URL = '/api';

export const orderService = {
    async getOrders() {
        const response = await fetch(`${API_BASE_URL}/orders?status=[1,2,3,4]`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch orders');
        }

        return await response.json();
    },

    async mergeOrderTables(orderId, tables) {
        const response = await fetch(`${API_BASE_URL}/orders/${orderId}/merge-tables`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ tables })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to merge tables');
        }

        return await response.json();
    },

    async processPayment(orderId, paymentData) {
        const response = await fetch(`${API_BASE_URL}/orders/${orderId}/payment`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(paymentData)
        });

        if (!response.ok) {
            throw new Error('Payment failed');
        }

        return await response.json();
    },
    async getQrApi(orderId) {
        const response = await fetch(`${API_BASE_URL}/orders/${orderId}/getQrPayment`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
        });

        if (!response.ok) {
            throw new Error('Payment failed');
        }

        return await response.json();
    },

    createOrder: async (orderData) => {
        try {
            const response = await fetch('/api/orders/store', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify(orderData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to create order');
            }

            const result = await response.json();
            return result.data || result;
        } catch (error) {
            console.error('Error creating order:', error);
            throw error;
        }
    },

    // Search customers by phone/name
    searchCustomers: async (query) => {
        try {
            const response = await fetch(`/api/customers/search?query=${encodeURIComponent(query)}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (!response.ok) {
                throw new Error('Failed to search customers');
            }

            const result = await response.json();
            return result.data || result;
        } catch (error) {
            console.error('Error searching customers:', error);
            return [];
        }
    },
};
