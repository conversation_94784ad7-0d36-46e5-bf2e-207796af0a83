import { notification } from 'antd';

class RedisRealtimeService {
    constructor() {
        this.eventSource = null;
        this.websocket = null;
        this.listeners = new Map();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.soundEnabled = JSON.parse(localStorage.getItem('kitchen_sound_enabled') ?? 'true');
        this.connectionStatus = 'disconnected';
        this.orderCache = new Map();
        this.statsCache = null;
    }

    /**
     * Sound management
     */
    playNotificationSound(soundType) {
        if (!this.soundEnabled) return;

        const sounds = {
            'new-order': '/sounds/new-order.mp3',
            'order-ready': '/sounds/order-ready.mp3',
            'status-update': '/sounds/status-update.mp3',
            'priority-alert': '/sounds/priority-alert.mp3',
            'urgent-alert': '/sounds/urgent-alert.mp3',
            'chef-assigned': '/sounds/chef-assigned.mp3'
        };

        const soundFile = sounds[soundType];
        if (soundFile) {
            const audio = new Audio(soundFile);
            audio.volume = 0.7;
            audio.play().catch(error => {
                console.log('Could not play sound:', error);
            });
        }
    }

    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        localStorage.setItem('kitchen_sound_enabled', JSON.stringify(this.soundEnabled));
        return this.soundEnabled;
    }

    isSoundEnabled() {
        return this.soundEnabled;
    }

    /**
     * Connection status
     */
    getConnectionStatus() {
        return this.connectionStatus;
    }

    isConnected() {
        return this.connectionStatus === 'connected';
    }

    /**
     * Cleanup and disconnect
     */
    disconnect() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        this.listeners.clear();
        this.orderCache.clear();
        this.statsCache = null;
        this.connectionStatus = 'disconnected';
    }

    /**
     * Cache management
     */
    clearCache() {
        this.orderCache.clear();
        this.statsCache = null;

        notification.info({
            message: '🗑️ Cache đã được xóa',
            description: 'Dữ liệu cục bộ đã được làm mới',
            placement: 'topRight',
            duration: 2
        });
    }

    getCacheSize() {
        return {
            orders: this.orderCache.size,
            stats: this.statsCache ? 1 : 0
        };
    }

    /**
     * Performance monitoring
     */
    getPerformanceMetrics() {
        return {
            connection_status: this.connectionStatus,
            reconnect_attempts: this.reconnectAttempts,
            cache_size: this.getCacheSize(),
            last_message_time: this.lastMessageTime || null,
            sound_enabled: this.soundEnabled
        };
    }

init() {
    this.connectWebSocket();
    this.setupPeriodicSync();
    this.setupVisibilityHandler();
}

/**
 * Connect via WebSocket for real-time updates
 */
connectWebSocket() {
    try {
        const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8080/kitchen';
        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            console.log('✅ WebSocket connected to Redis');
            this.connectionStatus = 'connected';
            this.reconnectAttempts = 0;
            this.notifyListeners('connection-status', { status: 'connected' });

            // Subscribe to kitchen channels
            this.subscribeToChannels();
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleRedisMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        this.websocket.onclose = () => {
            console.log('❌ WebSocket disconnected from Redis');
            this.connectionStatus = 'disconnected';
            this.notifyListeners('connection-status', { status: 'disconnected' });
            this.scheduleReconnect();
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.connectionStatus = 'error';
        };

    } catch (error) {
        console.error('Failed to connect WebSocket:', error);
        this.fallbackToSSE();
    }
}

/**
 * Fallback to Server-Sent Events if WebSocket fails
 */
fallbackToSSE() {
    console.log('🔄 Falling back to SSE');

    const sseUrl = '/api/kitchen/realtime-updates';
    this.eventSource = new EventSource(sseUrl);

    this.eventSource.onopen = () => {
        console.log('✅ SSE connected');
        this.connectionStatus = 'connected';
        this.notifyListeners('connection-status', { status: 'connected' });
    };

    this.eventSource.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data);
            this.handleRedisMessage(data);
        } catch (error) {
            console.error('Error parsing SSE message:', error);
        }
    };

    this.eventSource.onerror = () => {
        console.log('❌ SSE connection error');
        this.connectionStatus = 'disconnected';
        this.notifyListeners('connection-status', { status: 'disconnected' });
    };
}

/**
 * Subscribe to Redis channels via WebSocket
 */
subscribeToChannels() {
    const channels = ['kitchen:orders', 'kitchen:stats', 'restaurant:updates'];

    channels.forEach(channel => {
        this.sendMessage({
            action: 'subscribe',
            channel: channel
        });
    });
}

/**
 * Handle messages from Redis
 */
handleRedisMessage(data) {
    console.log('📨 Redis message:', data);

    switch (data.type) {
        case 'new_order':
            this.handleNewOrder(data);
            break;
        case 'order_updated':
            this.handleOrderUpdate(data);
            break;
        case 'chef_assigned':
            this.handleChefAssignment(data);
            break;
        case 'stats_updated':
            this.handleStatsUpdate(data);
            break;
        case 'priority_escalated':
            this.handlePriorityEscalation(data);
            break;
        case 'overdue_alert':
            this.handleOverdueAlert(data);
            break;
        default:
            console.log('Unknown message type:', data.type);
    }
}

/**
 * Handle new order from Redis
 */
handleNewOrder(data) {
    const order = data.order;

    // Update local cache
    this.orderCache.set(order.id, order);

    // Play notification sound
    if (data.sound) {
        this.playNotificationSound(data.sound);
    }

    // Show notification
    const notificationType = order.priority >= 3 ? 'warning' : 'info';
    notification[notificationType]({
        message: '🆕 Đơn hàng mới',
        description: `${order.order_number} - ${order.table_name}`,
        placement: 'topRight',
        duration: order.priority >= 3 ? 10 : 5,
        className: order.priority >= 3 ? 'animate-bounce' : ''
    });

    // Notify listeners
    this.notifyListeners('new-order', data);
}

/**
 * Handle order status update
 */
handleOrderUpdate(data) {
    const order = data.order;

    // Update local cache
    this.orderCache.set(order.id, order);

    // Play notification sound
    if (data.sound) {
        this.playNotificationSound(data.sound);
    }

    // Show notification based on action
    const statusMessages = {
        1: '⏳ Chờ chế biến',
        2: '🔥 Đang chế biến',
        3: '✅ Sẵn sàng',
        4: '🎉 Hoàn thành'
    };

    const message = data.action === 'ready' ? '🔔 Sẵn sàng phục vụ' : 'Cập nhật đơn hàng';
    const description = `${order.order_number} → ${statusMessages[order.status]}`;

    const notificationType = data.action === 'ready' ? 'success' : 'info';
    notification[notificationType]({
        message,
        description,
        placement: 'topRight',
        duration: data.action === 'ready' ? 8 : 3
    });

    // Notify listeners
    this.notifyListeners('order-updated', data);
}

/**
 * Handle chef assignment
 */
handleChefAssignment(data) {
    notification.info({
        message: '👨‍🍳 Phân công đầu bếp',
        description: data.message,
        placement: 'topRight',
        duration: 3
    });

    this.notifyListeners('chef-assigned', data);
}

/**
 * Handle statistics update
 */
handleStatsUpdate(data) {
    this.statsCache = data.stats;
    this.notifyListeners('stats-updated', data);
}

/**
 * Handle priority escalation
 */
handlePriorityEscalation(data) {
    this.playNotificationSound('priority-alert');

    notification.warning({
        message: '⚠️ Nâng độ ưu tiên',
        description: data.message,
        placement: 'topRight',
        duration: 5,
        className: 'animate-pulse'
    });

    this.notifyListeners('priority-escalated', data);
}

/**
 * Handle overdue orders alert
 */
handleOverdueAlert(data) {
    this.playNotificationSound('urgent-alert');

    notification.error({
        message: '🚨 Đơn hàng quá hạn',
        description: data.message,
        placement: 'topRight',
        duration: 10,
        className: 'animate-bounce'
    });

    this.notifyListeners('overdue-alert', data);
}

/**
 * API methods for manual operations
 */
async updateOrderStatus(orderId, status, chefId = null, notes = null) {
    try {
        // Optimistic update
        const cachedOrder = this.orderCache.get(orderId);
        if (cachedOrder) {
            cachedOrder.status = status;
            cachedOrder.chef_assigned = chefId || cachedOrder.chef_assigned;
            this.orderCache.set(orderId, cachedOrder);
        }

        const response = await fetch(`/api/kitchen/orders/${orderId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify({
                status,
                chef_id: chefId,
                notes
            })
        });

        if (!response.ok) {
            // Revert optimistic update
            if (cachedOrder) {
                this.orderCache.delete(orderId);
            }
            throw new Error('Failed to update order status');
        }

        const result = await response.json();

        // Update cache with server response
        if (result.order) {
            this.orderCache.set(orderId, result.order);
        }

        return result;
    } catch (error) {
        console.error('Error updating order status:', error);
        notification.error({
            message: 'Lỗi cập nhật',
            description: 'Không thể cập nhật trạng thái đơn hàng',
            placement: 'topRight'
        });
        throw error;
    }
}

/**
 * Get cached orders (faster than API call)
 */
getCachedOrders() {
    return Array.from(this.orderCache.values());
}

/**
 * Get cached statistics
 */
getCachedStats() {
    return this.statsCache;
}

/**
 * Force sync with Redis
 */
async syncWithRedis() {
    try {
        const response = await fetch('/api/kitchen/sync-redis', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            }
        });

        if (response.ok) {
            // Reload orders after sync
            await this.loadOrders();

            notification.success({
                message: '✅ Đồng bộ thành công',
                description: 'Dữ liệu đã được cập nhật từ Redis',
                placement: 'topRight'
            });
        }
    } catch (error) {
        console.error('Sync failed:', error);
        notification.error({
            message: 'Lỗi đồng bộ',
            description: 'Không thể đồng bộ với Redis',
            placement: 'topRight'
        });
    }
}

/**
 * Load orders from API and cache them
 */
async loadOrders(params = {}) {
    try {
        const queryParams = new URLSearchParams(params);
        const response = await fetch(`/api/kitchen/orders?${queryParams}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
            }
        });

        if (!response.ok) throw new Error('Failed to load orders');

        const data = await response.json();

        // Update cache
        if (data.orders) {
            data.orders.forEach(order => {
                this.orderCache.set(order.id, order);
            });
        }

        if (data.statistics) {
            this.statsCache = data.statistics;
        }

        return data;
    } catch (error) {
        console.error('Error loading orders:', error);
        throw error;
    }
}

/**
 * Periodic sync with backend (fallback)
 */
setupPeriodicSync() {
    setInterval(() => {
        if (this.connectionStatus !== 'connected') {
            this.loadOrders().catch(console.error);
        }
    }, 30000); // Every 30 seconds when disconnected
}

/**
 * Handle page visibility changes
 */
setupVisibilityHandler() {
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden && this.connectionStatus !== 'connected') {
            // Page became visible and we're disconnected, try to reconnect
            this.init();
        }
    });
}

/**
 * Schedule reconnection attempts
 */
scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

        setTimeout(() => {
            console.log(`🔄 Reconnection attempt ${this.reconnectAttempts}`);
            this.connectWebSocket();
        }, delay);
    } else {
        console.log('❌ Max reconnection attempts reached');
        this.fallbackToSSE();
    }
}

/**
 * Send message via WebSocket
 */
sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify(message));
    }
}

/**
 * Event listener management
 */
addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
        this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);

    return () => {
        this.listeners.get(event)?.delete(callback);
    };
}

notifyListeners(event, data) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
        eventListeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`Error in event listener for ${event}:`, error);
            }
        });
    }
}
}
export default new RedisRealtimeService();
