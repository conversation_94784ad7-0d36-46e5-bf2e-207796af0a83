export const getOrders = async (params = {}) => {
    try {
        const response = await fetch(`/api/kitchen/orderFoods`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
            },
        });

        if (!response.ok) throw new Error('Failed to load foods');

        return await response.json();
    } catch (error) {
        console.error('Error loading foods:', error);
        throw error;
    }
};

export const updateFoodStatus = async (foodId, status, notes = null) => {
    try {
        const response = await fetch(`/api/kitchen/foods/${foodId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify({
                status,
                notes
            })
        });

        if (!response.ok) {
            throw new Error('Failed to update food status');
        }

        return await response.json();
    } catch (error) {
        console.error('Error updating food status:', error);
        throw error;
    }
};

export const updateFoodQuantity = async (foodName, quantity, status) => {
    try {
        const response = await fetch(`/api/kitchen/foods/updateStatus`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify({
                foodName,
                quantity,
                status
            })
        });

        if (!response.ok) {
            throw new Error('Failed to update food quantity');
        }

        return await response.json();
    } catch (error) {
        console.error('Error updating food quantity:', error);
        throw error;
    }
};

export const loudSpeaker = async (text) => {
    console.log(text);
    // Skip if text is empty
    if (text.length === 0) return;

    try {
      // Create an AudioContext
      const ctx = new (window.AudioContext || window.webkitAudioContext)();

      // Resume AudioContext if suspended (required for autoplay in browsers)
      if (ctx.state === 'suspended') {
        await ctx.resume();
        console.log('AudioContext resumed successfully');
      }

      // Fetch MP3 from API using query parameter
      const response = await fetch(`/api/mp3?q=${encodeURIComponent(text)}`, {
        method: 'GET',
        headers: {
          'Accept': 'audio/mpeg' // Use 'Accept' header for expected response type
        }
      });

      // Check if response is OK
      if (!response.ok) {
        throw new Error(`Failed to fetch MP3: ${response.statusText}`);
      }

      // Convert response to ArrayBuffer and decode to AudioBuffer
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await ctx.decodeAudioData(arrayBuffer);

      // Create and play audio source
      const source = ctx.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(ctx.destination);
      source.start();

      // Optionally, stop after the audio's full duration
      source.onended = () => {
        console.log('Audio playback finished');
        ctx.close(); // Clean up AudioContext
      };

      // Example: Stop after duration (optional, remove if not needed)
      // source.stop(ctx.currentTime + audioBuffer.duration);
    } catch (error) {
      console.error('Error in loudSpeaker:', error);
    }
  };
