export default {
  // Header
  order: 'Order',
  
  // Navigation tabs
  menu: 'Thực đơn',
  cart: 'Giỏ hàng',
  history: 'L<PERSON>ch sử',
  
  // Messages
  orderPlacedSuccess: 'Đặt món thành công!',
  cannotUpdateOrderStatus: '<PERSON>h<PERSON><PERSON> thể cập nhật trạng thái đơn hàng.',
  paymentRequestSent: 'Đã gửi yêu cầu thanh toán. Vui lòng đợi nhân viên.',
  cannotSendPaymentRequest: 'Không thể gửi yêu cầu thanh toán.',
  paymentRequestError: 'Lỗi khi gửi yêu cầu thanh toán.',
  
  // Payment modal
  requestPayment: 'Gọi thanh toán',
  requestPaymentTitle: 'Gọi thanh toán',
  enterDiscountCode: 'Nhập mã giảm giá (nếu có)',
  confirmPaymentRequest: 'Bạn có chắc muốn gọi nhân viên để thanh toán cho đơn hàng này?',
  cancel: 'Hủy',
  
  // Common
  loading: '<PERSON>ang tải...',
  error: 'Lỗi',
  success: 'Thành công',
  confirm: 'Xác nhận',
  
  // Language switcher
  language: 'Ngôn ngữ',
  english: 'Tiếng Anh',
  vietnamese: 'Tiếng Việt',

  // Cart View
  cartEmpty: 'Giỏ hàng trống',
  addItemsFromMenu: 'Hãy thêm món ăn từ thực đơn',
  cartItems: 'Giỏ hàng ({count} món)',
  clearAll: 'Xóa tất cả',
  removeFromCart: 'Xóa món khỏi giỏ hàng?',
  confirmRemoveItem: 'Bạn có chắc muốn xóa "{name}" khỏi giỏ hàng?',
  remove: 'Xóa',
  removeAllItems: 'Xóa tất cả món?',
  confirmClearCart: 'Bạn có chắc muốn xóa tất cả món khỏi giỏ hàng?',
  orderSummary: 'Tổng đơn hàng',
  placeOrder: 'Đặt món ({count} món)',
  orderPlacedSuccessfully: 'Đặt món thành công!',
  orderError: 'Có lỗi xảy ra khi đặt món',
  cartEmptyWarning: 'Giỏ hàng trống',
  perItem: '/ món',

  // Menu View
  all: 'Tất cả',
  searchPlaceholder: 'Tìm kiếm món ăn...',
  addToCart: 'Thêm vào giỏ',
  add: 'Thêm',
  hot: 'Hot',
  outOfStock: 'Tạm hết',

  // Order History
  noOrders: 'Chưa có đơn hàng nào',
  orderDetails: 'Đơn hàng',
  emptyOrder: 'Đơn hàng trống',
  paymentDetails: 'Chi tiết thanh toán',
  subtotal: 'Tạm tính:',
  discount: 'Giảm giá:',
  total: 'Tổng cộng:',
  payment: 'Thanh toán:',
  itemTypes: 'Loại món',
  totalQuantity: 'Tổng số lượng',
  removeFromOrder: 'Xóa món khỏi đơn hàng?',
  confirmRemoveFromOrder: 'Bạn có chắc muốn xóa món này khỏi đơn hàng?',
  itemRemovedSuccess: 'Đã xóa món khỏi đơn hàng',
  cannotRemoveItem: 'Không thể xóa món',
  removeItemError: 'Lỗi khi xóa món',
  totalAmount: 'Tổng tiền',

  // Order Status
  pending: 'Đang xử lý',
  confirmed: 'Đã xác nhận',
  preparing: 'Đang chuẩn bị',
  ready: 'Sẵn sàng',
  served: 'Đã phục vụ',
  completed: 'Hoàn thành',
  cancelled: 'Đã hủy'
};
