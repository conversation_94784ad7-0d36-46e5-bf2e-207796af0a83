export default {
  // Header
  order: 'Order',
  
  // Navigation tabs
  menu: 'Thực đơn',
  cart: 'Giỏ hàng',
  history: 'L<PERSON>ch sử',
  
  // Messages
  orderPlacedSuccess: 'Đặt món thành công!',
  cannotUpdateOrderStatus: '<PERSON>h<PERSON><PERSON> thể cập nhật trạng thái đơn hàng.',
  paymentRequestSent: 'Đã gửi yêu cầu thanh toán. Vui lòng đợi nhân viên.',
  cannotSendPaymentRequest: 'Không thể gửi yêu cầu thanh toán.',
  paymentRequestError: 'Lỗi khi gửi yêu cầu thanh toán.',
  
  // Payment modal
  requestPayment: 'Gọi thanh toán',
  requestPaymentTitle: 'Gọi thanh toán',
  enterDiscountCode: 'Nhập mã giảm giá (nếu có)',
  confirmPaymentRequest: 'Bạn có chắc muốn gọi nhân viên để thanh toán cho đơn hàng này?',
  cancel: 'Hủy',
  
  // Common
  loading: '<PERSON>ang tải...',
  error: 'Lỗi',
  success: 'Thành công',
  confirm: 'Xác nhận',
  
  // Language switcher
  language: 'Ngôn ngữ',
  english: 'Tiếng Anh',
  vietnamese: 'Tiếng Việt'
};
