import en from './en.js';
import vi from './vi.js';

export const locales = {
  en,
  vi
};

export const defaultLocale = 'vi';

export const getLocale = () => {
  const stored = localStorage.getItem('locale');
  return stored && locales[stored] ? stored : defaultLocale;
};

export const setLocale = (locale) => {
  if (locales[locale]) {
    localStorage.setItem('locale', locale);
    return true;
  }
  return false;
};
