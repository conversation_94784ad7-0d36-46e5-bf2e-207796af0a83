export default {
  // Header
  order: 'Order',
  
  // Navigation tabs
  menu: 'Menu',
  cart: 'Cart',
  history: 'History',
  
  // Messages
  orderPlacedSuccess: 'Order placed successfully!',
  cannotUpdateOrderStatus: 'Cannot update order status.',
  paymentRequestSent: 'Payment request sent. Please wait for staff.',
  cannotSendPaymentRequest: 'Cannot send payment request.',
  paymentRequestError: 'Error sending payment request.',
  
  // Payment modal
  requestPayment: 'Request Payment',
  requestPaymentTitle: 'Request Payment',
  enterDiscountCode: 'Enter discount code (if any)',
  confirmPaymentRequest: 'Are you sure you want to call staff to pay for this order?',
  cancel: 'Cancel',
  
  // Common
  loading: 'Loading...',
  error: 'Error',
  success: 'Success',
  confirm: 'Confirm',
  
  // Language switcher
  language: 'Language',
  english: 'English',
  vietnamese: 'Vietnamese',

  // Cart View
  cartEmpty: 'Cart is empty',
  addItemsFromMenu: 'Add items from menu',
  cartItems: 'Cart ({count} items)',
  clearAll: 'Clear all',
  removeFromCart: 'Remove from cart?',
  confirmRemoveItem: 'Are you sure you want to remove "{name}" from cart?',
  remove: 'Remove',
  removeAllItems: 'Remove all items?',
  confirmClearCart: 'Are you sure you want to remove all items from cart?',
  orderSummary: 'Order Summary',
  placeOrder: 'Place Order ({count} items)',
  orderPlacedSuccessfully: 'Order placed successfully!',
  orderError: 'An error occurred while placing the order',
  cartEmptyWarning: 'Cart is empty',
  perItem: '/ item',

  // Menu View
  all: 'All',
  searchPlaceholder: 'Search dishes...',
  addToCart: 'Add to cart',
  add: 'Add',
  hot: 'Hot',
  outOfStock: 'Out of stock',

  // Order History
  noOrders: 'No orders yet',
  orderDetails: 'Order Details',
  emptyOrder: 'Empty order',
  paymentDetails: 'Payment Details',
  subtotal: 'Subtotal:',
  discount: 'Discount:',
  total: 'Total:',
  payment: 'Payment:',
  itemTypes: 'Item types',
  totalQuantity: 'Total quantity',
  removeFromOrder: 'Remove from order?',
  confirmRemoveFromOrder: 'Are you sure you want to remove this item from the order?',
  itemRemovedSuccess: 'Item removed from order',
  cannotRemoveItem: 'Cannot remove item',
  removeItemError: 'Error removing item',
  totalAmount: 'Total amount',

  // Order Status
  pending: 'Pending',
  confirmed: 'Confirmed',
  preparing: 'Preparing',
  ready: 'Ready',
  served: 'Served',
  completed: 'Completed',
  cancelled: 'Cancelled'
};
