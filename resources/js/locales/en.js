export default {
  // Header
  order: 'Order',
  
  // Navigation tabs
  menu: 'Menu',
  cart: 'Cart',
  history: 'History',
  
  // Messages
  orderPlacedSuccess: 'Order placed successfully!',
  cannotUpdateOrderStatus: 'Cannot update order status.',
  paymentRequestSent: 'Payment request sent. Please wait for staff.',
  cannotSendPaymentRequest: 'Cannot send payment request.',
  paymentRequestError: 'Error sending payment request.',
  
  // Payment modal
  requestPayment: 'Request Payment',
  requestPaymentTitle: 'Request Payment',
  enterDiscountCode: 'Enter discount code (if any)',
  confirmPaymentRequest: 'Are you sure you want to call staff to pay for this order?',
  cancel: 'Cancel',
  
  // Common
  loading: 'Loading...',
  error: 'Error',
  success: 'Success',
  confirm: 'Confirm',
  
  // Language switcher
  language: 'Language',
  english: 'English',
  vietnamese: 'Vietnamese'
};
