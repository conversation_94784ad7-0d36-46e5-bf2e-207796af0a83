import React from 'react';
import { createRoot } from 'react-dom/client';
import { ConfigProvider } from 'antd';
import viVN from 'antd/locale/vi_VN';
import enUS from 'antd/locale/en_US';
import MobileOrdering from './components/MobileOrdering';
import { TranslationProvider } from './hooks/useTranslation.js';
import { getLocale } from './locales/index.js';

const table = window.tableData;
const activeOrder = window.activeOrderData;

const App = () => {
    const currentLocale = getLocale();
    const antdLocale = currentLocale === 'en' ? enUS : viVN;

    return (
        <TranslationProvider>
            <ConfigProvider locale={antdLocale}>
                <MobileOrdering table={table} activeOrder={activeOrder} />
            </ConfigProvider>
        </TranslationProvider>
    );
};

// Mount the app
const container = document.getElementById('mobile-ordering-root');
if (container) {
    const root = createRoot(container);
    root.render(<App />);
}
