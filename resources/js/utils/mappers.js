export const mapOrderFromAPI = (apiOrder) => ({
    id: apiOrder.id,
    number: apiOrder.order_number,
    status: mapOrderStatus(apiOrder.status),
    items: apiOrder.items,
    totalAmount: apiOrder.total_amount || 0,
    tables: apiOrder.tables || [],
    createdAt: formatTime(apiOrder.order_time),
    customer: apiOrder.customer,
    paymentStatus: apiOrder.payment_status,
    paymentMethod: apiOrder.payment_method,
    qrCodeUrl: apiOrder.qr_code_url,
    notes: apiOrder.notes,
    priority: apiOrder.priority,
    customerName: `${apiOrder.id}: ${apiOrder.customer?.name || 'Khách hàng'}`
});

export const mapTableFromAPI = (apiTable) => ({
    id: apiTable.id,
    name: apiTable.name,
    code: apiTable.code,
    capacity: apiTable.capacity,
    description: apiTable.description,
    location: apiTable.location,
    occupied: apiTable.status === 0,
    qrCode: apiTable.qr_code
});

const mapOrderStatus = (apiStatus) => {
    const statusMap = {
        1: 'pending',
        2: 'preparing',
        3: 'ready',
        4: 'served'
    };
    return statusMap[apiStatus] || 'pending';
};

const formatTime = (timeString) => {
    if (!timeString) return '';
    const date = new Date(timeString);
    return date.toLocaleString('vi-VN', {
        dateStyle: 'short',
        timeStyle: 'short'
    });
};
