import React from 'react';
import {
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ExclamationCircleOutlined,
    SyncOutlined,
    RocketOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import 'dayjs/locale/vi';
import relativeTime from 'dayjs/plugin/relativeTime';

// Configure dayjs
dayjs.extend(relativeTime);
dayjs.locale('vi');

/**
 * Format currency to Vietnamese Dong
 * @param {number} amount - Amount to format
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount) => {
    if (typeof amount !== 'number') {
        return '0 ₫';
    }

    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

/**
 * Remove Vietnamese accents from a string
 * @param {string} str - String to remove accents from
 * @returns {string} String without accents
 */
export const removeVietnameseAccents = (str) => {
    if (!str) return '';

    return str
        .normalize('NFD') // Tách dấu ra khỏi chữ cái
        .replace(/[\u0300-\u036f]/g, '') // Loại bỏ các dấu
        .replace(/đ/g, 'd')
        .replace(/Đ/g, 'D')
        .toLowerCase();
};

/**
 * Format date and time
 * @param {string|Date} date - Date to format
 * @param {string} format - Format string (default: 'DD/MM/YYYY HH:mm')
 * @returns {string} Formatted date string
 */
export const formatDateTime = (date, format = 'DD/MM/YYYY HH:mm') => {
    if (!date) return '-';
    return dayjs(date).format(format);
};

/**
 * Format date only
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDate = (date) => {
    return formatDateTime(date, 'DD/MM/YYYY');
};

/**
 * Format time only
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted time string
 */
export const formatTime = (date) => {
    return formatDateTime(date, 'HH:mm');
};

/**
 * Get relative time (e.g., "2 giờ trước")
 * @param {string|Date} date - Date to format
 * @returns {string} Relative time string
 */
export const getRelativeTime = (date) => {
    if (!date) return '-';
    return dayjs(date).fromNow();
};

/**
 * Get status color for tags
 * @param {string} status - Order status
 * @returns {string} Color for Ant Design Tag
 */
export const getStatusColor = (status) => {
    const statusColors = {
        'pending': 'orange',
        'confirmed': 'blue',
        'preparing': 'purple',
        'ready': 'cyan',
        'served': 'green',
        'completed': 'success',
        'cancelled': 'error'
    };

    return statusColors[status] || 'default';
};

/**
 * Get status icon
 * @param {string} status - Order status
 * @returns {React.Element} Icon component
 */
export const getStatusIcon = (status) => {
    const icons = {
        pending: { component: 'ClockCircleOutlined' },
        confirmed: { component: 'ExclamationCircleOutlined' },
        preparing: { component: 'SyncOutlined', props: { spin: true } },
        ready: { component: 'RocketOutlined' },
        served: { component: 'CheckCircleOutlined' },
        completed: { component: 'CheckCircleOutlined' },
        cancelled: { component: 'CloseCircleOutlined' },
        default: { component: 'ClockCircleOutlined' }
    };

    return icons[status] || icons.default;
};
/**
 * Format phone number
 * @param {string} phone - Phone number to format
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phone) => {
    if (!phone) return '-';

    // Remove all non-digits
    const cleaned = phone.replace(/\D/g, '');

    // Format Vietnamese phone number
    if (cleaned.length === 10) {
        return cleaned.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
    } else if (cleaned.length === 11) {
        return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
    }

    return phone;
};

/**
 * Truncate text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length (default: 50)
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 50) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
};

/**
 * Format number with thousand separators
 * @param {number} num - Number to format
 * @returns {string} Formatted number
 */
export const formatNumber = (num) => {
    if (typeof num !== 'number') return '0';
    return new Intl.NumberFormat('vi-VN').format(num);
};

/**
 * Calculate percentage
 * @param {number} value - Current value
 * @param {number} total - Total value
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} Percentage string
 */
export const calculatePercentage = (value, total, decimals = 1) => {
    if (!total || total === 0) return '0%';
    const percentage = (value / total) * 100;
    return `${percentage.toFixed(decimals)}%`;
};

/**
 * Format file size
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get order status priority for sorting
 * @param {string} status - Order status
 * @returns {number} Priority number (lower = higher priority)
 */
export const getStatusPriority = (status) => {
    const priorities = {
        'pending': 1,
        'confirmed': 2,
        'preparing': 3,
        'ready': 4,
        'served': 5,
        'completed': 6,
        'cancelled': 7
    };

    return priorities[status] || 999;
};

/**
 * Check if date is today
 * @param {string|Date} date - Date to check
 * @returns {boolean} True if date is today
 */
export const isToday = (date) => {
    if (!date) return false;
    return dayjs(date).isSame(dayjs(), 'day');
};

/**
 * Check if date is yesterday
 * @param {string|Date} date - Date to check
 * @returns {boolean} True if date is yesterday
 */
export const isYesterday = (date) => {
    if (!date) return false;
    return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day');
};

/**
 * Get friendly date text
 * @param {string|Date} date - Date to format
 * @returns {string} Friendly date text
 */
export const getFriendlyDate = (date) => {
    if (!date) return '-';

    if (isToday(date)) {
        return `Hôm nay, ${formatTime(date)}`;
    } else if (isYesterday(date)) {
        return `Hôm qua, ${formatTime(date)}`;
    } else {
        return formatDateTime(date);
    }
};

export const getStatusOrderItem = (status) => {
    const statusMap = {
        '0': 'Chờ chế biến',
        '1': 'Đang chế biến',
        '2': 'Sẵn sàng',
        '4': 'Hoàn thành'
    }
    return statusMap[status] || status;
}

export const getStatusText = (status, t = null) => {
    if (t) {
        // Use translation function if provided
        const statusMap = {
            '0': t('pending'),
            '1': t('confirmed'),
            '2': t('preparing'),
            '3': t('ready'),
            'served': t('served'),
            '4': t('completed'),
            '5': t('cancelled')
        };
        return statusMap[status] || status;
    }

    // Fallback to Vietnamese (for backward compatibility)
    const statusMap = {
        '0': 'Đang xử lý',
        '1': 'Đã xác nhận',
        '2': 'Đang chuẩn bị',
        '3': 'Sẵn sàng',
        'served': 'Đã phục vụ',
        '4': 'Hoàn thành',
        '5': 'Đã hủy'
    };
    return statusMap[status] || status;
};

export const getStatusOrderItemColor = (status) => {
    const statusColors = {
        '0': 'bg-blue-100 text-blue-800',
        '1': 'bg-orange-100 text-orange-800',
        '2': 'bg-green-100 text-green-800',
        '4': 'bg-yellow-100 text-yellow-800'
    };

    return statusColors[status] || 'gray';
};
