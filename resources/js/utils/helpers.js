export const getStatusBadge = (status) => {
    const statusMap = {
        'pending': { color: 'gold', text: 'Chờ xử lý' },
        'preparing': { color: 'blue', text: 'Đang làm' },
        'ready': { color: 'green', text: 'Sẵn sàng' },
        'served': { color: 'default', text: 'Đã phục vụ' }
    };
    return statusMap[status] || { color: 'default', text: status };
};

export const formatCurrency = (amount) => {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(amount);
};

export const getStatusText = (status) => {
    const statusMap = {
        'pending': 'Chờ xử lý',
        'preparing': 'Đang làm',
        'ready': 'Sẵn sàng',
        'served': 'Đã phục vụ'
    };
    return statusMap[status] || status;
};
