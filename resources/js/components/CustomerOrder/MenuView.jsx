import React, { useState, useEffect } from 'react';
import {
    Card,
    List,
    Typography,
    Button,
    Input,
    Tag,
    Space,
    Spin,
    Empty,
    Badge,
    Image,
    Tabs,
    Row,
    Col
} from 'antd';
import {
    PlusOutlined,
    SearchOutlined,
    HeartOutlined,
    StarOutlined,
    FireOutlined,
    CoffeeOutlined,
    AppstoreOutlined
} from '@ant-design/icons';
import {formatCurrency, removeVietnameseAccents} from "../../utils/formatter.js";
import { useTranslation } from '../../hooks/useTranslation.jsx';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

const MenuView = ({ order, onAddToCart, cartItems = [] }) => {
    const { t } = useTranslation();
    const [menuData, setMenuData] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchText, setSearchText] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [favorites, setFavorites] = useState([]);

    // Fetch menu data
    useEffect(() => {
        fetchMenuData();
    }, []);

    // Refetch menu data when language changes
    useEffect(() => {
        fetchMenuData();
    }, [t]);

    const fetchMenuData = async () => {
        try {
            setLoading(true);
            const response = await fetch(`/api/menu/${order?.id}`);
            const data = await response.json();

            if (data.success) {
                setMenuData(data.data.menu_items || []);
                setCategories(data.data.categories || []);
            }
        } catch (error) {
            console.error('Error fetching menu:', error);
        } finally {
            setLoading(false);
        }
    };

    // Get filtered menu items
    const getFilteredItems = () => {
        let filtered = menuData;

        // Filter by category
        if (selectedCategory !== 'all') {
            filtered = filtered.filter(item => item.categories.some(category => category.id === selectedCategory));
        }

        // Filter by search text
        if (searchText) {
            const searchNormalized = removeVietnameseAccents(searchText);

            filtered = filtered.filter(item =>
                removeVietnameseAccents(item.name).includes(searchNormalized) ||
                removeVietnameseAccents(item.description || '').includes(searchNormalized)
            );
        }

        return filtered;
    };

    // Get item quantity in cart
    const getItemQuantityInCart = (itemId) => {
        const cartItem = cartItems.find(item => item.id === itemId);
        return cartItem ? cartItem.quantity : 0;
    };

    // Check if item is favorite
    const isFavorite = (itemId) => {
        return favorites.includes(itemId);
    };

    // Toggle favorite
    const toggleFavorite = (itemId) => {
        setFavorites(prev =>
            prev.includes(itemId)
                ? prev.filter(id => id !== itemId)
                : [...prev, itemId]
        );
    };

    // Get category icon
    const getCategoryIcon = (categoryName) => {
        const icons = {
            'Đồ uống': <CoffeeOutlined />,
            'Món chính': <AppstoreOutlined />,
            'Tráng miệng': <HeartOutlined />,
            'Khai vị': <StarOutlined />
        };
        return icons[categoryName] || <AppstoreOutlined />;
    };

    // Prepare category tabs
    const categoryTabs = [
        {
            key: 'all',
            label: (
                <Space>
                    <AppstoreOutlined />
                    {t('all')}
                </Space>
            )
        },
        ...categories.map(category => ({
            key: category.id,
            label: (
                <Space>
                    {getCategoryIcon(category.name)}
                    {category.name}
                </Space>
            )
        }))
    ];

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <Spin size="large" />
            </div>
        );
    }

    const filteredItems = getFilteredItems();

    return (
        <div className="menu-view-container p-4 bg-gray-50 min-h-screen pb-20">
            {/* Search Bar */}
            <div className="mb-4">
                <Input
                    placeholder={t('searchPlaceholder')}
                    allowClear
                    size="large"
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="rounded-lg"
                />
            </div>

            {/* Category Tabs */}
            <div className="mb-4">
                <Tabs
                    activeKey={selectedCategory}
                    onChange={setSelectedCategory}
                    size="small"
                    className="category-tabs"
                    items={categoryTabs}
                />
            </div>

            {/* Menu Items List */}
            {filteredItems.length === 0 ? (
                <Empty description="Không tìm thấy món ăn nào" className="mt-12" />
            ) : (
                <List
                    dataSource={filteredItems}
                    renderItem={(item) => (
                        <List.Item className="mb-3 p-0">
                            <Card className="w-full menu-item-card hover:shadow-md transition-shadow">
                                <div className="flex flex-col gap-3">
                                    {/* Top section - Image and Item Info */}
                                    <div className="flex gap-4">
                                        {/* Item Image */}
                                        {item.image && (
                                            <div className="flex-shrink-0">
                                                <div className="w-20 h-20 rounded-lg overflow-hidden">
                                                    <Image
                                                        src={item.image}
                                                        alt={item.name}
                                                        className="w-full h-full object-cover"
                                                        preview={false}
                                                    />
                                                </div>
                                            </div>
                                        )}

                                        {/* Item Info */}
                                        <div className="flex-1 min-w-0">
                                            <div className="flex justify-between items-start">
                                                <div className="flex-1 pr-2">
                                                    <div className="flex items-center gap-2 mb-1 flex-wrap">
                                                        <Text strong className="text-base text-gray-800 leading-tight">
                                                            {item.name}
                                                        </Text>
                                                        {item.is_popular && (
                                                            <Tag color="red" size="small">
                                                                <FireOutlined className="mr-1" />
                                                                {t('hot')}
                                                            </Tag>
                                                        )}
                                                    </div>

                                                    {item.description && (
                                                        <Paragraph
                                                            className="text-sm text-gray-600 mb-2 leading-tight"
                                                            ellipsis={{ rows: 2 }}
                                                        >
                                                            {item.description}
                                                        </Paragraph>
                                                    )}

                                                    {/* Rating */}
                                                    {item.rating && (
                                                        <div className="flex items-center gap-1">
                                                            <StarOutlined className="text-yellow-500 text-xs" />
                                                            <Text className="text-xs text-gray-600">
                                                                {item.rating} ({item.review_count || 0})
                                                            </Text>
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Favorite Button */}
                                                <Button
                                                    type="text"
                                                    size="small"
                                                    icon={
                                                        <HeartOutlined
                                                            className={isFavorite(item.id) ? 'text-red-500' : 'text-gray-400'}
                                                        />
                                                    }
                                                    onClick={() => toggleFavorite(item.id)}
                                                    className="flex-shrink-0 w-8 h-8"
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Bottom section - Price and Add Button on separate line */}
                                    <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                                        <div>
                                            <Text strong className="text-lg text-green-600">
                                                {formatCurrency(item.price)}
                                            </Text>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            {getItemQuantityInCart(item.id) > 0 && (
                                                <Badge
                                                    count={getItemQuantityInCart(item.id)}
                                                    size="small"
                                                    className="mr-1"
                                                />
                                            )}

                                            <Button
                                                type="primary"
                                                size="small"
                                                icon={<PlusOutlined />}
                                                onClick={() => onAddToCart(item)}
                                                disabled={!item.available}
                                                className="bg-blue-500 hover:bg-blue-600 rounded-full px-3 h-8 text-sm font-medium"
                                            >
                                                {t('add')}
                                            </Button>
                                        </div>
                                    </div>

                                    {/* Availability Status */}
                                    {!item.available && (
                                        <div>
                                            <Tag color="red" size="small">
                                                {t('outOfStock')}
                                            </Tag>
                                        </div>
                                    )}
                                </div>
                            </Card>
                        </List.Item>
                    )}
                />
            )}
        </div>
    );
};

export default MenuView;
