import React, {useState, useEffect, useMemo} from 'react';
import {
    Card,
    List,
    Typography,
    Tag,
    Space,
    Button,
    Empty,
    Spin,
    Modal,
    Divider,
    Badge,
    InputNumber,
    message
} from 'antd';
import {
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    EyeOutlined,
    CalendarOutlined,
    DollarOutlined,
    EditOutlined,
    DeleteOutlined,
    PlusOutlined,
    MinusOutlined,
    SaveOutlined, ExclamationCircleOutlined, SyncOutlined, RocketOutlined
} from '@ant-design/icons';
import {
    formatCurrency,
    formatDateTime,
    getStatusColor,
    getStatusOrderItem,
    getStatusOrderItemColor,
    getStatusText
} from '../../utils/formatter';

const { Title, Text } = Typography;

const OrderHistory = ({ orderId, onOrderCountChange, activeTab, handleRequestPayment, paymentLoading }) => {
     const getStatusIcon = (status) => {
        const icons = {
            0: <ClockCircleOutlined />,
            1: <ExclamationCircleOutlined/>,
            2: <SyncOutlined spin />,
            3: <RocketOutlined />,
            4: <CheckCircleOutlined />,
            5: <CloseCircleOutlined />,
            default: <ClockCircleOutlined />
        };

        return icons[status] || icons.default;
    };
    const [currentOrder, setCurrentOrder] = useState(null);
    const [orderItems, setOrderItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [editingItem, setEditingItem] = useState(null);
    const [tempQuantity, setTempQuantity] = useState(0);
    const [messageApi, messageContextHolder] = message.useMessage();
    const [modal, contextHolder] = Modal.useModal();
    // Fetch current order and its items
    useEffect(() => {
        if (activeTab !== 'history') return;
        fetchCurrentOrder();
    }, [orderId, activeTab]);

    const fetchCurrentOrder = async () => {
        try {
            setLoading(true);
            const response = await fetch(`/api/orders/current?since=${orderId}`);
            const data = await response.json();
            if (data.success) {
                setCurrentOrder(data.orders);
                setOrderItems(data.orders?.order_items || []);
                // Update order count for badge
                if (onOrderCountChange) {
                    onOrderCountChange(data.orders?.order_items?.length || 0);
                }
            }
        } catch (error) {
            console.error('Error fetching current order:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle quantity change
    const handleQuantityChange = async (itemId, newQuantity) => {
        if (newQuantity < 1) {
            handleRemoveItem(itemId);
            return;
        }

        try {
            const response = await fetch(`/api/orders/items/${itemId}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                },
                body: JSON.stringify({ quantity: newQuantity })
            });

            const data = await response.json();
            if (data.success) {
                // Update local state
                setOrderItems(prev =>
                    prev.map(item =>
                        item.id === itemId
                            ? { ...item, quantity: newQuantity }
                            : item
                    )
                );
                messageApi.success('Đã cập nhật số lượng');
            } else {
                messageApi.error('Không thể cập nhật số lượng');
            }
        } catch (error) {
            messageApi.error('Lỗi khi cập nhật số lượng');
        }
    };

    // Handle remove item
    // Handle remove item
    const handleRemoveItem = async (itemId) => {
        console.log("handleRemoveItem called with itemId:", itemId);
        modal.confirm({
            title: 'Xóa món khỏi đơn hàng?',
            content: 'Bạn có chắc muốn xóa món này khỏi đơn hàng?',
            onOk: async () => {
                try {
                    const response = await fetch(`/api/orders/items/${itemId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                        }
                    });

                    const data = await response.json();
                    if (data.success) {
                        setOrderItems(prev => prev.filter(item => item.id !== itemId));
                        messageApi.success('Đã xóa món khỏi đơn hàng');
                    } else {
                        messageApi.error('Không thể xóa món');
                    }
                } catch (error) {
                    messageApi.error('Lỗi khi xóa món');
                }
            }
        });
    };
    // Calculate totals
    const orderHash = useMemo(() => {
        return orderItems.map(item => `${item.id}-${item.quantity}-${item.unit_price}-${item.status}`).join('|');
    }, [orderItems]);

    const total = useMemo(() => {
        return orderItems.reduce((sum, item) => sum + ( item.status === 3 ? 0 : item.quantity * item.unit_price || 0), 0);
    }, [orderHash]);

    const subtotal = useMemo(() => {
        return orderItems.reduce((sum, item) => sum + (item.quantity * item.unit_price || 0), 0);
    }, [orderHash]);

    const canEdit = currentOrder && [0].includes(currentOrder.status);

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <Spin size="large" />
            </div>
        );
    }

    if (!currentOrder) {
        return (
            <div className="order-history-container p-4 bg-gray-50 min-h-screen">
                <Empty
                    description="Chưa có đơn hàng nào"
                    className="mt-20"
                />
            </div>
        );
    }


    return (
        <div className="order-history-container p-4 bg-gray-50 min-h-screen pb-24">
            {messageContextHolder}
            {contextHolder}

            {/* Order Header */}
            <div className="mb-6">
                <div className="flex justify-between items-start mb-4">
                    <div>
                        <Title level={3} className="mb-1 text-gray-800">
                            Đơn hàng
                        </Title>
                        <Text className="text-gray-500">
                            {formatDateTime(currentOrder.created_at)}
                        </Text>
                    </div>
                    <Tag
                        color={getStatusColor(currentOrder.status)}
                        icon={getStatusIcon(currentOrder.status)}
                        className="rounded-full px-3 py-1"
                    >
                        {getStatusText(currentOrder.status)}
                    </Tag>
                </div>

                {/* Order Stats */}
                <div className="grid grid-cols-2 gap-3 mb-4">
                    <Card size="small" className="text-center">
                        <div className="font-bold text-blue-600">{orderItems.length}</div>
                        <div className="text-xs text-gray-500">Loại món</div>
                    </Card>
                    <Card size="small" className="text-center">
                        <div className="font-bold text-orange-600">
                            {orderItems.reduce((sum, item) => sum + item.quantity, 0)}
                        </div>
                        <div className="text-xs text-gray-500">Tổng số</div>
                    </Card>
                    {/*<Card size="small" className="text-center">*/}
                    {/*    <div className="font-bold text-green-600">*/}
                    {/*        {formatCurrency(total)}*/}
                    {/*    </div>*/}
                    {/*    <div className="text-xs text-gray-500">Tổng tiền</div>*/}
                    {/*</Card>*/}
                </div>

                {canEdit && (
                    <div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
                        <Text className="text-blue-800 text-sm">
                            💡 Bạn có thể chỉnh sửa đơn hàng này vì chưa được xác nhận
                        </Text>
                    </div>
                )}
            </div>

            {/* Order Items */}
            {orderItems.length === 0 ? (
                <Empty description="Đơn hàng trống" className="mt-12" />
            ) : (
                <div className="space-y-3 mb-6">
                    {orderItems.map((item) => (
                        <Card key={item.id} className="border border-gray-200">
                            <div className="flex gap-3">
                                {/* Item Info */}
                                <div className="flex-1 min-w-0">
                                    <div className="flex justify-between items-start mb-2">
                                        <div className="flex-1">
                                            <div className="w-full flex justify-between">
                                                <Text strong className="text-gray-800">
                                                    {item?.food?.name}
                                                </Text>
                                            </div>
                                            <div className="text-sm text-gray-600">
                                                {formatCurrency(item?.unit_price)} / món
                                            </div>
                                        </div>

                                        {canEdit && (
                                            <Button
                                                type="text"
                                                size="small"
                                                icon={<DeleteOutlined />}
                                                onClick={() => handleRemoveItem(item.id)}
                                                className="text-red-500 hover:text-red-700 w-8 h-8"
                                            />
                                        )}
                                    </div>

                                    {/* Quantity and Total */}
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-2">
                                            {canEdit ? (
                                                <div className="flex items-center bg-gray-50 rounded-lg p-1">
                                                    <Button
                                                        type="text"
                                                        size="small"
                                                        icon={<MinusOutlined />}
                                                        onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                                        className="w-8 h-8 rounded-md hover:bg-white border-0"
                                                    />

                                                    <div className="mx-2 min-w-[40px] text-center">
                                                        <Text strong className="text-base">
                                                            {item.quantity}
                                                        </Text>
                                                    </div>

                                                    <Button
                                                        type="text"
                                                        size="small"
                                                        icon={<PlusOutlined />}
                                                        onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                                        className="w-8 h-8 rounded-md hover:bg-white border-0"
                                                    />
                                                </div>
                                            ) : (
                                                <Text className="text-gray-600">
                                                    Số lượng: {item.quantity}
                                                </Text>
                                            )}
                                        </div>

                                        <Text strong className="text-green-600">
                                            {formatCurrency(item.unit_price * item.quantity)}
                                        </Text>
                                    </div>

                                    {/* Order time for this item */}
                                    <div className="mt-2 text-xs text-gray-500">
                                        Đặt lúc: {formatDateTime(item.created_at)}
                                    </div>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            )}

            {/* Order Summary - Not fixed anymore */}
            <Card className="mt-4 shadow-lg">
                <Title level={5} className="mb-3">Chi tiết thanh toán</Title>

                <div className="space-y-2">
                    <div className="flex justify-between">
                        <Text>Tạm tính:</Text>
                        <Text>{formatCurrency(subtotal)}</Text>
                    </div>
                    {currentOrder.discount_amount > 0 && (
                            <div className="flex justify-between">
                                <Text>Giảm giá:</Text>
                                <Text>{formatCurrency(currentOrder.discount_amount)}</Text>
                            </div>
                        )
                    }
                    <Divider className="my-2" />

                    <div className="flex justify-between items-center">
                        <Text strong className="text-lg">Tổng cộng:</Text>
                        <Text strong className="text-lg text-green-600">
                            {currentOrder.payment_method ? formatCurrency(currentOrder.total_amount) : formatCurrency(total)}
                        </Text>
                    </div>
                </div>

                {currentOrder.payment_method && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                        <Text className="text-sm text-gray-600">
                            Thanh toán: {currentOrder.payment_method }
                        </Text>
                    </div>
                )}
                {/* Call for payment button */}
                {currentOrder?.id && ![0, 4, 5, 6].includes(currentOrder.status) && (
                    <Button
                        type="success"
                        shape="round"
                        icon={<DollarOutlined />}
                        size="large"
                        loading={paymentLoading}
                        onClick={handleRequestPayment}
                        className="shadow-lg bg-green-600 hover:bg-green-700 text-base font-semibold text-white w-full mt-3"
                    >
                        Gọi thanh toán
                    </Button>
                )}
            </Card>
        </div>
    );
};

export default OrderHistory;
