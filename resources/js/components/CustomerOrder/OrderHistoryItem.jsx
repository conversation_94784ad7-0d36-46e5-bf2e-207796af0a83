import React from 'react';
import {
    List,
    Typography,
    Tag,
    Space,
    Button,
    Badge,
    Avatar
} from 'antd';
import {
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    EyeOutlined,
    ShoppingCartOutlined,
    CalendarOutlined
} from '@ant-design/icons';
import { formatCurrency, formatDateTime, getStatusColor, getStatusIcon } from '../../utils/formatter';

const { Text } = Typography;

const OrderHistoryItem = ({ order, onClick }) => {
    const getItemCount = () => {
        return order.items?.reduce((total, item) => total + item.quantity, 0) || 0;
    };

    const getFirstItems = (limit = 2) => {
        if (!order.items || order.items.length === 0) return [];
        return order.items.slice(0, limit);
    };

    const getRemainingCount = () => {
        if (!order.items) return 0;
        return Math.max(0, order.items.length - 2);
    };

    return (
        <List.Item className="hover:bg-gray-50 transition-colors cursor-pointer border-b border-gray-100 last:border-b-0">
            <div className="w-full p-3" onClick={() => onClick(order)}>
                {/* Header */}
                <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center space-x-2">
                        <Badge
                            count={getItemCount()}
                            size="small"
                            className="bg-blue-100"
                        >
                            <Avatar
                                icon={<ShoppingCartOutlined />}
                                size="small"
                                className="bg-blue-500"
                            />
                        </Badge>
                        <div>
                            <Text strong className="text-gray-800">
                                Đơn #{order.id}
                            </Text>
                            <div className="text-xs text-gray-500 flex items-center mt-1">
                                <CalendarOutlined className="mr-1" />
                                {formatDateTime(order.created_at)}
                            </div>
                        </div>
                    </div>

                    <Tag
                        color={getStatusColor(order.status)}
                        icon={getStatusIcon(order.status)}
                        className="rounded-full px-3"
                    >
                        {getStatusText(order.status)}
                    </Tag>
                </div>

                {/* Order Items Preview */}
                <div className="mb-3">
                    <div className="space-y-1">
                        {getFirstItems().map((item, index) => (
                            <div key={index} className="flex justify-between items-center text-sm">
                                <div className="flex-1">
                                    <Text className="text-gray-700">
                                        {item.quantity}x {item.name}
                                    </Text>
                                    {item.note && (
                                        <div className="text-xs text-gray-500 ml-2">
                                            Ghi chú: {item.note}
                                        </div>
                                    )}
                                </div>
                                <Text className="text-gray-600 font-medium">
                                    {formatCurrency(item.price * item.quantity)}
                                </Text>
                            </div>
                        ))}

                        {getRemainingCount() > 0 && (
                            <div className="text-xs text-gray-500 italic">
                                +{getRemainingCount()} món khác...
                            </div>
                        )}
                    </div>
                </div>

                {/* Footer */}
                <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                    <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-600">
                            <Text>Tổng tiền:</Text>
                        </div>
                    </div>

                    <div className="flex items-center space-x-3">
                        <Text strong className="text-lg text-green-600">
                            {formatCurrency(order.total_amount)}
                        </Text>
                        <Button
                            type="text"
                            icon={<EyeOutlined />}
                            size="small"
                            className="text-blue-500 hover:text-blue-700"
                        >
                            Xem
                        </Button>
                    </div>
                </div>

                {/* Order Note */}
                {order.note && (
                    <div className="mt-2 p-2 bg-yellow-50 rounded text-sm">
                        <Text className="text-yellow-800">
                            <strong>Ghi chú:</strong> {order.note}
                        </Text>
                    </div>
                )}
            </div>
        </List.Item>
    );
};

// Helper function to get status text in Vietnamese
const getStatusText = (status) => {
    const statusMap = {
        'pending': 'Đang xử lý',
        'confirmed': 'Đã xác nhận',
        'preparing': 'Đang chuẩn bị',
        'ready': 'Sẵn sàng',
        'served': 'Đã phục vụ',
        'completed': 'Hoàn thành',
        'cancelled': 'Đã hủy'
    };
    return statusMap[status] || status;
};

export default OrderHistoryItem;
