import React from 'react';
import {
    Modal,
    Typography,
    Divider,
    Tag,
    Space,
    Card,
    List,
    Row,
    Col,
    Button,
    Timeline
} from 'antd';
import {
    CalendarOutlined,
    DollarOutlined,
    FileTextOutlined,
    ClockCircleOutlined,
    PrinterOutlined,
    ShareAltOutlined, ExclamationCircleOutlined, SyncOutlined, RocketOutlined, CheckCircleOutlined, CloseCircleOutlined
} from '@ant-design/icons';
import { formatCurrency, formatDateTime, getStatusColor } from '../../utils/formatter';

const { Title, Text, Paragraph } = Typography;

export const StatusIcon = ({ status }) => {
    switch (status) {
        case 'pending':
            return <ClockCircleOutlined />;
        case 'confirmed':
            return <ExclamationCircleOutlined />;
        case 'preparing':
            return <SyncOutlined spin />;
        case 'ready':
            return <RocketOutlined />;
        case 'served':
            return <CheckCircleOutlined />;
        case 'completed':
            return <CheckCircleOutlined />;
        case 'cancelled':
            return <CloseCircleOutlined />;
        default:
            return <ClockCircleOutlined />;
    }
};
const OrderDetailModal = ({ visible, order, onClose }) => {
    if (!order) return null;

    const getStatusText = (status) => {
        const statusMap = {
            'pending': 'Đang xử lý',
            'confirmed': 'Đã xác nhận',
            'preparing': 'Đang chuẩn bị',
            'ready': 'Sẵn sàng',
            'served': 'Đã phục vụ',
            'completed': 'Hoàn thành',
            'cancelled': 'Đã hủy'
        };
        return statusMap[status] || status;
    };

    const calculateItemTotal = (item) => {
        return item.price * item.quantity;
    };

    const getOrderTimeline = () => {
        const timeline = [
            {
                color: 'blue',
                dot: <CalendarOutlined />,
                children: (
                    <div>
                        <Text strong>Đặt món</Text>
                        <div className="text-xs text-gray-500">
                            {formatDateTime(order.created_at)}
                        </div>
                    </div>
                )
            }
        ];

        if (order.confirmed_at) {
            timeline.push({
                color: 'green',
                children: (
                    <div>
                        <Text strong>Xác nhận</Text>
                        <div className="text-xs text-gray-500">
                            {formatDateTime(order.confirmed_at)}
                        </div>
                    </div>
                )
            });
        }

        if (order.completed_at) {
            timeline.push({
                color: 'green',
                children: (
                    <div>
                        <Text strong>Hoàn thành</Text>
                        <div className="text-xs text-gray-500">
                            {formatDateTime(order.completed_at)}
                        </div>
                    </div>
                )
            });
        }

        if (order.cancelled_at) {
            timeline.push({
                color: 'red',
                children: (
                    <div>
                        <Text strong>Đã hủy</Text>
                        <div className="text-xs text-gray-500">
                            {formatDateTime(order.cancelled_at)}
                        </div>
                        {order.cancel_reason && (
                            <div className="text-xs text-red-500 mt-1">
                                Lý do: {order.cancel_reason}
                            </div>
                        )}
                    </div>
                )
            });
        }

        return timeline;
    };

    return (
        <Modal
            visible={visible}
            onCancel={onClose}
            footer={null}
            width="100%"
            style={{
                top: 0,
                maxWidth: '100vw',
                margin: 0,
                padding: 0
            }}
            bodyStyle={{
                padding: 0,
                maxHeight: '100vh',
                overflow: 'auto'
            }}
            className="mobile-full-modal"
        >
            <div className="p-4 bg-white">
                {/* Header */}
                <div className="flex justify-between items-start mb-4">
                    <div>
                        <Title level={4} className="mb-1">
                            Chi tiết đơn hàng #{order.id}
                        </Title>
                        <Text className="text-gray-500">
                            {formatDateTime(order.created_at)}
                        </Text>
                    </div>
                    <Tag
                        color={getStatusColor(order.status)}
                        icon={getStatusIcon(order.status)}
                        className="rounded-full px-3 py-1"
                    >
                        {getStatusText(order.status)}
                    </Tag>
                </div>

                {/* Order Info */}
                <Card size="small" className="mb-4">
                    <Row gutter={16}>
                        <Col span={12}>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {formatCurrency(order.total_amount)}
                                </div>
                                <div className="text-xs text-gray-500">Tổng tiền</div>
                            </div>
                        </Col>
                        <Col span={12}>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {order.items?.length || 0}
                                </div>
                                <div className="text-xs text-gray-500">Loại món</div>
                            </div>
                        </Col>
                    </Row>
                </Card>

                {/* Order Items */}
                <Card
                    title={
                        <Space>
                            <FileTextOutlined />
                            <span>Chi tiết món ăn</span>
                        </Space>
                    }
                    size="small"
                    className="mb-4"
                >
                    <List
                        dataSource={order.items || []}
                        renderItem={(item, index) => (
                            <List.Item className="px-0">
                                <div className="w-full">
                                    <div className="flex justify-between items-start mb-1">
                                        <div className="flex-1">
                                            <Text strong className="text-gray-800">
                                                {item.name}
                                            </Text>
                                            <div className="text-sm text-gray-500">
                                                {formatCurrency(item.price)} x {item.quantity}
                                            </div>
                                        </div>
                                        <Text strong className="text-green-600">
                                            {formatCurrency(calculateItemTotal(item))}
                                        </Text>
                                    </div>

                                    {item.note && (
                                        <div className="mt-1 p-2 bg-yellow-50 rounded text-sm">
                                            <Text className="text-yellow-800">
                                                Ghi chú: {item.note}
                                            </Text>
                                        </div>
                                    )}

                                    {item.options && item.options.length > 0 && (
                                        <div className="mt-1">
                                            <Text className="text-xs text-gray-500">
                                                Tùy chọn: {item.options.map(opt => opt.name).join(', ')}
                                            </Text>
                                        </div>
                                    )}
                                </div>
                            </List.Item>
                        )}
                    />
                </Card>

                {/* Order Timeline */}
                <Card
                    title={
                        <Space>
                            <ClockCircleOutlined />
                            <span>Tiến trình đơn hàng</span>
                        </Space>
                    }
                    size="small"
                    className="mb-4"
                >
                    <Timeline
                        items={getOrderTimeline()}
                        size="small"
                    />
                </Card>

                {/* Order Note */}
                {order.note && (
                    <Card
                        title="Ghi chú đặc biệt"
                        size="small"
                        className="mb-4"
                    >
                        <Paragraph className="mb-0 text-gray-700">
                            {order.note}
                        </Paragraph>
                    </Card>
                )}

                {/* Payment Info */}
                {order.payment_method && (
                    <Card
                        title={
                            <Space>
                                <DollarOutlined />
                                <span>Thông tin thanh toán</span>
                            </Space>
                        }
                        size="small"
                        className="mb-4"
                    >
                        <Row gutter={16}>
                            <Col span={12}>
                                <Text className="text-gray-500">Phương thức:</Text>
                                <div className="font-medium">
                                    {order.payment_method === 'cash' ? 'Tiền mặt' :
                                        order.payment_method === 'card' ? 'Thẻ' :
                                            'Chuyển khoản'}
                                </div>
                            </Col>
                            <Col span={12}>
                                <Text className="text-gray-500">Trạng thái:</Text>
                                <div>
                                    <Tag color={order.payment_status === 'paid' ? 'green' : 'orange'}>
                                        {order.payment_status === 'paid' ? 'Đã thanh toán' : 'Chưa thanh toán'}
                                    </Tag>
                                </div>
                            </Col>
                        </Row>
                    </Card>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2 mt-6">
                    <Button
                        type="default"
                        icon={<PrinterOutlined />}
                        block
                        onClick={() => window.print()}
                    >
                        In hóa đơn
                    </Button>
                    <Button
                        type="primary"
                        icon={<ShareAltOutlined />}
                        block
                        className="bg-blue-500 hover:bg-blue-600"
                    >
                        Chia sẻ
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default OrderDetailModal;
