import React from 'react';
import {
    Card,
    Radio,
    DatePicker,
    Button,
    Space,
    Divider
} from 'antd';
import {
    ReloadOutlined,
    FilterOutlined,
    CalendarOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

const OrderHistoryFilter = ({
                                filterStatus,
                                onFilterStatusChange,
                                dateRange,
                                onDateRangeChange,
                                onRefresh
                            }) => {
    const statusOptions = [
        { label: 'Tất cả', value: 'all' },
        { label: 'Đang xử lý', value: 'pending' },
        { label: 'Hoàn thành', value: 'completed' },
        { label: 'Đã hủy', value: 'cancelled' }
    ];

    const quickDateRanges = {
        'Hôm nay': [dayjs().startOf('day'), dayjs().endOf('day')],
        '7 ngày qua': [dayjs().subtract(7, 'day'), dayjs()],
        '30 ngày qua': [dayjs().subtract(30, 'day'), dayjs()],
        'Tháng này': [dayjs().startOf('month'), dayjs().endOf('month')]
    };

    const handleDateRangeChange = (dates) => {
        onDateRangeChange(dates || []);
    };

    const clearFilters = () => {
        onFilterStatusChange('all');
        onDateRangeChange([]);
    };

    const hasActiveFilters = filterStatus !== 'all' || dateRange.length > 0;

    return (
        <Card className="mb-4 shadow-sm">
            {/* Status Filter */}
            <div className="mb-4">
                <div className="flex items-center mb-2">
                    <FilterOutlined className="mr-2 text-gray-500" />
                    <span className="font-medium text-gray-700">Trạng thái</span>
                </div>
                <Radio.Group
                    value={filterStatus}
                    onChange={(e) => onFilterStatusChange(e.target.value)}
                    size="small"
                    className="w-full"
                >
                    <div className="grid grid-cols-2 gap-2">
                        {statusOptions.map(option => (
                            <Radio.Button
                                key={option.value}
                                value={option.value}
                                className="text-center"
                            >
                                {option.label}
                            </Radio.Button>
                        ))}
                    </div>
                </Radio.Group>
            </div>

            <Divider className="my-3" />

            {/* Date Range Filter */}
            <div className="mb-4">
                <div className="flex items-center mb-2">
                    <CalendarOutlined className="mr-2 text-gray-500" />
                    <span className="font-medium text-gray-700">Thời gian</span>
                </div>

                <RangePicker
                    value={dateRange}
                    onChange={handleDateRangeChange}
                    presets={quickDateRanges}
                    placeholder={['Từ ngày', 'Đến ngày']}
                    className="w-full mb-2"
                    size="small"
                    format="DD/MM/YYYY"
                />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center">
                <Button
                    type="text"
                    size="small"
                    onClick={clearFilters}
                    disabled={!hasActiveFilters}
                    className="text-gray-500"
                >
                    Xóa bộ lọc
                </Button>

                <Button
                    type="primary"
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={onRefresh}
                    className="bg-blue-500 hover:bg-blue-600"
                >
                    Làm mới
                </Button>
            </div>

            {/* Active Filters Indicator */}
            {hasActiveFilters && (
                <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="text-xs text-gray-500">
                        Đang áp dụng bộ lọc:
                        {filterStatus !== 'all' && (
                            <span className="ml-1 px-2 py-1 bg-blue-100 text-blue-600 rounded">
                                {statusOptions.find(s => s.value === filterStatus)?.label}
                            </span>
                        )}
                        {dateRange.length === 2 && (
                            <span className="ml-1 px-2 py-1 bg-green-100 text-green-600 rounded">
                                {dateRange[0].format('DD/MM')} - {dateRange[1].format('DD/MM')}
                            </span>
                        )}
                    </div>
                </div>
            )}
        </Card>
    );
};

export default OrderHistoryFilter;
