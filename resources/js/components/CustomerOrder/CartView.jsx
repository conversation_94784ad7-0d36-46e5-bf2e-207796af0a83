import React, { useState } from 'react';
import {
    Card,
    List,
    Typography,
    Button,
    InputNumber,
    Space,
    Empty,
    Divider,
    Input,
    Modal,
    Form,
    Radio,
    message,
    Spin
} from 'antd';
import {
    DeleteOutlined,
    MinusOutlined,
    PlusOutlined,
    ShoppingCartOutlined,
    ClearOutlined,
    CheckOutlined,
    ExclamationCircleOutlined
} from '@ant-design/icons';
import {formatCurrency} from "../../utils/formatter.js";

const { Title, Text } = Typography;
const { TextArea } = Input;

const CartView = ({
                      items = [],
                      table,
                      onUpdateQuantity,
                      onRemoveItem,
                      onClearCart,
                      onPlaceOrder,
                      total,
                      isHiddenPriceCart
                  }) => {
    const [modal, contextHolder] = Modal.useModal();
    const [messageApi, messageContextHolder] = message.useMessage();

    const [loading, setLoading] = useState(false);
    const [orderForm] = Form.useForm();
    const [isOrderModalVisible, setIsOrderModalVisible] = useState(false);

    // Calculate totals
    const subtotal = total;
    const finalTotal = subtotal;

    // Handle quantity change
    const handleQuantityChange = (itemId, newQuantity) => {
        if (newQuantity < 1) {
            handleRemoveItem(itemId);
            return;
        }
        onUpdateQuantity(itemId, newQuantity);
    };

    // Handle remove item with confirmation
    const handleRemoveItem = (itemId) => {
        const item = items.find(i => i.id === itemId);
        modal.confirm({
            title: 'Xóa món khỏi giỏ hàng?',
            content: `Bạn có chắc muốn xóa "${item?.name}" khỏi giỏ hàng?`,
            icon: <ExclamationCircleOutlined />,
            okText: 'Xóa',
            okType: 'danger',
            cancelText: 'Hủy',
            onOk: () => onRemoveItem(itemId)
        });
    };

    // Handle clear all items
    const handleClearCart = () => {
        modal.confirm({
            title: 'Xóa tất cả món?',
            content: 'Bạn có chắc muốn xóa tất cả món khỏi giỏ hàng?',
            icon: <ExclamationCircleOutlined />,
            okText: 'Xóa tất cả',
            okType: 'danger',
            cancelText: 'Hủy',
            onOk: onClearCart
        });
    };

    // Handle place order
    const handlePlaceOrder = async (orderData) => {
        try {
            setLoading(true);
            const result = await onPlaceOrder({
                ...orderData,
                subtotal,
                total: finalTotal
            });

            if (result.success) {
                messageApi.success('Đặt món thành công!');
                setIsOrderModalVisible(false);
                orderForm.resetFields();
            } else {
                messageApi.error(result.message || 'Có lỗi xảy ra khi đặt món');
            }
        } catch (error) {
            messageApi.error('Có lỗi xảy ra khi đặt món');
        } finally {
            setLoading(false);
        }
    };

    // Show order confirmation modal
    const handleOrder = () => {
        if (items.length === 0) {
            messageApi.warning('Giỏ hàng trống');
            return;
        }

    };

    if (items.length === 0) {
        return (
            <div className="cart-view-container p-4 bg-gray-50 min-h-screen">
                <Empty
                    image={<ShoppingCartOutlined className="text-6xl text-gray-300" />}
                    description={
                        <div>
                            <Text className="text-gray-500">Giỏ hàng trống</Text>
                            <br />
                            <Text className="text-sm text-gray-400">
                                Hãy thêm món ăn từ thực đơn
                            </Text>
                        </div>
                    }
                    className="mt-20"
                />
            </div>
        );
    }

    return (
        <>
            <div className="cart-view-container p-4 bg-gray-50 min-h-screen pb-32">
                {/* Header */}
                {contextHolder}
                {messageContextHolder}
                <div className="flex justify-between items-center mb-4">
                    <Title level={4} className="mb-0">
                        Giỏ hàng ({items.length} món)
                    </Title>
                    <Button
                        type="text"
                        size="small"
                        icon={<ClearOutlined />}
                        onClick={handleClearCart}
                        className="text-red-500 hover:text-red-700"
                    >
                        Xóa tất cả
                    </Button>
                </div>
                {/* Cart Items */}
                <div className="space-y-3 mb-6">
                    {items.map((item) => (
                        <Card key={item.id} className="cart-item-card border border-gray-200 shadow-sm">
                            <div className="flex gap-3">
                                {/* Item Details */}
                                <div className="flex-1 min-w-0">
                                    {/* Header with name and remove button */}
                                    <div className="flex justify-between items-start mb-2">
                                        <div className="flex-1 pr-2">
                                            <Text strong className="text-base text-gray-800 block leading-tight">
                                                {item.name}
                                            </Text>
                                            {!item.hide_price &&
                                                <Text className="text-sm text-gray-600">
                                                    {!item.hide_price && formatCurrency(item.price)} / món
                                                </Text>
                                            }
                                        </div>
                                        <Button
                                            type="text"
                                            size="small"
                                            icon={<DeleteOutlined />}
                                            onClick={() => handleRemoveItem(item.id)}
                                            className="text-red-500 hover:text-red-700 w-8 h-8 flex-shrink-0"
                                        />
                                    </div>

                                    {/* Quantity controls and total */}
                                    <div className="flex justify-between items-center">
                                        {/* Quantity Controls */}
                                        <div className="flex items-center bg-gray-50 rounded-lg p-1">
                                            <Button
                                                type="text"
                                                size="small"
                                                icon={<MinusOutlined />}
                                                onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                                className="w-8 h-8 rounded-md hover:bg-white border-0 flex items-center justify-center"
                                            />

                                            <div className="mx-2 min-w-[40px] text-center">
                                                <Text strong className="text-base">
                                                    {item.quantity}
                                                </Text>
                                            </div>

                                            <Button
                                                type="text"
                                                size="small"
                                                icon={<PlusOutlined />}
                                                onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                                className="w-8 h-8 rounded-md hover:bg-white border-0 flex items-center justify-center"
                                            />
                                        </div>

                                        {/* Item Total */}
                                        <Text strong className="text-lg text-green-600">
                                            {!item.hide_price && formatCurrency(item.price * item.quantity)}
                                        </Text>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>

                {/* Order Summary */}
                <Card className="mt-4 order-summary-card sticky bottom-20 z-10">
                    <Title level={5} className="mb-3">Tổng đơn hàng</Title>
                    {
                        !isHiddenPriceCart &&
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <Text>Tạm tính:</Text>
                                <Text>{formatCurrency(subtotal)}</Text>
                            </div>

                            <Divider className="my-2" />

                            <div className="flex justify-between items-center">
                                <Text strong className="text-lg">Tổng cộng:</Text>
                                <Text strong className="text-lg text-green-600">
                                    {formatCurrency(finalTotal)}
                                </Text>
                            </div>
                        </div>
                    }

                    <Button
                        type="primary"
                        size="large"
                        block
                        icon={<CheckOutlined />}
                        onClick={handlePlaceOrder}
                        className="mt-4 bg-blue-500 hover:bg-blue-600 h-12 text-lg font-medium"
                    >
                        Đặt món ({items.length} món)
                    </Button>
                </Card>
            </div>
        </>
    );
};

export default CartView;
