import React from 'react';
import { Button, Space } from 'antd';
import { ReloadOutlined, SoundOutlined } from '@ant-design/icons';

const KitchenHeader = ({
                           lastUpdate,
                           loading,
                           onReload,
                       }) => {
    return (
        <div className="flex justify-between items-center mb-4">
            <h1 className="text-3xl font-bold text-gray-800 flex items-center">
                🍳 Kitchen Dashboard
                <span className="ml-3 text-sm text-gray-500 font-normal">
                    Cập nhật: {lastUpdate.toLocaleTimeString()}
                </span>
            </h1>

            <Space>
                <Button
                    icon={<ReloadOutlined />}
                    onClick={onReload}
                    title="Refresh"
                    loading={loading}
                />
            </Space>
        </div>
    );
};

export default KitchenHeader;
