import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { Card, notification, Spin } from 'antd';

// Import components
import KitchenHeader from './KitchenHeader';
import KitchenFilters from './KitchenFilter';

// Import utilities and data
import { statusConfig } from './constants';
import { getOrders, loudSpeaker, updateFoodQuantity, updateFoodStatus } from "../../services/kitchent.js";
import StaticColumn from "./DroppableColumn.jsx";

const KitchenManagement = () => {
    const [notificationApi, notificationContextHolder] = notification.useNotification();
    // Core states
    const [foods, setFoods] = useState([]);
    const [loading, setLoading] = useState(false);
    const [lastUpdate, setLastUpdate] = useState(new Date());

    // Filter states
    const [filters, setFilters] = useState({});
    const [searchText, setSearchText] = useState('');

    // Sort states
    const [sortBy, setSortBy] = useState(null);
    const [sortOrder, setSortOrder] = useState('asc');

    // Food update management
    const [pendingUpdates, setPendingUpdates] = useState(new Set());
    const [lockedFoods, setLockedFoods] = useState(new Set());
    const originalFoodStates = useRef(new Map());
    const updateQueue = useRef([]);
    const isProcessingQueue = useRef(false);

    const [waitingFood, setWaitingFood] = useState([]);
    const [preparingFood, setPreparingFood] = useState([]);

    // Initialize
    useEffect(() => {
        loadInitialData();
        const interval = setInterval(() => {
            if (pendingUpdates.size === 0 && !isProcessingQueue.current) {
                loadFoods();
            }
        }, 60000);
        return () => clearInterval(interval);
    }, [pendingUpdates.size]);

    const loadInitialData = async () => {
        setLoading(true);
        try {
            await loadFoods();
        } catch (error) {
            notificationApi.error({ message: 'Lỗi tải dữ liệu' });
        } finally {
            setLoading(false);
        }
    };

    const loadFoods = async () => {
        try {
            const response = await getOrders();

            if (pendingUpdates.size === 0 && !isProcessingQueue.current) {
                setFoods(response?.foods || []);
                setLastUpdate(new Date());
                setWaitingFood(response?.waitingFood || []);
                setPreparingFood(response?.preparingFood || []);
                if (response?.waitingFood.length > 0) {
                    handleLoudSpeaker([...response?.waitingFood, ...response?.preparingFood]);
                }
            }
        } catch (error) {
            console.error('Error loading foods:', error);
        }
    };

    const handleLoudSpeaker = (order) => {
        const text = order.map(item => `${item.name} ${item.total_quantity} suất`).join(', ');
        loudSpeaker(text);
    }

    // Update queue processor
    const processUpdateQueue = useCallback(async () => {
        if (isProcessingQueue.current || updateQueue.current.length === 0) return;
        isProcessingQueue.current = true;
        while (updateQueue.current.length > 0) {
            const update = updateQueue.current.shift();
            await processFoodUpdate(update);
        }
        isProcessingQueue.current = false;
    }, [pendingUpdates.size]);

    // Process single food update
    const processFoodUpdate = async ({ foodId, food, newStatus, originalStatus }) => {
        const updateId = `${foodId}-${Date.now()}`;

        try {
            if (!food?.id) throw new Error(`Invalid food object for ${foodId}`);

            setPendingUpdates(prev => new Set(prev).add(updateId));
            setLockedFoods(prev => new Set(prev).add(foodId));

            const originalState = { ...food };
            originalFoodStates.current.set(foodId, originalState);

            // API call
            await updateFoodStatus(foodId, newStatus);
            setFoods(prev => prev.map(f => f.id === foodId ? { ...f, status: newStatus } : f));

            notificationApi.success({
                message: 'Cập nhật thành công',
                description: `${originalState.name} đã được chuyển sang ${statusConfig[newStatus]?.text}`,
                placement: 'topRight',
                duration: 2
            });

            originalFoodStates.current.delete(foodId);

        } catch (error) {
            // Rollback
            const originalState = originalFoodStates.current.get(foodId);
            if (originalState) {
                setFoods(prev => prev.map(f => f.id === foodId ? originalState : f));
                originalFoodStates.current.delete(foodId);
            }

            notificationApi.error({
                message: '❌ Cập nhật thất bại',
                description: 'Đã khôi phục trạng thái cũ',
                placement: 'topRight',
                duration: 4
            });
        } finally {
            setPendingUpdates(prev => { const newSet = new Set(prev); newSet.delete(updateId); return newSet; });
            setLockedFoods(prev => { const newSet = new Set(prev); newSet.delete(foodId); return newSet; });
            setLastUpdate(new Date());
        }
    };

    // Status change handler (replaces drag & drop)
    const handleStatusChange = useCallback(async (foodName, newStatus, quantity) => {
        //call api
        const response = await updateFoodQuantity(foodName, quantity, newStatus);
        if (response.success) {
            notificationApi.success({
                message: '✅ Cập nhật thành công',
                placement: 'topRight',
                duration: 4
            });
        } else {
            notificationApi.error({
                message: '❌ Cập nhật thất bại',
                placement: 'topRight',
                duration: 4
            });
        }
        loadFoods();
    }, [foods]);

    // Filter helpers
    const availableFilters = useMemo(() => {
        return {
            caregories: [...new Set(foods.flatMap(f => f.categories || []))].sort(),
            table: [...new Set(foods.flatMap(f => f.tables || []))].sort(),
            order_code: [...new Set(foods.map(f => f.order_code))].sort(),
            name: [...new Set(foods.map(f => f.name))].sort()
        };
    }, [foods]);

    // Apply filters, search and sort
    const filteredAndSortedFoods = useMemo(() => {
        let filtered = foods;

        // Apply multiple filters
        Object.entries(filters).forEach(([filterType, filterValue]) => {
            if (filterValue) {
                filtered = filtered.filter(food => {
                    if (filterType === 'table') {
                        return food.tables && food.tables.includes(filterValue);
                    }
                    if (filterType === 'caregories') {
                        return food.categories && food.categories.includes(filterValue);
                    }
                    return food[filterType] === filterValue;
                });
            }
        });

        // Apply search
        if (searchText) {
            filtered = filtered.filter(food =>
                food.name.toLowerCase().includes(searchText.toLowerCase()) ||
                food.caregories.toLowerCase().includes(searchText.toLowerCase()) ||
                (food.tables && food.tables.some(table =>
                    table.toLowerCase().includes(searchText.toLowerCase())
                )) ||
                food.order_code.toLowerCase().includes(searchText.toLowerCase())
            );
        }

        // Apply sorting
        if (sortBy) {
            filtered = [...filtered].sort((a, b) => {
                let aValue, bValue;

                switch (sortBy) {
                    case 'table':
                        aValue = a.tables && a.tables.length > 0 ? a.tables[0] : '';
                        bValue = b.tables && b.tables.length > 0 ? b.tables[0] : '';
                        break;
                    case 'name':
                        aValue = a.name || '';
                        bValue = b.name || '';
                        break;
                    default:
                        return 0;
                }

                if (sortBy === 'order_time') {
                    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
                } else if (sortBy === 'quantity') {
                    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    const comparison = aValue.localeCompare(bValue, 'vi', { numeric: true });
                    return sortOrder === 'asc' ? comparison : -comparison;
                }
            });
        }

        return filtered;
    }, [foods, filters, searchText, sortBy, sortOrder]);

    // Filter and sort handlers
    const handleClearFilters = () => {
        setFilters({});
        setSearchText('');
        setSortBy(null);
        setSortOrder('asc');
    };

    const handleSortChange = (value) => {
        setSortBy(value);
        if (!value) {
            setSortOrder('asc');
        }
    };

    return (
        <div className="w-100 flex justify-center">
            {notificationContextHolder}
            <div className="p-6 bg-gray-100 min-h-screen container">
                <Spin spinning={loading}>
                    <div className="mb-6">
                        <KitchenHeader
                            lastUpdate={lastUpdate}
                            loading={loading || pendingUpdates.size > 0}
                            onReload={loadFoods}
                            pendingCount={pendingUpdates.size}
                        />
                    </div>

                    {/* Summary Statistics */}
                    <div className="mb-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                        <Card size="small" className="text-center">
                            <div className="text-2xl font-bold text-blue-600">
                                {filteredAndSortedFoods.filter(f => (f.status !== undefined ? f.status : 0) === 0).length}
                            </div>
                            <div className="text-gray-500">Chờ chế biến</div>
                        </Card>
                        <Card size="small" className="text-center">
                            <div className="text-2xl font-bold text-orange-600">
                                {filteredAndSortedFoods.filter(f => (f.status !== undefined ? f.status : 0) === 1).length}
                            </div>
                            <div className="text-gray-500">Đang chế biến</div>
                        </Card>
                        <Card size="small" className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                                {filteredAndSortedFoods.filter(f => (f.status !== undefined ? f.status : 0) === 2).length}
                            </div>
                            <div className="text-gray-500">Sẵn sàng</div>
                        </Card>
                        <Card size="small" className="text-center">
                            <div className="text-2xl font-bold text-purple-600">
                                {filteredAndSortedFoods.reduce((sum, f) => sum + f.quantity, 0)}
                            </div>
                            <div className="text-gray-500">Tổng số phần</div>
                        </Card>
                    </div>

                    <div className="flex space-x-4 pb-4">
                        {/* Static columns without drag & drop */}
                        <StaticColumn
                            status={0}
                            title={statusConfig[0].text}
                            foods={waitingFood || []}
                            lockedFoods={lockedFoods}
                            sortBy={sortBy}
                            sortOrder={sortOrder}
                            onStatusChange={handleStatusChange}
                        />
                        <StaticColumn
                            status={1}
                            title={statusConfig[1].text}
                            foods={preparingFood || []}
                            lockedFoods={lockedFoods}
                            sortBy={sortBy}
                            sortOrder={sortOrder}
                            onStatusChange={handleStatusChange}
                        />
                    </div>
                </Spin>
            </div>
        </div>
    );
};

export default KitchenManagement;
