// Updated DroppableColumn.jsx - Removed drag & drop functionality
import React, { useMemo, useState } from 'react';
import { Badge, Button, notification, Select, Tooltip } from 'antd';
import { BellOutlined, ClockCircleOutlined, FireOutlined, CheckCircleOutlined, SortAscendingOutlined, SortDescendingOutlined, SettingOutlined } from '@ant-design/icons';
import FoodCard from './FoodCard';
import { statusConfig } from './constants';

const iconComponents = {
    ClockCircleOutlined: <ClockCircleOutlined />,
    FireOutlined: <FireOutlined />,
    CheckCircleOutlined: <CheckCircleOutlined />
};

const { Option } = Select;

const StaticColumn = ({
                          status,
                          title,
                          foods,
                          lockedFoods = new Set(),
                          sortBy = null,
                          sortOrder = 'asc',
                          onStatusChange
                      }) => {
    // Local sort states for this column
    const [localSortBy, setLocalSortBy] = useState(null);
    const [localSortOrder, setLocalSortOrder] = useState('asc');

    const totalQuantity = foods.reduce((sum, food) => sum + food.total_quantity, 0);
    const lockedCount = foods.filter(food => lockedFoods.has(food.id)).length;

    // Determine which sort to use: local takes priority over global
    const effectiveSortBy = localSortBy || sortBy;
    const effectiveSortOrder = localSortBy ? localSortOrder : sortOrder;

    // Sort foods within the column
    const sortedFoods = useMemo(() => {
        if (!effectiveSortBy) {
            // Default sort by order_time if no sortBy specified
            return [...foods].sort((a, b) => new Date(a.order_time) - new Date(b.order_time));
        }

        return [...foods].sort((a, b) => {
            let aValue, bValue;

            switch (effectiveSortBy) {
                case 'table':
                    aValue = a.tables && a.tables.length > 0 ? a.tables[0] : '';
                    bValue = b.tables && b.tables.length > 0 ? b.tables[0] : '';
                    break;
                case 'name':
                    aValue = a.name || '';
                    bValue = b.name || '';
                    break;
                case 'order_time':
                    aValue = new Date(a.order_time || 0);
                    bValue = new Date(b.order_time || 0);
                    break;
                case 'quantity':
                    aValue = a.quantity || 0;
                    bValue = b.quantity || 0;
                    break;
                case 'category':
                    aValue = a.category || '';
                    bValue = b.category || '';
                    break;
                case 'order_code':
                    aValue = a.order_code || '';
                    bValue = b.order_code || '';
                    break;
                default:
                    aValue = new Date(a.order_time || 0);
                    bValue = new Date(b.order_time || 0);
            }

            // Handle different data types
            if (effectiveSortBy === 'order_time') {
                return effectiveSortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            } else if (effectiveSortBy === 'quantity') {
                return effectiveSortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            } else {
                const comparison = aValue.localeCompare(bValue, 'vi', {
                    numeric: true,
                    sensitivity: 'base'
                });
                return effectiveSortOrder === 'asc' ? comparison : -comparison;
            }
        });
    }, [foods, effectiveSortBy, effectiveSortOrder]);

    const getColumnClasses = () => {
        const baseClasses = "flex-1 min-w-80 rounded-lg p-4 transition-all duration-200 relative";
        const bgColor = statusConfig[status]?.bgColor || 'bg-gray-50';
        return `${baseClasses} ${bgColor} border-2 border-solid border-gray-200`;
    };

    const getSortDisplayText = () => {
        if (!effectiveSortBy) return null;

        const sortLabels = {
            table: 'Bàn',
            name: 'Tên món',
        };

        const source = localSortBy ? '🔧' : '🌐';
        return `${source} ${sortLabels[effectiveSortBy]} ${effectiveSortOrder === 'asc' ? '↑' : '↓'}`;
    };

    const handleLocalSortChange = (newSortBy) => {
        setLocalSortBy(newSortBy);
        if (!newSortBy) {
            setLocalSortOrder('asc');
        }
    };

    const toggleSortOrder = () => {
        setLocalSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    };

    return (
        <div
            className={getColumnClasses()}
            style={{ minHeight: '600px' }}
        >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                    <div className="text-gray-600">
                        {iconComponents[statusConfig[status]?.icon]}
                    </div>
                    <h3 className="font-semibold text-gray-800">
                        {title}
                    </h3>
                    <Badge
                        count={totalQuantity}
                        style={{
                            backgroundColor: statusConfig[status]?.color
                        }}
                    />
                    {lockedCount > 0 && (
                        <Badge
                            count={`${lockedCount} đang xử lý`}
                            style={{ backgroundColor: '#fa8c16' }}
                            className="animate-pulse"
                        />
                    )}
                </div>

                {/* Column Sort Controls */}
                <div className="flex items-center space-x-1">
                    {effectiveSortBy && (
                        <Tooltip title="Đảo thứ tự sắp xếp">
                            <Button
                                size="small"
                                icon={effectiveSortOrder === 'asc' ? <SortAscendingOutlined /> : <SortDescendingOutlined />}
                                onClick={toggleSortOrder}
                                type="text"
                                className="opacity-70 hover:opacity-100"
                                disabled={!localSortBy}
                            />
                        </Tooltip>
                    )}
                </div>
            </div>

            {/* Sort indicator */}
            {effectiveSortBy && (
                <div className="mb-3">
                    <div className={`text-xs px-2 py-1 rounded inline-flex items-center ${
                        localSortBy ? 'text-blue-600 bg-blue-50 border border-blue-200' : 'text-gray-500 bg-gray-100'
                    }`}>
                        <span className="mr-1">📊</span>
                        Sắp xếp: {getSortDisplayText()}
                        {localSortBy && (
                            <span className="ml-1 text-xs">(Riêng)</span>
                        )}
                        {!localSortBy && sortBy && (
                            <span className="ml-1 text-xs">(Chung)</span>
                        )}
                    </div>
                </div>
            )}

            <div className="space-y-3">
                {sortedFoods.map((food) => (
                    <FoodCard
                        key={food.id}
                        food={food}
                        isLocked={lockedFoods.has(food.id)}
                        onStatusChange={onStatusChange}
                        currentStatus={status}
                    />
                ))}

                {foods.length === 0 && (
                    <div className="text-center text-gray-400 py-8">
                        <div className="text-4xl mb-2">📭</div>
                        <div>Không có món ăn</div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default StaticColumn;
