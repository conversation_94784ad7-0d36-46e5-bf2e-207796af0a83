import React from 'react';
import { Select, But<PERSON>, Space, Tag, Input } from 'antd';
import { ClearOutlined, SearchOutlined, SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons';

const { Option } = Select;
const { Search } = Input;

const KitchenFilters = ({
                            filters,
                            onFiltersChange,
                            searchText,
                            onSearchChange,
                            availableFilters,
                            onClearFilters,
                            sortBy,
                            onSortChange,
                            sortOrder,
                            onSortOrderChange
                        }) => {
    const handleFilterChange = (filterType, value) => {
        onFiltersChange({
            ...filters,
            [filterType]: value
        });
    };

    const handleRemoveFilter = (filterType) => {
        const newFilters = { ...filters };
        delete newFilters[filterType];
        onFiltersChange(newFilters);
    };

    const getFilterLabel = (filterType) => {
        const labels = {
            category: 'Danh mục',
            table: 'Bàn',
            order_code: '<PERSON><PERSON> đơn',
            name: '<PERSON><PERSON><PERSON> món'
        };
        return labels[filterType] || filterType;
    };

    const getSortLabel = (sortType) => {
        const labels = {
            table: 'Bàn',
            name: '<PERSON><PERSON><PERSON> món',
        };
        return labels[sortType] || sortType;
    };

    const activeFiltersCount = Object.keys(filters).length + (searchText ? 1 : 0);

    return (
        <div className="bg-white p-4 rounded-lg shadow-sm mb-4">
            <div className="flex flex-wrap items-center gap-4">
                {/* Category Filter */}
                <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">Danh mục:</span>
                    <Select
                        value={filters.caregories}
                        onChange={(value) => handleFilterChange('caregories', value)}
                        style={{ width: 150 }}
                        size="middle"
                        allowClear
                        placeholder="Chọn danh mục"
                        showSearch
                        filterOption={(input, option) =>
                            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        {availableFilters.caregories?.map(value => (
                            <Option key={value} value={value}>
                                {value}
                            </Option>
                        ))}
                    </Select>
                </div>

                {/* Table Filter */}
                <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">Bàn:</span>
                    <Select
                        value={filters.table}
                        onChange={(value) => handleFilterChange('table', value)}
                        style={{ width: 120 }}
                        size="middle"
                        allowClear
                        placeholder="Chọn bàn"
                        showSearch
                        filterOption={(input, option) =>
                            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        {availableFilters.table?.map(value => (
                            <Option key={value} value={value}>
                                {value}
                            </Option>
                        ))}
                    </Select>
                </div>

                {/* Order Code Filter */}
                <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">Mã đơn:</span>
                    <Select
                        value={filters.order_code}
                        onChange={(value) => handleFilterChange('order_code', value)}
                        style={{ width: 180 }}
                        size="middle"
                        allowClear
                        placeholder="Chọn mã đơn"
                        showSearch
                        filterOption={(input, option) =>
                            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        {availableFilters.order_code?.map(value => (
                            <Option key={value} value={value}>
                                {value}
                            </Option>
                        ))}
                    </Select>
                </div>

                {/* Food Name Filter */}
                <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">Tên món:</span>
                    <Select
                        value={filters.name}
                        onChange={(value) => handleFilterChange('name', value)}
                        style={{ width: 160 }}
                        size="middle"
                        allowClear
                        placeholder="Chọn món ăn"
                        showSearch
                        filterOption={(input, option) =>
                            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        {availableFilters.name?.map(value => (
                            <Option key={value} value={value}>
                                {value}
                            </Option>
                        ))}
                    </Select>
                </div>

                {/* Search */}
                <div className="flex items-center space-x-2">
                    <Search
                        placeholder="Tìm kiếm món ăn..."
                        value={searchText}
                        onChange={(e) => onSearchChange(e.target.value)}
                        style={{ width: 200 }}
                        size="middle"
                        allowClear
                    />
                </div>
            </div>

            {/* Second row - Sort options */}
            <div className="flex flex-wrap items-center gap-4 mt-4 pt-4 border-t border-gray-200">
                {/* Sort By */}
                <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">Sắp xếp theo:</span>
                    <Select
                        value={sortBy}
                        onChange={onSortChange}
                        style={{ width: 150 }}
                        size="middle"
                        placeholder="Chọn sắp xếp"
                    >
                        <Option value="table">Bàn</Option>
                        <Option value="name">Tên món</Option>
                    </Select>
                </div>

                {/* Sort Order */}
                {sortBy && (
                    <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-700">Thứ tự:</span>
                        <Select
                            value={sortOrder}
                            onChange={onSortOrderChange}
                            style={{ width: 120 }}
                            size="middle"
                        >
                            <Option value="asc">
                                <div className="flex items-center space-x-1">
                                    <SortAscendingOutlined />
                                    <span>Tăng dần</span>
                                </div>
                            </Option>
                            <Option value="desc">
                                <div className="flex items-center space-x-1">
                                    <SortDescendingOutlined />
                                    <span>Giảm dần</span>
                                </div>
                            </Option>
                        </Select>
                    </div>
                )}

                {/* Clear All Filters */}
                <Button
                    icon={<ClearOutlined />}
                    onClick={onClearFilters}
                    size="middle"
                    type="default"
                    disabled={activeFiltersCount === 0 && !sortBy}
                >
                    Xóa bộ lọc ({activeFiltersCount + (sortBy ? 1 : 0)})
                </Button>
            </div>

            {/* Active Filters Display */}
            {(Object.keys(filters).length > 0 || searchText || sortBy) && (
                <div className="mt-3 flex items-center flex-wrap gap-2">
                    <span className="text-sm text-gray-500">Bộ lọc đang áp dụng:</span>
                    {Object.entries(filters).map(([filterType, value]) => (
                        <Tag
                            key={filterType}
                            closable
                            onClose={() => handleRemoveFilter(filterType)}
                            color="blue"
                        >
                            {getFilterLabel(filterType)}: {value}
                        </Tag>
                    ))}
                    {searchText && (
                        <Tag
                            closable
                            onClose={() => onSearchChange('')}
                            color="green"
                        >
                            Tìm kiếm: {searchText}
                        </Tag>
                    )}
                    {sortBy && (
                        <Tag
                            closable
                            onClose={() => onSortChange(null)}
                            color="purple"
                        >
                            Sắp xếp: {getSortLabel(sortBy)} ({sortOrder === 'asc' ? 'Tăng dần' : 'Giảm dần'})
                        </Tag>
                    )}
                </div>
            )}
        </div>
    );
};

export default KitchenFilters;
