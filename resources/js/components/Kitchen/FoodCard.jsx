import React, { useState } from 'react';
import { Card, Badge, Tag, Tooltip, Button, Modal, InputNumber, Form, Space } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { statusConfig, priorityConfig } from './constants';

const FoodCard = ({
                      food,
                      isLocked = false,
                      onStatusChange,
                      currentStatus
                  }) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    const getCardClasses = () => {
        const baseClasses = "transition-all duration-200 border-l-4 mb-3";
        const priorityClasses = 'border-l-blue-500';
        const lockedClasses = isLocked ? 'opacity-60 cursor-not-allowed' : '';

        if (isLocked) {
            return `${baseClasses} ${priorityClasses} bg-white ${lockedClasses} border-dashed`;
        }

        return `${baseClasses} ${priorityClasses} bg-white hover:shadow-lg`;
    };

    // Helper function to render tables
    const renderTables = () => {
        if (!food.tables || food.tables.length === 0) {
            return (
                <Tag color="blue" className="text-xs">
                    N/A
                </Tag>
            );
        }

        if (food.tables.length === 1) {
            return (
                <Tag color="blue" className="text-xs">
                    {food.tables[0]}
                </Tag>
            );
        }

        if (food.tables.length <= 3) {
            return food.tables.map((table, index) => (
                <Tag key={index} color="blue" size="small" className="text-xs">
                    {table}
                </Tag>
            ));
        }

        return (
            <>
                <Tag color="blue" className="text-xs">
                    {food.tables[0]}
                </Tag>
                <Tooltip title={food.tables.join(', ')}>
                    <Tag color="blue" className="text-xs">
                        +{food.tables.length - 1}
                    </Tag>
                </Tooltip>
            </>
        );
    };

    const getWaitingTimeFromString = (orderedAtString) => {
        try {
            const [datePart, timePart] = orderedAtString.split(' ');
            const [day, month, year] = datePart.split('/');
            const [hour, minute] = timePart.split(':');

            const orderedDate = new Date(year, month - 1, day, hour, minute);
            const now = new Date();
            const diffInMinutes = Math.floor((now - orderedDate) / 60000);

            return Math.max(0, diffInMinutes);
        } catch (error) {
            return 0;
        }
    };

    const getNextStatus = () => {
        switch (currentStatus) {
            case 0: return 1; // waiting -> preparing
            case 1: return 2; // preparing -> cooked
            default: return null;
        }
    };

    const getStatusButtonText = () => {
        switch (currentStatus) {
            case 0: return 'Chế biến';
            case 1: return 'Hoàn thành';
            default: return 'Completed';
        }
    };

    const getModalTitle = () => {
        switch (currentStatus) {
            case 0: return 'Xác nhận bắt đầu chế biến';
            case 1: return 'Xác nhận hoàn thành món ăn';
            default: return 'Xác nhận thay đổi trạng thái';
        }
    };

    const getModalIcon = () => {
        switch (currentStatus) {
            case 0: return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
            case 1: return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
            default: return <CheckCircleOutlined />;
        }
    };

    const showModal = () => {
        if (isLocked) return;

        // Set default quantity to total quantity
        form.setFieldsValue({
            quantity: food.total_quantity
        });
        setIsModalVisible(true);
    };

    const handleCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
    };
    const getTimeColor = (minutes) => {
        if (minutes < 15) return 'green';
        if (minutes < 30) return 'orange';
        return 'red';
    };

    const handleConfirm = async () => {
        try {
            setLoading(true);
            const values = await form.validateFields();
            const nextStatus = getNextStatus();

            if (nextStatus !== null && onStatusChange) {
                // Pass both food info and quantity to parent
                await onStatusChange(food.name, nextStatus, values.quantity);
            }

            setIsModalVisible(false);
            form.resetFields();
        } catch (error) {
            console.error('Validation failed:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Card
                size="small"
                className={getCardClasses()}
                title={
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2 flex-wrap">
                            <span className="font-semibold text-sm">
                                {food.name}
                            </span>
                            {isLocked && (
                                <Tag color="orange" size="small" className="animate-pulse">
                                    🔒 Đang xử lý
                                </Tag>
                            )}
                        </div>
                    </div>
                }
                extra={
                    <div className="flex items-center space-x-1 flex-wrap">
                        <Badge
                            count={food.total_quantity}
                            style={{
                                backgroundColor: '#52c41a',
                            }}
                            title="Số lượng"
                        />
                    </div>
                }
            >
                <div className="space-y-2">
                    <div className="flex justify-between items-center text-xs">
                        <div className="flex items-center text-gray-500">
                            {(() => {
                                const waitingMinutes = getWaitingTimeFromString(food.order_time);
                                return (
                                    <Tag color={getTimeColor(waitingMinutes)}>
                                        {waitingMinutes} phút
                                    </Tag>
                                );
                            })()}
                        </div>
                        <div className="text-gray-600 flex items-center justify-between text-xs">
                            {getNextStatus() !== null && (
                                <Button
                                    size="small"
                                    type="primary"
                                    onClick={showModal}
                                    disabled={isLocked}
                                    className="ml-2"
                                >
                                    <CheckCircleOutlined />
                                    {getStatusButtonText()}
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </Card>

            {/* Quantity Confirmation Modal */}
            <Modal
                title={
                    <div className="flex items-center space-x-2">
                        {getModalIcon()}
                        <span>{getModalTitle()}</span>
                    </div>
                }
                open={isModalVisible}
                onCancel={handleCancel}
                footer={[
                    <Button key="cancel" onClick={handleCancel}>
                        Hủy
                    </Button>,
                    <Button
                        key="confirm"
                        type="primary"
                        loading={loading}
                        onClick={handleConfirm}
                    >
                        Xác nhận
                    </Button>
                ]}
                width={400}
                centered
            >
                <div className="py-4">
                    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-gray-700">Món ăn:</span>
                            <span className="font-semibold">{food.name}</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="font-medium text-gray-700">Tổng số lượng:</span>
                            <Badge
                                count={food.total_quantity}
                                style={{ backgroundColor: '#52c41a' }}
                            />
                        </div>
                    </div>

                    <Form form={form} layout="vertical">
                        <Form.Item
                            label="Số lượng cần xử lý"
                            name="quantity"
                            rules={[
                                { required: true, message: 'Vui lòng nhập số lượng!' },
                                {
                                    type: 'number',
                                    min: 1,
                                    max: food.total_quantity,
                                    message: `Số lượng phải từ 1 đến ${food.total_quantity}!`
                                }
                            ]}
                        >
                            <InputNumber
                                min={1}
                                max={food.total_quantity}
                                style={{ width: '100%' }}
                                placeholder="Nhập số lượng"
                                addonAfter="phần"
                            />
                        </Form.Item>

                        <div className="text-sm text-gray-500">
                            💡 Mặc định sẽ xử lý tất cả {food.total_quantity} phần
                        </div>
                    </Form>
                </div>
            </Modal>
        </>
    );
};

export default FoodCard;
