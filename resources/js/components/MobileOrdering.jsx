import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {
    Layout,
    Tabs,
    Badge,
    Button,
    Input,
    Typography,
} from 'antd';
const { Title } = Typography;

import {
    ShoppingCartOutlined,
    HistoryOutlined,
    MenuOutlined,
    HomeOutlined,
    BellOutlined,
    DollarOutlined
} from '@ant-design/icons';
import OrderHistory from "./CustomerOrder/OrderHistory.jsx";
import MenuView from "./CustomerOrder/MenuView.jsx";
import CartView from "./CustomerOrder/CartView.jsx";
import { message } from 'antd';
import { Modal } from 'antd';

const { Content } = Layout;

const MobileOrdering = ({ table, activeOrder }) => {
    const [activeTab, setActiveTab] = useState('menu');
    const [cartItems, setCartItems] = useState([]);
    const [orderCount, setOrderCount] = useState(0);
    const [notifications, setNotifications] = useState([]);
    const [paymentLoading, setPaymentLoading] = useState(false);
    const [modal, contextHolder] = Modal.useModal();
    const [messageApi, messageContextHolder] = message.useMessage();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [discountCode, setDiscountCode] = useState('');

    const [activeOrderData, setActiveOrderData] = useState(window.activeOrderData || {});

    const fetchActiveOrder = useCallback(async () => {
        if (!activeOrderData?.id) return;
        try {
            const response = await fetch(`/api/orders/${activeOrderData.id}/details`);
            const data = await response.json();
            if (data.order) {
                setActiveOrderData(data.order);
            }
        } catch (error) {
            console.error('Failed to fetch active order', error);
            messageApi.error('Không thể cập nhật trạng thái đơn hàng.');
        }
    }, [activeOrderData.id, messageApi]);

    useEffect(() => {
        // Set up the interval to fetch order details every 30 seconds
        const intervalId = setInterval(() => {
            fetchActiveOrder();
        }, 30000); // 30 seconds

        // Clean up the interval on component unmount
        return () => clearInterval(intervalId);
    }, [fetchActiveOrder]);

    // Get cart item count
    const getCartItemCount = () => {
        return cartItems.reduce((total, item) => total + (item.quantity || 0), 0);
    };
    const isHiddenPriceCart = cartItems.some((item) => item.hide_price === true)

    // Get total cart amount
    const getCartTotal = () => {
        return cartItems.reduce((total, item) => {
            return total + ((item.price || 0) * (item.quantity || 0));
        }, 0);
    };

    // Add item to cart
    const addToCart = (item) => {
        setCartItems(prevItems => {
            const existingItem = prevItems.find(cartItem => cartItem.id === item.id);

            if (existingItem) {
                return prevItems.map(cartItem =>
                    cartItem.id === item.id
                        ? { ...cartItem, quantity: cartItem.quantity + 1 }
                        : cartItem
                );
            } else {
                return [...prevItems, { ...item, quantity: 1 }];
            }
        });
    };

    // Remove item from cart
    const removeFromCart = (itemId) => {
        setCartItems(prevItems => prevItems.filter(item => item.id !== itemId));
    };

    // Update item quantity
    const updateItemQuantity = (itemId, quantity) => {
        if (quantity <= 0) {
            removeFromCart(itemId);
            return;
        }

        setCartItems(prevItems =>
            prevItems.map(item =>
                item.id === itemId
                    ? { ...item, quantity }
                    : item
            )
        );
    };

    // Clear cart
    const clearCart = () => {
        setCartItems([]);
    };

    // Handle order placement
    const handlePlaceOrder = async () => {
        try {
            const response = await fetch('/api/orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    items: cartItems,
                    existing_order_id: activeOrderData.id
                })
            });

            const result = await response.json();

            if (result.success) {
                clearCart();
                setActiveTab('history');
                // Show success notification
                setNotifications(prev => [...prev, {
                    type: 'success',
                    message: 'Đặt món thành công!',
                    timestamp: new Date()
                }]);
            }

            return result;
        } catch (error) {
            console.error('Error placing order:', error);
            return { success: false, error: 'Network error' };
        }
    };

    const handleRequestPayment = async () => {
        setIsModalVisible(true);
    };

    const handlePaymentOk = async () => {
        setPaymentLoading(true);
        try {
            const response = await fetch(`/api/orders/${activeOrderData.id}/request-payment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    discount_code: discountCode
                })
            });

            const result = await response.json();

            if (result.success) {
                if (result.qr_payment_url) {
                    // Open QR payment link in new tab
                    window.open(result.qr_payment_url, '_blank');
                }
                messageApi.success('Đã gửi yêu cầu thanh toán. Vui lòng đợi nhân viên.');
                fetchActiveOrder();
                setIsModalVisible(false);
            } else {
                messageApi.error(result.message || 'Không thể gửi yêu cầu thanh toán.');
            }
        } catch (error) {
            console.error('Error requesting payment:', error);
            messageApi.error('Lỗi khi gửi yêu cầu thanh toán.');
        } finally {
            setPaymentLoading(false);
        }
    };

    // Tab items configuration
    const tabItems = [
        {
            key: 'menu',
            label: (
                <div className="flex flex-col items-center py-1">
                    <MenuOutlined className="text-lg mb-1" />
                    <span className="text-xs">Thực đơn</span>
                </div>
            ),
            children: (
                <MenuView
                    order={activeOrderData}
                    onAddToCart={addToCart}
                    cartItems={cartItems}
                />
            )
        },
        {
            key: 'cart',
            label: (
                <div className="flex flex-col items-center py-1">
                    <Badge count={getCartItemCount()} size="small" offset={[10, -5]}>
                        <ShoppingCartOutlined className="text-lg mb-1" />
                    </Badge>
                    <span className="text-xs">Giỏ hàng</span>
                </div>
            ),
            children: (
                <CartView
                    items={cartItems}
                    table={table}
                    onUpdateQuantity={updateItemQuantity}
                    onRemoveItem={removeFromCart}
                    onClearCart={clearCart}
                    onPlaceOrder={handlePlaceOrder}
                    total={getCartTotal()}
                    isHiddenPriceCart={isHiddenPriceCart}
                />
            )
        },
        {
            key: 'history',
            label: (
                <div className="flex flex-col items-center py-1">
                    <Badge count={orderCount} size="small" offset={[10, -5]}>
                        <HistoryOutlined className="text-lg mb-1" />
                    </Badge>
                    <span className="text-xs">Lịch sử</span>
                </div>
            ),
            children: (
                <OrderHistory
                    orderId={activeOrderData.id}
                    activeTab={activeTab}
                    onOrderCountChange={setOrderCount}
                    paymentLoading={paymentLoading}
                    handleRequestPayment={handleRequestPayment}
                />
            )
        }
    ];

    return (
        <>
            <Layout className="mobile-ordering-layout min-h-screen bg-gray-50" style={{marginBottom: '58px'}}>
                {messageContextHolder}
                {contextHolder}
                {/* Header */}
                <div className="sticky top-0 z-50 bg-white shadow-sm border-b">
                    <div className="flex items-center justify-between p-4">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                <HomeOutlined className="text-white text-lg" />
                            </div>
                            <div>
                                <div className="font-semibold text-gray-800">
                                    Order {activeOrderData.order_number}
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Badge count={notifications.length} size="small">
                                <BellOutlined className="text-xl text-gray-600" />
                            </Badge>
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <Content className="flex-1">
                    <Tabs
                        activeKey={activeTab}
                        onChange={setActiveTab}
                        centered
                        size="large"
                        className="[&_.ant-tabs-tab]:!px-3 [&_.ant-tabs-tab]:!py-2 [&_.ant-tabs-tab]:!min-w-[80px] [&_.ant-tabs-tab]:!flex-1 [&_.ant-tabs-tab-btn]:!w-full [&_.ant-tabs-tab-btn]:!flex [&_.ant-tabs-tab-btn]:!flex-col [&_.ant-tabs-tab-btn]:!items-center [&_.ant-tabs-tab-btn]:!justify-center [&_.ant-tabs-tab-btn]:!text-xs [&_.ant-tabs-ink-bar]:!h-1 [&_.ant-tabs-ink-bar]:!rounded [&_.ant-tabs-content-holder]:!p-0 [&_.ant-tabs-tabpane]:!p-0"
                        tabBarStyle={{
                            backgroundColor: 'white',
                            margin: 0,
                            padding: '0 8px',
                            borderTop: '1px solid #f0f0f0',
                            position: 'fixed',
                            width: '100%',
                            height: '58px',
                            bottom: '0',
                            zIndex: 40
                        }}
                        items={tabItems}
                    />
                </Content>
            </Layout>
            <Modal
                title="Gọi thanh toán"
                visible={isModalVisible}
                onOk={handlePaymentOk}
                onCancel={() => setIsModalVisible(false)}
                okText="Gọi thanh toán"
                cancelText="Hủy"
                confirmLoading={paymentLoading}
            >
                <div style={{ marginBottom: 16 }}>
                    <Input
                        prefix={<DollarOutlined />}
                        className="w-full mb-2 border border-gray-300 rounded-md p-2"
                        placeholder="Nhập mã giảm giá (nếu có)"
                        value={discountCode}
                        onChange={(e) => setDiscountCode(e.target.value)}
                    />
                </div>
                <div>
                    <Title level={5} strong>Bạn có chắc muốn gọi nhân viên để thanh toán cho đơn hàng này?</Title>
                </div>
            </Modal>
        </>
    );
};

export default MobileOrdering;
