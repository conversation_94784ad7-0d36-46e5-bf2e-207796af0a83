import React, { useState, useEffect } from 'react';
import {use} from "../services/mealTicket.js";

const PublicMealTicketsApp = ({ apiBaseUrl = '/api' }) => {
    const [tickets, setTickets] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [modalData, setModalData] = useState({ isOpen: false, ticket: null, usedCount: '' });
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        fetchTickets();
    }, []);

    // Function to remove Vietnamese accents
    const removeVietnameseAccents = (str) => {
        return str
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/đ/g, 'd')
            .replace(/Đ/g, 'D');
    };

    // Filter tickets based on search term
    const filteredTickets = tickets.filter(ticket => {
        if (!searchTerm || searchTerm.trim() === '') return true;

        const searchNormalized = removeVietnameseAccents(searchTerm.toLowerCase().trim());
        const nameNormalized = removeVietnameseAccents(ticket.client_name.toLowerCase());
        const idNormalized = ticket.id.toString().toLowerCase();
        const roomsNormalized = removeVietnameseAccents(ticket.rooms.join(' ').toLowerCase());

        return nameNormalized.includes(searchNormalized) ||
            idNormalized.includes(searchNormalized) ||
            roomsNormalized.includes(searchNormalized);
    });

    // Debug log
    console.log('Search term:', searchTerm);
    console.log('Total tickets:', tickets.length);
    console.log('Filtered tickets:', filteredTickets.length);

    // Calculate totals
    const totalUnused = filteredTickets.reduce((sum, ticket) => sum + ticket.remaining_tickets, 0);

    const fetchTickets = async (isRefresh = false) => {
        if (isRefresh) setIsRefreshing(true);
        else setIsLoading(true);

        try {
            const response = await fetch(`${apiBaseUrl}/public/meal-tickets/data`);
            const data = await response.json();
            setTickets(data.tickets || []);
        } catch (error) {
            console.error('Error:', error);
        } finally {
            setIsLoading(false);
            setIsRefreshing(false);
        }
    };

    const openModal = (ticket) => {
        setModalData({ isOpen: true, ticket, usedCount: '' });
    };

    const closeModal = () => {
        setModalData({ isOpen: false, ticket: null, usedCount: '' });
    };

    const handleSubmitUsage = async () => {
        if (!modalData.ticket || !modalData.usedCount) return;

        const usedCount = parseInt(modalData.usedCount);
        if (isNaN(usedCount) || usedCount <= 0) {
            alert('Vui lòng nhập số lượng hợp lệ');
            return;
        }

        if (usedCount > modalData.ticket.remaining_tickets) {
            alert('Số lượng sử dụng không thể lớn hơn số vé còn lại');
            return;
        }

        setIsSubmitting(true);

        try {
            const response = await use(modalData.ticket.id, usedCount)
            if (response.ok) {
                // Refresh tickets data
                await fetchTickets();
                closeModal();

                // Show success message
                const notification = document.createElement('div');
                notification.className = 'fixed top-6 right-6 bg-green-500 text-white px-4 py-3 rounded-xl shadow-lg z-50';
                notification.textContent = `Đã cập nhật ${usedCount} vé cho ${modalData.ticket.client_name}`;
                document.body.appendChild(notification);

                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 3000);
            } else {
                throw new Error('Có lỗi xảy ra khi cập nhật');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi cập nhật vé');
        } finally {
            setIsSubmitting(false);
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-slate-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="w-8 h-8 border-3 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-slate-600 font-medium">Đang tải dữ liệu...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-slate-50">
            {/* Header */}
            <div className="bg-white shadow-sm sticky top-0 z-10 shadow-md">
                <div className="max-w-6xl mx-auto px-6 py-8">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
                        <div>
                            <h1 className="text-2xl font-bold text-slate-900 mb-2">
                                Quản lý vé ăn
                            </h1>
                            <div className="flex items-center gap-6 text-sm">
                                <p className="text-slate-600">
                                    <span className="font-semibold text-blue-600">{filteredTickets.length}</span>
                                    {searchTerm ? ' kết quả tìm kiếm' : ' khách hàng đang có vé'}
                                </p>
                                <div className="flex items-center gap-4">
                                    <div className="flex items-center gap-1">
                                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                        <span className="text-slate-600">
                                            Chưa dùng: <span className="font-semibold text-emerald-600">{totalUnused}</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center gap-4">
                            {/* Search */}
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                                <input
                                    type="text"
                                    placeholder="Tìm kiếm..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-64 pl-10 pr-10 py-2.5 bg-slate-50 border border-slate-200 rounded-xl focus:bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                                />
                                {searchTerm && (
                                    <button
                                        onClick={() => setSearchTerm('')}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                    >
                                        <svg className="w-4 h-4 text-slate-400 hover:text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                )}
                            </div>

                            {/* Refresh Button */}
                            <button
                                onClick={() => fetchTickets(true)}
                                disabled={isRefreshing}
                                className="flex items-center gap-2 px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-colors disabled:opacity-50"
                            >
                                <svg
                                    className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Cập nhật
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="max-w-6xl mx-auto px-6 py-8">
                {tickets.length === 0 ? (
                    <div className="text-center py-20">
                        <div className="w-20 h-20 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <svg className="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                        </div>
                        <h3 className="text-xl font-semibold text-slate-700 mb-2">
                            Chưa có dữ liệu
                        </h3>
                        <p className="text-slate-500 mb-6">
                            Hiện tại chưa có khách hàng nào đang có vé ăn
                        </p>
                    </div>
                ) : filteredTickets.length === 0 ? (
                    <div className="text-center py-20">
                        <div className="w-20 h-20 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <svg className="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <h3 className="text-xl font-semibold text-slate-700 mb-2">
                            Không tìm thấy kết quả
                        </h3>
                        <p className="text-slate-500 mb-6">
                            Không có khách hàng nào phù hợp với "{searchTerm}"
                        </p>
                        <button
                            onClick={() => setSearchTerm('')}
                            className="px-4 py-2 bg-slate-200 hover:bg-slate-300 text-slate-700 font-medium rounded-lg transition-colors"
                        >
                            Xóa bộ lọc
                        </button>
                    </div>
                ) : (
                    <div className="grid gap-4">
                        {filteredTickets.map((ticket) => {
                            const isLowStock = ticket.remaining_tickets <= 3;
                            const isVeryLow = ticket.remaining_tickets <= 1;

                            return (
                                <div
                                    key={ticket.id}
                                    className="bg-white rounded-2xl shadow-sm hover:shadow-md transition-all duration-200 border border-slate-100 overflow-hidden"
                                >
                                    <div className="p-6">
                                        <div className="flex items-center justify-between">
                                            {/* Customer Info */}
                                            <div className="flex items-center gap-4">
                                                <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-sm">
                                                    <span className="text-white font-bold text-lg">
                                                        {ticket.client_name.charAt(0).toUpperCase()}
                                                    </span>
                                                </div>

                                                <div>
                                                    <div className="flex items-center gap-3 mb-1">
                                                        <h3 className="text-xl font-bold text-slate-900">
                                                            {ticket.client_name}
                                                        </h3>
                                                        <button
                                                            onClick={() => openModal(ticket)}
                                                            className="flex items-center gap-1 px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-lg transition-colors"
                                                        >
                                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                            </svg>
                                                            Sử dụng
                                                        </button>
                                                    </div>
                                                     <div className="flex items-center gap-4 text-sm text-slate-500">
                                                        <span className="flex items-center gap-1">
                                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                                            </svg>
                                                            ID: {ticket.id}
                                                        </span>

                                                        <span className="flex items-center gap-1">
                                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                            Check in: {new Date(ticket.start_date).toLocaleDateString('vi-VN')}
                                                        </span>

                                                        <span className="flex items-center gap-1">
                                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                                                            </svg>
                                                             {ticket.day_count} đêm
                                                        </span>
                                                    </div>


                                                    <div className="flex items-center gap-4 text-sm text-slate-500 pt-2">
                                                        <span className="flex items-center gap-1">
                                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                                                            </svg>
                                                            Phòng: {ticket.rooms.join(', ')}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Ticket Stats */}
                                            <div className="flex items-center gap-8">
                                                <div className="text-center">
                                                    <div className="text-2xl font-bold text-slate-600">
                                                        {ticket.total_tickets}
                                                    </div>
                                                    <div className="text-xs font-medium text-slate-400 uppercase tracking-wide">
                                                        Tổng
                                                    </div>
                                                </div>

                                                <div className="text-center">
                                                    <div className="text-2xl font-bold text-amber-600">
                                                        {ticket.used_tickets}
                                                    </div>
                                                    <div className="text-xs font-medium text-slate-400 uppercase tracking-wide">
                                                        Đã dùng
                                                    </div>
                                                </div>

                                                <div className="text-center">
                                                    <div className={`text-3xl font-black ${
                                                        isVeryLow ? 'text-red-600' :
                                                            isLowStock ? 'text-orange-600' : 'text-emerald-600'
                                                    }`}>
                                                        {ticket.remaining_tickets}
                                                    </div>
                                                    <div className="text-xs font-medium text-slate-400 uppercase tracking-wide">
                                                        Còn lại
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}
            </div>

            {/* Modal */}
            {modalData.isOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-2xl shadow-xl max-w-md w-full">
                        <div className="p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-xl font-bold text-slate-900">
                                    Sử dụng vé ăn
                                </h3>
                                <button
                                    onClick={closeModal}
                                    className="text-slate-400 hover:text-slate-600 transition-colors"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <div className="mb-6">
                                <div className="flex items-center gap-3 mb-4">
                                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                        <span className="text-white font-bold">
                                            {modalData.ticket?.client_name.charAt(0).toUpperCase()}
                                        </span>
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-slate-900">
                                            {modalData.ticket?.client_name}
                                        </h4>
                                        <p className="text-sm text-slate-500">
                                            Còn lại: {modalData.ticket?.remaining_tickets} vé
                                        </p>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-slate-700 mb-2">
                                        Số lượng vé sử dụng
                                    </label>
                                    <input
                                        type="number"
                                        min="1"
                                        max={modalData.ticket?.remaining_tickets}
                                        value={modalData.usedCount}
                                        onChange={(e) => setModalData({...modalData, usedCount: e.target.value})}
                                        className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                                        placeholder="Nhập số lượng..."
                                    />
                                </div>
                            </div>

                            <div className="flex gap-3">
                                <button
                                    onClick={closeModal}
                                    className="flex-1 px-4 py-3 bg-slate-200 hover:bg-slate-300 text-slate-700 font-medium rounded-xl transition-colors"
                                >
                                    Hủy
                                </button>
                                <button
                                    onClick={handleSubmitUsage}
                                    disabled={!modalData.usedCount || isSubmitting || modalData.usedCount > modalData.ticket?.remaining_tickets}
                                    className="flex-1 px-4 py-3 bg-green-500 hover:bg-green-600 text-white font-medium rounded-xl transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                                >
                                    {isSubmitting ? (
                                        <>
                                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                            Đang xử lý...
                                        </>
                                    ) : (
                                        'Xác nhận'
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Refresh Notification */}
            {isRefreshing && (
                <div className="fixed top-6 right-6 bg-white rounded-xl shadow-lg border border-slate-200 px-4 py-3 flex items-center gap-3">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-sm font-medium text-slate-700">Đang cập nhật dữ liệu...</span>
                </div>
            )}
        </div>
    );
};

export default PublicMealTicketsApp;
