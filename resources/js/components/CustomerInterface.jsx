import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    Button,
    Tag,
    Badge,
    Space,
    Typography,
    Row,
    Col,
    Divider,
    List,
    Image,
    message
} from 'antd';
import {
    ClockCircleOutlined,
    FireOutlined,
    BellOutlined,
    CheckCircleOutlined,
    MobileOutlined,
    ShoppingCartOutlined,
    QrcodeOutlined,
    SyncOutlined, CloseCircleOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import {getStatusOrderItem, getStatusOrderItemColor} from "../utils/formatter.js";

const { Title, Text, Paragraph } = Typography;

const CustomerInterface = () => {
    const [messageApi, contextHolder] = message.useMessage();

    // Lấy dữ liệu từ window object (từ Blade template)
    const [order, setOrder] = useState(() => {
        // Fallback data nếu window.orderData không tồn tại
        return window.orderData || {
            id: 1,
            order_number: 'ORD-001',
            status: 1,
            total_amount: 250000,
            formatted_total_amount: '250,000đ',
            created_at: new Date().toISOString(),
            qr_code_url: null,
            order_items: [
                { food_name: 'Phở bò tái', quantity: 2, unit_price: 80000, notes: 'Ít hành' },
                { food_name: 'Cà phê sữa đá', quantity: 1, unit_price: 25000, notes: '' },
                { food_name: 'Bánh mì thịt nướng', quantity: 1, unit_price: 35000, notes: 'Không rau thơm' }
            ]
        };
    });

    const [loading, setLoading] = useState(false);
    const [confirmed, setConfirmed] = useState(false);

    useEffect(() => {
        const isConfirmed = order?.order_items?.every(item => item.status !== 3);
        setConfirmed(isConfirmed);

    }, [order])

    // Status configurations for Ant Design
    const getStatusConfig = (status) => {
        const configs = {
            0: {
                text: 'Chờ xác nhận',
                color: 'warning',
                icon: <ClockCircleOutlined />,
                badgeStatus: 'warning'
            },
            1: {
                text: 'Đã xác nhận',
                color: 'processing',
                icon: <ExclamationCircleOutlined />,
                badgeStatus: 'processing'
            },
            2: {
                text: 'Đang chuẩn bị',
                color: 'processing',
                icon: <FireOutlined />,
                badgeStatus: 'processing'
            },
            3: {
                text: 'Sẵn sàng',
                color: 'success',
                icon: <BellOutlined />,
                badgeStatus: 'success'
            },
            4: {
                text: 'Hoàn thành',
                color: 'success',
                icon: <CheckCircleOutlined />,
                badgeStatus: 'success'
            },
            5: {
                text: 'Đã hủy',
                color: 'error',
                icon: <CloseCircleOutlined />,
                badgeStatus: 'error'
            }
        };
        return configs[status] || {
            text: 'Không xác định',
            color: 'default',
            icon: <ClockCircleOutlined />,
            badgeStatus: 'default'
        };
    };

    // QR Code component với QR code từ order
    const QRCodeComponent = () => {
        const qrContainerStyle = {
            width: window.innerWidth < 768 ? 200 : 240,
            height: window.innerWidth < 768 ? 200 : 240,
            backgroundColor: '#000',
            borderRadius: 16,
            padding: 8,
            boxShadow: '0 20px 40px rgba(0,0,0,0.3)'
        };

        const qrInnerStyle = {
            width: '100%',
            height: '100%',
            backgroundColor: '#fff',
            borderRadius: 12,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
        };

        if (order.qr_code_url) {
            return (
                <a href={`/orders/${order.id}/print`} target="_blank" rel="noopener noreferrer" style={{ textDecoration: 'none' }}>
                    <div style={qrContainerStyle}>
                        <div style={qrInnerStyle}>
                            <Image
                                src={order.qr_code_url}
                                alt="QR Code"
                                style={{
                                    width: window.innerWidth < 768 ? 180 : 220,
                                    height: window.innerWidth < 768 ? 180 : 220,
                                    borderRadius: 8
                                }}
                                preview={false}
                                fallback={
                                    <div style={{ textAlign: 'center' }}>
                                        <QrcodeOutlined style={{ fontSize: 48, marginBottom: 8, color: '#ccc' }} />
                                        <br />
                                        <Text type="secondary" style={{ fontSize: 12 }}>QR Code</Text>
                                        <br />
                                        <Text type="secondary" style={{ fontSize: 12 }}>Scan to Order</Text>
                                    </div>
                                }
                            />
                        </div>
                   </div>
                </a>
            );
        }

        // Fallback nếu không có QR code URL
        return (
            <div style={qrContainerStyle}>
                <div style={qrInnerStyle}>
                    <div style={{ textAlign: 'center' }}>
                        <QrcodeOutlined style={{ fontSize: 48, marginBottom: 8, color: '#ccc' }} />
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>QR Code</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>Scan to Order</Text>
                    </div>
                </div>
            </div>
        );
    };

    // Handle order confirmation
    const handleConfirmOrder = async () => {
        setLoading(true);
        try {
            // Gọi API Laravel để xác nhận đơn hàng
            const response = await fetch(`/api/orders/${order.id}/confirm`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content || ''
                }
            });

            if (response.ok) {
                const data = await response.json();
                setConfirmed(true);
                setOrder(prev => ({ ...prev, status: 1 }));
                messageApi.success('Đơn hàng đã được xác nhận!');
            } else {
                throw new Error('Failed to confirm order');
            }
        } catch (error) {
            console.error('Error confirming order:', error);
            messageApi.error('Có lỗi xảy ra khi xác nhận đơn hàng');
        } finally {
            setLoading(false);
        }
    };

    // Fetch current order items (long polling)
    const fetchOrderItems = async () => {
        try {
            const response = await fetch(`/api/orders/${order.id}/items`, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content || ''
                }
            });

            if (response.ok) {
                const data = await response.json();
                let isItemPending = false;
                data.order_items.forEach(item => {
                    if (item.status === 3) {
                        isItemPending = true;
                    }
                });
                if (isItemPending) {
                    setConfirmed(false);
                }
                setOrder(prev => ({
                    ...prev,
                    order_items: data.order_items,
                    total_amount: data.total_amount,
                    formatted_total_amount: data.formatted_total_amount,
                    status: data.status
                }));
            }
        } catch (error) {
            console.error('Error fetching order items:', error);
        }
    };

    // Long polling for order items (real-time updates)
    useEffect(() => {
        // Fetch dữ liệu ban đầu
        fetchOrderItems();

        // Set up interval để cập nhật order items thường xuyên (long polling)
        const orderItemsInterval = setInterval(fetchOrderItems, 5000); // Cập nhật mỗi 5 giây

        return () => {
            clearInterval(orderItemsInterval);
        };
    }, [order.id]);

    // Responsive styles
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return (
        <div style={{
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)'
        }}>
            {contextHolder}

            <Row style={{ minHeight: '100vh' }}>
                {/* QR Code Section */}
                <Col xs={24} md={12} lg={10} xl={8}>
                    <div style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        minHeight: '100vh',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: isMobile ? '32px 24px' : '48px 32px',
                        color: 'white'
                    }}>

                        {/* Mobile Header */}
                        {isMobile && (
                            <div style={{ textAlign: 'center', marginBottom: 24 }}>
                                <Title level={2} style={{ color: 'white', marginBottom: 8 }}>
                                    Chào mừng quý khách!
                                </Title>
                                <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 16 }}>
                                    Quét mã QR để gọi thêm món
                                </Text>
                            </div>
                        )}

                        {/* QR Code Card */}
                        <Card style={{
                            marginBottom: isMobile ? 24 : 32,
                            borderRadius: 24,
                            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
                            backgroundColor: 'rgba(255,255,255,0.95)',
                            backdropFilter: 'blur(10px)',
                            border: 'none'
                        }}>
                            <div style={{ textAlign: 'center', padding: isMobile ? 16 : 24 }}>
                                <div style={{ marginBottom: isMobile ? 16 : 24, display: 'flex', justifyContent: 'center' }}>
                                    <QRCodeComponent />
                                </div>

                                <Title level={isMobile ? 3 : 2} style={{ marginBottom: 8 }}>
                                    {order.order_number}
                                </Title>
                                <Paragraph style={{
                                    color: '#6b7280',
                                    marginBottom: isMobile ? 16 : 24,
                                    fontSize: isMobile ? 14 : 16
                                }}>
                                    Quét mã để gọi thêm món
                                </Paragraph>
                            </div>
                        </Card>

                        {/* Mobile Status Badge */}
                        {isMobile && (
                            <div style={{ marginBottom: 24 }}>
                                <Badge
                                    status={getStatusConfig(order.status).badgeStatus}
                                    text={
                                        <Tag
                                            color={getStatusConfig(order.status).color}
                                            icon={getStatusConfig(order.status).icon}
                                            style={{
                                                border: 'none',
                                                color: 'white',
                                                backgroundColor: 'rgba(255,255,255,0.2)'
                                            }}
                                        >
                                            {getStatusConfig(order.status).text}
                                        </Tag>
                                    }
                                />
                            </div>
                        )}

                        {/* Desktop Welcome Text */}
                        {!isMobile && (
                            <div style={{ textAlign: 'center', maxWidth: 400 }}>
                                <Title level={1} style={{
                                    color: 'white',
                                    marginBottom: 16,
                                    fontSize: window.innerWidth > 1200 ? 48 : 36
                                }}>
                                    Chào mừng quý khách!
                                </Title>
                                <Paragraph style={{
                                    color: 'rgba(255,255,255,0.9)',
                                    marginBottom: 32,
                                    fontSize: 18,
                                    lineHeight: 1.6
                                }}>
                                    Quét mã QR để gọi thêm món hoặc theo dõi trạng thái đơn hàng của bạn
                                </Paragraph>

                                <Space size="large">
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{
                                            width: 48,
                                            height: 48,
                                            backgroundColor: 'rgba(255,255,255,0.2)',
                                            borderRadius: 16,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginBottom: 12,
                                            margin: '0 auto 12px auto'
                                        }}>
                                            <MobileOutlined style={{ fontSize: 24, color: 'white' }} />
                                        </div>
                                        <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 14 }}>Quét QR</Text>
                                    </div>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{
                                            width: 48,
                                            height: 48,
                                            backgroundColor: 'rgba(255,255,255,0.2)',
                                            borderRadius: 16,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginBottom: 12,
                                            margin: '0 auto 12px auto'
                                        }}>
                                            <ShoppingCartOutlined style={{ fontSize: 24, color: 'white' }} />
                                        </div>
                                        <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 14 }}>Gọi thêm món</Text>
                                    </div>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{
                                            width: 48,
                                            height: 48,
                                            backgroundColor: 'rgba(255,255,255,0.2)',
                                            borderRadius: 16,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginBottom: 12,
                                            margin: '0 auto 12px auto'
                                        }}>
                                            <CheckCircleOutlined style={{ fontSize: 24, color: 'white' }} />
                                        </div>
                                        <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 14 }}>Theo dõi</Text>
                                    </div>
                                </Space>
                            </div>
                        )}

                        {/* Mobile Feature Icons */}
                        {isMobile && (
                            <Space size="large" style={{ marginTop: 24 }}>
                                <div style={{ textAlign: 'center' }}>
                                    <MobileOutlined style={{ fontSize: 24, marginBottom: 4, display: 'block' }} />
                                    <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 12 }}>Quét QR</Text>
                                </div>
                                <div style={{ textAlign: 'center' }}>
                                    <ShoppingCartOutlined style={{ fontSize: 24, marginBottom: 4, display: 'block' }} />
                                    <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 12 }}>Gọi món</Text>
                                </div>
                                <div style={{ textAlign: 'center' }}>
                                    <CheckCircleOutlined style={{ fontSize: 24, marginBottom: 4, display: 'block' }} />
                                    <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 12 }}>Theo dõi</Text>
                                </div>
                            </Space>
                        )}
                    </div>
                </Col>

                {/* Order Details Section */}
                <Col xs={24} md={12} lg={14} xl={16}>
                    <div style={{
                        minHeight: '100vh',
                        overflow: 'auto',
                        padding: isMobile ? 24 : 48
                    }}>
                        <div style={{ maxWidth: 800, margin: '0 auto' }}>
                            <div style={{
                                display: 'flex',
                                flexDirection: isMobile ? 'column' : 'row',
                                justifyContent: 'space-between',
                                alignItems: isMobile ? 'flex-start' : 'center',
                                marginBottom: isMobile ? 24 : 32
                            }}>
                                <Title level={2} style={{ marginBottom: isMobile ? 8 : 0 }}>
                                    Chi tiết đơn hàng
                                </Title>
                                {!isMobile && (
                                    <Badge
                                        status={getStatusConfig(order.status).badgeStatus}
                                        text={
                                            <Tag
                                                color={getStatusConfig(order.status).color}
                                                icon={getStatusConfig(order.status).icon}
                                                style={{
                                                    padding: '8px 16px',
                                                    fontSize: 14,
                                                    fontWeight: 500
                                                }}
                                            >
                                                {getStatusConfig(order.status).text}
                                            </Tag>
                                        }
                                    />
                                )}
                            </div>

                            <Card style={{
                                boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
                                border: 'none',
                                borderRadius: isMobile ? 16 : 24,
                                marginBottom: isMobile ? 24 : 32
                            }}>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: isMobile ? 'column' : 'row',
                                    justifyContent: 'space-between',
                                    alignItems: isMobile ? 'flex-start' : 'flex-start',
                                    marginBottom: isMobile ? 16 : 24
                                }}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: isMobile ? 12 : 0 }}>
                                        <div style={{
                                            width: isMobile ? 40 : 48,
                                            height: isMobile ? 40 : 48,
                                            backgroundColor: '#f3e8ff',
                                            borderRadius: isMobile ? 12 : 16,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}>
                                            {getStatusConfig(order.status).icon}
                                        </div>
                                        <div>
                                            <Title level={isMobile ? 4 : 3} style={{ marginBottom: 4 }}>
                                                {order.order_number}
                                            </Title>
                                            <Text style={{ color: '#6b7280', fontSize: isMobile ? 12 : 14 }}>
                                                Đặt lúc {new Date(order.created_at).toLocaleTimeString('vi-VN', {
                                                hour: '2-digit',
                                                minute: '2-digit'
                                            })}
                                            </Text>
                                        </div>
                                    </div>
                                    <div style={{ textAlign: isMobile ? 'left' : 'right' }}>
                                        <Title level={3} style={{ marginBottom: 0, color: '#7c3aed' }}>
                                            {order.formatted_total_amount}
                                        </Title>
                                    </div>
                                </div>

                                <Divider style={{ margin: isMobile ? '16px 0' : '24px 0' }} />

                                {order.order_items && order.order_items.length > 0 ? (
                                    <List
                                        dataSource={order.order_items}
                                        renderItem={(item) => (
                                            <List.Item
                                                style={{
                                                    padding: isMobile ? '12px 0' : '16px 0',
                                                    borderRadius: 12,
                                                    transition: 'all 0.3s ease',
                                                    cursor: 'default'
                                                }}
                                                onMouseEnter={(e) => {
                                                    if (!isMobile) {
                                                        e.currentTarget.style.backgroundColor = '#f9fafb';
                                                        e.currentTarget.style.padding = '16px';
                                                        e.currentTarget.style.margin = '0 -16px';
                                                    }
                                                }}
                                                onMouseLeave={(e) => {
                                                    if (!isMobile) {
                                                        e.currentTarget.style.backgroundColor = 'transparent';
                                                        e.currentTarget.style.padding = '16px 0';
                                                        e.currentTarget.style.margin = '0';
                                                    }
                                                }}
                                            >
                                                <div style={{ width: '100%' }}>
                                                    <div style={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'flex-start',
                                                        marginBottom: 4
                                                    }}>
                                                        <Title level={5} style={{
                                                            marginBottom: 0,
                                                            fontSize: isMobile ? 14 : 16
                                                        }}>
                                                            {item.food_name}
                                                        </Title>
                                                        <Text strong style={{
                                                            color: '#7c3aed',
                                                            fontSize: isMobile ? 16 : 18
                                                        }}>
                                                            {(item.quantity * item.unit_price).toLocaleString()}đ
                                                        </Text>
                                                    </div>
                                                    <div style={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center'
                                                    }}>
                                                        <Text className={`rounded-full px-2 py-1 text-xs ${getStatusOrderItemColor(item.status)}`}
                                                        >
                                                            {getStatusOrderItem(item.status) || 'Chờ xác nhận'}
                                                        </Text>
                                                        <div style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: 8,
                                                            marginLeft: 'auto'
                                                        }}>
                                                            <Text style={{
                                                                color: '#6b7280',
                                                                fontSize: isMobile ? 12 : 14
                                                            }}>
                                                                {item.unit_price.toLocaleString()}đ
                                                            </Text>
                                                            <Text style={{
                                                                backgroundColor: '#f3e8ff',
                                                                color: '#7c3aed',
                                                                padding: isMobile ? '2px 8px' : '4px 12px',
                                                                borderRadius: 20,
                                                                fontSize: isMobile ? 12 : 14,
                                                                fontWeight: 500
                                                            }}>
                                                                x{item.quantity}
                                                            </Text>
                                                        </div>
                                                    </div>
                                                </div>
                                            </List.Item>
                                        )}
                                    />
                                ) : (
                                    <div style={{
                                        textAlign: 'center',
                                        padding: isMobile ? 32 : 48
                                    }}>
                                        <ShoppingCartOutlined style={{
                                            fontSize: isMobile ? 48 : 64,
                                            color: '#d1d5db',
                                            marginBottom: 16
                                        }} />
                                        <Text style={{
                                            color: '#9ca3af',
                                            fontSize: isMobile ? 16 : 18
                                        }}>
                                            Chưa có món nào được chọn
                                        </Text>
                                    </div>
                                )}
                            </Card>

                            <Button
                                type="primary"
                                size="large"
                                onClick={handleConfirmOrder}
                                disabled={confirmed}
                                loading={loading}
                                icon={confirmed ? <CheckCircleOutlined /> : null}
                                style={{
                                    width: isMobile ? '100%' : 'auto',
                                    height: isMobile ? 48 : 56,
                                    borderRadius: isMobile ? 12 : 16,
                                    fontSize: isMobile ? 16 : 18,
                                    fontWeight: 500,
                                    padding: isMobile ? '0 32px' : '0 48px',
                                    boxShadow: '0 10px 30px rgba(16, 185, 129, 0.3)',
                                    backgroundColor: confirmed ? '#d9d9d9' : '#10b981',
                                    borderColor: confirmed  ? '#d9d9d9' : '#10b981'
                                }}
                            >
                                {confirmed ? 'Đã xác nhận' : 'Xác nhận đơn hàng'}
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    );
};

export default CustomerInterface;
