import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Space } from 'antd';

function App() {
    return (
        <div className="min-h-screen bg-gray-100 p-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-8">
                    Laravel + React + Ant Design + Tailwind
                </h1>

                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                    <Card title="Ant Design Component" className="shadow-lg">
                        <p className="text-gray-600 mb-4">
                            This card uses Ant Design components with Tailwind classes.
                        </p>
                        <Button type="primary" size="large">
                            Ant Design Button
                        </Button>
                    </Card>

                    <div className="bg-white p-6 rounded-lg shadow-lg">
                        <h2 className="text-xl font-semibold text-gray-800 mb-3">
                            Tailwind Styling
                        </h2>
                        <p className="text-gray-600">
                            This section uses pure Tailwind CSS classes.
                        </p>
                    </div>
                </Space>
            </div>
        </div>
    );
}

export default App;
