import React from 'react';
import { Card, Row, Col, Typography, Tag, Space } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import OrderCard from './OrderCard';
import OrderDetails from './OrderDetails';

const { Title } = Typography;

const OrdersGrid = ({
                        orders,
                        tables,
                        selectedOrder,
                        onSelectOrder,
                        onDeselectOrder,
                        onTableModal,
                        getSelectedOrder
                    }) => {
    const selectedOrderData = orders.find(order => order.id === selectedOrder);

    return (
        <Card className="rounded-xl shadow-lg border-0">
            <div className="flex items-center justify-between mb-6 flex-wrap">
                <Title level={3} className="!mb-0">Danh sách Orders</Title>

                {selectedOrderData && (
                    <Tag
                        closable
                        onClose={onDeselectOrder}
                        closeIcon={<CloseOutlined />}
                        className="bg-slate-100 border-slate-300 text-slate-700 px-3 py-1 rounded-lg font-medium"
                    >
                        <PERSON><PERSON> chọn: Order #{selectedOrderData.customerName}
                    </Tag>
                )}
            </div>

            <Row gutter={[16, 16]}>
                {orders.map(order => (
                    <Col
                        key={order.id}
                        xs={12}
                        sm={8}
                        md={6}
                        lg={4}
                        xl={4}
                        xxl={3}
                    >
                        <OrderCard
                            order={order}
                            tables={tables}
                            isSelected={selectedOrder === order.id}
                            onSelect={() => onSelectOrder(order.id)}
                        />
                    </Col>
                ))}
            </Row>
        </Card>
    );
};

export default OrdersGrid;
