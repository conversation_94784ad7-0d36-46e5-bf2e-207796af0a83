// src/components/OrderManagement/Header.jsx
import React from 'react';
import { Card, Button, Space, Typography } from 'antd';
import { TableOutlined, CreditCardOutlined, ReloadOutlined, PlusOutlined, GiftOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Header = ({ selectedOrder, onTableModal, onPaymentModal, onRefresh, onCreateOrder }) => {
    return (
        <Card className="mb-6 rounded-xl shadow-lg overflow-hidden border-0 sticky top-0 z-50 bg-white">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-5 -m-6 mb-4">
                <Title level={4} className="!text-white !mb-1">
                    Quản Lý Order  <ReloadOutlined  onClick={onRefresh}  title="Làm mới"/>
                </Title>
            </div>

            <Space wrap className="w-full">
                <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    size="middle"
                    onClick={onCreateOrder}
                    className="bg-indigo-500 hover:bg-indigo-600 border-0 rounded-lg h-10 px-5"
                >
                    Tạo đơn hàng mới
                </Button>
                <Button
                    type="primary"
                    icon={<TableOutlined />}
                    size="middle"
                    disabled={!selectedOrder}
                    onClick={onTableModal}
                    className="bg-blue-500 hover:bg-blue-600 border-0 rounded-lg h-10 px-5"
                >
                    Ghép bàn
                </Button>
                {/*<Button*/}
                {/*    type="primary"*/}
                {/*    icon={<CreditCardOutlined />}*/}
                {/*    size="middle"*/}
                {/*    disabled={!selectedOrder}*/}
                {/*    onClick={onPaymentModal}*/}
                {/*    className="bg-green-500 hover:bg-green-600 border-0 rounded-lg h-10 px-5"*/}
                {/*>*/}
                {/*    Thanh toán*/}
                {/*</Button>*/}

                <a
                    href="/meal-tickets"
                    target="_blank"
                    className="inline-flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white border-0 rounded-lg h-10 px-5 no-underline hover:no-underline font-medium text-sm"
                    style={{ textDecoration: 'none', color: 'white' }}
                >
                    <GiftOutlined style={{ fontSize: '14px' }} />
                    Vé ăn
                </a>
            </Space>
        </Card>
    );
};

export default Header;
