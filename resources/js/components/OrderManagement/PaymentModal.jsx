import React, {useEffect, useState} from 'react';
import { Modal, Button, Card, Checkbox, Form, Input, Row, Col, Typography, Divider, notification, Radio, Space } from 'antd';
import { CheckOutlined, UserOutlined, MailOutlined, PhoneOutlined, BankOutlined, HomeOutlined, QrcodeOutlined, WalletOutlined } from '@ant-design/icons';
import { orderService } from '../../services/orderService';
import { formatCurrency } from '../../utils/helpers';

const { Title, Text } = Typography;

const PaymentModal = ({ visible, onClose, selectedOrder, setOrders, onRefresh, onDeselectOrder }) => {
    const [needInvoice, setNeedInvoice] = useState(false);
    const [paymentMethod, setPaymentMethod] = useState('cash'); // 'cash' hoặc 'transfer'
    const [qrOrder, setQrOrder] = useState('');
    const [invoiceForm] = Form.useForm();

    // Sử dụng useNotification hook của Ant Design mới
    const [api, contextHolder] = notification.useNotification();

    const closePaymentModal = () => {
        onClose();
        setNeedInvoice(false);
        setPaymentMethod('cash');
        invoiceForm.resetFields();
    };

    useEffect(() => {
        const fetchQrData = async () => {
            try {
                const qrData = await orderService.getQrApi(selectedOrder.id);
                setQrOrder(qrData.qr_payment_url)
                console.log(qrData);
            } catch (error) {
                console.error('Error fetching QR data:', error);
            }
        };

        // Chỉ fetch QR khi modal visible, có selectedOrder và chọn chuyển khoản
        if (visible && selectedOrder && paymentMethod === 'transfer') {
            fetchQrData();
        }
    }, [selectedOrder, visible, paymentMethod]);

    const processPayment = async () => {
        if (!selectedOrder) return;

        if (needInvoice) {
            try {
                await invoiceForm.validateFields();
            } catch {
                api.error({
                    message: 'Lỗi',
                    description: 'Vui lòng điền đầy đủ thông tin hóa đơn!',
                    placement: 'topRight',
                    duration: 4,
                    style: {
                        borderRadius: '8px',
                        border: '1px solid #ef4444',
                        backgroundColor: '#fef2f2',
                    }
                });
                return;
            }
        }

        const loadingKey = 'payment-loading';
        try {
            const paymentData = {
                payment_status: 1,
                payment_method: paymentMethod,
                status: 4
            };

            if (needInvoice) {
                paymentData.invoice_data = invoiceForm.getFieldsValue();
            }

            await orderService.processPayment(selectedOrder.id, paymentData);

            // Close loading and show success
            api.destroy(loadingKey);

            const paymentMethodText = paymentMethod === 'cash' ? 'tiền mặt' : 'chuyển khoản';
            api.success({
                message: '💳 Thanh toán thành công!',
                description: `Đã thanh toán ${formatCurrency(selectedOrder.totalAmount)} bằng ${paymentMethodText} cho order #${selectedOrder.number}`,
                placement: 'topRight',
                duration: 5,
                style: {
                    borderRadius: '8px',
                    border: '1px solid #10b981',
                    backgroundColor: '#dcfce7',
                }
            });

            if (needInvoice) {
                setTimeout(() => {
                    api.info({
                        message: '🧾 Hóa đơn điện tử',
                        description: 'Hóa đơn sẽ được gửi qua email trong vài phút!',
                        placement: 'topRight',
                        duration: 4,
                        style: {
                            borderRadius: '8px',
                            border: '1px solid #3b82f6',
                            backgroundColor: '#eff6ff',
                        }
                    });
                }, 1000);
            }

            onDeselectOrder();
            closePaymentModal();
            onRefresh();
        } catch (error) {
            console.error('Error processing payment:', error);

            api.destroy(loadingKey);
            api.error({
                message: '❌ Lỗi thanh toán',
                description: 'Không thể xử lý thanh toán. Vui lòng thử lại!',
                placement: 'topRight',
                duration: 4,
                style: {
                    borderRadius: '8px',
                    border: '1px solid #ef4444',
                    backgroundColor: '#fef2f2',
                }
            });
        }
    };

    if (!selectedOrder) return null;

    return (
        <div>
            {contextHolder}

            <Modal
                title={
                    <div className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-4 -m-6 mb-4 rounded-t-xl">
                        <Title level={3} className="!text-white !mb-1">💳 Thanh toán</Title>
                        <Text className="text-blue-100 text-sm">Xử lý thanh toán cho order #{selectedOrder?.customerName || selectedOrder?.number}</Text>
                    </div>
                }
                open={visible}
                onCancel={closePaymentModal}
                footer={[
                    <Button
                        key="cancel"
                        onClick={closePaymentModal}
                        size="middle"
                        className="h-10 px-6 rounded-lg border border-gray-300 hover:border-gray-400"
                    >
                        Hủy
                    </Button>,
                    <Button
                        key="confirm"
                        type="primary"
                        onClick={processPayment}
                        className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-0 h-10 px-6 rounded-lg"
                        size="middle"
                    >
                        <CheckOutlined /> Xác nhận thanh toán
                    </Button>
                ]}
                width={800}
                className="payment-modal"
                styles={{
                    content: {
                        borderRadius: '16px',
                        overflow: 'hidden'
                    }
                }}
            >
                {/* Payment Method Selection */}
                <Card className="mb-4 rounded-xl border-0 shadow-sm">
                    <div className="flex items-center mb-3">
                        <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-2">
                            💳
                        </div>
                        <Title level={5} className="!mb-0">Phương thức thanh toán</Title>
                    </div>

                    <Radio.Group
                        value={paymentMethod}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="w-full"
                    >
                        <Space size="middle">
                            <Radio value="cash">
                                <div className="flex items-center">
                                    <WalletOutlined className="text-green-600 mr-2" />
                                    <span>Tiền mặt</span>
                                </div>
                            </Radio>

                            <Radio value="transfer">
                                <div className="flex items-center">
                                    <QrcodeOutlined className="text-blue-600 mr-2" />
                                    <span>Chuyển khoản</span>
                                </div>
                            </Radio>
                        </Space>
                    </Radio.Group>
                </Card>

                {/* Order Summary */}
                <Card className="mb-4 bg-gradient-to-br from-gray-50 to-white rounded-xl border-0 shadow-sm">
                    <div className="flex items-center mb-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 text-sm">
                            📋
                        </div>
                        <Title level={5} className="!mb-0">Chi tiết order</Title>
                    </div>

                    <div className="space-y-2 mb-3">
                        {selectedOrder.items.map((item, index) => (
                            <div key={`${item.food_id}-${item.id}-${index}`}
                                 className="flex justify-between items-center py-3 px-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                                <div className="flex-1">
                                    <div className="font-medium text-gray-800 text-sm mb-1">{item.food_name}</div>
                                    {item.notes && (
                                        <Text type="secondary" className="text-xs italic">({item.notes})</Text>
                                    )}
                                    <Text type="secondary" className="text-xs block">
                                        {item.quantity} x {formatCurrency(item.unit_price)}
                                    </Text>
                                </div>
                                <div className="text-right">
                                    <div className="font-semibold text-base text-green-600">
                                        {formatCurrency(item.total_price || item.quantity * item.unit_price)}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    <Divider className="border-gray-200 my-3" />
                    <div className="flex justify-between items-center bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg">
                        <span className="text-lg font-semibold text-gray-800">Tổng tiền:</span>
                        <span className="text-xl font-bold text-green-600">
                            {formatCurrency(selectedOrder.totalAmount)}
                        </span>
                    </div>
                </Card>

                {/* QR Code Section - Only show when transfer is selected */}
                {paymentMethod === 'transfer' && (
                    <Card className="mb-4 text-center rounded-xl border-0 shadow-sm">
                        <Title level={5} className="mb-4">
                            Mã QR để thanh toán
                            <a target="_blank" href={qrOrder}> tại đây</a>
                        </Title>

                        {/*<div className="w-[280px] h-[280px] mx-auto bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-3">*/}
                        {/*    {qrOrder ? (*/}
                        {/*        <img*/}
                        {/*            src={qrOrder}*/}
                        {/*            alt="QR Code"*/}
                        {/*            className="object-contain rounded-lg max-w-full max-h-full"*/}
                        {/*        />*/}
                        {/*    ) : (*/}
                        {/*        <div className="text-center">*/}
                        {/*            <div className="text-4xl mb-2">📱</div>*/}
                        {/*            <Text type="secondary" className="text-sm">Đang tải QR Code...</Text>*/}
                        {/*        </div>*/}
                        {/*    )}*/}
                        {/*</div>*/}

                        <Text className="text-gray-600 text-sm">
                            Hỗ trợ: MoMo, ZaloPay, VietQR, Banking Apps
                        </Text>
                    </Card>
                )}

                {/* Cash Payment Info - Only show when cash is selected */}
                {paymentMethod === 'cash' && (
                    <Card className="mb-4 text-center rounded-xl border-0 shadow-sm bg-green-50">
                        <div className="flex items-center justify-center mb-3">
                            <WalletOutlined className="text-green-600 text-xl mr-2" />
                            <Title level={5} className="!mb-0">Thanh toán tiền mặt</Title>
                        </div>

                        <div className="bg-white rounded-lg p-4 border">
                            <Text className="text-gray-600 block mb-2">Số tiền cần thu:</Text>
                            <div className="text-2xl font-bold text-green-600">
                                {formatCurrency(selectedOrder.totalAmount)}
                            </div>
                        </div>
                    </Card>
                )}

                {/* Invoice Option */}
                <Card className="bg-blue-50 rounded-xl border-0 shadow-sm">
                    <div className="flex items-center mb-3">
                        <Checkbox
                            checked={needInvoice}
                            onChange={(e) => setNeedInvoice(e.target.checked)}
                            className="mr-3"
                        />
                        <span className="text-base font-medium text-gray-800">Yêu cầu xuất hóa đơn</span>
                    </div>

                    {needInvoice && (
                        <div className="mt-3 bg-white rounded-lg p-4">
                            <Form form={invoiceForm} layout="vertical" size="small">
                                <Row gutter={[16, 12]}>
                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="tax_code"
                                            label="Mã số thuế"
                                            rules={[{ required: true, message: 'Vui lòng nhập mã số thuế!' }]}
                                        >
                                            <Input
                                                prefix={<BankOutlined className="text-gray-400" />}
                                                placeholder="Nhập mã số thuế"
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="company_name"
                                            label="Tên công ty"
                                            rules={[{ required: true, message: 'Vui lòng nhập tên công ty!' }]}
                                        >
                                            <Input
                                                prefix={<BankOutlined className="text-gray-400" />}
                                                placeholder="Nhập tên công ty"
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24}>
                                        <Form.Item
                                            name="company_address"
                                            label="Địa chỉ công ty"
                                            rules={[{ required: true, message: 'Vui lòng nhập địa chỉ!' }]}
                                        >
                                            <Input
                                                prefix={<HomeOutlined className="text-gray-400" />}
                                                placeholder="Nhập địa chỉ công ty"
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="invoice_email"
                                            label="Email nhận hóa đơn"
                                            rules={[
                                                { required: true, message: 'Vui lòng nhập email!' },
                                                { type: 'email', message: 'Email không hợp lệ!' }
                                            ]}
                                        >
                                            <Input
                                                prefix={<MailOutlined className="text-gray-400" />}
                                                placeholder="<EMAIL>"
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} md={12}>
                                        <Form.Item name="buyer_name" label="Tên người mua hàng">
                                            <Input
                                                prefix={<UserOutlined className="text-gray-400" />}
                                                placeholder="Nhập tên người mua"
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24}>
                                        <Form.Item name="buyer_phone" label="Số điện thoại">
                                            <Input
                                                prefix={<PhoneOutlined className="text-gray-400" />}
                                                placeholder="0xxx xxx xxx"
                                            />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </Form>
                        </div>
                    )}
                </Card>
            </Modal>
        </div>
    );
};

export default PaymentModal;
