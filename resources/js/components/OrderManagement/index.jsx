import React, { useState, useEffect } from 'react';
import { notification } from 'antd';
import Header from './Header.jsx';
import OrdersGrid from './OrdersGrid.jsx';
import TableModal from './TableModal';
import PaymentModal from './PaymentModal';
import CreateOrderModal from './CreateOrderModal';
import { orderService } from '../../services/orderService';
import { mapOrderFromAPI, mapTableFromAPI } from '../../utils/mappers';

const OrderManagement = () => {
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [showTableModal, setShowTableModal] = useState(false);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [showCreateOrderModal, setShowCreateOrderModal] = useState(false);
    const [selectedTables, setSelectedTables] = useState([]);
    const [orders, setOrders] = useState([]);
    const [tables, setTables] = useState([]);
    const [api, contextHolder] = notification.useNotification();

    useEffect(() => {
        loadData();
        const interval = setInterval(refreshOrders, 60000);
        return () => clearInterval(interval);
    }, []);

    const loadData = async () => {
        try {
            const data = await orderService.getOrders();
            setOrders(data.orders.map(mapOrderFromAPI));
            setTables(data.tables.map(mapTableFromAPI));
        } catch (error) {
            console.error('Error loading data:', error);
        }
    };

    const selectOrder = (orderId) => {
        setSelectedOrder(orderId);
        const order = orders.find(o => o.id === orderId);
        setSelectedTables([...order.tables]);
        setTimeout(() => setShowPaymentModal(true))
    };

    const refreshOrders = async () => {
        await loadData();
        notification.success({
            message: 'Thành công',
            description: 'Đã làm mới dữ liệu!'
        });
    };

    const handleCreateOrder = async (orderData) => {
        try {
            // Call API to create order
            const newOrder = await orderService.createOrder(orderData);

            // Refresh data to show new order
            await loadData();

            api.success({
                message: 'Thành công',
                description: 'Đã tạo đơn hàng mới thành công!',
                placement: 'topRight'
            });

            return newOrder;
        } catch (error) {
            console.error('Error creating order:', error);
            api.error({
                message: 'Lỗi',
                description: error.message || 'Không thể tạo đơn hàng. Vui lòng thử lại.',
                placement: 'topRight'
            });
            throw error;
        }
    };

    const getSelectedOrder = () => orders.find(order => order.id === selectedOrder);

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 lg:p-6 sticky top-0">
            {contextHolder}
            <Header
                selectedOrder={selectedOrder}
                onTableModal={() => setShowTableModal(true)}
                onPaymentModal={() => setShowPaymentModal(true)}
                onCreateOrder={() => setShowCreateOrderModal(true)}
                onRefresh={refreshOrders}
            />

            <OrdersGrid
                orders={orders}
                tables={tables}
                selectedOrder={selectedOrder}
                onSelectOrder={selectOrder}
                onDeselectOrder={() => setSelectedOrder(null)}
                onTableModal={() => setShowTableModal(true)}
                getSelectedOrder={getSelectedOrder}
            />

            {/* Create Order Modal */}
            <CreateOrderModal
                visible={showCreateOrderModal}
                onClose={() => setShowCreateOrderModal(false)}
                tables={tables}
                onCreateOrder={handleCreateOrder}
                onRefresh={refreshOrders}
            />

            <TableModal
                visible={showTableModal}
                onClose={() => setShowTableModal(false)}
                tables={tables}
                selectedTables={selectedTables}
                setSelectedTables={setSelectedTables}
                selectedOrder={getSelectedOrder()}
                setOrders={setOrders}
            />

            <PaymentModal
                visible={showPaymentModal}
                onClose={() => setShowPaymentModal(false)}
                selectedOrder={getSelectedOrder()}
                setOrders={setOrders}
                onRefresh={refreshOrders}
                onDeselectOrder={() => setSelectedOrder(null)}
            />
        </div>
    );
};

export default OrderManagement;
