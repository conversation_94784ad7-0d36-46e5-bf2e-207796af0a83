// src/components/OrderManagement/CreateOrderModal.jsx
import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Form, Button, Steps } from 'antd';
import { FileTextOutlined } from '@ant-design/icons';

// Components
import OrderTypeSection from './CreateOrder/OrderTypeSection';
import CustomerInfoSection from './CreateOrder/CustomerInfoSection';
import TableSelectionSection from './CreateOrder/TableSelectionSection';
import NotesSection from './CreateOrder/NotesSection';
import QrDisplayModal from './CreateOrder/QrDisplayModal';

// Hook
import { useCustomerSearch } from '../../hooks/useCustomerSearch';

const { Step } = Steps;

const CreateOrderModal = ({
                              visible,
                              onClose,
                              tables = [],
                              onCreateOrder,
                              onRefresh
                          }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [selectedTables, setSelectedTables] = useState([]);
    const [currentStep, setCurrentStep] = useState(0);

    // Order type states
    const [customerType, setCustomerType] = useState('');
    const [serviceType, setServiceType] = useState('');

    // QR Code popup states
    const [showQrModal, setShowQrModal] = useState(false);
    const [createdOrder, setCreatedOrder] = useState(null);

    // Customer search hook
    const {
        customerSuggestions,
        customerInfo,
        handlePhoneSearch,
        handleCustomerSelect,
        resetCustomerData
    } = useCustomerSearch();

    // Reset form when modal opens/closes
    useEffect(() => {
        if (visible) {
            form.resetFields();
            setSelectedTables([]);
            setCurrentStep(0);
            setCustomerType('');
            setServiceType('');
            resetCustomerData();
        }
    }, [visible, resetCustomerData]);

    const handleTableChange = useCallback((tableIds) => {
        setSelectedTables(tableIds);
    }, []);

    const handleCustomerTypeChange = useCallback((e) => {
        const value = e.target.value;
        setCustomerType(value);
        setServiceType(''); // Reset service type when customer type changes
        form.setFieldsValue({ customer_type: value, service_type: undefined });
    }, [form]);

    const handleServiceTypeChange = useCallback((e) => {
        const value = e.target.value;
        setServiceType(value);
        form.setFieldsValue({ service_type: value });
    }, [form]);

    const handleNext = useCallback(async () => {
        try {
            // Validate current step
            if (currentStep === 0) {
                await form.validateFields(['customer_type', 'service_type']);
            }
            setCurrentStep(prev => prev + 1);
        } catch (error) {
            console.error('Validation failed:', error);
        }
    }, [currentStep, form]);

    const handlePrevious = useCallback(() => {
        setCurrentStep(prev => prev - 1);
    }, []);

    const handleSubmit = useCallback(async (values) => {
        setLoading(true);
        try {
            const orderData = {
                ...values,
                table_ids: selectedTables,
                customer_type: customerType,
                service_type: serviceType,
                customer_birthday: values.customer_birthday ?
                    values.customer_birthday.format('YYYY-MM-DD') : null
            };

            // Call API to create order
            const newOrder = await onCreateOrder(orderData);

            // Store created order for QR display
            setCreatedOrder(newOrder);

            // Close create modal and reset form
            form.resetFields();
            setSelectedTables([]);
            setCurrentStep(0);
            setCustomerType('');
            setServiceType('');
            resetCustomerData();
            onClose();

            // Show QR code modal
            setShowQrModal(true);

        } catch (error) {
            console.error('Error creating order:', error);
        } finally {
            setLoading(false);
        }
    }, [selectedTables, customerType, serviceType, onCreateOrder, resetCustomerData, onClose, form]);

    const handleQrModalClose = useCallback(() => {
        setShowQrModal(false);
        setCreatedOrder(null);
    }, []);

    const handleCustomerSelectWrapper = useCallback((value, option) => {
        handleCustomerSelect(value, option, form);
    }, [handleCustomerSelect, form]);

    const steps = [
        {
            title: 'Loại đơn hàng',
            description: currentStep > 0 && customerType && serviceType ?
                `${customerType === 'individual' ? 'Khách lẻ' : 'Ăn sáng kèm đặt phòng'} - ${serviceType === 'buffet' ? 'Buffet' : 'Gọi món lẻ'}` :
                'Chọn loại khách và hình thức phục vụ'
        },
        {
            title: 'Thông tin đơn hàng',
            description: 'Nhập thông tin khách hàng và bàn'
        }
    ];

    const renderOrderSummary = () => {
        if (currentStep === 0) return null;

        const customerTypeText = customerType === 'individual' ? 'Khách lẻ' : 'Ăn sáng kèm đặt phòng';
        const serviceTypeText = serviceType === 'buffet' ? 'Buffet' : 'Gọi món lẻ';

        return (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-blue-700">Loại khách:</span>
                            <span className="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                {customerTypeText}
                            </span>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-blue-700">Hình thức:</span>
                            <span className="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                {serviceTypeText}
                            </span>
                        </div>
                    </div>
                    <Button
                        size="small"
                        type="link"
                        onClick={handlePrevious}
                        className="text-blue-600"
                    >
                        Chỉnh sửa
                    </Button>
                </div>
            </div>
        );
    };

    const renderStepContent = () => {
        switch (currentStep) {
            case 0:
                return (
                    <OrderTypeSection
                        customerType={customerType}
                        serviceType={serviceType}
                        onCustomerTypeChange={handleCustomerTypeChange}
                        onServiceTypeChange={handleServiceTypeChange}
                    />
                );
            case 1:
                return (
                    <div className="space-y-4">
                        {renderOrderSummary()}

                        <CustomerInfoSection
                            customerSuggestions={customerSuggestions}
                            onPhoneSearch={handlePhoneSearch}
                            onCustomerSelect={handleCustomerSelectWrapper}
                            customerInfo={customerInfo}
                        />

                        <TableSelectionSection
                            tables={tables}
                            selectedTables={selectedTables}
                            onTableChange={handleTableChange}
                        />

                        <NotesSection />
                    </div>
                );
            default:
                return null;
        }
    };

    const getStepButtons = () => {
        if (currentStep === 0) {
            return (
                <div className="flex justify-between pt-4 border-t">
                    <Button
                        onClick={onClose}
                        size="large"
                    >
                        Hủy
                    </Button>
                    <Button
                        type="primary"
                        onClick={handleNext}
                        size="large"
                        disabled={!customerType || !serviceType}
                        className="bg-gradient-to-r from-indigo-500 to-purple-600 border-0"
                    >
                        Tiếp tục
                    </Button>
                </div>
            );
        }

        if (currentStep === 1) {
            return (
                <div className="flex justify-between pt-4 border-t">
                    <Button
                        onClick={handlePrevious}
                        size="large"
                    >
                        Quay lại
                    </Button>
                    <div className="flex gap-3">
                        <Button
                            onClick={onClose}
                            size="large"
                        >
                            Hủy
                        </Button>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            size="large"
                            className="bg-gradient-to-r from-indigo-500 to-purple-600 border-0"
                        >
                            Tạo đơn hàng
                        </Button>
                    </div>
                </div>
            );
        }
    };

    return (
        <>
            {/* Create Order Modal */}
            <Modal
                title={
                    <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <FileTextOutlined className="text-white text-sm" />
                        </div>
                        <span className="text-lg font-semibold">Tạo đơn hàng mới</span>
                    </div>
                }
                open={visible}
                onCancel={onClose}
                footer={null}
                width={800}
                className="create-order-modal"
            >
                {/* Steps */}
                <div className="mb-6">
                    <Steps current={currentStep} size="small">
                        {steps.map((step, index) => (
                            <Step
                                key={index}
                                title={step.title}
                                description={step.description}
                            />
                        ))}
                    </Steps>
                </div>

                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                    className="space-y-4"
                >
                    {/* Step Content */}
                    <div className="min-h-[400px]">
                        {renderStepContent()}
                    </div>

                    {/* Step Buttons */}
                    {getStepButtons()}
                </Form>
            </Modal>

            {/* QR Code Display Modal */}
            <QrDisplayModal
                visible={showQrModal}
                onClose={handleQrModalClose}
                createdOrder={createdOrder}
            />
        </>
    );
};

export default CreateOrderModal;
