import React from 'react';
import { Card, Badge, Typography } from 'antd';
import { getStatusBadge, formatCurrency } from '../../utils/helpers';

const { Text } = Typography;

const OrderCard = ({ order, tables, isSelected, onSelect }) => {

    const getCardBackground = (status) => {
        if (isSelected) return 'bg-slate-100';

        return 'bg-gray-50';
    };

    return (
        <Card
            hoverable
            onClick={onSelect}
            className={`rounded-lg border transition-all duration-200 min-h-[160px] h-full shadow-md hover:shadow-lg ${
                isSelected
                    ? 'border-slate-400 shadow-xl'
                    : 'border-gray-200'
            } ${getCardBackground(order.status)}`}
            bodyStyle={{ padding: '16px' }}
        >
            {isSelected && (
                <div className="absolute top-2 right-2 w-5 h-5 bg-slate-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                    ✓
                </div>
            )}

            <div className="space-y-3">
                {/* Customer Name */}
                <div>
                    <div className="font-semibold text-gray-800 truncate text-sm" title={order.customerName}>
                        {order.customerName}
                    </div>
                </div>

                {/* Time and Amount */}
                <div className="space-y-1">
                    <Text type="secondary" className="text-xs">
                        {order.createdAt}
                    </Text>
                    <div className="font-semibold text-green-600 text-sm">
                        {formatCurrency(order.totalAmount)}
                    </div>
                </div>
            </div>

            {/* Table Info */}
            <div className="mt-3 bg-gray-100 rounded p-2 text-center">
                {order.tables.length > 0 ? (
                    <span className="text-xs text-gray-700">
                        🍽️ {order.tables.map(tableId =>
                        tables.find(t => t.id === tableId)?.name || `Bàn ${tableId}`
                    ).join(', ')}
                    </span>
                ) : (
                    <span className="text-orange-600 font-medium text-xs">
                        ⚠️ Chưa ghép bàn
                    </span>
                )}
            </div>
        </Card>
    );
};

export default OrderCard;
