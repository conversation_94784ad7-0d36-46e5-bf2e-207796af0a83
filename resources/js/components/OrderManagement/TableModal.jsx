import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Card, Typography, notification } from 'antd';
import { orderService } from '../../services/orderService';

const { Title, Text } = Typography;

const TableModal = ({
                        visible,
                        onClose,
                        tables,
                        selectedTables,
                        setSelectedTables,
                        selectedOrder,
                        setOrders
                    }) => {
    // Sử dụng useNotification hook của Ant Design mới
    const [api, contextHolder] = notification.useNotification();

    const toggleTableSelection = (tableId) => {
        setSelectedTables(prev =>
            prev.includes(tableId)
                ? prev.filter(id => id !== tableId)
                : [...prev, tableId]
        );
    };

    const confirmTableAssignment = async () => {
        if (!selectedOrder) return;

        if (selectedTables.length === 0) {
            api.warning({
                message: '⚠️ Chưa chọn bàn',
                description: 'Vui lòng chọn ít nhất một bàn để ghép!',
                placement: 'topRight',
                duration: 4,
                style: {
                    borderRadius: '8px',
                    border: '1px solid #f59e0b',
                    backgroundColor: '#fffbeb',
                }
            });
            return;
        }

        // Show loading notification
        const loadingKey = 'table-merging';
        api.open({
            key: loadingKey,
            message: '⏳ Đang ghép bàn...',
            description: `Ghép ${selectedTables.length} bàn cho order #${selectedOrder.number}`,
            duration: 0,
            placement: 'topRight',
            style: {
                borderRadius: '8px',
                border: '1px solid #3b82f6',
                backgroundColor: '#eff6ff',
            }
        });

        try {
            await orderService.mergeOrderTables(selectedOrder.id, selectedTables);

            setOrders(prev => prev.map(o =>
                o.id === selectedOrder.id ? { ...o, tables: [...selectedTables] } : o
            ));

            // Close loading and show success
            api.destroy(loadingKey);

            const tableNames = selectedTables.map(tableId => {
                const table = tables.find(t => t.id === tableId);
                return table?.name || `Bàn ${tableId}`;
            }).join(', ');

            api.success({
                message: '🍽️ Ghép bàn thành công!',
                description: `Đã ghép ${tableNames} cho order #${selectedOrder.number}`,
                placement: 'topRight',
                duration: 5,
                style: {
                    borderRadius: '8px',
                    border: '1px solid #10b981',
                    backgroundColor: '#dcfce7',
                }
            });

            onClose();
        } catch (error) {
            console.error('Error merging tables:', error);

            // Close loading and show error
            api.destroy(loadingKey);

            api.error({
                message: '❌ Lỗi ghép bàn',
                description: `Không thể ghép bàn: ${error.message || 'Vui lòng thử lại!'}`,
                placement: 'topRight',
                duration: 4,
                style: {
                    borderRadius: '8px',
                    border: '1px solid #ef4444',
                    backgroundColor: '#fef2f2',
                }
            });
        }
    };

    return (
        <div>
            {/* Context holder để render notifications */}
            {contextHolder}

            <Modal
                title={
                    <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4 -m-6 mb-4 rounded-t-xl">
                        <Title level={4} className="!text-white !mb-1">🍽️ Chọn bàn</Title>
                        <Text className="text-blue-100 text-sm">
                            Ghép bàn cho order #{selectedOrder?.customerName || ''}
                        </Text>
                        {selectedTables.length > 0 && (
                            <div className="mt-2 px-2 py-1 bg-white/20 rounded text-xs">
                                <Text className="text-white">
                                    Đã chọn: {selectedTables.length} bàn
                                </Text>
                            </div>
                        )}
                    </div>
                }
                open={visible}
                onCancel={onClose}
                footer={[
                    <Button
                        key="cancel"
                        onClick={onClose}
                        size="middle"
                        className="h-10 px-6 rounded-lg border border-gray-300 hover:border-gray-400"
                    >
                        Hủy
                    </Button>,
                    <Button
                        key="confirm"
                        type="primary"
                        onClick={confirmTableAssignment}
                        size="middle"
                        disabled={selectedTables.length === 0}
                        className="bg-purple-500 hover:bg-purple-600 border-0 h-10 px-6 rounded-lg disabled:opacity-50"
                    >
                        ✓ Xác nhận ({selectedTables.length})
                    </Button>
                ]}
                width={700}
                className="table-modal"
                styles={{
                    content: {
                        borderRadius: '16px',
                        overflow: 'hidden'
                    }
                }}
            >
                {/* Instructions */}
                <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center mb-2">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2 text-xs">
                            💡
                        </div>
                        <Text className="font-medium text-gray-700 text-sm">Hướng dẫn</Text>
                    </div>
                    <Text className="text-gray-600 text-xs">
                        Nhấp vào bàn để chọn/bỏ chọn • Có thể chọn nhiều bàn • Bàn đã chọn có màu xanh
                    </Text>
                </div>

                <Row gutter={[16, 16]}>
                    {tables.map(table => {
                        const isSelected = selectedTables.includes(table.id);

                        return (
                            <Col key={table.id} xs={8} sm={6} md={4} lg={3}>
                                <Card
                                    hoverable
                                    onClick={() => toggleTableSelection(table.id)}
                                    className={`text-center rounded-lg border transition-all duration-200 h-full min-h-[90px] cursor-pointer ${
                                        isSelected
                                            ? 'border-blue-500 bg-blue-500 text-white shadow-md'
                                            : 'border-gray-300 hover:border-blue-300 hover:shadow-sm bg-white'
                                    }`}
                                    bodyStyle={{
                                        padding: '12px 8px',
                                        height: '100%',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'center'
                                    }}
                                >
                                    {/* Selection indicator */}
                                    {isSelected && (
                                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-white text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">
                                            ✓
                                        </div>
                                    )}

                                    {/* Table status indicator */}
                                    <div className={`absolute top-2 left-2 w-2 h-2 rounded-full ${
                                        table.occupied
                                            ? 'bg-red-400'
                                            : isSelected
                                                ? 'bg-white'
                                                : 'bg-green-400'
                                    }`}></div>

                                    <div className="flex flex-col items-center justify-center h-full space-y-1">
                                        <div className="text-xl">
                                            🍽️
                                        </div>
                                        <div className={`text-xs font-medium ${isSelected ? 'text-white' : 'text-gray-800'}`}>
                                            {table.name || `Bàn ${table.id}`}
                                        </div>
                                        {table.capacity && (
                                            <div className={`text-xs ${isSelected ? 'text-blue-100' : 'text-gray-500'}`}>
                                                👥 {table.capacity}
                                            </div>
                                        )}
                                    </div>
                                </Card>
                            </Col>
                        );
                    })}
                </Row>

                {/* Selected tables summary */}
                {selectedTables.length > 0 && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <div className="flex items-center justify-between">
                            <div>
                                <Text className="font-medium text-gray-700 text-sm mb-1 block">
                                    🍽️ Bàn đã chọn ({selectedTables.length}):
                                </Text>
                                <Text className="text-gray-600 text-xs">
                                    {selectedTables.map(tableId => {
                                        const table = tables.find(t => t.id === tableId);
                                        return table?.name || `Bàn ${tableId}`;
                                    }).join(', ')}
                                </Text>
                            </div>
                            <Button
                                type="text"
                                size="small"
                                onClick={() => setSelectedTables([])}
                                className="text-gray-500 hover:text-red-500"
                            >
                                🗑️ Xóa
                            </Button>
                        </div>
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default TableModal;
