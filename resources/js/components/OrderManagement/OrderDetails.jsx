
import React from 'react';
import { Card, Button, Typography, Space, Divider } from 'antd';
import {  formatCurrency } from '../../utils/helpers';

const { Title, Text } = Typography;

const OrderDetails = ({ order, tables, onClose, onTableModal }) => {

    return (
        <Card className="mt-8 bg-gradient-to-r from-purple-50 to-blue-50 border-2 border-purple-200 rounded-3xl">
            <div className="flex items-center justify-between mb-6">
                <div>
                    <Title level={3} className="!mb-2">
                        Chi tiết Order #{order.number || order.id}
                    </Title>
                    <Space>
                        <Text type="secondary">•</Text>
                        <Text type="secondary">{order.createdAt}</Text>
                    </Space>
                </div>
                <Button
                    type="text"
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                    ×
                </Button>
            </div>

            {/* Order Items */}
            <Card className="mb-6 rounded-2xl shadow-sm">
                <Title level={4} className="!mb-4">Món đã order:</Title>
                <div className="space-y-3">
                    {order.items.map((item, index) => (
                        <div key={`${item.food_id}-${item.id}-${index}`}
                             className="flex justify-between items-center py-3 border-b border-gray-100 last:border-b-0">
                            <div className="flex-1">
                                <div className="font-medium text-gray-800">{item.food_name}</div>
                                <Text type="secondary" className="text-sm">
                                    {item.quantity} x {formatCurrency(item.unit_price)}
                                </Text>
                            </div>
                            <div className="font-bold text-green-600 text-lg">
                                {formatCurrency(item.quantity * item.unit_price)}
                            </div>
                        </div>
                    ))}
                </div>

                <Divider />
                <div className="flex justify-between items-center">
                    <span className="text-xl font-bold text-gray-800">Tổng tiền:</span>
                    <span className="text-2xl font-bold text-green-600">
            {formatCurrency(order.totalAmount)}
          </span>
                </div>
            </Card>

            {/* Table Assignment */}
            <Card className="rounded-2xl shadow-sm">
                <div className="flex items-center justify-between">
                    <div>
                        <Title level={4} className="!mb-2">Bàn đã ghép:</Title>
                        <Text className="text-gray-600">
                            {order.tables.length > 0
                                ? order.tables.map(tableId =>
                                    tables.find(t => t.id === tableId)?.name || `Bàn ${tableId}`
                                ).join(', ')
                                : <span className="text-orange-600 font-medium">Chưa ghép bàn</span>
                            }
                        </Text>
                    </div>
                    <Button
                        type="primary"
                        onClick={onTableModal}
                        className="bg-blue-600 hover:bg-blue-700 border-0 rounded-xl"
                    >
                        Ghép bàn
                    </Button>
                </div>
            </Card>
        </Card>
    );
};

export default OrderDetails;
