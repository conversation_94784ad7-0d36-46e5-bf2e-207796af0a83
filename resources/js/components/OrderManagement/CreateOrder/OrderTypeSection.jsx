// src/components/OrderManagement/CreateOrder/OrderTypeSection.jsx
import React from 'react';
import { Form, Card, Radio, Space } from 'antd';
import {
    UserOutlined,
    HomeOutlined,
    CoffeeOutlined,
    OrderedListOutlined
} from '@ant-design/icons';

const OrderTypeSection = ({
                              customerType,
                              serviceType,
                              onCustomerTypeChange,
                              onServiceTypeChange
                          }) => {
    return (
        <div className="space-y-4">
            {/* Customer Type Selection */}
            <Card
                title={
                    <div className="flex items-center gap-2">
                        <UserOutlined className="text-blue-600" />
                        <span>Loại khách hàng</span>
                    </div>
                }
                className="border-blue-200"
            >
                <Form.Item
                    name="customer_type"
                    rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng' }]}
                >
                    <Radio.Group
                        value={customerType}
                        onChange={onCustomerTypeChange}
                        className="w-full"
                    >
                        <Space direction="vertical" className="w-full">
                            <Radio
                                value="individual"
                                className="w-full p-3 border rounded-lg hover:bg-blue-50 transition-colors"
                            >
                                <div className="flex items-center gap-3">
                                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <UserOutlined className="text-blue-600" />
                                    </div>
                                    <div>
                                        <div className="font-medium">Khách lẻ</div>
                                        <div className="text-sm text-gray-500">Khách hàng đến trực tiếp</div>
                                    </div>
                                </div>
                            </Radio>

                            <Radio
                                value="breakfast-room"
                                className="w-full p-3 border rounded-lg hover:bg-green-50 transition-colors"
                            >
                                <div className="flex items-center gap-3">
                                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <HomeOutlined className="text-green-600" />
                                    </div>
                                    <div>
                                        <div className="font-medium">Ăn sáng kèm đặt phòng</div>
                                        <div className="text-sm text-gray-500">
                                            Khách có đặt phòng - Miễn phí các món ăn sáng
                                        </div>
                                    </div>
                                </div>
                            </Radio>
                        </Space>
                    </Radio.Group>
                </Form.Item>
            </Card>

            {/* Service Type Selection - Only show when customer type is selected */}
            {customerType && (
                <Card
                    title={
                        <div className="flex items-center gap-2">
                            <CoffeeOutlined className="text-orange-600" />
                            <span>Hình thức phục vụ</span>
                        </div>
                    }
                    className="border-orange-200"
                >
                    <Form.Item
                        name="service_type"
                        rules={[{ required: true, message: 'Vui lòng chọn hình thức phục vụ' }]}
                    >
                        <Radio.Group
                            value={serviceType}
                            onChange={onServiceTypeChange}
                            className="w-full"
                        >
                            <Space direction="vertical" className="w-full">
                                <Radio
                                    value="buffet"
                                    className="w-full p-3 border rounded-lg hover:bg-orange-50 transition-colors"
                                >
                                    <div className="flex items-center gap-3">
                                        <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                            <CoffeeOutlined className="text-orange-600" />
                                        </div>
                                        <div>
                                            <div className="font-medium">Buffet</div>
                                            <div className="text-sm text-gray-500">
                                                Món buffet miễn phí, món ngoài menu tính tiền bình thường
                                            </div>
                                        </div>
                                    </div>
                                </Radio>

                                <Radio
                                    value="ala-carte"
                                    className="w-full p-3 border rounded-lg hover:bg-purple-50 transition-colors"
                                >
                                    <div className="flex items-center gap-3">
                                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                            <OrderedListOutlined className="text-purple-600" />
                                        </div>
                                        <div>
                                            <div className="font-medium">Gọi món lẻ</div>
                                            <div className="text-sm text-gray-500">Tất cả món đều tính tiền theo menu</div>
                                        </div>
                                    </div>
                                </Radio>
                            </Space>
                        </Radio.Group>
                    </Form.Item>
                </Card>
            )}

            {/* Service Type Info */}
            {serviceType === 'buffet' && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                        <div className="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center mt-0.5">
                            <span className="text-white text-xs">!</span>
                        </div>
                        <div>
                            <div className="font-medium text-amber-800">Lưu ý về Buffet</div>
                            <div className="text-sm text-amber-700 mt-1">
                                Khi chọn buffet, các món thuộc danh mục buffet sẽ không tính tiền khi thanh toán.
                                Chỉ các món ngoài menu buffet mới được tính giá bình thường.
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {customerType === 'breakfast-room' && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                        <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                            <span className="text-white text-xs">!</span>
                        </div>
                        <div>
                            <div className="font-medium text-green-800">Lưu ý về Ăn sáng kèm đặt phòng</div>
                            <div className="text-sm text-green-700 mt-1">
                                Khách đặt phòng được miễn phí tất cả các món ăn trong danh mục ăn sáng.
                                Các món ngoài danh mục ăn sáng vẫn tính tiền bình thường.
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default OrderTypeSection;
