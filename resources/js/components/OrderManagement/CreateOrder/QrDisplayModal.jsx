// src/components/OrderManagement/CreateOrder/QrDisplayModal.jsx
import React from 'react';
import { Modal, Button, Typography, notification } from 'antd';
import { CheckCircleOutlined, QrcodeOutlined, PrinterOutlined, ShareAltOutlined } from '@ant-design/icons';

const { Text, Title } = Typography;

const QrDisplayModal = ({
                            visible,
                            onClose,
                            createdOrder
                        }) => {
    return (
        <Modal
            title={
                <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                        <CheckCircleOutlined className="text-white text-sm" />
                    </div>
                    <span className="text-lg font-semibold text-green-700">Đơn hàng đã được tạo!</span>
                </div>
            }
            open={visible}
            onCancel={onClose}
            footer={[
                <Button key="close" onClick={onClose}>
                    Đóng
                </Button>,
                <a
                    key="print"
                    href={`/orders/${createdOrder?.id}/print`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="ant-btn ant-btn-default inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <PrinterOutlined />
                    In QR
                </a>
            ]}
            width={500}
            className="qr-display-modal"
            centered
        >
            <div className="text-center">
                {/* Order Info */}
                <div className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center justify-center mb-3">
                        <CheckCircleOutlined className="text-green-600 text-2xl mr-2" />
                        <Title level={4} className="!mb-0 text-green-700">
                            Order #{createdOrder?.id}
                        </Title>
                    </div>
                    <Text className="text-gray-600">
                        Khách hàng: <Text strong>{createdOrder?.customer?.name}</Text>
                    </Text>
                    <br />
                    <Text className="text-gray-600">
                        Bàn: <Text strong>
                        {createdOrder?.tables?.map(table => table.name).join(', ')}
                    </Text>
                    </Text>
                </div>

                {/* QR Code */}
                <div className="mb-4">
                    <Title level={5} className="mb-4 flex items-center justify-center">
                        <QrcodeOutlined className="mr-2 text-blue-600" />
                        Mã QR cho khách hàng
                    </Title>

                    <div className="w-[280px] h-[280px] mx-auto bg-white border-2 border-gray-200 rounded-lg flex items-center justify-center shadow-sm">
                        {createdOrder?.qr_code_url ? (
                            <img
                                src={createdOrder.qr_code_url}
                                alt="Order QR Code"
                                className="object-contain max-w-full max-h-full rounded"
                            />
                        ) : (
                            <div className="text-center text-gray-500">
                                <QrcodeOutlined className="text-4xl mb-2" />
                                <div>QR Code không khả dụng</div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Instructions */}
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <Text className="text-blue-800 text-sm">
                        💡 <strong>Hướng dẫn:</strong> Khách hàng có thể quét mã QR này để xem menu và đặt món trực tiếp từ bàn
                    </Text>
                </div>
            </div>
        </Modal>
    );
};

export default QrDisplayModal;
