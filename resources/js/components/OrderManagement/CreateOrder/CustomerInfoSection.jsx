// src/components/OrderManagement/CreateOrder/CustomerInfoSection.jsx
import React from 'react';
import { Card, Row, Col, Form, Input, AutoComplete, DatePicker, Typography } from 'antd';
import { UserOutlined, PhoneOutlined, MailOutlined, HomeOutlined, CalendarOutlined, RocketOutlined } from '@ant-design/icons';
import CustomerSuggestionItem from './CustomerSuggestionItem';

const { Text, Title } = Typography;

const CustomerInfoSection = ({
                                 customerSuggestions,
                                 onPhoneSearch,
                                 onCustomerSelect,
                                 customerInfo
                             }) => {
    // Format suggestions for AutoComplete
    const formattedSuggestions = customerSuggestions.map(suggestion => ({
        value: suggestion.value,
        label: <CustomerSuggestionItem customer={suggestion.customer} />,
        customer: suggestion.customer
    }));

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <UserOutlined className="text-indigo-500" />
                    <span>Thông tin khách hàng</span>
                </div>
            }
            className="mb-6"
        >
            <Row gutter={16}>
                <Col span={12}>
                    <Form.Item
                        label="Số điện thoại"
                        name="customer_phone"
                    >
                        <AutoComplete
                            options={formattedSuggestions}
                            onSearch={onPhoneSearch}
                            onSelect={onCustomerSelect}
                            placeholder="Nhập số điện thoại để tìm khách hàng"
                            prefix={<PhoneOutlined />}
                        />
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item
                        label="Tên khách hàng"
                        name="customer_name"
                        rules={[{ required: true, message: 'Vui lòng nhập tên khách hàng!' }]}
                    >
                        <Input
                            placeholder="Nhập tên khách hàng"
                            prefix={<UserOutlined />}
                        />
                    </Form.Item>
                </Col>
            </Row>

            <Row gutter={16}>
                <Col span={12}>
                    <Form.Item
                        label="Email"
                        name="customer_email"
                    >
                        <Input
                            type="email"
                            placeholder="Nhập email"
                            prefix={<MailOutlined />}
                        />
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item
                        label="Order Room Id"
                        name="breakfast_ticket_id"
                    >
                        <Input
                            placeholder="Nhập Order Room Id (chỉ nhập khi có vé ăn sáng)"
                            prefix={<RocketOutlined />}
                        />
                    </Form.Item>
                </Col>
            </Row>

            <Row gutter={16}>
                <Col span={12}>
                    <Form.Item
                        label="Ngày sinh nhật"
                        name="customer_birthday"
                    >
                        <DatePicker
                            placeholder="Chọn ngày sinh nhật"
                            format="DD/MM/YYYY"
                            className="w-full"
                            prefix={<CalendarOutlined />}
                        />
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item
                        label="Địa chỉ"
                        name="customer_address"
                    >
                        <Input
                            placeholder="Nhập địa chỉ"
                            prefix={<HomeOutlined />}
                        />
                    </Form.Item>
                </Col>
            </Row>

            {/* Customer Info Display */}
            {customerInfo && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <Title level={5} className="text-blue-900 mb-2">
                        Thông tin khách hàng
                    </Title>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Text strong>Tổng đơn hàng:</Text> {customerInfo.total_orders}
                        </Col>
                        <Col span={12}>
                            <Text strong>Tổng chi tiêu:</Text> {customerInfo.total_spent}
                        </Col>
                    </Row>
                    {customerInfo.is_birthday_today && (
                        <div className="mt-2 p-2 bg-yellow-100 text-yellow-800 rounded text-center">
                            🎂 <Text strong>Hôm nay là sinh nhật khách hàng!</Text> Có thể áp dụng khuyến mại sinh nhật.
                        </div>
                    )}
                    {customerInfo.is_birthday_this_week && !customerInfo.is_birthday_today && (
                        <div className="mt-2 p-2 bg-blue-100 text-blue-800 rounded text-center">
                            🎉 Sinh nhật khách hàng trong tuần này ({customerInfo.formatted_birthday})
                        </div>
                    )}
                </div>
            )}
        </Card>
    );
};

export default CustomerInfoSection;
