// src/components/OrderManagement/CreateOrder/NotesSection.jsx
import React from 'react';
import { Card, Form, Input } from 'antd';
import { FileTextOutlined } from '@ant-design/icons';

const { TextArea } = Input;

const NotesSection = () => {
    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <FileTextOutlined className="text-indigo-500" />
                    <span>Ghi chú</span>
                </div>
            }
            className="mb-6"
        >
            <Form.Item
                label="Ghi chú đơn hàng"
                name="notes"
            >
                <TextArea
                    rows={3}
                    placeholder="Ghi chú đặc biệt cho đơn hàng..."
                />
            </Form.Item>
        </Card>
    );
};

export default NotesSection;
