// src/components/OrderManagement/CreateOrder/CustomerSuggestionItem.jsx
import React from 'react';
import { Tag } from 'antd';

const CustomerSuggestionItem = ({ customer }) => {
    return (
        <div className="flex justify-between items-center">
            <div>
                <div className="font-medium">{customer.name}</div>
                <div className="text-sm text-gray-500">{customer.phone}</div>
                {customer.email && <div className="text-xs text-gray-400">{customer.email}</div>}
            </div>
            <div className="text-right">
                <div className="text-xs text-gray-500">{customer.total_orders} đơn hàng</div>
                <div className="text-xs text-green-600 font-medium">{customer.total_spent}</div>
                {customer.is_birthday_today && (
                    <Tag color="gold" size="small">🎂 Sinh nhật</Tag>
                )}
            </div>
        </div>
    );
};

export default CustomerSuggestionItem;
