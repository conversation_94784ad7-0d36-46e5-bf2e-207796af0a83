// src/components/OrderManagement/CreateOrder/TableSelectionSection.jsx
import React from 'react';
import { Card, Form, Select, Typography, Tag, Divider } from 'antd';
import { TableOutlined, UserOutlined } from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

const TableSelectionSection = ({
                                   tables,
                                   selectedTables,
                                   onTableChange
                               }) => {

    const getTotalCapacity = () => {
        return selectedTables.reduce((total, tableId) => {
            const table = tables.find(t => t.id === tableId);
            return total + (table ? table.capacity : 0);
        }, 0);
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <TableOutlined className="text-indigo-500" />
                    <span>Chọn bàn</span>
                </div>
            }
            className="mb-6"
        >
            <Form.Item
                label="Bàn được chọn"
                name="table_ids"
            >
                <Select
                    mode="multiple"
                    placeholder="Chọn bàn..."
                    value={selectedTables}
                    onChange={onTableChange}
                    optionLabelProp="label"
                    className="w-full"
                >
                    {tables.map(table => (
                        <Option
                            key={table.id}
                            value={table.id}
                            label={`${table.name} (${table.capacity} chỗ)`}
                        >
                            <div className="flex justify-between items-center">
                                <div>
                                    <Text strong>{table.name}</Text>
                                    <div className="text-xs text-gray-500">
                                        {table.location} • {table.capacity} chỗ
                                    </div>
                                </div>
                                <Tag color="green" size="small">Trống</Tag>
                            </div>
                        </Option>
                    ))}
                </Select>
            </Form.Item>

            {/* Selected Tables Summary */}
            {selectedTables.length > 0 && (
                <div className="mt-4 p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                    <div className="flex justify-between items-center">
                        <div>
                            <Text strong className="text-indigo-900">
                                Đã chọn {selectedTables.length} bàn
                            </Text>
                            <div className="text-sm text-indigo-700 mt-1">
                                {selectedTables.map(tableId => {
                                    const table = tables.find(t => t.id === tableId);
                                    return table ? table.name : '';
                                }).join(', ')}
                            </div>
                        </div>
                        <div className="text-right">
                            <div className="text-lg font-bold text-indigo-600">
                                <UserOutlined className="mr-1" />
                                {getTotalCapacity()} chỗ ngồi
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </Card>
    );
};

export default TableSelectionSection;
