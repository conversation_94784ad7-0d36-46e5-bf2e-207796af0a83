import React from 'react';
import { Select } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useTranslation } from '../hooks/useTranslation.js';

const { Option } = Select;

const LanguageSwitcher = ({ className = '' }) => {
  const { locale, changeLocale, t } = useTranslation();

  const handleChange = (value) => {
    changeLocale(value);
  };

  return (
    <Select
      value={locale}
      onChange={handleChange}
      className={`language-switcher ${className}`}
      style={{ minWidth: 120 }}
      suffixIcon={<GlobalOutlined />}
      size="small"
    >
      <Option value="vi">{t('vietnamese')}</Option>
      <Option value="en">{t('english')}</Option>
    </Select>
  );
};

export default LanguageSwitcher;
