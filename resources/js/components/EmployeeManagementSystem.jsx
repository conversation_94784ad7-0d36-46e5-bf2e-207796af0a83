import React, { useState, useEffect } from 'react';
import { Card, Button, Badge, Typography, Space, Tag, Row, Col, Popconfirm, Divider } from 'antd';
import { CheckOutlined, ClockCircleOutlined, FireOutlined, TableOutlined, ReloadOutlined } from '@ant-design/icons';
import {loudSpeaker} from "../services/kitchent.js";

const { Title, Text } = Typography;

const KitchenManagementSystem = () => {
    const [processedFoods, setProcessedFoods] = useState({});
    const [waitingFoods, setWaitingFoods] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // State cho popup confirms - mỗi item có key riêng
    const [openPopconfirms, setOpenPopconfirms] = useState({});
    const [confirmLoadings, setConfirmLoadings] = useState({});
    const [readyLoadings, setReadyLoadings] = useState({});

    const [lastRefresh, setLastRefresh] = useState(new Date());

    // Show popup cho item cụ thể
    const showPopconfirm = (orderCode, dishName) => {
        const itemKey = `${orderCode}-${dishName}`;
        setOpenPopconfirms(prev => ({ ...prev, [itemKey]: true }));
    };

    // Hide popup cho item cụ thể
    const hidePopconfirm = (orderCode, dishName) => {
        const itemKey = `${orderCode}-${dishName}`;
        setOpenPopconfirms(prev => ({ ...prev, [itemKey]: false }));
    };

    // Calculate waiting time from date string "30/06/2025 22:55"
    const getWaitingTimeFromString = (orderedAtString) => {
        try {
            const [datePart, timePart] = orderedAtString.split(' ');
            const [day, month, year] = datePart.split('/');
            const [hour, minute] = timePart.split(':');

            const orderedDate = new Date(year, month - 1, day, hour, minute);
            const now = new Date();
            const diffInMinutes = Math.floor((now - orderedDate) / 60000);

            return Math.max(0, diffInMinutes);
        } catch (error) {
            return 0;
        }
    };

    // Fetch orders from API
    const fetchOrders = async () => {
        try {
            setError(null);
            setLoading(true);

            const response = await fetch(`/api/reception-foods`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                setProcessedFoods(data.processedFoods || {});
                setWaitingFoods(data.waitingFood || {});
                setLastRefresh(new Date());
                handleLoudSpeaker(data.waitingFoodByName || [])
            } else {
                throw new Error('API returned error');
            }

        } catch (err) {
            console.error('Error fetching orders:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };
    const handleLoudSpeaker = (waitingFoodByName) => {
        const text = waitingFoodByName.map(item => `${item.name} ${item.quantity} suất`).join(', ');
        if (!text) return;
        loudSpeaker("Các Món Chưa trả: " + text);
    }

    // Update item status via API
    const updateItemStatus = async (items, dishName, statusCode, itemKey, orderCode) => {
        try {
            // Set loading cho item cụ thể
            if (statusCode === 4) {
                setConfirmLoadings(prev => ({ ...prev, [itemKey]: true }));
            } else {
                setReadyLoadings(prev => ({ ...prev, [itemKey]: true }));
            }

            const response = await fetch(`/api/reception-foods/update-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    items: items,
                    dish_name: dishName,
                    status: statusCode
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Refresh data after successful update
            await fetchOrders();

            // Hide popup sau khi thành công
            if (statusCode === 4) {
                hidePopconfirm(orderCode, dishName);
            }

        } catch (err) {
            console.error('Error updating item status:', err);
            setError(`Lỗi cập nhật: ${err.message}`);
        } finally {
            // Clear loading states
            setConfirmLoadings(prev => ({ ...prev, [itemKey]: false }));
            setReadyLoadings(prev => ({ ...prev, [itemKey]: false }));
        }
    };

    // Đánh dấu món đã hoàn thành (chuyển từ waiting sang processed)
    const markItemAsReady = async (orderCode, dishName) => {
        const itemKey = `${orderCode}-${dishName}`;
        await updateItemStatus(orderCode, dishName, 3, itemKey); // Status 3 = ready
    };

    // Đánh dấu món đã trả khách (xóa khỏi processed)
    const markItemAsServed = async (items, dishName, orderCode) => {
        const itemKey = `${orderCode}-${dishName}`;
        await updateItemStatus(items, dishName, 4, itemKey, orderCode); // Status 4 = served
    };

    useEffect(() => {
        fetchOrders();
        const interval = setInterval(() => {
            fetchOrders();
        }, 60000);

        return () => clearInterval(interval);
    }, []);

    // Get time color based on waiting time
    const getTimeColor = (minutes) => {
        if (minutes < 15) return 'green';
        if (minutes < 30) return 'orange';
        return 'red';
    };

    const totalProcessedOrder = () => {
        return Object.values(processedFoods).reduce((total, order) => total + order.total_quantity, 0);
    }

    const totalWaitingOrder = () => {
        return Object.values(waitingFoods).reduce((total, order) => total + order.foods.reduce((total, food) => total + food.total_quantity, 0), 0);
    }

    // Show loading state
    if (loading && Object.keys(processedFoods).length === 0 && Object.keys(waitingFoods).length === 0) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <ClockCircleOutlined className="text-6xl text-gray-400 mb-4" />
                    <div className="text-xl text-gray-500">Đang tải dữ liệu...</div>
                </div>
            </div>
        );
    }

    // Show error state
    if (error && Object.keys(processedFoods).length === 0 && Object.keys(waitingFoods).length === 0) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 text-2xl mb-4">⚠️ Lỗi kết nối</div>
                    <div className="text-gray-500 mb-4 text-lg">{error}</div>
                    <Button type="primary" size="large" onClick={fetchOrders}>
                        Thử lại
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50" style={{ padding: '12px' }}>
            <div className="max-w-full mx-auto">
                {/* Compact Header */}
                <div className="mb-4 bg-white rounded-lg shadow-sm p-4">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center">
                            <FireOutlined className="text-red-500 text-2xl mr-3" />
                            <div>
                                <Title level={3} className="mb-0">
                                    Quản Lý Món Ăn
                                </Title>
                                <Text type="secondary" className="text-sm">
                                    <ClockCircleOutlined className="mr-1" />
                                    {lastRefresh.toLocaleTimeString()}
                                </Text>
                            </div>
                        </div>
                        <Button
                            type="primary"
                            icon={<ReloadOutlined />}
                            loading={loading}
                            onClick={fetchOrders}
                            size="large"
                        >
                            Cập nhật
                        </Button>
                    </div>
                    {error && (
                        <div className="mt-2 text-red-500 text-sm">
                            ⚠️ {error}
                        </div>
                    )}
                </div>

                {/* Main Content - Two Columns for iPad */}
                <Row gutter={[16, 16]}>
                    {/* Left Column: Món Ăn Đã Sẵn Sàng */}
                    <Col xs={24} lg={12}>
                        <Card
                            title={
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <FireOutlined className="text-orange-500 text-xl mr-2" />
                                        <span className="text-lg font-semibold">Món Ăn Đã Sẵn Sàng</span>
                                    </div>
                                    <Badge
                                        count={totalProcessedOrder()}
                                        style={{ backgroundColor: '#52c41a' }}
                                    />
                                </div>
                            }
                            className="shadow-sm"
                            bodyStyle={{ padding: '16px', height: 'calc(100vh - 200px)', overflowY: 'auto' }}
                        >
                            {Object.keys(processedFoods).length === 0 ? (
                                <div className="text-center text-gray-500 py-8">
                                    <FireOutlined className="text-4xl mb-2 text-gray-300" />
                                    <div className="text-lg">Chưa có món nào hoàn thành</div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {Object.entries(processedFoods).map(([dishName, dishInfo]) => (
                                        <Card
                                            key={dishName}
                                            size="small"
                                            className="border-l-4 border-l-green-500 shadow-sm hover:shadow-md transition-shadow"
                                            bodyStyle={{ padding: '12px' }}
                                        >
                                            <div className="mb-3">
                                                <Title level={5} className="mb-1 text-green-600 text-base">
                                                    {dishName}
                                                </Title>
                                            </div>

                                            <div className="space-y-3">
                                                {dishInfo.orders?.sort((a, b) =>
                                                    getWaitingTimeFromString(b.ordered_at) - getWaitingTimeFromString(a.ordered_at)
                                                ).map((order, index) => {
                                                    const itemKey = `${order.order_code}-${dishName}`;
                                                    const waitingMinutes = getWaitingTimeFromString(order.ordered_at);
                                                    return (
                                                        <div key={index} className="bg-gray-50 rounded-lg p-3">
                                                            {/* Order Info Row */}
                                                            <div className="flex justify-between items-start mb-2">
                                                                <div className="flex flex-wrap gap-1">
                                                                    {order.tables?.length > 0 && order.tables.map((table_name) => (
                                                                        <Tag color="blue" key={table_name} className="text-xs">
                                                                            {table_name ?? 'N/A'}
                                                                        </Tag>
                                                                    ))}
                                                                </div>
                                                                <div className="flex items-center space-x-2">
                                                                    <Text type="secondary" className="text-xs font-bold">{order.name}</Text>
                                                                    <Badge count={order.quantity} style={{ backgroundColor: '#1890ff' }} />
                                                                </div>
                                                            </div>

                                                            {/* Quantity and Action Row */}
                                                            <div className="flex justify-between items-center">
                                                                <div className="flex gap-1">
                                                                    <Tag color={getTimeColor(waitingMinutes)} className="font-medium m-0">
                                                                        {waitingMinutes} phút
                                                                    </Tag>
                                                                </div>
                                                                <Popconfirm
                                                                    title="Xác nhận trả khách"
                                                                    description={`Đánh dấu ${dishName} đã trả khách?`}
                                                                    open={openPopconfirms[itemKey] || false}
                                                                    onConfirm={() => markItemAsServed(order.items, dishName, order.order_code)}
                                                                    okButtonProps={{ loading: confirmLoadings[itemKey] || false }}
                                                                    onCancel={() => hidePopconfirm(order.order_code, dishName)}
                                                                    okText="Đã trả"
                                                                    cancelText="Hủy"
                                                                >
                                                                    <Button
                                                                        type="primary"
                                                                        size="small"
                                                                        icon={<CheckOutlined />}
                                                                        onClick={() => showPopconfirm(order.order_code, dishName)}
                                                                        className="bg-green-500 hover:bg-green-600 border-green-500"
                                                                    >
                                                                        Đã Trả
                                                                    </Button>
                                                                </Popconfirm>
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </Card>
                                    ))}
                                </div>
                            )}
                        </Card>
                    </Col>

                    {/* Right Column: Các Món Chưa/Đang Làm */}
                    <Col xs={24} lg={12}>
                        <Card
                            title={
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <TableOutlined className="text-blue-500 text-xl mr-2" />
                                        <span className="text-lg font-semibold">Các Món Chưa trả</span>
                                    </div>
                                    <Badge
                                        count={totalWaitingOrder()}
                                        style={{ backgroundColor: '#1890ff' }}
                                    />
                                </div>
                            }
                            className="shadow-sm"
                            bodyStyle={{ padding: '16px', height: 'calc(100vh - 200px)', overflowY: 'auto' }}
                        >
                            {Object.keys(waitingFoods).length === 0 ? (
                                <div className="text-center text-gray-500 py-8">
                                    <TableOutlined className="text-4xl mb-2 text-gray-300" />
                                    <div className="text-lg">Tất cả đơn hàng đã hoàn thành</div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {Object.entries(waitingFoods).map(([orderCode, orderInfo]) => (
                                        <Card
                                            key={orderCode}
                                            size="small"
                                            className="border-l-4 border-l-blue-500 shadow-sm hover:shadow-md transition-shadow"
                                            bodyStyle={{ padding: '12px' }}
                                        >
                                            <div className="mb-3">
                                                <Title level={5} className="mb-1 text-blue-600 text-base">
                                                    {orderInfo?.name}
                                                </Title>
                                                <div className="flex flex-wrap gap-1">
                                                    {orderInfo.tables?.length > 0 && orderInfo.tables.map((table_name) => (
                                                        <Tag color="blue" key={table_name} size="small">
                                                            <TableOutlined className="mr-1" />
                                                            {table_name ?? 'N/A'}
                                                        </Tag>
                                                    ))}
                                                </div>
                                            </div>

                                            <div className="space-y-2">
                                                {orderInfo.foods?.map((food, index) => {
                                                    const waitingMinutes = getWaitingTimeFromString(food.ordered_at);
                                                    return (
                                                        <div key={index} className="bg-gray-50 rounded p-2">
                                                            <div className="flex justify-between items-center">
                                                                <div className="flex items-center space-x-2 flex-1">
                                                                    <Text strong className="text-sm">{food.name}</Text>
                                                                    <Badge count={food.total_quantity} size="small" />
                                                                </div>
                                                                <Tag color={getTimeColor(waitingMinutes)} className="font-medium ml-2">
                                                                    {waitingMinutes} phút
                                                                </Tag>
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </Card>
                                    ))}
                                </div>
                            )}
                        </Card>
                    </Col>
                </Row>

                {/* Footer */}
                <div className="mt-6 text-center text-gray-500 text-sm bg-white rounded-lg p-3 shadow-sm">
                    <ClockCircleOutlined className="mr-1" />
                    Tự động cập nhật mỗi 30 giây
                </div>
            </div>
        </div>
    );
};

export default KitchenManagementSystem;
