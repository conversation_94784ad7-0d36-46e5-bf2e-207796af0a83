// resources/js/components/OrderApp.jsx
import React, { useState, useEffect } from 'react';
import {
    Table,
    Button,
    Card,
    Space,
    Tag,
    Modal,
    Form,
    Input,
    Select,
    message,
    Spin
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

const { Option } = Select;

function OrderApp() {
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingOrder, setEditingOrder] = useState(null);
    const [form] = Form.useForm();

    // Fetch orders từ Laravel API
    useEffect(() => {
        fetchOrders();
    }, []);

    const fetchOrders = async () => {
        setLoading(true);
        try {
            const response = await fetch('/api/orders');
            const data = await response.json();
            setOrders(data);
        } catch (error) {
            message.error('Failed to fetch orders');
        } finally {
            setLoading(false);
        }
    };

    const handleCreateOrder = () => {
        setEditingOrder(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleEditOrder = (order) => {
        setEditingOrder(order);
        form.setFieldsValue(order);
        setModalVisible(true);
    };

    const handleDeleteOrder = async (orderId) => {
        try {
            await fetch(`/api/orders/${orderId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });
            message.success('Order deleted successfully');
            fetchOrders();
        } catch (error) {
            message.error('Failed to delete order');
        }
    };

    const handleSubmit = async (values) => {
        try {
            const method = editingOrder ? 'PUT' : 'POST';
            const url = editingOrder ? `/api/orders/${editingOrder.id}` : '/api/orders';

            await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(values)
            });

            message.success(`Order ${editingOrder ? 'updated' : 'created'} successfully`);
            setModalVisible(false);
            fetchOrders();
        } catch (error) {
            message.error('Failed to save order');
        }
    };

    const columns = [
        {
            title: 'Order ID',
            dataIndex: 'id',
            key: 'id',
        },
        {
            title: 'Customer',
            dataIndex: 'customer_name',
            key: 'customer_name',
        },
        {
            title: 'Product',
            dataIndex: 'product',
            key: 'product',
        },
        {
            title: 'Amount',
            dataIndex: 'amount',
            key: 'amount',
            render: (amount) => `$${amount}`,
        },
        {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            render: (status) => {
                const color = status === 'completed' ? 'green' :
                    status === 'pending' ? 'orange' : 'red';
                return <Tag color={color}>{status.toUpperCase()}</Tag>;
            },
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (_, record) => (
                <Space>
                    <Button
                        type="primary"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEditOrder(record)}
                    >
                        Edit
                    </Button>
                    <Button
                        danger
                        size="small"
                        icon={<DeleteOutlined />}
                        onClick={() => handleDeleteOrder(record.id)}
                    >
                        Delete
                    </Button>
                </Space>
            ),
        },
    ];

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
                <Card
                    title={
                        <div className="flex justify-between items-center">
                            <h1 className="text-2xl font-bold text-gray-800">Order Management</h1>
                            <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                onClick={handleCreateOrder}
                                className="bg-blue-500 hover:bg-blue-600"
                            >
                                New Order
                            </Button>
                        </div>
                    }
                    className="shadow-lg"
                >
                    <Spin spinning={loading}>
                        <Table
                            dataSource={orders}
                            columns={columns}
                            rowKey="id"
                            pagination={{ pageSize: 10 }}
                            className="bg-white"
                        />
                    </Spin>
                </Card>

                <Modal
                    title={editingOrder ? 'Edit Order' : 'Create New Order'}
                    open={modalVisible}
                    onCancel={() => setModalVisible(false)}
                    footer={null}
                    width={600}
                >
                    <Form
                        form={form}
                        layout="vertical"
                        onFinish={handleSubmit}
                        className="mt-4"
                    >
                        <Form.Item
                            name="customer_name"
                            label="Customer Name"
                            rules={[{ required: true, message: 'Please enter customer name' }]}
                        >
                            <Input placeholder="Enter customer name" />
                        </Form.Item>

                        <Form.Item
                            name="product"
                            label="Product"
                            rules={[{ required: true, message: 'Please enter product' }]}
                        >
                            <Input placeholder="Enter product name" />
                        </Form.Item>

                        <Form.Item
                            name="amount"
                            label="Amount"
                            rules={[{ required: true, message: 'Please enter amount' }]}
                        >
                            <Input type="number" placeholder="Enter amount" prefix="$" />
                        </Form.Item>

                        <Form.Item
                            name="status"
                            label="Status"
                            rules={[{ required: true, message: 'Please select status' }]}
                        >
                            <Select placeholder="Select status">
                                <Option value="pending">Pending</Option>
                                <Option value="completed">Completed</Option>
                                <Option value="cancelled">Cancelled</Option>
                            </Select>
                        </Form.Item>

                        <Form.Item className="mb-0 text-right">
                            <Space>
                                <Button onClick={() => setModalVisible(false)}>
                                    Cancel
                                </Button>
                                <Button type="primary" htmlType="submit">
                                    {editingOrder ? 'Update' : 'Create'}
                                </Button>
                            </Space>
                        </Form.Item>
                    </Form>
                </Modal>
            </div>
        </div>
    );
}

export default OrderApp;
