@props(['app' => 'default'])

<script>
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/sw-{{ $app }}.js')
                .then(function(registration) {
                    console.log('SW registered for {{ $app }}:', registration);
                })
                .catch(function(registrationError) {
                    console.log('SW registration failed for {{ $app }}:', registrationError);
                });
        });
    }

    // PWA Install Prompt
    let deferredPrompt;
    const installButton = document.createElement('button');
    installButton.textContent = 'Cài đặt App';
    installButton.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 20px;
    background: #4f46e5;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: none;
    z-index: 1000;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
`;

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        document.body.appendChild(installButton);
        installButton.style.display = 'block';
    });

    installButton.addEventListener('click', async () => {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;

            if (outcome === 'accepted') {
                console.log('User accepted install for {{ $app }}');
            }

            deferredPrompt = null;
            installButton.style.display = 'none';
        }
    });

    window.addEventListener('appinstalled', () => {
        console.log('{{ $app }} was installed');
        installButton.style.display = 'none';
        deferredPrompt = null;
    });
</script>
