{{-- resources/views/components/pwa-head.blade.php --}}
@props(['app' => 'default', 'title' => null, 'path' => null])
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
<meta name="csrf-token" content="{{ csrf_token() }}">
<meta name="format-detection" content="telephone=no">
<meta name="msapplication-tap-highlight" content="no">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

<meta name="theme-color" content="#000000">
<meta name="apple-mobile-web-app-title" content="OrderManager">
<meta name="application-name" content="OrderManager">
<meta name="msapplication-TileColor" content="#000000">

<link rel="apple-touch-icon" href="{{ asset('icons/apple-touch-icon.png') }}">
<link rel="icon" type="image/png" sizes="192x192" href="{{ asset('icons/icon-192x192.png') }}">
<link rel="icon" type="image/png" sizes="512x512" href="{{ asset('icons/icon-512x512.png') }}">
<base href="{{ url('/') }}/">
{{-- Manifest --}}
@if($path)
    <link rel="manifest" href="/manifest/{{ $app }}/{{ $path }}">
@else
    <link rel="manifest" href="/manifest/{{ $app }}">
@endif

{{-- Page Title --}}
<title>{{ $title }} - {{ config('app.name') }}</title>
