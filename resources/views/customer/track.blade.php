<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Order Tracking">

    <title>Theo dõi đơn hàng - {{ $order->order_number }}</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])

    <style>
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-confirmed { background-color: #dbeafe; color: #1e40af; }
        .status-preparing { background-color: #fde68a; color: #d97706; }
        .status-ready { background-color: #d1fae5; color: #065f46; }
        .status-completed { background-color: #dcfce7; color: #166534; }
        .status-cancelled { background-color: #fee2e2; color: #991b1b; }

        .progress-step {
            transition: all 0.3s ease;
        }
        .progress-step.active {
            background-color: #4f46e5;
            color: white;
        }
        .progress-step.completed {
            background-color: #10b981;
            color: white;
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="{{ route('customer.order', $order->table->code) }}" class="text-indigo-600 hover:text-indigo-800 mr-3">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </a>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900">Theo dõi đơn hàng</h1>
                        <p class="text-sm text-gray-600">{{ $order->order_number }}</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-600">{{ $order->table->name }}</div>
                    <div class="text-xs text-gray-500">{{ $order->order_time->format('d/m/Y H:i') }}</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Order Status -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">Trạng thái đơn hàng</h2>
                    <span class="status-badge status-{{ strtolower(str_replace(' ', '-', $order->status_text)) }}">
                        <i class="fas fa-{{ $order->status_icon }} mr-2"></i>
                        {{ $order->status_text }}
                    </span>
                </div>

                <!-- Progress Steps -->
                <div class="flex items-center justify-between mb-6">
                    @php
                        $steps = [
                            ['key' => 'pending', 'label' => 'Chờ xác nhận', 'icon' => 'clock'],
                            ['key' => 'confirmed', 'label' => 'Đã xác nhận', 'icon' => 'check'],
                            ['key' => 'preparing', 'label' => 'Đang chuẩn bị', 'icon' => 'utensils'],
                            ['key' => 'ready', 'label' => 'Sẵn sàng', 'icon' => 'bell'],
                            ['key' => 'completed', 'label' => 'Hoàn thành', 'icon' => 'check-circle']
                        ];
                        $currentStatus = $order->status;
                    @endphp

                    @foreach($steps as $index => $step)
                        <div class="flex flex-col items-center flex-1">
                            <div class="progress-step w-10 h-10 rounded-full border-2 flex items-center justify-center mb-2
                                {{ $index < $currentStatus ? 'completed' : ($index == $currentStatus ? 'active' : 'border-gray-300 text-gray-400') }}">
                                <i class="fas fa-{{ $step['icon'] }} text-sm"></i>
                            </div>
                            <span class="text-xs text-center {{ $index <= $currentStatus ? 'text-gray-900 font-medium' : 'text-gray-500' }}">
                                {{ $step['label'] }}
                            </span>
                            @if($index < count($steps) - 1)
                                <div class="hidden sm:block w-full h-0.5 bg-gray-300 mt-2 {{ $index < $currentStatus ? 'bg-green-500' : '' }}"></div>
                            @endif
                        </div>
                    @endforeach
                </div>

                @if($order->notes)
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-medium text-blue-900 mb-1">Ghi chú đơn hàng:</h4>
                        <p class="text-blue-800">{{ $order->notes }}</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Customer Info -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thông tin khách hàng</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Tên khách hàng</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $order->customer->name }}</p>
                    </div>
                    @if($order->customer->phone)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Số điện thoại</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $order->customer->phone }}</p>
                        </div>
                    @endif
                    @if($order->customer->email)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $order->customer->email }}</p>
                        </div>
                    @endif
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Bàn</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $order->table->name }} ({{ $order->table->capacity }} chỗ)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Items -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Chi tiết đơn hàng</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @foreach($order->orderItems as $item)
                        <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                            <div class="flex items-center space-x-4">
                                @if($item->food->image_id)
                                    <img class="h-16 w-16 rounded-lg object-cover flex-shrink-0"
                                         src="{{ \App\Helper\helper::getImageGoogleDrive($item->food->image_id) }}"
                                         alt="{{ $item->food->name }}">
                                @else
                                    <div class="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-utensils text-gray-400"></i>
                                    </div>
                                @endif
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900">{{ $item->food->name }}</h4>
                                    <p class="text-sm text-gray-600">{{ $item->formatted_unit_price }}</p>
                                    @if($item->notes)
                                        <p class="text-xs text-gray-500 mt-1">Ghi chú: {{ $item->notes }}</p>
                                    @endif
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-gray-900">x{{ $item->quantity }}</div>
                                <div class="text-sm font-semibold text-indigo-600">{{ $item->formatted_total_price }}</div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Order Summary -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Tạm tính:</span>
                            <span class="text-gray-900">{{ $order->formatted_subtotal }}</span>
                        </div>
                        @if($order->discount_amount > 0)
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Giảm giá:</span>
                                <span class="text-red-600">-{{ $order->formatted_discount_amount }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between text-lg font-semibold border-t border-gray-200 pt-2">
                            <span class="text-gray-900">Tổng cộng:</span>
                            <span class="text-indigo-600">{{ $order->formatted_total_amount }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="{{ route('customer.menu', $order->table->code) }}"
                       class="flex items-center justify-center px-6 py-3 border border-indigo-300 rounded-lg text-indigo-700 bg-indigo-50 hover:bg-indigo-100 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Đặt thêm món
                    </a>

                    <button onclick="callStaff()"
                            class="flex items-center justify-center px-6 py-3 border border-green-300 rounded-lg text-green-700 bg-green-50 hover:bg-green-100 transition-colors duration-200">
                        <i class="fas fa-bell mr-2"></i>
                        Gọi nhân viên
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- PWA Scripts -->
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('SW registered successfully:', registration);
                } catch (error) {
                    console.warn('SW registration failed:', error);
                }
            });
        }

        // Auto refresh order status every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);

        function callStaff() {
            if (confirm('Bạn có muốn gọi nhân viên không?')) {
                alert('Đã gửi yêu cầu gọi nhân viên. Nhân viên sẽ đến bàn trong giây lát.');
            }
        }
    </script>
</body>
</html>
