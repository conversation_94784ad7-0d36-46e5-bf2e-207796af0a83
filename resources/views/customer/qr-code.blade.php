<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code - {{ $table->name }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .qr-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        
        .qr-code {
            width: 250px;
            height: 250px;
            margin: 0 auto 2rem;
            border: 8px solid #f3f4f6;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9fafb;
        }
        
        .table-info {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0.5rem 0;
            color: #6b7280;
        }
        
        .info-item i {
            margin-right: 0.5rem;
            color: #667eea;
        }
        
        @media print {
            body {
                background: white;
            }
            
            .qr-container {
                box-shadow: none;
                border: 2px solid #e5e7eb;
            }
        }
    </style>
</head>
<body>
    <div class="qr-container">
        <!-- Table Name -->
        <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ $table->name }}</h1>
        <p class="text-gray-600 mb-6">Quét mã để gọi món</p>
        
        <!-- QR Code -->
        <div class="qr-code">
            <div id="qrcode"></div>
        </div>
        
        <!-- Table Information -->
        <div class="table-info">
            <div class="info-item">
                <i class="fas fa-users"></i>
                <span>{{ $table->capacity }} chỗ ngồi</span>
            </div>
            @if($table->location)
                <div class="info-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>{{ $table->location }}</span>
                </div>
            @endif
            <div class="info-item">
                <i class="fas fa-qrcode"></i>
                <span class="font-mono">{{ $table->code }}</span>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="text-sm text-gray-600 space-y-2">
            <p><i class="fas fa-mobile-alt mr-2 text-purple-600"></i>Sử dụng camera điện thoại để quét</p>
            <p><i class="fas fa-utensils mr-2 text-green-600"></i>Chọn món và thêm vào giỏ hàng</p>
            <p><i class="fas fa-bell mr-2 text-blue-600"></i>Theo dõi đơn hàng real-time</p>
        </div>
        
        <!-- Print Button -->
        <button onclick="window.print()" class="mt-6 px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            <i class="fas fa-print mr-2"></i>
            In QR Code
        </button>
    </div>

    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // Generate QR Code
        const qrUrl = '{{ $qrUrl }}';
        
        QRCode.toCanvas(document.getElementById('qrcode'), qrUrl, {
            width: 200,
            height: 200,
            colorDark: '#1f2937',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.M
        }, function (error) {
            if (error) {
                console.error('Error generating QR code:', error);
                document.getElementById('qrcode').innerHTML = `
                    <div class="text-red-500">
                        <i class="fas fa-exclamation-triangle text-4xl mb-2"></i>
                        <p>Không thể tạo QR code</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
