<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iPad Interface - {{ $order->order_number }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @vite(['resources/css/app.css', 'resources/js/customer-ipad.jsx'])
</head>
<body>
<!-- React App Container -->
<div id="ipad-interface-root"></div>
<!-- Pass data to React -->
<script>
    window.orderData = {
        id: {{ $order->id }},
        order_number: '{{ $order->order_number }}',
        status: {{ $order->status }},
        total_amount: {{ $order->total_amount }},
        formatted_total_amount: '{{ number_format($order->total_amount) }}đ',
        created_at: '{{ $order->created_at->toISOString() }}',
        qr_code_url: '{{ $order->qr_code_url }}', // Thêm dòng này
        order_items: [
                @foreach($order->orderItems as $item)
            {
                id: {{ $item->id }},
                food_name: '{{ addslashes($item->food->name) }}',
                quantity: {{ $item->quantity }},
                status: {{ $item->status }},
                unit_price: {{ $item->unit_price }},
                notes: '{{ addslashes($item->notes ?? '') }}'
            }@if(!$loop->last),@endif
            @endforeach
        ]
    };
</script>
</body>
</html>
