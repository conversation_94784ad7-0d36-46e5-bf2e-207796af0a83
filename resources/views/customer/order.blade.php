<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Restaurant App">

    <title>{{ $table->name }} - {{ config('app.name') }}</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/images/icon-192x192.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">{{ config('app.name') }}</h1>
                    </div>
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-chair mr-1"></i>
                        {{ $table->name }}
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Welcome Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-indigo-100 mb-4">
                        <i class="fas fa-utensils text-indigo-600 text-2xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Chào mừng đến với {{ $table->name }}!</h2>
                    <p class="text-gray-600 mb-4">Cảm ơn bạn đã chọn nhà hàng của chúng tôi</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-6">
                        <div class="flex items-center justify-center">
                            <i class="fas fa-users mr-2 text-indigo-600"></i>
                            <span>{{ $table->capacity }} chỗ ngồi</span>
                        </div>
                        @if($table->location)
                            <div class="flex items-center justify-center">
                                <i class="fas fa-map-marker-alt mr-2 text-indigo-600"></i>
                                <span>{{ $table->location }}</span>
                            </div>
                        @endif
                        <div class="flex items-center justify-center">
                            <i class="fas fa-qrcode mr-2 text-indigo-600"></i>
                            <span class="font-mono">{{ $table->code }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Order Status -->
            @if($table->currentOrder->first())
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-receipt mr-2 text-indigo-600"></i>
                        Đơn hàng hiện tại
                    </h3>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-blue-900">{{ $table->currentOrder->order_number }}</h4>
                                <p class="text-sm text-blue-700">{{ $table->currentOrder->customer->name ?? 'Khách vãng lai' }}</p>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $table->currentOrder->status_color }}">
                                    {{ $table->currentOrder->status_text }}
                                </span>
                                <p class="text-sm font-medium text-blue-900 mt-1">{{ $table->currentOrder->formatted_total_amount }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="space-y-3">
                        <h5 class="font-medium text-gray-900">Món đã gọi:</h5>
                        @foreach($table->currentOrder->orderItems as $item)
                            <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                                <div class="flex-1">
                                    <span class="text-sm font-medium text-gray-900">{{ $item->food->name }}</span>
                                    <span class="text-sm text-gray-500 ml-2">x{{ $item->quantity }}</span>
                                    @if($item->notes)
                                        <p class="text-xs text-gray-500 mt-1">Ghi chú: {{ $item->notes }}</p>
                                    @endif
                                </div>
                                <span class="text-sm font-medium text-gray-900">{{ $item->formatted_total_price }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Action Buttons -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-concierge-bell mr-2 text-indigo-600"></i>
                    Dịch vụ
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="{{ route('customer.menu', $table->code) }}" class="flex items-center justify-center px-6 py-4 border border-indigo-300 rounded-lg text-indigo-700 bg-indigo-50 hover:bg-indigo-100 transition-colors duration-200">
                        <i class="fas fa-utensils mr-3 text-xl"></i>
                        <div class="text-left">
                            <div class="font-medium">Xem thực đơn</div>
                            <div class="text-sm text-indigo-600">Đặt món trực tuyến</div>
                        </div>
                    </a>

                    <button onclick="callStaff()" class="flex items-center justify-center px-6 py-4 border border-green-300 rounded-lg text-green-700 bg-green-50 hover:bg-green-100 transition-colors duration-200">
                        <i class="fas fa-bell mr-3 text-xl"></i>
                        <div class="text-left">
                            <div class="font-medium">Gọi nhân viên</div>
                            <div class="text-sm text-green-600">Hỗ trợ ngay lập tức</div>
                        </div>
                    </button>

                    <button onclick="requestPayment()" class="flex items-center justify-center px-6 py-4 border border-yellow-300 rounded-lg text-yellow-700 bg-yellow-50 hover:bg-yellow-100 transition-colors duration-200">
                        <i class="fas fa-credit-card mr-3 text-xl"></i>
                        <div class="text-left">
                            <div class="font-medium">Thanh toán</div>
                            <div class="text-sm text-yellow-600">Yêu cầu thanh toán</div>
                        </div>
                    </button>

                    <button onclick="showFeedback()" class="flex items-center justify-center px-6 py-4 border border-purple-300 rounded-lg text-purple-700 bg-purple-50 hover:bg-purple-100 transition-colors duration-200">
                        <i class="fas fa-star mr-3 text-xl"></i>
                        <div class="text-left">
                            <div class="font-medium">Đánh giá</div>
                            <div class="text-sm text-purple-600">Chia sẻ trải nghiệm</div>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Info Section -->
            <div class="bg-gray-50 rounded-lg p-6 mt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info-circle mr-2 text-gray-600"></i>
                    Thông tin hữu ích
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Giờ phục vụ</h4>
                        <ul class="space-y-1">
                            <li>Thứ 2 - Thứ 6: 10:00 - 22:00</li>
                            <li>Thứ 7 - Chủ nhật: 09:00 - 23:00</li>
                        </ul>
                    </div>

                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Liên hệ</h4>
                        <ul class="space-y-1">
                            <li><i class="fas fa-phone mr-2"></i>0123 456 789</li>
                            <li><i class="fas fa-envelope mr-2"></i><EMAIL></li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="text-center text-sm text-gray-500">
                    <p>&copy; {{ date('Y') }} {{ config('app.name') }}. Cảm ơn quý khách đã sử dụng dịch vụ.</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- PWA Scripts -->
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('SW registered successfully:', registration);
                } catch (error) {
                    console.warn('SW registration failed:', error);
                }
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            showInstallButton();
        });

        function showInstallButton() {
            const installButton = document.createElement('button');
            installButton.textContent = 'Cài đặt ứng dụng';
            installButton.className = 'fixed bottom-4 right-4 bg-indigo-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            installButton.onclick = installApp;
            document.body.appendChild(installButton);
        }

        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                });
            }
        }

        // Customer Actions
        function callStaff() {
            if (confirm('Bạn có muốn gọi nhân viên không?')) {
                // Here you can implement real-time notification to staff
                alert('Đã gửi yêu cầu gọi nhân viên. Nhân viên sẽ đến bàn trong giây lát.');
            }
        }

        function requestPayment() {
            if (confirm('Bạn có muốn yêu cầu thanh toán không?')) {
                // Here you can implement payment request
                alert('Đã gửi yêu cầu thanh toán. Nhân viên sẽ mang hóa đơn đến bàn.');
            }
        }

        function showFeedback() {
            // Here you can implement feedback form
            alert('Cảm ơn bạn! Chức năng đánh giá sẽ được cập nhật sớm.');
        }

        // Auto refresh order status
        setInterval(() => {
            if (document.querySelector('.current-order')) {
                location.reload();
            }
        }, 30000); // Refresh every 30 seconds
    </script>
</body>
</html>
