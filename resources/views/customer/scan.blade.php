<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quét mã QR - G<PERSON><PERSON> món</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .scan-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .scan-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .qr-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 20px rgba(102, 126, 234, 0);
            }
        }
        
        .step-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin: 0.5rem 0;
            background: #f8fafc;
            border-radius: 12px;
            text-align: left;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body>
    <div class="scan-container">
        <div class="scan-card">
            <!-- QR Icon -->
            <div class="qr-icon">
                <i class="fas fa-qrcode text-white text-5xl"></i>
            </div>
            
            <!-- Title -->
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Quét mã QR để gọi món</h1>
            <p class="text-gray-600 mb-8">Sử dụng camera điện thoại để quét mã QR trên bàn</p>
            
            <!-- Steps -->
            <div class="space-y-3 mb-8">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div>
                        <div class="font-semibold text-gray-800">Tìm mã QR trên bàn</div>
                        <div class="text-sm text-gray-600">Mã QR được đặt trên mỗi bàn ăn</div>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div>
                        <div class="font-semibold text-gray-800">Mở camera điện thoại</div>
                        <div class="text-sm text-gray-600">Sử dụng ứng dụng camera hoặc quét QR</div>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div>
                        <div class="font-semibold text-gray-800">Quét mã QR</div>
                        <div class="text-sm text-gray-600">Đưa camera về phía mã QR để quét</div>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div>
                        <div class="font-semibold text-gray-800">Bắt đầu gọi món</div>
                        <div class="text-sm text-gray-600">Chọn món ăn và thêm vào giỏ hàng</div>
                    </div>
                </div>
            </div>
            
            <!-- Demo Button -->
            <button class="demo-button w-full" onclick="demoOrder()">
                <i class="fas fa-play mr-2"></i>
                Xem demo gọi món
            </button>
            
            <!-- Help Text -->
            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                <div class="flex items-center text-blue-800">
                    <i class="fas fa-info-circle mr-2"></i>
                    <span class="font-semibold">Cần hỗ trợ?</span>
                </div>
                <p class="text-blue-700 text-sm mt-1">
                    Gọi nhân viên hoặc sử dụng nút gọi trên bàn để được hỗ trợ
                </p>
            </div>
            
            <!-- Alternative Options -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <p class="text-gray-600 text-sm mb-4">Hoặc bạn có thể:</p>
                <div class="grid grid-cols-2 gap-3">
                    <button onclick="callStaff()" class="flex items-center justify-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-bell mr-2 text-gray-600"></i>
                        <span class="text-sm text-gray-700">Gọi nhân viên</span>
                    </button>
                    <button onclick="viewMenu()" class="flex items-center justify-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-book mr-2 text-gray-600"></i>
                        <span class="text-sm text-gray-700">Xem menu</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function demoOrder() {
            // Redirect to demo order page with a sample table
            window.location.href = '/customer/order?table=DEMO001';
        }
        
        function callStaff() {
            alert('Tính năng gọi nhân viên sẽ được cập nhật sớm!');
        }
        
        function viewMenu() {
            // You can redirect to a general menu page
            alert('Vui lòng quét mã QR trên bàn để xem menu và gọi món');
        }
        
        // Check if user came from QR scan
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');
        
        if (error) {
            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            errorDiv.textContent = error;
            document.body.appendChild(errorDiv);
            
            // Remove error after 5 seconds
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate steps on load
            const steps = document.querySelectorAll('.step-item');
            steps.forEach((step, index) => {
                step.style.opacity = '0';
                step.style.transform = 'translateX(-20px)';
                
                setTimeout(() => {
                    step.style.transition = 'all 0.5s ease';
                    step.style.opacity = '1';
                    step.style.transform = 'translateX(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
