<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Restaurant Menu">

    <title>Thực đơn - {{ $table->name }}</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/images/icon-192x192.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])

    <style>
        .food-item {
            transition: all 0.2s ease;
        }
        .food-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .cart-badge {
            animation: bounce 0.5s ease;
        }
        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }
        .fixed-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid rgba(229, 231, 235, 0.8);
        }
        .fixed-category-nav {
            position: fixed;
            top: 64px; /* Height of main header */
            left: 0;
            right: 0;
            z-index: 40;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(229, 231, 235, 0.8);
        }
        .category-nav {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        .category-nav::-webkit-scrollbar {
            display: none;
        }
        .main-content {
            margin-top: 128px; /* Header + Category nav height */
        }
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 60;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 100%;
            max-width: 400px;
            max-height: 90vh;
            overflow-y: auto;
        }

        /* Ensure hidden class works properly */
        .hidden {
            display: none !important;
        }

        /* Debug styles */
        .debug-visible {
            border: 2px solid red !important;
            background-color: rgba(255, 0, 0, 0.1) !important;
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Fixed Header -->
    <header class="fixed-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-3">
                <div class="flex items-center">
                    <a href="{{ route('customer.order', $table->code) }}" class="text-indigo-600 hover:text-indigo-800 mr-3">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </a>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900">Thực đơn</h1>
                        <p class="text-sm text-gray-600">{{ $table->name }}</p>
                    </div>
                </div>
                <button onclick="toggleCart()" class="relative p-2 text-indigo-600 hover:text-indigo-800">
                    <i class="fas fa-shopping-cart text-xl"></i>
                    <span id="cartBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center cart-badge" style="display: none;">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Fixed Category Navigation -->
    <div class="fixed-category-nav">
        <div class="category-nav flex overflow-x-auto px-4 py-3 space-x-4">
            @foreach($categories as $category)
                <button onclick="scrollToCategory('category-{{ $category->id }}')"
                        class="flex-shrink-0 px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-full hover:bg-indigo-100 hover:text-indigo-600 transition-colors duration-200">
                    {{ $category->name }}
                </button>
            @endforeach
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 pb-32">
        @foreach($categories as $category)
            <div id="category-{{ $category->id }}" class="mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                    {{ $category->name }}
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @foreach($category->foods as $food)
                        <div class="food-item bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                            <div class="p-4">
                                <div class="flex items-start space-x-4">
                                    @if($food->image_id)
                                        <img class="h-20 w-20 rounded-lg object-cover flex-shrink-0"
                                             src="{{ \App\Helper\helper::getImageGoogleDrive($food->image_id) }}"
                                             alt="{{ $food->name }}"
                                             loading="lazy">
                                    @else
                                        <div class="h-20 w-20 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                                            <i class="fas fa-utensils text-gray-400 text-xl"></i>
                                        </div>
                                    @endif

                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $food->name }}</h3>
                                        <p class="text-sm text-gray-600 mb-2 line-clamp-2">{{ $food->description }}</p>
                                        <p class="text-lg font-bold text-indigo-600">{{ $food->formatted_price }}</p>
                                    </div>
                                </div>

                                <div class="mt-4 flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="decreaseQuantity({{ $food->id }})"
                                                class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors duration-200">
                                            <i class="fas fa-minus text-sm text-gray-600"></i>
                                        </button>
                                        <span id="quantity-{{ $food->id }}" class="w-8 text-center font-medium">0</span>
                                        <button onclick="increaseQuantity({{ $food->id }}, '{{ addslashes($food->name) }}', {{ $food->price }})"
                                                class="w-8 h-8 rounded-full bg-indigo-600 flex items-center justify-center hover:bg-indigo-700 transition-colors duration-200">
                                            <i class="fas fa-plus text-sm text-white"></i>
                                        </button>
                                    </div>

                                    <button onclick="addToCart({{ $food->id }}, '{{ addslashes($food->name) }}', {{ $food->price }})"
                                            class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                                        <i class="fas fa-plus mr-1"></i>
                                        Thêm
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endforeach
    </main>

    <!-- Shopping Cart Overlay -->
    <div id="cartOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" onclick="toggleCart()">
        <div class="fixed bottom-0 left-0 right-0 bg-white rounded-t-xl max-h-[80vh] overflow-hidden" onclick="event.stopPropagation()">
            <!-- Cart Header -->
            <div class="p-4 border-b border-gray-200 bg-white sticky top-0">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Giỏ hàng</h3>
                    <button onclick="toggleCart()" class="text-gray-400 hover:text-gray-600 p-1">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Cart Items -->
            <div id="cartItems" class="p-4 max-h-[50vh] overflow-y-auto">
                <!-- Cart items will be populated here -->
            </div>

            <!-- Cart Footer -->
            <div class="p-4 border-t border-gray-200 bg-gray-50 sticky bottom-0">
                <div class="flex items-center justify-between mb-4">
                    <span class="text-lg font-semibold text-gray-900">Tổng cộng:</span>
                    <span id="cartTotal" class="text-xl font-bold text-indigo-600">0 VNĐ</span>
                </div>
                <button onclick="proceedToCheckout()"
                        class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors duration-200 shadow-lg">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    Đặt món ngay
                </button>
            </div>
        </div>
    </div>

    <!-- Floating Cart Button -->
    <div id="floatingCart" class="fixed bottom-6 right-6 z-40" style="display: none;">
        <button onclick="toggleCart()" class="bg-indigo-600 text-white p-4 rounded-full shadow-xl hover:bg-indigo-700 hover:scale-110 transition-all duration-200 relative">
            <i class="fas fa-shopping-cart text-xl"></i>
            <span id="floatingCartBadge" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg">0</span>
        </button>
    </div>

    <!-- Debug Button (only in development) -->
    @if(config('app.debug'))
    <div class="fixed bottom-6 left-6 z-40">
        <button onclick="debugModals()" class="bg-red-600 text-white p-2 rounded-full shadow-lg hover:bg-red-700 transition-all duration-200">
            <i class="fas fa-bug text-sm"></i>
        </button>
    </div>
    @endif

    <!-- Customer Info Modal -->
    <div id="customerModal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Thông tin khách hàng</h3>
                    <button onclick="closeCustomerModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="customerForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tên khách hàng *</label>
                        <input type="text" id="customerName" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập tên của bạn">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Số điện thoại</label>
                        <input type="tel" id="customerPhone"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="0123 456 789">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" id="customerEmail"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Ghi chú đặc biệt</label>
                        <textarea id="orderNotes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                                  placeholder="Yêu cầu đặc biệt cho đơn hàng..."></textarea>
                    </div>

                    <div class="flex space-x-3 pt-4">
                        <button type="button" onclick="closeCustomerModal()"
                                class="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium">
                            Hủy
                        </button>
                        <button type="submit"
                                class="flex-1 px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 font-medium">
                            Xác nhận đặt món
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="modal-overlay hidden">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
            <p class="text-gray-600 font-medium">Đang xử lý đơn hàng...</p>
            <p class="text-sm text-gray-500 mt-2">Vui lòng đợi trong giây lát</p>
        </div>
    </div>

    <!-- PWA & Cart Scripts -->
    <script>
        // PWA Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('SW registered successfully:', registration);

                    // Listen for updates
                    registration.addEventListener('updatefound', () => {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                // New content available, reload page
                                if (confirm('Có phiên bản mới! Bạn có muốn cập nhật không?')) {
                                    window.location.reload();
                                }
                            }
                        });
                    });
                } catch (error) {
                    console.warn('SW registration failed:', error);
                }
            });
        }

        // Initialize page state
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure all modals are hidden on page load
            document.getElementById('customerModal').classList.add('hidden');
            document.getElementById('loadingOverlay').classList.add('hidden');
            document.getElementById('cartOverlay').classList.add('hidden');

            console.log('Page initialized - all modals hidden');
        });

        // Cart Management
        let cart = JSON.parse(localStorage.getItem('cart_{{ $table->code }}') || '[]');
        let quantities = {};

        // Initialize quantities from cart
        cart.forEach(item => {
            quantities[item.food_id] = item.quantity;
            updateQuantityDisplay(item.food_id);
        });

        updateCartDisplay();

        function increaseQuantity(foodId, foodName, price) {
            quantities[foodId] = (quantities[foodId] || 0) + 1;
            updateQuantityDisplay(foodId);
        }

        function decreaseQuantity(foodId) {
            if (quantities[foodId] > 0) {
                quantities[foodId]--;
                updateQuantityDisplay(foodId);
            }
        }

        function updateQuantityDisplay(foodId) {
            const quantityEl = document.getElementById(`quantity-${foodId}`);
            if (quantityEl) {
                quantityEl.textContent = quantities[foodId] || 0;
            }
        }

        function addToCart(foodId, foodName, price) {
            const quantity = quantities[foodId] || 0;
            if (quantity === 0) {
                quantities[foodId] = 1;
                updateQuantityDisplay(foodId);
            }

            const existingIndex = cart.findIndex(item => item.food_id === foodId);

            if (existingIndex !== -1) {
                cart[existingIndex].quantity = quantities[foodId];
                cart[existingIndex].total_price = cart[existingIndex].quantity * price;
            } else {
                cart.push({
                    food_id: foodId,
                    name: foodName,
                    unit_price: price,
                    quantity: quantities[foodId],
                    total_price: quantities[foodId] * price,
                    notes: ''
                });
            }

            saveCart();
            updateCartDisplay();
            showCartBadgeAnimation();
        }

        function removeFromCart(foodId) {
            cart = cart.filter(item => item.food_id !== foodId);
            quantities[foodId] = 0;
            updateQuantityDisplay(foodId);
            saveCart();
            updateCartDisplay();
        }

        function updateCartItemQuantity(foodId, newQuantity) {
            const item = cart.find(item => item.food_id === foodId);
            if (item) {
                if (newQuantity <= 0) {
                    removeFromCart(foodId);
                } else {
                    item.quantity = newQuantity;
                    item.total_price = item.quantity * item.unit_price;
                    quantities[foodId] = newQuantity;
                    updateQuantityDisplay(foodId);
                    saveCart();
                    updateCartDisplay();
                }
            }
        }

        function saveCart() {
            localStorage.setItem('cart_{{ $table->code }}', JSON.stringify(cart));
        }

        function updateCartDisplay() {
            const cartBadge = document.getElementById('cartBadge');
            const floatingCartBadge = document.getElementById('floatingCartBadge');
            const floatingCart = document.getElementById('floatingCart');
            const cartItems = document.getElementById('cartItems');
            const cartTotal = document.getElementById('cartTotal');

            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            const totalPrice = cart.reduce((sum, item) => sum + item.total_price, 0);

            // Update badges
            if (totalItems > 0) {
                cartBadge.style.display = 'flex';
                cartBadge.textContent = totalItems;
                floatingCartBadge.textContent = totalItems;
                floatingCart.style.display = 'block';
            } else {
                cartBadge.style.display = 'none';
                floatingCart.style.display = 'none';
            }

            // Update cart items
            cartItems.innerHTML = '';
            if (cart.length === 0) {
                cartItems.innerHTML = '<p class="text-gray-500 text-center py-8">Giỏ hàng trống</p>';
            } else {
                cart.forEach(item => {
                    const itemEl = document.createElement('div');
                    itemEl.className = 'flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0';
                    itemEl.innerHTML = `
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">${item.name}</h4>
                            <p class="text-sm text-gray-600">${formatPrice(item.unit_price)}</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center space-x-2">
                                <button onclick="updateCartItemQuantity(${item.food_id}, ${item.quantity - 1})"
                                        class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="fas fa-minus text-xs"></i>
                                </button>
                                <span class="w-8 text-center">${item.quantity}</span>
                                <button onclick="updateCartItemQuantity(${item.food_id}, ${item.quantity + 1})"
                                        class="w-6 h-6 rounded-full bg-indigo-600 text-white flex items-center justify-center">
                                    <i class="fas fa-plus text-xs"></i>
                                </button>
                            </div>
                            <button onclick="removeFromCart(${item.food_id})"
                                    class="text-red-500 hover:text-red-700">
                                <i class="fas fa-trash text-sm"></i>
                            </button>
                        </div>
                    `;
                    cartItems.appendChild(itemEl);
                });
            }

            // Update total
            cartTotal.textContent = formatPrice(totalPrice);
        }

        function showCartBadgeAnimation() {
            const badge = document.getElementById('cartBadge');
            badge.classList.remove('cart-badge');
            setTimeout(() => badge.classList.add('cart-badge'), 10);
        }

        function toggleCart() {
            const overlay = document.getElementById('cartOverlay');
            overlay.classList.toggle('hidden');
            console.log('Cart toggled:', overlay.classList.contains('hidden') ? 'closed' : 'opened');
        }

        // Reset all modals to hidden state
        function resetModals() {
            document.getElementById('customerModal').classList.add('hidden');
            document.getElementById('loadingOverlay').classList.add('hidden');
            document.getElementById('cartOverlay').classList.add('hidden');
            console.log('All modals reset to hidden state');
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC key closes all modals
            if (e.key === 'Escape') {
                resetModals();
            }
        });

        function scrollToCategory(categoryId) {
            const element = document.getElementById(categoryId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        function proceedToCheckout() {
            if (cart.length === 0) {
                alert('Giỏ hàng trống!');
                return;
            }

            console.log('Proceeding to checkout...');
            toggleCart();

            // Small delay to ensure cart is closed first
            setTimeout(() => {
                document.getElementById('customerModal').classList.remove('hidden');
                console.log('Customer modal opened');
            }, 100);
        }

        function closeCustomerModal() {
            document.getElementById('customerModal').classList.add('hidden');
            document.getElementById('loadingOverlay').classList.add('hidden');
            console.log('Customer modal closed');
        }

        // Ensure modals are closed when clicking outside
        document.addEventListener('click', function(e) {
            const customerModal = document.getElementById('customerModal');
            const cartOverlay = document.getElementById('cartOverlay');
            const loadingOverlay = document.getElementById('loadingOverlay');

            // Close customer modal if clicking outside
            if (e.target === customerModal) {
                closeCustomerModal();
            }

            // Close cart if clicking outside
            if (e.target === cartOverlay) {
                toggleCart();
            }
        });

        function formatPrice(price) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(price);
        }

        // Customer form submission
        document.getElementById('customerForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const customerName = document.getElementById('customerName').value;
            const customerPhone = document.getElementById('customerPhone').value;
            const customerEmail = document.getElementById('customerEmail').value;
            const orderNotes = document.getElementById('orderNotes').value;

            if (!customerName.trim()) {
                alert('Vui lòng nhập tên khách hàng!');
                return;
            }

            closeCustomerModal();
            document.getElementById('loadingOverlay').classList.remove('hidden');

            try {
                // Check if online
                if (!navigator.onLine) {
                    throw new Error('No internet connection');
                }

                const response = await fetch(`/api/customer/order/{{ $table->code }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        customer_name: customerName,
                        customer_phone: customerPhone,
                        customer_email: customerEmail,
                        notes: orderNotes,
                        items: cart.map(item => ({
                            food_id: item.food_id,
                            quantity: item.quantity,
                            notes: item.notes
                        }))
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success) {
                    // Clear cart
                    cart = [];
                    quantities = {};
                    saveCart();

                    // Reset quantity displays
                    Object.keys(quantities).forEach(foodId => updateQuantityDisplay(foodId));
                    updateCartDisplay();

                    alert('Đặt món thành công! Mã đơn hàng: ' + result.order.order_number);

                    // Redirect back to table page
                    window.location.href = '{{ route("customer.order", $table->code) }}';
                } else {
                    throw new Error(result.message || 'Unknown error occurred');
                }
            } catch (error) {
                console.error('Order submission error:', error);

                // Try to save order offline
                try {
                    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
                        await saveOrderOffline({
                            table_code: '{{ $table->code }}',
                            customer_name: customerName,
                            customer_phone: customerPhone,
                            customer_email: customerEmail,
                            notes: orderNotes,
                            items: cart.map(item => ({
                                food_id: item.food_id,
                                quantity: item.quantity,
                                notes: item.notes
                            })),
                            csrf_token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        });

                        alert('Không có kết nối internet. Đơn hàng đã được lưu và sẽ được gửi khi có kết nối.');

                        // Clear cart since it's saved offline
                        cart = [];
                        quantities = {};
                        saveCart();
                        Object.keys(quantities).forEach(foodId => updateQuantityDisplay(foodId));
                        updateCartDisplay();

                    } else {
                        alert('Có lỗi xảy ra khi đặt món: ' + error.message + '\nVui lòng thử lại!');
                    }
                } catch (offlineError) {
                    console.error('Offline save failed:', offlineError);
                    alert('Có lỗi xảy ra khi đặt món: ' + error.message + '\nVui lòng kiểm tra kết nối và thử lại!');
                }
            } finally {
                document.getElementById('loadingOverlay').classList.add('hidden');
            }
        });

        // Offline order storage
        async function saveOrderOffline(orderData) {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open('RestaurantDB', 1);

                request.onerror = () => reject(request.error);

                request.onupgradeneeded = () => {
                    const db = request.result;
                    if (!db.objectStoreNames.contains('orders')) {
                        db.createObjectStore('orders', { keyPath: 'id', autoIncrement: true });
                    }
                };

                request.onsuccess = () => {
                    const db = request.result;
                    const transaction = db.transaction(['orders'], 'readwrite');
                    const store = transaction.objectStore('orders');

                    orderData.timestamp = Date.now();
                    const addRequest = store.add(orderData);

                    addRequest.onsuccess = () => {
                        // Register background sync
                        navigator.serviceWorker.ready.then(registration => {
                            return registration.sync.register('background-sync-order');
                        });
                        resolve();
                    };

                    addRequest.onerror = () => reject(addRequest.error);
                };
            });
        }

        // Auto-save cart periodically
        setInterval(saveCart, 5000);

        // Debug function for modal states
        function debugModals() {
            const customerModal = document.getElementById('customerModal');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const cartOverlay = document.getElementById('cartOverlay');

            const states = {
                customerModal: customerModal.classList.contains('hidden') ? 'hidden' : 'visible',
                loadingOverlay: loadingOverlay.classList.contains('hidden') ? 'hidden' : 'visible',
                cartOverlay: cartOverlay.classList.contains('hidden') ? 'hidden' : 'visible',
                cartItems: cart.length,
                pageLoaded: document.readyState
            };

            console.log('Modal Debug States:', states);
            alert('Modal States:\n' + JSON.stringify(states, null, 2));

            // Force reset all modals
            resetModals();
        }

        // Additional safety check on window load
        window.addEventListener('load', function() {
            console.log('Window fully loaded, ensuring modals are hidden');
            resetModals();
        });
    </script>
</body>
</html>
