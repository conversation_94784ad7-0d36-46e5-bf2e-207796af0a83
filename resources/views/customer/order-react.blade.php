<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>G<PERSON><PERSON> món</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @vite(['resources/css/app.css', 'resources/js/mobile-ordering.jsx'])
    <style>
        body {
            touch-action: pan-x pan-y; /* Cho phép cuộn ngang và dọc, nhưng ngăn pinch-to-zoom */
        }
    </style>
</head>
<body>
    <!-- React App Container -->
    <div id="mobile-ordering-root">
        <div style="display: flex; justify-content: center; align-items: center; height: 100vh;">
            <div style="text-align: center;">
                <div style="font-size: 24px; margin-bottom: 16px;">🍽️</div>
                <div><PERSON><PERSON> tải giao diện gọi món...</div>
            </div>
        </div>
    </div>

    <!-- Pass data to React -->
    <script>
        @if($activeOrder)
            @php
                $orderData = [
                    'id' => $activeOrder->id,
                    'order_number' => $activeOrder->order_number,
                    'status' => $activeOrder->status,
                    'total_amount' => $activeOrder->total_amount,
                    'formatted_total_amount' => number_format($activeOrder->total_amount) . 'đ',
                    'items' => $activeOrder->orderItems->map(function ($item) {
                        return [
                            'id' => $item->food->id,
                            'name' => $item->food->name,
                            'quantity' => $item->quantity,
                            'categories' => $item->food->categories->map(fn($cate) => $cate->name),
                            'price' => $item->total_price,
                            'image' => $item->food->image_id ? \App\Helper\helper::getImageGoogleDrive($item->food->image_id) : null
                        ];
                    })->toArray()
                ];
            @endphp
            window.activeOrderData = @json($orderData);
        @else
            window.activeOrderData = null;
        @endif

        window.categories = @json($categories);

    </script>
</body>
</html>
