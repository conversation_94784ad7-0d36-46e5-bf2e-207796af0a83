<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <x-pwa-head app="order-manager" title="<PERSON><PERSON>ản lý đơn hàng" path="{{$device}}" />
    <meta name="mobile-web-app-title" content="OrderManager">
    <base href="{{ url('/') }}/">
    <link rel="canonical" href="{{ url('/manager/order/'. $device) }}">
    @vite(['resources/css/app.css', 'resources/js/manager-order.jsx'])
    <style>
        .title-dashboard { display: none; }
        body {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -webkit-tap-highlight-color: transparent;
        }
    </style>
</head>
<body>
<div id="manager-ordering-root"></div>
<x-pwa-scripts app="order-manager" />
<script>
    console.log('Manifest test:');
    fetch('/manifest/order-manager')
        .then(r => r.json())
        .then(m => console.log(m))
        .catch(e => console.error(e));
</script>
</body>
</html>
