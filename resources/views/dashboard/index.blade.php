<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-4 rounded-xl shadow-lg">
                    <i class="fas fa-tachometer-alt text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="font-bold text-3xl text-gray-800 leading-tight">
                        Dashboard Nhà Hàng
                    </h1>
                    <p class="text-gray-600 mt-1">Theo dõi đơn hàng và món ăn real-time</p>
                </div>
            </div>
            <div class="flex items-center space-x-6">
                <!-- Notification Bell -->
                <div id="notificationBell" class="relative cursor-pointer" onclick="openNotificationPopup()">
                    <div class="bg-yellow-100 p-3 rounded-full hover:bg-yellow-200 transition-colors">
                        <i class="fas fa-bell text-yellow-600 text-xl"></i>
                    </div>
                    <div id="notificationBadge" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-semibold hidden">
                        0
                    </div>
                </div>
                <!-- Connection Status -->
                <div id="connectionStatus" class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-green-400 rounded-full pulse-animation"></div>
                    <span class="text-gray-600 font-medium">Online</span>
                </div>
                <!-- Last Update -->
                <div class="text-right">
                    <div class="text-sm text-gray-500">Cập nhật lúc</div>
                    <div id="lastUpdate" class="text-lg font-semibold text-gray-800">--:--</div>
                </div>
            </div>
        </div>
    </x-slot>

    <!-- Main Dashboard Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        @include('dashboard.partials.stats-cards')
    </div>

    <!-- Detailed Lists -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        @include('dashboard.partials.pending-payments')
        @include('dashboard.partials.pending-dishes')
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
        @include('dashboard.partials.preparing-dishes')
        @include('dashboard.partials.ready-dishes')
    </div>

    @include('dashboard.partials.notification-toast')
    @include('dashboard.partials.audio-notification')

    @push('styles')
        <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
    @endpush

    @push('scripts')
        <script src="{{ asset('js/dashboard.js') }}"></script>
        <script>
            // Global functions for popup
            function closeNotificationPopup() {
                document.getElementById('notificationPopup').classList.add('hidden');
                document.body.style.overflow = 'auto';
            }

            function clearAllNotifications() {
                if (window.restaurantDashboard) {
                    window.restaurantDashboard.clearAllNotifications();
                }
            }

            // Close popup on Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeNotificationPopup();
                }
            });
        </script>
    @endpush
</x-app-layout>

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
@endpush

@push('scripts')
    <script src="{{ asset('js/dashboard.js?v=2.1.9') }}"></script>
@endpush
