@extends('layouts.admin')

@section('title', 'Chi tiết món ăn')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.foods.index') }}" class="text-gray-500 hover:text-gray-700">Món ăn</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Chi tiết</span>
        </div>
    </li>
@endsection

@section('content-header-actions')
    <div class="flex space-x-3">
        <a href="{{ route('admin.foods.edit', $food) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            Chỉnh sửa
        </a>
        <form action="{{ route('admin.foods.toggle-status', $food) }}" method="POST" class="inline">
            @csrf
            @method('PATCH')
            <button type="submit"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white {{ $food->status == 0 ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' }} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                <i class="fas fa-{{ $food->status == 0 ? 'toggle-off' : 'toggle-on' }} mr-2"></i>
                {{ $food->status == 0 ? 'Vô hiệu hóa' : 'Kích hoạt' }}
            </button>
        </form>
    </div>
@endsection

@section('content')
    <div class="max-w-7xl mx-auto space-y-6">
        <!-- Main Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="md:flex">
                <!-- Image Section -->
                <div class="md:w-1/3 lg:w-1/4">
                    @if($food->image_id)
                        <img src="{{ \App\Helper\helper::getImageGoogleDrive($food->image_id) }}"
                             alt="{{ $food->name }}"
                             class="w-full h-64 md:h-full object-cover">
                    @else
                        <div class="w-full h-64 md:h-full bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-utensils text-gray-400 text-6xl"></i>
                        </div>
                    @endif
                </div>

                <!-- Content Section -->
                <div class="md:w-2/3 lg:w-3/4 p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $food->name }}</h1>
                            @if($food->name_english)
                                <h2 class="text-xl font-semibold text-blue-600 mb-3">{{ $food->name_english }}</h2>
                            @endif
                            <div class="flex items-center space-x-4 mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $food->status == 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                <span class="w-2 h-2 mr-2 rounded-full {{ $food->status == 0 ? 'bg-green-400' : 'bg-red-400' }}"></span>
                                {{ $food->status_text }}
                            </span>
                                @if($food->category)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-tag mr-2"></i>
                                    {{ $food->category->name }}
                                </span>
                                @endif
                            </div>

                            <div class="text-4xl font-bold text-indigo-600 mb-4">
                                {{ $food->formatted_price }}
                            </div>

                            <div class="prose max-w-none">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Mô tả</h3>
                                <p class="text-gray-700 leading-relaxed">{{ $food->description }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Details Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin cơ bản</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">ID:</span>
                        <span class="text-sm text-gray-900">#{{ $food->id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Tên món ăn (Tiếng Việt):</span>
                        <span class="text-sm text-gray-900 font-medium">{{ $food->name }}</span>
                    </div>
                    @if($food->name_english)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Tên món ăn (Tiếng Anh):</span>
                        <span class="text-sm text-blue-600 font-medium">{{ $food->name_english }}</span>
                    </div>
                    @endif
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Giá:</span>
                        <span class="text-sm text-gray-900 font-semibold">{{ $food->formatted_price }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Danh mục:</span>
                        @foreach($food->categories as $category)
                            <span class="text-sm text-gray-900">
                                {{ $category->name }}
                            </span>
                        @endforeach
                    </div>

                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Trạng thái:</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $food->status == 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $food->status_text }}
                    </span>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thời gian</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Ngày tạo:</span>
                        <span class="text-sm text-gray-900">{{ $food->created_at->format('d/m/Y H:i:s') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Cập nhật lần cuối:</span>
                        <span class="text-sm text-gray-900">{{ $food->updated_at->format('d/m/Y H:i:s') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Thời gian tồn tại:</span>
                        <span class="text-sm text-gray-900">{{ $food->created_at->diffForHumans() }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Locks Section -->
        @if($food->dateLocks->count() > 0)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Ngày bị khóa</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    {{ $food->dateLocks->count() }} ngày
                </span>
                    </div>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                        @foreach($food->dateLocks as $dateLock)
                            @php
                                $today = today();
                                $lockDate = $dateLock->date;
                            @endphp
                            <div class="flex flex-col items-center p-3 rounded-lg border {{ $lockDate->eq($today) ? 'border-red-300 bg-red-50' : ($lockDate->lt($today) ? 'border-gray-300 bg-gray-50' : 'border-yellow-300 bg-yellow-50') }}">
                                <div class="text-xs font-medium {{ $lockDate->eq($today) ? 'text-red-600' : ($lockDate->lt($today) ? 'text-gray-600' : 'text-yellow-600') }}">
                                    {{ $lockDate->format('d/m') }}
                                </div>
                                <div class="text-xs {{ $lockDate->eq($today) ? 'text-red-500' : ($lockDate->lt($today) ? 'text-gray-500' : 'text-yellow-500') }}">
                                    {{ $lockDate->locale('vi')->translatedFormat('D') }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4">
                <div class="flex flex-wrap gap-3">
                    <a href="{{ route('admin.foods.edit', $food) }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i>
                        Chỉnh sửa món ăn
                    </a>

                    <form action="{{ route('admin.foods.toggle-status', $food) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white {{ $food->status == 0 ? 'bg-orange-600 hover:bg-orange-700' : 'bg-green-600 hover:bg-green-700' }} transition-colors duration-200">
                            <i class="fas fa-{{ $food->status == 0 ? 'pause' : 'play' }} mr-2"></i>
                            {{ $food->status == 0 ? 'Tạm dừng' : 'Kích hoạt' }}
                        </button>
                    </form>

                    <a href="{{ route('admin.foods.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Quay lại danh sách
                    </a>

                    <form action="{{ route('admin.foods.destroy', $food) }}" method="POST" class="inline"
                          onsubmit="return confirm('Bạn có chắc chắn muốn xóa món ăn này? Hành động này không thể hoàn tác!')">
                        @csrf
                        @method('DELETE')
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>
                            Xóa món ăn
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
