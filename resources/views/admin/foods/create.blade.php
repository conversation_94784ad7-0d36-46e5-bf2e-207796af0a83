@extends('layouts.admin')

@section('title', 'Thêm món ăn mới')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.foods.index') }}" class="text-gray-500 hover:text-gray-700">Món ăn</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Thêm mới</span>
        </div>
    </li>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <form action="{{ route('admin.foods.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf

            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin cơ bản</h3>
                    <p class="mt-1 text-sm text-gray-600">Nhập thông tin chi tiết về món ăn</p>
                </div>

                <div class="px-6 py-4 space-y-6">
                    <!-- Food Name -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                                Tên món ăn (Tiếng Việt) <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   name="name"
                                   id="name"
                                   value="{{ old('name') }}"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('name') border-red-300 @enderror"
                                   placeholder="Nhập tên món ăn bằng tiếng Việt..."
                                   required>
                            @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="name_english" class="block text-sm font-medium text-gray-700 mb-1">
                                Tên món ăn (Tiếng Anh)
                            </label>
                            <input type="text"
                                   name="name_english"
                                   id="name_english"
                                   value="{{ old('name_english') }}"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('name_english') border-red-300 @enderror"
                                   placeholder="Enter English name...">
                            @error('name_english')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                <i class="fas fa-info-circle mr-1"></i>
                                Tùy chọn - để trống nếu không có tên tiếng Anh
                            </p>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                            Mô tả <span class="text-red-500">*</span>
                        </label>
                        <textarea name="description"
                                  id="description"
                                  rows="4"
                                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('description') border-red-300 @enderror"
                                  placeholder="Mô tả về món ăn..."
                                  required>{{ old('description') }}</textarea>
                        @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Price -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-1">
                                Giá (VNĐ) <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="number"
                                       name="price"
                                       id="price"
                                       value="{{ old('price') }}"
                                       min="0"
                                       step="1"
                                       class="block w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('price') border-red-300 @enderror"
                                       placeholder="0"
                                       required>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">VNĐ</span>
                                </div>
                            </div>
                            @error('price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                                Trạng thái <span class="text-red-500">*</span>
                            </label>
                            <select name="status"
                                    id="status"
                                    class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('status') border-red-300 @enderror"
                                    required>
                                <option value="0" {{ old('status', '0') == '0' ? 'selected' : '' }}>Hoạt động</option>
                                <option value="1" {{ old('status') == '1' ? 'selected' : '' }}>Không hoạt động</option>
                            </select>
                            @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Categories (Multiple Selection) -->

                    <div x-data="{
                            selectedCategories: @js(old('categories', [])),
                            categories: @js($categories->map(function($cat) {
                                return [
                                    'id' => $cat->id,
                                    'name' => $cat->name,
                                    'status' => $cat->status
                                ];
                            })),
                            isOpen: false,
                            toggleCategory(categoryId) {
                                if (this.selectedCategories.includes(categoryId)) {
                                    this.selectedCategories = this.selectedCategories.filter(id => id !== categoryId);
                                } else {
                                    this.selectedCategories.push(categoryId);
                                }
                            },
                            isSelected(categoryId) {
                                return this.selectedCategories.includes(categoryId);
                            },
                            getCategoryName(categoryId) {
                                const category = this.categories.find(cat => cat.id === categoryId);
                                return category ? category.name : '';
                            },
                            get selectedCount() {
                                return this.selectedCategories.length;
                            }
                        }">
                        <!-- Hidden inputs for form submission -->
                        <template x-for="categoryId in selectedCategories" :key="categoryId">
                            <input type="hidden" name="categories[]" :value="categoryId">
                        </template>

                        <label class="block text-sm font-medium text-gray-700 mb-3">
                            Danh mục <span class="text-red-500">*</span>
                        </label>

                        <!-- Dropdown trigger button -->
                        <div class="relative">
                            <button type="button"
                                    @click="isOpen = !isOpen"
                                    class="w-full px-3 py-2 text-left border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('categories') border-red-300 @enderror min-h-[2.5rem] flex items-center justify-between">
                                <div class="flex-1">
                <span x-show="selectedCategories.length === 0" class="text-gray-400">
                    Chọn danh mục...
                </span>
                                    <span x-show="selectedCategories.length > 0" class="text-gray-900" x-text="selectedCount + ' danh mục đã chọn'">
                </span>
                                </div>
                                <i class="fas fa-chevron-down text-gray-400 transition-transform duration-200"
                                   :class="isOpen ? 'rotate-180' : ''"></i>
                            </button>

                            <!-- Dropdown content -->
                            <div x-show="isOpen"
                                 @click.away="isOpen = false"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-hidden"
                                 x-cloak>

                                <div class="max-h-48 overflow-y-auto bg-gray-50">
                                    <div class="p-2">
                                        <div class="space-y-1">
                                            <template x-for="category in categories" :key="category.id">
                                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded-md cursor-pointer transition-colors duration-150"
                                                       :class="category.status === 0 ? 'bg-white' : 'bg-gray-100 opacity-75'"
                                                       @click="toggleCategory(category.id)">
                                                    <input type="checkbox"
                                                           :checked="isSelected(category.id)"
                                                           @change="toggleCategory(category.id)"
                                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded transition-colors">
                                                    <div class="flex-1 flex items-center justify-between">
                                    <span class="text-sm"
                                          :class="category.status === 0 ? 'text-gray-900 font-medium' : 'text-gray-500'"
                                          x-text="category.name">
                                    </span>
                                                        <span class="text-xs px-2 py-1 rounded-full"
                                                              :class="category.status !== 0 ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'"
                                                              x-text="category.status !== 0 ? 'Hoạt động' : 'Không hoạt động'">
                                    </span>
                                                    </div>
                                                </label>
                                            </template>

                                            <div x-show="categories.length === 0" class="text-center py-4 text-gray-500">
                                                <i class="fas fa-folder-open text-2xl mb-2"></i>
                                                <p class="text-sm">Chưa có danh mục nào</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Selected categories display -->
                        <div x-show="selectedCategories.length > 0" class="mt-3">
                            <div class="flex flex-wrap gap-2">
                                <template x-for="categoryId in selectedCategories" :key="categoryId">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                    <span x-text="getCategoryName(categoryId)"></span>
                    <button type="button"
                            @click="toggleCategory(categoryId)"
                            class="ml-1 h-4 w-4 rounded-full inline-flex items-center justify-center text-indigo-400 hover:bg-indigo-200 hover:text-indigo-500 focus:outline-none">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </span>
                                </template>
                            </div>
                        </div>

                        @error('categories')
                        <p class="mt-1 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                        </p>
                        @enderror
                        @error('categories.*')
                        <p class="mt-1 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                        </p>
                        @enderror

                        <div class="mt-2 p-2 bg-blue-50 rounded border border-blue-200">
                            <p class="text-sm text-blue-700">
                                <i class="fas fa-info-circle mr-1"></i>
                                Chọn ít nhất một danh mục cho món ăn. Click vào danh mục để chọn/bỏ chọn.
                            </p>
                        </div>
                    </div>


                </div>
            </div>

            <!-- Image Upload -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Hình ảnh</h3>
                    <p class="mt-1 text-sm text-gray-600">Tải lên hình ảnh cho món ăn</p>
                </div>

                <div class="px-6 py-4">
                    <div x-data="{
                    imagePreview: null,
                    handleFileSelect(event) {
                        const file = event.target.files[0];
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                this.imagePreview = e.target.result;
                            };
                            reader.readAsDataURL(file);
                        }
                    }
                }">
                        <div class="flex items-center space-x-6">
                            <div class="shrink-0">
                                <div x-show="!imagePreview" class="h-32 w-32 rounded-lg bg-gray-100 flex items-center justify-center border-2 border-dashed border-gray-300">
                                    <i class="fas fa-image text-gray-400 text-2xl"></i>
                                </div>
                                <img x-show="imagePreview"
                                     :src="imagePreview"
                                     class="h-32 w-32 object-cover rounded-lg shadow-sm"
                                     x-cloak>
                            </div>
                            <div class="flex-1">
                                <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Chọn hình ảnh
                                </label>
                                <input type="file"
                                       name="image"
                                       id="image"
                                       accept="image/*"
                                       @change="handleFileSelect($event)"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                <p class="mt-1 text-sm text-gray-500">PNG, JPG, GIF tối đa 2MB</p>
                                @error('image')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="{{ route('admin.foods.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Hủy
                </a>
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Lưu món ăn
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Format price input
            const priceInput = document.getElementById('price');
            priceInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                e.target.value = value;
            });

            // Category selection validation
            const categoryCheckboxes = document.querySelectorAll('input[name="categories[]"]');
            const form = document.querySelector('form');

            form.addEventListener('submit', function(e) {
                const checkedCategories = document.querySelectorAll('input[name="categories[]"]:checked');
                if (checkedCategories.length === 0) {
                    e.preventDefault();
                    alert('Vui lòng chọn ít nhất một danh mục cho món ăn.');
                    return false;
                }
            });
        });
    </script>
@endpush
