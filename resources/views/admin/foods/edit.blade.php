@extends('layouts.admin')

@section('title', 'Chỉnh sửa món ăn')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.foods.index') }}" class="text-gray-500 hover:text-gray-700">Món ăn</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Chỉnh sửa</span>
        </div>
    </li>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <form action="{{ route('admin.foods.update', $food) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin cơ bản</h3>
                    <p class="mt-1 text-sm text-gray-600">Chỉnh sửa thông tin chi tiết về món ăn</p>
                </div>

                <div class="px-6 py-4 space-y-6">
                    <!-- Food Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                            Tên món ăn <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               name="name"
                               id="name"
                               value="{{ old('name', $food->name) }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('name') border-red-300 @enderror"
                               placeholder="Nhập tên món ăn..."
                               required>
                        @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                            Mô tả <span class="text-red-500">*</span>
                        </label>
                        <textarea name="description"
                                  id="description"
                                  rows="4"
                                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('description') border-red-300 @enderror"
                                  placeholder="Mô tả về món ăn..."
                                  required>{{ old('description', $food->description) }}</textarea>
                        @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Price -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-1">
                                Giá (VNĐ) <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="number"
                                       name="price"
                                       id="price"
                                       value="{{ old('price', $food->price) }}"
                                       min="0"
                                       step="1"
                                       class="block w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('price') border-red-300 @enderror"
                                       placeholder="0"
                                       required>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">VNĐ</span>
                                </div>
                            </div>
                            @error('price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                                Trạng thái <span class="text-red-500">*</span>
                            </label>
                            <select name="status"
                                    id="status"
                                    class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('status') border-red-300 @enderror"
                                    required>
                                <option value="0" {{ old('status', $food->status) == '0' ? 'selected' : '' }}>Hoạt động</option>
                                <option value="1" {{ old('status', $food->status) == '1' ? 'selected' : '' }}>Không hoạt động</option>
                            </select>
                            @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Categories (Multiple Selection) -->
                    <div x-data="{
                            selectedCategories: @js(old('categories', isset($food) ? $food->categories->pluck('id')->toArray() : [])),
                            allCategories: @js($categories->map(function($cat) {
                                return [
                                    'id' => $cat->id,
                                    'name' => $cat->name,
                                    'status' => $cat->status
                                ];
                            })),
                            isOpen: false,
                            searchTerm: '',
                            get filteredCategories() {
                                return this.allCategories.filter(cat =>
                                    cat.name.toLowerCase().includes(this.searchTerm.toLowerCase())
                                );
                            },
                            get selectedCategoryNames() {
                                return this.allCategories
                                    .filter(cat => this.selectedCategories.includes(cat.id))
                                    .map(cat => cat.name);
                            },
                            toggleCategory(categoryId) {
                                if (this.selectedCategories.includes(categoryId)) {
                                    this.selectedCategories = this.selectedCategories.filter(id => id !== categoryId);
                                } else {
                                    this.selectedCategories.push(categoryId);
                                }
                            },
                            isSelected(categoryId) {
                                return this.selectedCategories.includes(categoryId);
                            }
                        }">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Danh mục <span class="text-red-500">*</span>
                        </label>

                        <!-- Hidden inputs for form submission -->
                        <template x-for="categoryId in selectedCategories" :key="categoryId">
                            <input type="hidden" name="categories[]" :value="categoryId">
                        </template>

                        <!-- Dropdown trigger -->
                        <div class="relative">
                            <button type="button"
                                    @click="isOpen = !isOpen"
                                    class="w-full px-3 py-2 text-left border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('categories') border-red-300 @enderror"
                                    :class="selectedCategories.length === 0 ? 'text-gray-400' : 'text-gray-900'">
                                <span x-show="selectedCategories.length === 0">Chọn danh mục...</span>
                                <span x-show="selectedCategories.length > 0" x-text="selectedCategories.length + ' danh mục đã chọn'"></span>
                                <i class="fas fa-chevron-down float-right mt-1"></i>
                            </button>

                            <!-- Dropdown content -->
                            <div x-show="isOpen"
                                 @click.away="isOpen = false"
                                 class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden"
                                 x-cloak>

                                <!-- Search box -->
                                <div class="p-2 border-b border-gray-200">
                                    <input type="text"
                                           x-model="searchTerm"
                                           placeholder="Tìm kiếm danh mục..."
                                           class="w-full px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                </div>

                                <!-- Category list -->
                                <div class="max-h-48 overflow-y-auto">
                                    <template x-for="category in filteredCategories" :key="category.id">
                                        <label class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
                                               :class="category.status === 0 ? '' : 'opacity-60'">
                                            <input type="checkbox"
                                                   :checked="isSelected(category.id)"
                                                   @change="toggleCategory(category.id)"
                                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mr-3">
                                            <span class="text-sm" x-text="category.name"></span>
                                            <span x-show="category.status === 0" class="text-xs text-gray-500 ml-auto">(Không hoạt động)</span>
                                        </label>
                                    </template>

                                    <!-- No results -->
                                    <div x-show="filteredCategories.length === 0" class="px-3 py-2 text-sm text-gray-500">
                                        Không tìm thấy danh mục nào
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Selected categories display -->
                        <div x-show="selectedCategories.length > 0" class="mt-2">
                            <div class="flex flex-wrap gap-1">
                                <template x-for="categoryName in selectedCategoryNames" :key="categoryName">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                    <span x-text="categoryName"></span>
                </span>
                                </template>
                            </div>
                        </div>

                        @error('categories')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        @error('categories.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Chọn ít nhất một danh mục cho món ăn</p>
                    </div>
                </div>
            </div>

            <!-- Image Upload -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Hình ảnh</h3>
                    <p class="mt-1 text-sm text-gray-600">Cập nhật hình ảnh cho món ăn</p>
                </div>

                <div class="px-6 py-4">
                    <div x-data="{
                    imagePreview: null,
                    currentImage: '{{ $food->image_id ? \App\Helper\helper::getImageGoogleDrive($food->image_id) : null }}',
                    handleFileSelect(event) {
                        const file = event.target.files[0];
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                this.imagePreview = e.target.result;
                            };
                            reader.readAsDataURL(file);
                        }
                    }
                }" x-init="imagePreview = currentImage">
                        <div class="flex items-center space-x-6">
                            <div class="shrink-0">
                                <div x-show="!imagePreview" class="h-32 w-32 rounded-lg bg-gray-100 flex items-center justify-center border-2 border-dashed border-gray-300">
                                    <i class="fas fa-image text-gray-400 text-2xl"></i>
                                </div>
                                <img x-show="imagePreview"
                                     :src="imagePreview"
                                     class="h-32 w-32 object-cover rounded-lg shadow-sm"
                                     x-cloak>
                            </div>
                            <div class="flex-1">
                                <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Chọn hình ảnh mới (để trống nếu không thay đổi)
                                </label>
                                <input type="file"
                                       name="image"
                                       id="image"
                                       accept="image/*"
                                       @change="handleFileSelect($event)"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                <p class="mt-1 text-sm text-gray-500">PNG, JPG, GIF tối đa 2MB</p>
                                @error('image')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Information Display -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin hiện tại</h3>
                    <p class="mt-1 text-sm text-gray-600">Thông tin chi tiết về món ăn này</p>
                </div>

                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm font-medium text-gray-500">ID:</span>
                                <span class="ml-2 text-sm text-gray-900">#{{ $food->id }}</span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Ngày tạo:</span>
                                <span class="ml-2 text-sm text-gray-900">{{ $food->created_at->format('d/m/Y H:i') }}</span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Cập nhật lần cuối:</span>
                                <span class="ml-2 text-sm text-gray-900">{{ $food->updated_at->format('d/m/Y H:i') }}</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Trạng thái hiện tại:</span>
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $food->status == 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $food->status_text }}
                            </span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Danh mục hiện tại:</span>
                                <div class="ml-2 mt-1 flex flex-wrap gap-1">
                                    @forelse($food->categories as $category)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ $category->name }}
                                        </span>
                                    @empty
                                        <span class="text-sm text-gray-500">Chưa có danh mục</span>
                                    @endforelse
                                </div>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Giá hiện tại:</span>
                                <span class="ml-2 text-sm font-semibold text-gray-900">{{ $food->formatted_price }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="{{ route('admin.foods.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Hủy
                </a>
                <a href="{{ route('admin.foods.show', $food) }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-eye mr-2"></i>
                    Xem chi tiết
                </a>
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Cập nhật món ăn
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Format price input
            const priceInput = document.getElementById('price');
            priceInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                e.target.value = value;
            });

            // Category selection validation
            const categoryCheckboxes = document.querySelectorAll('input[name="categories[]"]');
            const form = document.querySelector('form');

            form.addEventListener('submit', function(e) {
                const checkedCategories = document.querySelectorAll('input[name="categories[]"]:checked');
                if (checkedCategories.length === 0) {
                    e.preventDefault();
                    alert('Vui lòng chọn ít nhất một danh mục cho món ăn.');
                    return false;
                }
            });
        });
    </script>
@endpush
