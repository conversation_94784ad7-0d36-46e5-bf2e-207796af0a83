@extends('layouts.admin')

@section('title', 'Thêm danh mục mới')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.food-categories.index') }}" class="text-gray-500 hover:text-gray-700">Danh mục món ăn</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Thêm mới</span>
        </div>
    </li>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <form action="{{ route('admin.food-categories.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf

            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin danh mục</h3>
                    <p class="mt-1 text-sm text-gray-600">Nhập thông tin chi tiết về danh mục món ăn</p>
                </div>

                <div class="px-6 py-4 space-y-6">
                    <!-- Category Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                            Tên danh mục <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               name="name"
                               id="name"
                               value="{{ old('name') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('name') border-red-300 @enderror"
                               placeholder="Nhập tên danh mục..."
                               required>
                        @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                            Trạng thái <span class="text-red-500">*</span>
                        </label>
                        <select name="status"
                                id="status"
                                class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('status') border-red-300 @enderror"
                                required>
                            <option value="0" {{ old('status', '0') == '0' ? 'selected' : '' }}>Hoạt động</option>
                            <option value="1" {{ old('status') == '1' ? 'selected' : '' }}>Không hoạt động</option>
                        </select>
                        @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Checkbox Options -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900">Tùy chọn hiển thị</h4>

                        <!-- Hide Price Checkbox -->
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="hide_price"
                                       name="hide_price"
                                       type="checkbox"
                                       value="1"
                                       {{ old('hide_price') ? 'checked' : '' }}
                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="hide_price" class="font-medium text-gray-700">Không hiện giá</label>
                                <p class="text-gray-500">Khi tích vào, khách hàng sẽ không thấy giá của các món ăn trong danh mục này</p>
                            </div>
                        </div>

                        <!-- Breakfast with Room Checkbox -->
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="breakfast_with_room"
                                       name="breakfast_with_room"
                                       type="checkbox"
                                       value="1"
                                       {{ old('breakfast_with_room') ? 'checked' : '' }}
                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="breakfast_with_room" class="font-medium text-gray-700">Ăn sáng kèm phòng</label>
                                <p class="text-gray-500">Các món trong danh mục này sẽ hiển thị ở link vé ăn sáng cho khách đặt phòng</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image Upload -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Hình ảnh danh mục</h3>
                    <p class="mt-1 text-sm text-gray-600">Tải lên hình ảnh đại diện cho danh mục</p>
                </div>

                <div class="px-6 py-4">
                    <div x-data="{
                    imagePreview: null,
                    handleFileSelect(event) {
                        const file = event.target.files[0];
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                this.imagePreview = e.target.result;
                            };
                            reader.readAsDataURL(file);
                        }
                    }
                }">
                        <div class="flex items-center space-x-6">
                            <div class="shrink-0">
                                <div x-show="!imagePreview" class="h-32 w-32 rounded-lg bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center border-2 border-dashed border-gray-300">
                                    <i class="fas fa-tags text-white text-3xl"></i>
                                </div>
                                <img x-show="imagePreview"
                                     :src="imagePreview"
                                     class="h-32 w-32 object-cover rounded-lg shadow-sm"
                                     x-cloak>
                            </div>
                            <div class="flex-1">
                                <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Chọn hình ảnh
                                </label>
                                <input type="file"
                                       name="image"
                                       id="image"
                                       accept="image/*"
                                       @change="handleFileSelect($event)"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                <p class="mt-1 text-sm text-gray-500">PNG, JPG, GIF tối đa 2MB</p>
                                @error('image')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Xem trước</h3>
                    <p class="mt-1 text-sm text-gray-600">Xem trước danh mục sẽ hiển thị như thế nào</p>
                </div>

                <div class="px-6 py-4">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flex items-center space-x-4">
                            <div class="h-16 w-16 rounded-lg bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center">
                                <i class="fas fa-tags text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-medium text-gray-900" id="preview-name">Tên danh mục</h4>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800" id="preview-status">
                                        <span class="w-1.5 h-1.5 mr-1.5 rounded-full bg-green-400"></span>
                                        Hoạt động
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800" id="preview-hide-price" style="display: none;">
                                        <i class="fas fa-eye-slash mr-1"></i>
                                        Ẩn giá
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800" id="preview-breakfast" style="display: none;">
                                        <i class="fas fa-coffee mr-1"></i>
                                        Ăn sáng kèm phòng
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="{{ route('admin.food-categories.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Hủy
                </a>
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Lưu danh mục
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function updatePreview() {
            const name = document.getElementById('name').value || 'Tên danh mục';
            const status = document.getElementById('status').value;
            const hidePrice = document.getElementById('hide_price').checked;
            const breakfastWithRoom = document.getElementById('breakfast_with_room').checked;

            document.getElementById('preview-name').textContent = name;

            const statusElement = document.getElementById('preview-status');
            if (status === '0') {
                statusElement.innerHTML = '<span class="w-1.5 h-1.5 mr-1.5 rounded-full bg-green-400"></span>Hoạt động';
                statusElement.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800';
            } else {
                statusElement.innerHTML = '<span class="w-1.5 h-1.5 mr-1.5 rounded-full bg-red-400"></span>Không hoạt động';
                statusElement.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800';
            }

            // Show/hide preview badges
            document.getElementById('preview-hide-price').style.display = hidePrice ? 'inline-flex' : 'none';
            document.getElementById('preview-breakfast').style.display = breakfastWithRoom ? 'inline-flex' : 'none';
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for real-time preview updates
            ['name', 'status', 'hide_price', 'breakfast_with_room'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updatePreview);
                    element.addEventListener('change', updatePreview);
                }
            });

            // Initial preview update
            updatePreview();
        });
    </script>
@endpush
