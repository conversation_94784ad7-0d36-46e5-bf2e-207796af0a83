@extends('layouts.admin')

@section('title', 'Chi tiết danh mục')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.food-categories.index') }}" class="text-gray-500 hover:text-gray-700"><PERSON>h mục món ăn</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Chi tiết</span>
        </div>
    </li>
@endsection

@section('content-header-actions')
    <div class="flex space-x-3">
        <a href="{{ route('admin.food-categories.edit', $foodCategory) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            Chỉnh sửa
        </a>
        <form action="{{ route('admin.food-categories.toggle-status', $foodCategory) }}" method="POST" class="inline">
            @csrf
            @method('PATCH')
            <button type="submit"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white {{ $foodCategory->status == 0 ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' }} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                <i class="fas fa-{{ $foodCategory->status == 0 ? 'toggle-off' : 'toggle-on' }} mr-2"></i>
                {{ $foodCategory->status == 0 ? 'Vô hiệu hóa' : 'Kích hoạt' }}
            </button>
        </form>
    </div>
@endsection

@section('content')
    <div class="max-w-7xl mx-auto space-y-6">
        <!-- Main Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="md:flex">
                <!-- Image Section -->
                <div class="md:w-1/3 lg:w-1/4">
                    @if($foodCategory->image_id)
                        <img src="{{ \App\Helper\helper::getImageGoogleDrive($foodCategory->image_id) }}"
                             alt="{{ $foodCategory->name }}"
                             class="w-full h-64 md:h-full object-cover">
                    @else
                        <div class="w-full h-64 md:h-full bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center">
                            <i class="fas fa-tags text-white text-6xl"></i>
                        </div>
                    @endif
                </div>

                <!-- Content Section -->
                <div class="md:w-2/3 lg:w-3/4 p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $foodCategory->name }}</h1>
                            <div class="flex items-center space-x-2 mb-4 flex-wrap">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $foodCategory->status == 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                <span class="w-2 h-2 mr-2 rounded-full {{ $foodCategory->status == 0 ? 'bg-green-400' : 'bg-red-400' }}"></span>
                                {{ $foodCategory->status_text }}
                            </span>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-utensils mr-2"></i>
                                {{ $foodCategory->foods()->count() }} món ăn
                            </span>
                                @if($foodCategory->hide_price ?? false)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                                        <i class="fas fa-eye-slash mr-2"></i>
                                        Ẩn giá
                                    </span>
                                @endif
                                @if($foodCategory->breakfast_with_room ?? false)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                        <i class="fas fa-coffee mr-2"></i>
                                        Ăn sáng kèm phòng
                                    </span>
                                @endif
                            </div>

                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="text-2xl font-bold text-gray-900">{{ $foodCategory->id }}</div>
                                    <div class="text-sm text-gray-500">ID Danh mục</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="text-2xl font-bold text-blue-600">{{ $foodCategory->foods()->count() }}</div>
                                    <div class="text-sm text-gray-500">Tổng món ăn</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="text-2xl font-bold text-green-600">{{ $foodCategory->foods()->where('status', 0)->count() }}</div>
                                    <div class="text-sm text-gray-500">Đang hoạt động</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Details Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin cơ bản</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">ID:</span>
                        <span class="text-sm text-gray-900">#{{ $foodCategory->id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Tên danh mục:</span>
                        <span class="text-sm text-gray-900 font-medium">{{ $foodCategory->name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Trạng thái:</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $foodCategory->status == 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $foodCategory->status_text }}
                    </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Số món ăn:</span>
                        <span class="text-sm text-gray-900 font-semibold">{{ $foodCategory->foods()->count() }} món</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Ẩn giá:</span>
                        <span class="text-sm {{ ($foodCategory->hide_price ?? false) ? 'text-orange-600 font-medium' : 'text-gray-500' }}">
                            {{ ($foodCategory->hide_price ?? false) ? 'Có' : 'Không' }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Ăn sáng kèm phòng:</span>
                        <span class="text-sm {{ ($foodCategory->breakfast_with_room ?? false) ? 'text-purple-600 font-medium' : 'text-gray-500' }}">
                            {{ ($foodCategory->breakfast_with_room ?? false) ? 'Có' : 'Không' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thời gian</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Ngày tạo:</span>
                        <span class="text-sm text-gray-900">{{ $foodCategory->created_at->format('d/m/Y H:i:s') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Cập nhật lần cuối:</span>
                        <span class="text-sm text-gray-900">{{ $foodCategory->updated_at->format('d/m/Y H:i:s') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Thời gian tồn tại:</span>
                        <span class="text-sm text-gray-900">{{ $foodCategory->created_at->diffForHumans() }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Special Options Info -->
        @if(($foodCategory->hide_price ?? false) || ($foodCategory->breakfast_with_room ?? false))
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Tùy chọn đặc biệt</h3>
                    <p class="mt-1 text-sm text-gray-600">Các tính năng đặc biệt được kích hoạt cho danh mục này</p>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @if($foodCategory->hide_price ?? false)
                            <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-eye-slash text-orange-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="text-sm font-medium text-orange-800">Không hiển thị giá</h4>
                                        <p class="mt-1 text-sm text-orange-700">
                                            Khách hàng sẽ không thấy giá của các món ăn trong danh mục này khi xem menu.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($foodCategory->breakfast_with_room ?? false)
                            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-coffee text-purple-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="text-sm font-medium text-purple-800">Ăn sáng kèm phòng</h4>
                                        <p class="mt-1 text-sm text-purple-700">
                                            Các món trong danh mục này sẽ hiển thị ở link vé ăn sáng cho khách đặt phòng.
                                            Khách có thể đặt món tương ứng với số vé ăn sáng có trong đơn đặt phòng.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        <!-- Foods in Category -->
        @if($foodCategory->foods()->count() > 0)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Món ăn trong danh mục</h3>
                        <a href="{{ route('admin.foods.index', ['category' => $foodCategory->id]) }}"
                           class="text-sm text-indigo-600 hover:text-indigo-900">
                            Xem tất cả →
                        </a>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Món ăn
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Giá
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Trạng thái
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Thao tác
                            </th>
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($foodCategory->foods()->latest()->take(10)->get() as $food)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        @if($food->image_id)
                                            <img class="h-10 w-10 rounded-lg object-cover mr-3"
                                                 src="{{ \App\Helper\helper::getImageGoogleDrive($food->image_id) }}"
                                                 alt="{{ $food->name }}">
                                        @else
                                            <div class="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center mr-3">
                                                <i class="fas fa-utensils text-gray-400"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $food->name }}</div>
                                            <div class="text-sm text-gray-500">{{ Str::limit($food->description, 30) }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $food->formatted_price }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $food->status == 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $food->status_text }}
                                </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{{ route('admin.foods.show', $food) }}"
                                       class="text-indigo-600 hover:text-indigo-900">
                                        Xem
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                @if($foodCategory->foods()->count() > 10)
                    <div class="px-6 py-3 border-t border-gray-200 text-center">
                        <a href="{{ route('admin.foods.index', ['category' => $foodCategory->id]) }}"
                           class="text-sm text-indigo-600 hover:text-indigo-900">
                            Xem thêm {{ $foodCategory->foods()->count() - 10 }} món ăn →
                        </a>
                    </div>
                @endif
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4">
                <div class="flex flex-wrap gap-3">
                    <a href="{{ route('admin.food-categories.edit', $foodCategory) }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i>
                        Chỉnh sửa danh mục
                    </a>

                    <form action="{{ route('admin.food-categories.toggle-status', $foodCategory) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white {{ $foodCategory->status == 0 ? 'bg-orange-600 hover:bg-orange-700' : 'bg-green-600 hover:bg-green-700' }} transition-colors duration-200">
                            <i class="fas fa-{{ $foodCategory->status == 0 ? 'pause' : 'play' }} mr-2"></i>
                            {{ $foodCategory->status == 0 ? 'Tạm dừng' : 'Kích hoạt' }}
                        </button>
                    </form>

                    <a href="{{ route('admin.food-categories.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Quay lại danh sách
                    </a>

                    @if($foodCategory->foods()->count() == 0)
                        <form action="{{ route('admin.food-categories.destroy', $foodCategory) }}" method="POST" class="inline"
                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa danh mục này? Hành động này không thể hoàn tác!')">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-200">
                                <i class="fas fa-trash mr-2"></i>
                                Xóa danh mục
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
