@extends('layouts.admin')

@section('content')
    <div class="py-6">
        <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Sửa cấu hình VietQR</h2>
                        <a href="{{ route('admin.vietqr.index') }}"
                           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Quay lại
                        </a>
                    </div>

                    <form method="POST" action="{{ route('admin.vietqr.update', $vietqr) }}" id="vietqr-form">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Mã ngân hàng</label>
                                <input type="text" name="bank_code" value="{{ old('bank_code', $vietqr->bank_code) }}"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                                @error('bank_code')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Số tài khoản</label>
                                <input type="text" name="account_number" value="{{ old('account_number', $vietqr->account_number) }}"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                                @error('account_number')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tên chủ tài khoản</label>
                                <input type="text" name="account_name" value="{{ old('account_name', $vietqr->account_name) }}"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                                @error('account_name')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Template</label>
                                <input type="text" name="template" value="{{ old('template', $vietqr->template) }}"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                                @error('template')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Prefix đơn hàng</label>
                                <input type="text" name="order_prefix" value="{{ old('order_prefix', $vietqr->order_prefix) }}"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                                <div class="mt-2 flex flex-wrap gap-2">
                                    <button type="button" onclick="insertPlaceholder('order_prefix', '{order_id}')"
                                            class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                        {order_id}
                                    </button>
                                    <button type="button" onclick="insertPlaceholder('order_prefix', '{order_number}')"
                                            class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                        {order_number}
                                    </button>
                                </div>
                                <small class="text-gray-500 mt-1 block">VD: "ORDER {order_number}" → "ORDER DH001"</small>
                                @error('order_prefix')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="md:col-span-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', $vietqr->is_active) ? 'checked' : '' }}
                                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200">
                                    <span class="ml-2 text-sm text-gray-700">Kích hoạt cấu hình này</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-between items-center mt-6 pt-6 border-t border-gray-200">
                            <button type="button" onclick="previewQR()"
                                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                Xem trước QR
                            </button>

                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Cập nhật
                            </button>
                        </div>
                    </form>

                    <div id="qr-preview" class="mt-6 text-center" style="display: none;">
                        <h3 class="text-lg font-medium mb-4">Preview QR Code (Demo)</h3>
                        <div id="qr-image"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function previewQR(event) {
            const formData = new FormData(document.getElementById('vietqr-form'));
            formData.delete('_method');
            fetch('{{ route("admin.vietqr.preview") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('qr-preview').style.display = 'block';
                        document.getElementById('qr-image').innerHTML = `
                <img src="${data.url}" alt="QR Preview" class="mx-auto max-w-xs border rounded">
                <p class="mt-2 text-sm text-gray-600">Demo: 500,000 VNĐ - DH001</p>
            `;
                    }
                }

                );
        }
    </script>
@endsection
