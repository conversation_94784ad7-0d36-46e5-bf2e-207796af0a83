@extends('layouts.admin')

@section('title', 'Chi tiết khóa ngày')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.date-locks.index') }}" class="text-gray-500 hover:text-gray-700">Khóa ngày</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Chi tiết</span>
        </div>
    </li>
@endsection

@section('content-header-actions')
    <div class="flex space-x-3">
        <a href="{{ route('admin.date-locks.edit', $dateLock) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            Chỉnh sửa
        </a>
        <form action="{{ route('admin.date-locks.destroy', $dateLock) }}" method="POST" class="inline"
              onsubmit="return confirm('Bạn có chắc chắn muốn xóa khóa ngày này?')">
            @csrf
            @method('DELETE')
            <button type="submit"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-trash mr-2"></i>
                Xóa khóa
            </button>
        </form>
    </div>
@endsection

@section('content')
    <div class="max-w-7xl mx-auto space-y-6">
        <!-- Main Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="md:flex">
                <!-- Food Image Section -->
                <div class="md:w-1/3 lg:w-1/4 relative">
                    @if($dateLock->food->image_id)
                        <img src="{{\App\Helper\helper::getImageGoogleDrive($dateLock->food->image_id) }}"
                             alt="{{ $dateLock->food->name }}"
                             class="w-full h-64 md:h-full object-cover">
                    @else
                        <div class="w-full h-64 md:h-full bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-utensils text-gray-400 text-6xl"></i>
                        </div>
                    @endif
                    <div class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        <i class="fas fa-lock mr-1"></i>
                        Bị khóa
                    </div>
                </div>

                <!-- Content Section -->
                <div class="md:w-2/3 lg:w-3/4 p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $dateLock->food->name }}</h1>
                            <div class="flex items-center space-x-4 mb-4">
                                @php
                                    $today = today();
                                    $lockDate = $dateLock->date;
                                @endphp

                                @if($lockDate->lt($today))
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                    <span class="w-2 h-2 mr-2 rounded-full bg-gray-400"></span>
                                    Đã qua
                                </span>
                                @elseif($lockDate->eq($today))
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                    <span class="w-2 h-2 mr-2 rounded-full bg-red-400"></span>
                                    Hôm nay
                                </span>
                                @else
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                    <span class="w-2 h-2 mr-2 rounded-full bg-yellow-400"></span>
                                    Sắp tới
                                </span>
                                @endif

                                @if($dateLock->food->category)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-tag mr-2"></i>
                                    {{ $dateLock->food->category->name }}
                                </span>
                                @endif
                            </div>

                            <div class="text-4xl font-bold text-indigo-600 mb-4">
                                {{ $dateLock->food->formatted_price }}
                            </div>

                            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-times text-red-600 text-xl mr-3"></i>
                                    <div>
                                        <h3 class="text-lg font-medium text-red-800">Ngày bị khóa</h3>
                                        <p class="text-red-600">
                                            {{ $dateLock->date->format('d/m/Y') }} - {{ $dateLock->date->locale('vi')->translatedFormat('l') }}
                                        </p>
                                        <p class="text-sm text-red-500 mt-1">
                                            Món ăn này không thể được đặt vào ngày này
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="prose max-w-none">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Mô tả món ăn</h3>
                                <p class="text-gray-700 leading-relaxed">{{ $dateLock->food->description }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Details Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Lock Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin khóa ngày</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">ID Khóa:</span>
                        <span class="text-sm text-gray-900">#{{ $dateLock->id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Ngày khóa:</span>
                        <span class="text-sm text-gray-900 font-medium">{{ $dateLock->date->format('d/m/Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Thứ:</span>
                        <span class="text-sm text-gray-900">{{ $dateLock->date->locale('vi')->translatedFormat('l') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Trạng thái:</span>
                        @if($lockDate->lt($today))
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            Đã qua
                        </span>
                        @elseif($lockDate->eq($today))
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            Đang khóa
                        </span>
                        @else
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                            Sắp khóa
                        </span>
                        @endif
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Còn lại:</span>
                        <span class="text-sm text-gray-900">{{ $dateLock->date->diffForHumans() }}</span>
                    </div>
                </div>
            </div>

            <!-- Food Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin món ăn</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">ID Món ăn:</span>
                        <span class="text-sm text-gray-900">#{{ $dateLock->food->id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Tên món:</span>
                        <span class="text-sm text-gray-900 font-medium">{{ $dateLock->food->name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Giá:</span>
                        <span class="text-sm text-gray-900 font-semibold">{{ $dateLock->food->formatted_price }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Danh mục:</span>
                        <span class="text-sm text-gray-900">
                        {{ $dateLock->food->category ? $dateLock->food->category->name : 'Chưa phân loại' }}
                    </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Trạng thái món:</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $dateLock->food->status == 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $dateLock->food->status_text }}
                    </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Timestamps -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thời gian tạo khóa</h3>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">{{ $dateLock->created_at->format('d/m/Y') }}</div>
                        <div class="text-sm text-gray-500">Ngày tạo</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">{{ $dateLock->created_at->format('H:i:s') }}</div>
                        <div class="text-sm text-gray-500">Giờ tạo</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">{{ $dateLock->created_at->diffForHumans() }}</div>
                        <div class="text-sm text-gray-500">Thời gian tồn tại</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Date Locks -->
        @php
            $relatedLocks = \App\Models\FoodDateLock::where('food_id', $dateLock->food_id)
                                                    ->where('id', '!=', $dateLock->id)
                                                    ->orderBy('date')
                                                    ->take(10)
                                                    ->get();
        @endphp

        @if($relatedLocks->count() > 0)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Các ngày khóa khác của món này</h3>
                        <a href="{{ route('admin.date-locks.index', ['food_id' => $dateLock->food_id]) }}"
                           class="text-sm text-indigo-600 hover:text-indigo-900">
                            Xem tất cả →
                        </a>
                    </div>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-2 md:grid-cols-5 lg:grid-cols-7 gap-3">
                        @foreach($relatedLocks as $lock)
                            @php
                                $today = today();
                                $lockDate = $lock->date;
                            @endphp
                            <div class="flex flex-col items-center p-3 rounded-lg border {{ $lockDate->eq($today) ? 'border-red-300 bg-red-50' : ($lockDate->lt($today) ? 'border-gray-300 bg-gray-50' : 'border-yellow-300 bg-yellow-50') }}">
                                <div class="text-sm font-medium {{ $lockDate->eq($today) ? 'text-red-600' : ($lockDate->lt($today) ? 'text-gray-600' : 'text-yellow-600') }}">
                                    {{ $lockDate->format('d/m') }}
                                </div>
                                <div class="text-xs {{ $lockDate->eq($today) ? 'text-red-500' : ($lockDate->lt($today) ? 'text-gray-500' : 'text-yellow-500') }}">
                                    {{ $lockDate->locale('vi')->translatedFormat('D') }}
                                </div>
                                <a href="{{ route('admin.date-locks.show', $lock) }}"
                                   class="text-xs text-blue-600 hover:text-blue-800 mt-1">
                                    Xem
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4">
                <div class="flex flex-wrap gap-3">
                    <a href="{{ route('admin.date-locks.edit', $dateLock) }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i>
                        Chỉnh sửa khóa ngày
                    </a>

                    <a href="{{ route('admin.foods.show', $dateLock->food) }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-utensils mr-2"></i>
                        Xem món ăn
                    </a>

                    <a href="{{ route('admin.date-locks.create', ['food_id' => $dateLock->food_id]) }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Thêm khóa khác
                    </a>

                    <a href="{{ route('admin.date-locks.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Quay lại danh sách
                    </a>

                    <form action="{{ route('admin.date-locks.destroy', $dateLock) }}" method="POST" class="inline"
                          onsubmit="return confirm('Bạn có chắc chắn muốn xóa khóa ngày này? Hành động này không thể hoàn tác!')">
                        @csrf
                        @method('DELETE')
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>
                            Xóa khóa ngày
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
