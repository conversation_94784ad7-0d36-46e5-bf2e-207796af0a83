@extends('layouts.admin')

@section('title', 'Chỉnh sửa khóa ngày')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.date-locks.index') }}" class="text-gray-500 hover:text-gray-700">Khóa ngày</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Chỉnh sửa</span>
        </div>
    </li>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <form action="{{ route('admin.date-locks.update', $dateLock) }}" method="POST" class="space-y-6" x-data="{
        selectedFood: '{{ old('food_id', $dateLock->food_id) }}',
        selectedDate: '{{ old('date', $dateLock->date->format('Y-m-d')) }}',
        validateForm() {
            if (!this.selectedFood) {
                alert('Vui lòng chọn món ăn!');
                return false;
            }
            if (!this.selectedDate) {
                alert('Vui lòng chọn ngày!');
                return false;
            }
            return true;
        }
    }" @submit="return validateForm()">
            @csrf
            @method('PUT')

            <!-- Current Information Display -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin hiện tại</h3>
                    <p class="mt-1 text-sm text-gray-600">Thông tin khóa ngày đang được chỉnh sửa</p>
                </div>

                <div class="px-6 py-4">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flex items-center space-x-4">
                            @if($dateLock->food->image_id)
                                <img class="h-16 w-16 rounded-lg object-cover"
                                     src="{{ \App\Helper\helper::getImageGoogleDrive($dateLock->food->image_id) }}"
                                     alt="{{ $dateLock->food->name }}">
                            @else
                                <div class="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-utensils text-gray-400 text-xl"></i>
                                </div>
                            @endif
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">{{ $dateLock->food->name }}</h4>
                                <p class="text-gray-600">{{ $dateLock->food->formatted_price }}</p>
                                <p class="text-sm text-red-600">
                                    <i class="fas fa-lock mr-1"></i>
                                    Hiện đang khóa ngày: {{ $dateLock->date->format('d/m/Y') }} ({{ $dateLock->date->locale('vi')->translatedFormat('l') }})
                                </p>
                            </div>
                            <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                ID: #{{ $dateLock->id }}
                            </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit Form -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Chỉnh sửa thông tin</h3>
                    <p class="mt-1 text-sm text-gray-600">Cập nhật món ăn và ngày cần khóa</p>
                </div>

                <div class="px-6 py-4 space-y-6">
                    <!-- Food Selection -->
                    <div>
                        <label for="food_id" class="block text-sm font-medium text-gray-700 mb-1">
                            Món ăn <span class="text-red-500">*</span>
                        </label>
                        <select name="food_id"
                                id="food_id"
                                x-model="selectedFood"
                                class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('food_id') border-red-300 @enderror"
                                required>
                            <option value="">Chọn món ăn cần khóa</option>
                            @foreach($foods as $food)
                                <option value="{{ $food->id }}" {{ old('food_id', $dateLock->food_id) == $food->id ? 'selected' : '' }}>
                                    {{ $food->name }} - {{ $food->formatted_price }}
                                </option>
                            @endforeach
                        </select>
                        @error('food_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Chỉ hiển thị các món ăn đang hoạt động</p>
                    </div>

                    <!-- Date Selection -->
                    <div>
                        <label for="date" class="block text-sm font-medium text-gray-700 mb-1">
                            Ngày khóa <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               name="date"
                               id="date"
                               x-model="selectedDate"
                               value="{{ old('date', $dateLock->date->format('Y-m-d')) }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('date') border-red-300 @enderror"
                               required>
                        @error('date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Có thể chọn bất kỳ ngày nào (kể cả quá khứ)</p>
                    </div>

                    <!-- Quick Date Presets -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Chọn nhanh</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                            <button type="button"
                                    onclick="setDatePreset(0)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Hôm nay
                            </button>
                            <button type="button"
                                    onclick="setDatePreset(1)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Ngày mai
                            </button>
                            <button type="button"
                                    onclick="setDatePreset(7)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Tuần sau
                            </button>
                            <button type="button"
                                    onclick="setDatePreset(30)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Tháng sau
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200" x-show="selectedFood && selectedDate">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Xem trước thay đổi</h3>
                    <p class="mt-1 text-sm text-gray-600">Thông tin khóa ngày sau khi cập nhật</p>
                </div>

                <div class="px-6 py-4">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-lock text-red-600 text-2xl"></i>
                            </div>
                            <div class="ml-4 flex-1">
                                <h4 class="text-lg font-medium text-red-800" id="preview-food">{{ $dateLock->food->name }}</h4>
                                <p class="text-red-600 mt-1">
                                    Sẽ bị khóa vào ngày: <span id="preview-date" class="font-semibold">{{ $dateLock->date->format('d/m/Y') }}</span>
                                </p>
                                <p class="text-sm text-red-500 mt-2">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    Món ăn này sẽ không thể đặt vào ngày đã chọn
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change History -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Lịch sử thay đổi</h3>
                </div>

                <div class="px-6 py-4">
                    <div class="space-y-3">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <span class="text-sm text-gray-500">Ngày tạo:</span>
                            <span class="text-sm text-gray-900">{{ $dateLock->created_at->format('d/m/Y H:i:s') }}</span>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <span class="text-sm text-gray-500">Lần cập nhật cuối:</span>
                            <span class="text-sm text-gray-900">{{ $dateLock->updated_at->format('d/m/Y H:i:s') }}</span>
                        </div>
                        <div class="flex items-center justify-between py-2">
                            <span class="text-sm text-gray-500">Thời gian tồn tại:</span>
                            <span class="text-sm text-gray-900">{{ $dateLock->created_at->diffForHumans() }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warning -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Lưu ý khi chỉnh sửa</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc pl-5 space-y-1">
                                <li>Thay đổi món ăn hoặc ngày sẽ ảnh hưởng đến hệ thống đặt hàng</li>
                                <li>Không thể tạo khóa ngày trùng lặp cho cùng một món ăn</li>
                                <li>Hãy chắc chắn về thay đổi trước khi lưu</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="{{ route('admin.date-locks.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Hủy
                </a>
                <a href="{{ route('admin.date-locks.show', $dateLock) }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-eye mr-2"></i>
                    Xem chi tiết
                </a>
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Cập nhật khóa ngày
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function setDatePreset(days) {
            const today = new Date();
            const targetDate = new Date(today);
            targetDate.setDate(today.getDate() + days);

            const formattedDate = targetDate.toISOString().split('T')[0];
            document.getElementById('date').value = formattedDate;

            updatePreview();
        }

        function updatePreview() {
            const foodSelect = document.getElementById('food_id');
            const dateInput = document.getElementById('date');

            const selectedFoodName = foodSelect.options[foodSelect.selectedIndex]?.text || 'Chọn món ăn';
            const selectedDate = dateInput.value;

            document.getElementById('preview-food').textContent = selectedFoodName;

            if (selectedDate) {
                const dateObj = new Date(selectedDate);
                const formattedDate = dateObj.toLocaleDateString('vi-VN', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                document.getElementById('preview-date').textContent = formattedDate;
            } else {
                document.getElementById('preview-date').textContent = 'Chọn ngày';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for real-time preview updates
            ['food_id', 'date'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', updatePreview);
                }
            });

            // Initial preview update
            updatePreview();
        });
    </script>
@endpush
