@extends('layouts.admin')

@section('title', 'Khóa ngày hàng loạt')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.date-locks.index') }}" class="text-gray-500 hover:text-gray-700">Khóa ngày</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Khóa hàng loạt</span>
        </div>
    </li>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto">
        <form action="{{ route('admin.date-locks.bulk-store') }}" method="POST" class="space-y-6" x-data="{
            selectedFoods: [],
            selectedDates: [],
            dateInput: '',
            validateForm() {
                if (this.selectedFoods.length === 0) {
                    alert('Vui lòng chọn ít nhất một món ăn!');
                    return false;
                }
                if (this.selectedDates.length === 0) {
                    alert('Vui lòng chọn ít nhất một ngày!');
                    return false;
                }
                return true;
            },
            toggleFood(foodId) {
                const index = this.selectedFoods.indexOf(foodId);
                if (index > -1) {
                    this.selectedFoods.splice(index, 1);
                } else {
                    this.selectedFoods.push(foodId);
                }
            },
            selectAllFoods() {
                const checkboxes = document.querySelectorAll('input[name=\'food_ids[]\']');
                this.selectedFoods = Array.from(checkboxes).map(cb => cb.value);
                checkboxes.forEach(cb => cb.checked = true);
            },
            clearAllFoods() {
                this.selectedFoods = [];
                document.querySelectorAll('input[name=\'food_ids[]\']').forEach(cb => cb.checked = false);
            },
            addDate() {
                if (!this.dateInput) {
                    alert('Vui lòng chọn ngày!');
                    return;
                }
                if (!this.selectedDates.includes(this.dateInput)) {
                    this.selectedDates.push(this.dateInput);
                    this.dateInput = '';
                } else {
                    alert('Ngày này đã được chọn!');
                    this.dateInput = '';
                }
            },
            removeDate(date) {
                const index = this.selectedDates.indexOf(date);
                if (index > -1) {
                    this.selectedDates.splice(index, 1);
                }
            },
            clearDates() {
                this.selectedDates = [];
            },
            addDateRange(days) {
                const today = new Date();
                for (let i = 0; i < days; i++) {
                    const date = new Date(today);
                    date.setDate(today.getDate() + i);
                    const dateString = this.formatDate(date);
                    if (!this.selectedDates.includes(dateString)) {
                        this.selectedDates.push(dateString);
                    }
                }
            },
            addWeekends(weeks) {
                const today = new Date();
                const startDate = new Date(today);
                const daysUntilSaturday = (6 - startDate.getDay()) % 7;
                startDate.setDate(today.getDate() + daysUntilSaturday);
                for (let week = 0; week < weeks; week++) {
                    const saturday = new Date(startDate);
                    saturday.setDate(startDate.getDate() + (week * 7));
                    const saturdayString = this.formatDate(saturday);
                    const sunday = new Date(saturday);
                    sunday.setDate(saturday.getDate() + 1);
                    const sundayString = this.formatDate(sunday);
                    if (!this.selectedDates.includes(saturdayString)) {
                        this.selectedDates.push(saturdayString);
                    }
                    if (!this.selectedDates.includes(sundayString)) {
                        this.selectedDates.push(sundayString);
                    }
                }
            },
            addHolidays() {
                const year = new Date().getFullYear();
                const holidays = [
                    `${year}-01-01`, // New Year
                    `${year}-04-30`, // Liberation Day
                    `${year}-05-01`, // Labor Day
                    `${year}-09-02`, // National Day
                ];
                holidays.forEach(holiday => {
                    if (!this.selectedDates.includes(holiday)) {
                        this.selectedDates.push(holiday);
                    }
                });
            },
            formatDate(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            },
            formatDateDisplay(dateString) {
                const date = new Date(dateString);
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const weekday = date.toLocaleDateString('vi-VN', { weekday: 'short' });
                return `${day}/${month} (${weekday})`;
            }
        }" @submit="validateForm()">
            @csrf

            <!-- Instructions -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-400 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-blue-800">Khóa ngày hàng loạt</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p>Tính năng này cho phép bạn khóa nhiều món ăn trong nhiều ngày cùng một lúc. Hữu ích khi:</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li>Khóa tất cả món ăn trong ngày lễ, Tết</li>
                                <li>Khóa một nhóm món ăn cụ thể trong khoảng thời gian nhất định</li>
                                <li>Thiết lập khóa ngày định kỳ cho các món ăn đặc biệt</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Food Selection -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Chọn món ăn</h3>
                            <p class="mt-1 text-sm text-gray-600">Chọn các món ăn cần khóa</p>
                        </div>
                        <div class="flex space-x-2">
                            <button type="button"
                                    @click="selectAllFoods()"
                                    class="text-sm text-indigo-600 hover:text-indigo-900">
                                Chọn tất cả
                            </button>
                            <span class="text-gray-300">|</span>
                            <button type="button"
                                    @click="clearAllFoods()"
                                    class="text-sm text-gray-600 hover:text-gray-900">
                                Bỏ chọn
                            </button>
                        </div>
                    </div>
                </div>

                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($foods as $food)
                            <div class="relative">
                                <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                                    <input type="checkbox"
                                           name="food_ids[]"
                                           value="{{ $food->id }}"
                                           x-model="selectedFoods"
                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <div class="ml-3 flex-1">
                                        <div class="flex items-center space-x-3">
                                            @if($food->image_id)
                                                <img class="h-12 w-12 rounded-lg object-cover"
                                                     src="{{ \App\Helper\helper::getImageGoogleDrive($food->image_id) }}"
                                                     alt="{{ $food->name }}">
                                            @else
                                                <div class="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                                                    <i class="fas fa-utensils text-gray-400"></i>
                                                </div>
                                            @endif
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-gray-900">{{ $food->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $food->formatted_price }}</div>
                                                @if($food->category)
                                                    <div class="text-xs text-blue-600">{{ $food->category->name }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        @endforeach
                    </div>

                    @if($foods->isEmpty())
                        <div class="text-center py-8">
                            <i class="fas fa-utensils text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-1">Không có món ăn nào</h3>
                            <p class="text-gray-500">Vui lòng thêm món ăn trước khi sử dụng tính năng này.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Date Selection -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Chọn ngày khóa</h3>
                    <p class="mt-1 text-sm text-gray-600">Chọn các ngày cần khóa món ăn</p>
                </div>

                <div class="px-6 py-4 space-y-6">
                    <!-- Date Input -->
                    <div>
                        <label for="date_input" class="block text-sm font-medium text-gray-700 mb-2">
                            Thêm ngày
                        </label>
                        <div class="flex space-x-2">
                            <input type="date"
                                   id="date_input"
                                   x-model="dateInput"
                                   min="{{ date('Y-m-d') }}"
                                   @keypress.enter.prevent="addDate(); dateInput = ''"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <button type="button"
                                    @click="addDate()"
                                    class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors duration-200">
                                <i class="fas fa-plus mr-1"></i>
                                Thêm
                            </button>
                        </div>
                    </div>

                    <!-- Quick Date Presets -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Thiết lập nhanh</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                            <button type="button"
                                    @click="addDateRange(7)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                7 ngày tới
                            </button>
                            <button type="button"
                                    @click="addWeekends(4)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Cuối tuần (4 tuần)
                            </button>
                            <button type="button"
                                    @click="addHolidays()"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Ngày lễ
                            </button>
                            <button type="button"
                                    @click="clearDates()"
                                    class="px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors duration-200">
                                Xóa tất cả
                            </button>
                        </div>
                    </div>

                    <!-- Selected Dates Display -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Ngày đã chọn (<span x-text="selectedDates.length"></span>)
                        </label>
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 min-h-[50px] p-3 border border-gray-200 rounded-md bg-gray-50">
                            <template x-for="date in selectedDates.sort()" :key="date">
                                <div class="flex items-center justify-between p-2 bg-white border border-gray-200 rounded text-sm">
                                    <span x-text="formatDateDisplay(date)"></span>
                                    <button type="button"
                                            @click="removeDate(date)"
                                            class="text-red-600 hover:text-red-800 ml-2">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <input type="hidden" name="dates[]" :value="date">
                                </div>
                            </template>
                            <div x-show="selectedDates.length === 0" class="text-gray-500 text-sm col-span-full text-center">
                                Chưa chọn ngày nào
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200" x-show="selectedFoods.length > 0 && selectedDates.length > 0">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Xem trước</h3>
                    <p class="mt-1 text-sm text-gray-600">Tổng quan về các khóa ngày sẽ được tạo</p>
                </div>

                <div class="px-6 py-4">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="text-center">
                            <i class="fas fa-lock text-red-600 text-3xl mb-4"></i>
                            <h4 class="text-xl font-medium text-red-800 mb-2">Tổng số khóa ngày sẽ tạo</h4>
                            <div class="text-4xl font-bold text-red-600 mb-2">
                                <span x-text="selectedFoods.length"></span> × <span x-text="selectedDates.length"></span> =
                                <span x-text="selectedFoods.length * selectedDates.length"></span>
                            </div>
                            <p class="text-red-600">
                                <span x-text="selectedFoods.length"></span> món ăn × <span x-text="selectedDates.length"></span> ngày
                            </p>
                            <p class="text-sm text-red-500 mt-2">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Các khóa ngày trùng lặp sẽ được bỏ qua
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="{{ route('admin.date-locks.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Hủy
                </a>
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Tạo khóa hàng loạt
                </button>
            </div>
        </form>
    </div>
@endsection
