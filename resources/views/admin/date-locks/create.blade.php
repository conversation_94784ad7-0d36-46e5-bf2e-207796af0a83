@extends('layouts.admin')

@section('title', 'Thêm khóa ngày mới')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.date-locks.index') }}" class="text-gray-500 hover:text-gray-700">Khóa ngày</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Thêm mới</span>
        </div>
    </li>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <form action="{{ route('admin.date-locks.store') }}" method="POST" class="space-y-6" x-data="{
        selectedFood: '{{ old('food_id') }}',
        selectedDate: '{{ old('date') }}',
        validateForm() {
            if (!this.selectedFood) {
                alert('Vui lòng chọn món ăn!');
                return false;
            }
            if (!this.selectedDate) {
                alert('Vui lòng chọn ngày!');
                return false;
            }
            if (new Date(this.selectedDate) < new Date().setHours(0,0,0,0)) {
                alert('Không thể khóa ngày trong quá khứ!');
                return false;
            }
            return true;
        }
    }" @submit="return validateForm()">
            @csrf

            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin khóa ngày</h3>
                    <p class="mt-1 text-sm text-gray-600">Chọn món ăn và ngày cần khóa</p>
                </div>

                <div class="px-6 py-4 space-y-6">
                    <!-- Food Selection -->
                    <div>
                        <label for="food_id" class="block text-sm font-medium text-gray-700 mb-1">
                            Món ăn <span class="text-red-500">*</span>
                        </label>
                        <select name="food_id"
                                id="food_id"
                                x-model="selectedFood"
                                class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('food_id') border-red-300 @enderror"
                                required>
                            <option value="">Chọn món ăn cần khóa</option>
                            @foreach($foods as $food)
                                <option value="{{ $food->id }}" {{ old('food_id') == $food->id ? 'selected' : '' }}>
                                    {{ $food->name }} - {{ $food->formatted_price }}
                                </option>
                            @endforeach
                        </select>
                        @error('food_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Chỉ hiển thị các món ăn đang hoạt động</p>
                    </div>

                    <!-- Date Selection -->
                    <div>
                        <label for="date" class="block text-sm font-medium text-gray-700 mb-1">
                            Ngày khóa <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               name="date"
                               id="date"
                               x-model="selectedDate"
                               value="{{ old('date') }}"
                               min="{{ date('Y-m-d') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('date') border-red-300 @enderror"
                               required>
                        @error('date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Chỉ có thể chọn ngày từ hôm nay trở đi</p>
                    </div>

                    <!-- Quick Date Presets -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Chọn nhanh</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                            <button type="button"
                                    onclick="setDatePreset(0)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Hôm nay
                            </button>
                            <button type="button"
                                    onclick="setDatePreset(1)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Ngày mai
                            </button>
                            <button type="button"
                                    onclick="setDatePreset(7)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Tuần sau
                            </button>
                            <button type="button"
                                    onclick="setDatePreset(30)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Tháng sau
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200" x-show="selectedFood && selectedDate">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Xem trước</h3>
                    <p class="mt-1 text-sm text-gray-600">Thông tin khóa ngày sẽ được tạo</p>
                </div>

                <div class="px-6 py-4">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-lock text-red-600 text-2xl"></i>
                            </div>
                            <div class="ml-4 flex-1">
                                <h4 class="text-lg font-medium text-red-800" id="preview-food">Chọn món ăn</h4>
                                <p class="text-red-600 mt-1">
                                    Sẽ bị khóa vào ngày: <span id="preview-date" class="font-semibold">Chọn ngày</span>
                                </p>
                                <p class="text-sm text-red-500 mt-2">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    Món ăn này sẽ không thể đặt vào ngày đã chọn
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warning -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Lưu ý quan trọng</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc pl-5 space-y-1">
                                <li>Khóa ngày sẽ ngăn không cho đặt món ăn này vào ngày đã chọn</li>
                                <li>Không thể tạo khóa ngày trùng lặp cho cùng một món ăn</li>
                                <li>Chỉ có thể khóa ngày từ hôm nay trở đi</li>
                                <li>Có thể sử dụng tính năng "Khóa hàng loạt" để khóa nhiều ngày cùng lúc</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="{{ route('admin.date-locks.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Hủy
                </a>
                <a href="{{ route('admin.date-locks.bulk-create') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Khóa hàng loạt
                </a>
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Tạo khóa ngày
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function setDatePreset(days) {
            const today = new Date();
            const targetDate = new Date(today);
            targetDate.setDate(today.getDate() + days);

            const formattedDate = targetDate.toISOString().split('T')[0];
            document.getElementById('date').value = formattedDate;

            updatePreview();
        }

        function updatePreview() {
            const foodSelect = document.getElementById('food_id');
            const dateInput = document.getElementById('date');

            const selectedFoodName = foodSelect.options[foodSelect.selectedIndex]?.text || 'Chọn món ăn';
            const selectedDate = dateInput.value;

            document.getElementById('preview-food').textContent = selectedFoodName;

            if (selectedDate) {
                const dateObj = new Date(selectedDate);
                const formattedDate = dateObj.toLocaleDateString('vi-VN', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                document.getElementById('preview-date').textContent = formattedDate;
            } else {
                document.getElementById('preview-date').textContent = 'Chọn ngày';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for real-time preview updates
            ['food_id', 'date'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', updatePreview);
                }
            });

            // Initial preview update
            updatePreview();
        });
    </script>
@endpush
