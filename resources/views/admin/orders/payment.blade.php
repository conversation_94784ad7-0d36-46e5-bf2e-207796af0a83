@extends('layouts.admin')

@section('title', 'Thanh toán đơn hàng #' . $order->order_number)

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.orders.index') }}" class="text-gray-500 hover:text-gray-700">Đ<PERSON>n hàng</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.orders.show', $order) }}" class="text-gray-500 hover:text-gray-700">{{ $order->order_number }}</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500"><PERSON><PERSON> toán</span>
        </div>
    </li>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto space-y-6" x-data="paymentForm()">
        <!-- Order Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thông tin đơn hàng</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Mã đơn hàng</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $order->order_number }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Khách hàng</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $order->customer->name ?? 'Khách vãng lai' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Bàn</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            @if($order->tables)
                                @foreach($order->tables as $table)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $table->name }}
                                    </span>
                                @endforeach
                            @else
                                <span class="text-gray-400">Mang về</span>
                            @endif
                        </dd>
                    </div>
                </div>

                <!-- Order Items Summary -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-4">Chi tiết món ăn</h4>
                    <div class="space-y-3">
                        @foreach($order->orderItems as $item)
                            <div class="flex justify-between items-center">
                                <div class="flex-1">
                                    <span class="text-sm text-gray-900">{{ $item->food->name }}</span>
                                    <span class="text-sm text-gray-500 ml-2">x{{ $item->quantity }}</span>
                                </div>
                                <span class="text-sm font-medium text-gray-900">{{ $item->formatted_total_price }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Form -->
        <form action="{{ route('admin.orders.process-payment', $order) }}" method="POST" class="space-y-6">
            @csrf

            <!-- Discount Code -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Mã giảm giá</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="discount_code" class="block text-sm font-medium text-gray-700 mb-2">
                                Mã giảm giá (tùy chọn)
                            </label>
                            <select name="discount_code" id="discount_code" x-model="selectedDiscountCode" @change="calculateDiscount()"
                                    class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">Không sử dụng mã giảm giá</option>
                                @foreach($discountCodes as $code)
                                    <option value="{{ $code->code }}"
                                            data-type="{{ $code->type }}"
                                            data-discount="{{ $code->discount }}"
                                            @if($order->discount_code === $code->code) selected @endif>                                    >
                                        {{ $code->code }} - {{ $code->discount_text }} ({{ $code->status }})
                                    </option>
                                @endforeach
                            </select>
                            @error('discount_code')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div x-show="selectedDiscountCode" class="bg-green-50 border border-green-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-green-800">Mã giảm giá được áp dụng</h4>
                                    <p class="text-sm text-green-700 mt-1" x-text="discountText"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Method -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Phương thức thanh toán</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <label class="relative flex cursor-pointer rounded-lg border border-gray-300 bg-white p-4 shadow-sm focus:outline-none">
                            <input type="radio" name="payment_method" value="Tiền mặt" class="sr-only" required x-model="paymentMethod">
                            <span class="flex flex-1">
                                <span class="flex flex-col">
                                    <span class="block text-sm font-medium text-gray-900">
                                        <i class="fas fa-money-bill-wave mr-2"></i>
                                        Tiền mặt
                                    </span>
                                    <span class="mt-1 flex items-center text-sm text-gray-500">
                                        Thanh toán bằng tiền mặt
                                    </span>
                                </span>
                            </span>
                        </label>

                        <label class="payment_qr_code relative flex cursor-pointer rounded-lg border border-gray-300 bg-white p-4 shadow-sm focus:outline-none">
                            <input type="radio" name="payment_method" value="Chuyển khoản" class="sr-only" x-model="paymentMethod">
                            <span class="flex flex-1">
                                <span class="flex flex-col">
                                    <span class="block text-sm font-medium text-gray-900">
                                        <i class="fas fa-university mr-2"></i>
                                        Chuyển khoản
                                    </span>
                                    <span class="mt-1 flex items-center text-sm text-gray-500">
                                        Thanh toán chuyển khoản
                                        <a
                                            x-show="paymentMethod === 'Chuyển khoản'"
                                            x-bind:href="`https://api.vietqr.io/image/970416-********-zahTi5g.jpg?accountName=CHAU%20TIEN%20VU&amount=${finalAmount}&addInfo={{ $order->order_number }}`"
                                            target="_blank"
                                            class="ri-qr-code-line font-semibold text-blue-900"
                                            title="QR thanh toán">
                                            Tạo mã QR
                                        </a>
                                    </span>
                                </span>
                            </span>
                        </label>
                    </div>
                    @error('payment_method')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Tổng kết thanh toán</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Tạm tính:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $order->formatted_subtotal }}</span>
                        </div>

                        <div class="flex justify-between items-center" x-show="discountAmount > 0">
                            <span class="text-sm text-gray-600">Giảm giá:</span>
                            <span class="text-sm font-medium text-green-600" x-text="'-' + formatPrice(discountAmount)"></span>
                        </div>

                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-semibold text-gray-900">Tổng thanh toán:</span>
                                <span class="text-2xl font-bold text-indigo-600" x-text="formatPrice(finalAmount)"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end gap-4">
                <a href="{{ route('admin.orders.show', $order) }}"
                   class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-arrow-left mr-2"></i>
                    quay lại
                </a>
                <button type="submit"
                        class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <i class="fas fa-credit-card mr-2"></i>
                    Xác nhận thanh toán
                </button>
            </div>
        </form>
    </div>
    <style>
        .ri-qr-code-line {
            padding-left: 5px;
        }
        .ri-qr-code-line:hover {
            color: red;
            text-decoration: underline;
        }
    </style
@endsection

@push('scripts')
<script>
function paymentForm() {
    return {
        selectedDiscountCode: '{{ $order->discount_code ?? '' }}',
        discountAmount: {{ $order->discount_amount ?? 0 }},
        discountText: @if($order->discount_code)
            '{{ $order->discount_code }} - Giảm {{ $order->discount_type == 1 ? $order->discount_amount . '%' : $order->formatted_discount_amount }}'
        @else '' @endif,
        subtotal: {{ $order->subtotal }},
        paymentMethod: '',

        init() {
            this.calculateDiscount(); // Gọi ngay khi khởi tạo để cập nhật discountText
        },

        calculateDiscount() {
            if (!this.selectedDiscountCode) {
                this.discountAmount = 0;
                this.discountText = '';
                return;
            }

            const option = document.querySelector(`option[value="${this.selectedDiscountCode}"]`);
            if (!option) return;

            const type = parseInt(option.dataset.type);
            const discount = parseFloat(option.dataset.discount);

            if (type === 1) {
                // Percentage discount
                this.discountAmount = (this.subtotal * discount) / 100;
                this.discountText = `Giảm ${discount}% = ${this.formatPrice(this.discountAmount)}`;
            } else {
                // Fixed amount discount
                this.discountAmount = Math.min(discount, this.subtotal);
                this.discountText = `Giảm ${this.formatPrice(this.discountAmount)}`;
            }
        },

        get finalAmount() {
            return Math.max(0, this.subtotal - this.discountAmount);
        },

        formatPrice(price) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(price);
        }
    }
}

// Handle radio button styling
document.addEventListener('DOMContentLoaded', function() {
    const radios = document.querySelectorAll('input[type="radio"][name="payment_method"]');
    radios.forEach(radio => {
        radio.addEventListener('change', function() {
            radios.forEach(r => {
                const label = r.closest('label');
                if (r.checked) {
                    label.classList.add('ring-2', 'ring-indigo-500', 'border-indigo-500');
                } else {
                    label.classList.remove('ring-2', 'ring-indigo-500', 'border-indigo-500');
                }
            });
        });
    });
});
</script>
@endpush
