@extends('layouts.admin')

@section('title', 'Tạo đơn hàng mới')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.orders.index') }}" class="text-gray-500 hover:text-gray-700">Đơn hàng</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Tạo mới</span>
        </div>
    </li>
@endsection

@push('styles')
    <style>
        /* Customer suggestion dropdown */
        .customer-suggestions {
            position: absolute;
            z-index: 60;
            width: 100%;
            background: white;
            border: 1px solid #d1d5db;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
        }

        .customer-suggestion-item {
            padding: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.15s ease;
        }

        .customer-suggestion-item:hover {
            background-color: #f9fafb;
        }

        .customer-suggestion-item:last-child {
            border-bottom: none;
        }

        .customer-suggestion-item.selected {
            background-color: #eef2ff;
        }

        .birthday-badge {
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            color: white;
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 12px;
            font-weight: 500;
        }

        /* Order Type Cards */
        .order-type-card {
            transition: all 0.2s ease;
            cursor: pointer;
            border: 2px solid #e5e7eb;
        }

        .order-type-card:hover {
            border-color: #6366f1;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .order-type-card.selected {
            border-color: #4f46e5;
            background-color: #f0f9ff;
        }

        /* Info boxes */
        .info-box {
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .info-box.buffet {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
        }

        .info-box.breakfast-room {
            background-color: #d1fae5;
            border: 1px solid #10b981;
        }

        /* Multiselect Dropdown Styles */
        .table-option {
            transition: background-color 0.15s ease;
        }

        .table-option:hover {
            background-color: #f9fafb;
        }

        .table-option.selected {
            background-color: #eef2ff;
        }

        /* Selected Table Tags */
        .table-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background: #eef2ff;
            border: 1px solid #c7d2fe;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            color: #3730a3;
            gap: 4px;
        }

        .table-tag .remove-btn {
            cursor: pointer;
            color: #6b7280;
            font-size: 10px;
            padding: 2px;
            border-radius: 50%;
            transition: all 0.15s ease;
        }

        .table-tag .remove-btn:hover {
            background: #dc2626;
            color: white;
        }

        /* Compact spacing */
        .compact-form .form-group {
            margin-bottom: 1rem;
        }

        .compact-form label {
            margin-bottom: 0.5rem;
        }

        .compact-form input,
        .compact-form select,
        .compact-form textarea {
            padding: 0.5rem;
            font-size: 0.875rem;
        }
    </style>
@endpush

@section('content')
    <form action="{{ route('admin.orders.store') }}" method="POST" id="orderForm" class="space-y-4 compact-form">
        @csrf

        <!-- Order Type Selection -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Loại đơn hàng</h3>
                <p class="mt-1 text-sm text-gray-600">Chọn loại khách hàng và hình thức phục vụ</p>
            </div>
            <div class="p-6">
                <!-- Customer Type Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        Loại khách hàng <span class="text-red-500">*</span>
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="order-type-card p-4 rounded-lg" onclick="selectCustomerType('individual')">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <input type="radio" name="customer_type" value="individual" id="customer_type_individual" class="mr-2">
                                        <h4 class="text-md font-medium text-gray-900">Khách lẻ</h4>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">Khách hàng đến trực tiếp</p>
                                </div>
                            </div>
                        </div>

                        <div class="order-type-card p-4 rounded-lg" onclick="selectCustomerType('breakfast-room')">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-home text-green-600"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <input type="radio" name="customer_type" value="breakfast-room" id="customer_type_breakfast-room" class="mr-2">
                                        <h4 class="text-md font-medium text-gray-900">Ăn sáng kèm đặt phòng</h4>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">Khách có đặt phòng - Miễn phí các món ăn sáng</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @error('customer_type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Service Type Selection -->
                <div id="serviceTypeSection" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        Hình thức phục vụ <span class="text-red-500">*</span>
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="order-type-card p-4 rounded-lg" onclick="selectServiceType('buffet')">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-coffee text-orange-600"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <input type="radio" name="service_type" value="buffet" id="service_type_buffet" class="mr-2">
                                        <h4 class="text-md font-medium text-gray-900">Buffet</h4>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">Món buffet miễn phí, món ngoài menu tính tiền bình thường</p>
                                </div>
                            </div>
                        </div>

                        <div class="order-type-card p-4 rounded-lg" onclick="selectServiceType('ala-carte')">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-list text-purple-600"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <input type="radio" name="service_type" value="ala-carte" id="service_type_ala-carte" class="mr-2">
                                        <h4 class="text-md font-medium text-gray-900">Gọi món lẻ</h4>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">Tất cả món đều tính tiền theo menu</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @error('service_type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Info Boxes -->
                <div id="buffetInfo" class="info-box buffet" style="display: none;">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-info-circle text-orange-600 mt-0.5"></i>
                        <div>
                            <h5 class="font-medium text-orange-800">Lưu ý về Buffet</h5>
                            <p class="text-sm text-orange-700 mt-1">
                                Khi chọn buffet, các món thuộc danh mục buffet sẽ không tính tiền khi thanh toán.
                                Chỉ các món ngoài menu buffet mới được tính giá bình thường.
                            </p>
                        </div>
                    </div>
                </div>

                <div id="breakfastRoomInfo" class="info-box breakfast-room" style="display: none;">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-info-circle text-green-600 mt-0.5"></i>
                        <div>
                            <h5 class="font-medium text-green-800">Lưu ý về Ăn sáng kèm đặt phòng</h5>
                            <p class="text-sm text-green-700 mt-1">
                                Khách đặt phòng được miễn phí tất cả các món ăn trong danh mục ăn sáng.
                                Các món ngoài danh mục ăn sáng vẫn tính tiền bình thường.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer & Table Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thông tin đơn hàng</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Chọn bàn
                        </label>

                        <!-- Multiselect Dropdown -->
                        <div class="relative">
                            <button type="button"
                                    id="tableDropdownBtn"
                                    class="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                    onclick="toggleTableDropdown()">
                                <span id="dropdownText" class="text-gray-500">Chọn bàn...</span>
                                <i class="fas fa-chevron-down float-right mt-1"></i>
                            </button>

                            <!-- Dropdown Content -->
                            <div id="tableDropdown" class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg hidden">
                                <!-- Search and Filters -->
                                <div class="p-3 border-b border-gray-200">
                                    <div class="flex gap-2 mb-2">
                                        <input type="text"
                                               id="tableSearch"
                                               placeholder="Tìm bàn..."
                                               class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded"
                                               onkeyup="filterDropdownTables()">
                                        <select id="statusFilter"
                                                class="px-2 py-1 text-sm border border-gray-300 rounded"
                                                onchange="filterDropdownTables()">
                                            <option value="">Tất cả</option>
                                            <option value="available">Trống</option>
                                            <option value="occupied">Có khách</option>
                                        </select>
                                    </div>
                                    <div class="flex gap-1">
                                        <button type="button" onclick="selectAllAvailable()"
                                                class="px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600">
                                            Chọn tất cả trống
                                        </button>
                                        <button type="button" onclick="clearSelection()"
                                                class="px-2 py-1 bg-gray-500 text-white rounded text-xs hover:bg-gray-600">
                                            Bỏ chọn
                                        </button>
                                    </div>
                                </div>

                                <!-- Table List -->
                                <div class="max-h-60 overflow-y-auto">
                                    @foreach($tables as $table)
                                        <div class="table-option px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                                             data-table-id="{{ $table->id }}"
                                             data-table-name="{{ $table->name }}"
                                             data-table-capacity="{{ $table->capacity }}"
                                             data-table-location="{{ $table->location }}"
                                             data-table-status="{{ $table->status == 0 ? 'available' : 'occupied' }}"
                                             onclick="toggleTableSelection({{ $table->id }})">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <input type="checkbox"
                                                           id="table_{{ $table->id }}"
                                                           class="table-checkbox mr-2"
                                                           onchange="event.stopPropagation()">
                                                    <div>
                                                        <div class="font-medium text-sm">{{ $table->name }}</div>
                                                        <div class="text-xs text-gray-500">{{ $table->location }} • {{ $table->capacity }} chỗ</div>
                                                    </div>
                                                </div>
                                                <div class="text-xs">
                                                    @if($table->status == 0)
                                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full">Trống</span>
                                                    @else
                                                        <span class="px-2 py-1 bg-orange-100 text-orange-800 rounded-full">Có khách</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <!-- Selected Tables Tags -->
                        <div id="selectedTablesTags" class="mt-2 flex flex-wrap gap-1" style="display: none;">
                            <!-- Tags will be inserted here by JavaScript -->
                        </div>

                        <!-- Summary -->
                        <div id="selectedTablesSummary" class="mt-2 text-sm text-gray-600" style="display: none;">
                            <span id="selectedTablesText"></span> •
                            <span class="font-medium text-indigo-600">
                                <i class="fas fa-users mr-1"></i><span id="totalCapacity">0</span> chỗ ngồi
                            </span>
                        </div>

                        <!-- Hidden inputs for selected tables -->
                        <div id="hiddenTableInputs"></div>

                        @error('table_ids')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        @error('table_ids.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="relative">
                        <label for="customer_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Số điện thoại
                        </label>
                        <input type="tel" name="customer_phone" id="customer_phone" value="{{ old('customer_phone') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập số điện thoại để tìm khách hàng"
                               oninput="searchCustomers(this.value)"
                               onkeydown="handleCustomerNavigation(event)">

                        <!-- Customer suggestions dropdown -->
                        <div id="customerSuggestions" class="customer-suggestions hidden"></div>

                        @error('customer_phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Tên khách hàng <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="customer_name" id="customer_name" value="{{ old('customer_name') }}" required
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập tên khách hàng">
                        @error('customer_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div id="breakfastTicketField" style="display: none;">
                        <label for="breakfast_ticket_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Order Room Id
                        </label>
                        <input type="text" name="breakfast_ticket_id" id="breakfast_ticket_id" value="{{ old('breakfast_ticket_id') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập Order Room Id (bắt buộc cho khách đặt phòng)">
                        @error('breakfast_ticket_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email
                        </label>
                        <input type="email" name="customer_email" id="customer_email" value="{{ old('customer_email') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập email">
                        @error('customer_email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_birthday" class="block text-sm font-medium text-gray-700 mb-2">
                            Ngày sinh nhật
                            <span class="text-xs text-gray-500">(để tạo khuyến mại sinh nhật)</span>
                        </label>
                        <input type="date" name="customer_birthday" id="customer_birthday" value="{{ old('customer_birthday') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        >
                        @error('customer_birthday')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_address" class="block text-sm font-medium text-gray-700 mb-2">
                            Địa chỉ
                        </label>
                        <input type="text" name="customer_address" id="customer_address" value="{{ old('customer_address') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập địa chỉ">
                        @error('customer_address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Ghi chú đơn hàng
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Ghi chú đặc biệt cho đơn hàng...">{{ old('notes') }}</textarea>
                    @error('notes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Customer Info Display -->
                <div id="customerInfoDisplay" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg hidden">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Thông tin khách hàng</h4>
                    <div id="customerInfoContent" class="text-sm text-blue-800"></div>
                </div>
            </div>
        </div>

        <!-- Selected Items (Removed - not used in this form) -->

        <!-- Submit Buttons -->
        <div class="flex items-center justify-end space-x-4">
            <a href="{{ route('admin.orders.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-times mr-2"></i>
                Hủy
            </a>
            <button type="submit" id="submitBtn"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 opacity-50 cursor-not-allowed"
                    disabled>
                <i class="fas fa-save mr-2"></i>
                Tạo đơn hàng
            </button>
        </div>
    </form>
@endsection

@push('scripts')
    <script>
        let selectedItems = [];
        let customerSuggestions = [];
        let selectedSuggestionIndex = -1;
        let selectedCustomerType = '';
        let selectedServiceType = '';

        // Order Type Selection Functions
        function selectCustomerType(type) {
            selectedCustomerType = type;
            console.log(type, 'type')
            // Update radio button
            document.getElementById('customer_type_' + type).checked = true;

            // Update visual selection
            document.querySelectorAll('[onclick*="selectCustomerType"]').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');

            // Show service type section
            document.getElementById('serviceTypeSection').style.display = 'block';

            // Show/hide breakfast ticket field and update its requirement
            const breakfastField = document.getElementById('breakfastTicketField');
            const breakfastInput = document.getElementById('breakfast_ticket_id');

            if (type === 'breakfast-room') {
                breakfastField.style.display = 'block';
                // Show breakfast room info
                document.getElementById('breakfastRoomInfo').style.display = 'block';
            } else {
                breakfastField.style.display = 'none';
                breakfastInput.required = false;
                breakfastInput.value = '';

                // Hide breakfast room info
                document.getElementById('breakfastRoomInfo').style.display = 'none';
            }

            // Reset service type selection
            selectedServiceType = '';
            document.querySelectorAll('input[name="service_type"]').forEach(radio => {
                radio.checked = false;
            });
            document.querySelectorAll('[onclick*="selectServiceType"]').forEach(card => {
                card.classList.remove('selected');
            });

            // Hide info boxes
            document.getElementById('buffetInfo').style.display = 'none';

            updateSubmitButton();
        }

        function selectServiceType(type) {
            selectedServiceType = type;

            // Update radio button
            document.getElementById('service_type_' + type).checked = true;

            // Update visual selection
            document.querySelectorAll('[onclick*="selectServiceType"]').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');

            // Show/hide info boxes
            if (type === 'buffet') {
                document.getElementById('buffetInfo').style.display = 'block';
            } else {
                document.getElementById('buffetInfo').style.display = 'none';
            }

            updateSubmitButton();
        }

        // Customer search functionality
        let searchTimeout;
        function searchCustomers(query) {
            clearTimeout(searchTimeout);

            if (query.length < 3) {
                hideCustomerSuggestions();
                return;
            }

            searchTimeout = setTimeout(() => {
                fetch(`/api/customers/search?query=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(customers => {
                        displayCustomerSuggestions(customers);
                    })
                    .catch(error => {
                        console.error('Error searching customers:', error);
                        hideCustomerSuggestions();
                    });
            }, 300);
        }

        function displayCustomerSuggestions(customers) {
            const suggestionsContainer = document.getElementById('customerSuggestions');

            if (customers.length === 0) {
                hideCustomerSuggestions();
                return;
            }

            customerSuggestions = customers;
            selectedSuggestionIndex = -1;

            let html = '';
            customers.forEach((customer, index) => {
                const birthdayBadge = customer.is_birthday_today ? '<span class="birthday-badge ml-2">🎂 Sinh nhật hôm nay!</span>' :
                    customer.is_birthday_this_week ? '<span class="birthday-badge ml-2">🎉 Sinh nhật tuần này</span>' :
                        customer.is_birthday_this_month ? '<span class="birthday-badge ml-2">🎈 Sinh nhật tháng này</span>' : '';

                html += `
                    <div class="customer-suggestion-item" data-index="${index}" onclick="selectCustomer(${index})">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">${customer.name}</div>
                                <div class="text-sm text-gray-600">${customer.phone}</div>
                                ${customer.email ? `<div class="text-sm text-gray-500">${customer.email}</div>` : ''}
                                ${customer.formatted_birthday ? `<div class="text-xs text-gray-500">Sinh nhật: ${customer.formatted_birthday}</div>` : ''}
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-500">${customer.total_orders} đơn hàng</div>
                                <div class="text-xs text-green-600 font-medium">${customer.total_spent}</div>
                                ${birthdayBadge}
                            </div>
                        </div>
                    </div>
                `;
            });

            suggestionsContainer.innerHTML = html;
            suggestionsContainer.classList.remove('hidden');
        }

        function hideCustomerSuggestions() {
            document.getElementById('customerSuggestions').classList.add('hidden');
            customerSuggestions = [];
            selectedSuggestionIndex = -1;
        }

        function selectCustomer(index) {
            const customer = customerSuggestions[index];

            // Fill form fields
            document.getElementById('customer_phone').value = customer.phone;
            document.getElementById('customer_name').value = customer.name;
            document.getElementById('customer_email').value = customer.email || '';
            document.getElementById('customer_birthday').value = customer.birthday || '';
            document.getElementById('customer_address').value = customer.address || '';

            // Show customer info
            displayCustomerInfo(customer);

            hideCustomerSuggestions();
            updateSubmitButton();
        }

        function displayCustomerInfo(customer) {
            const infoDisplay = document.getElementById('customerInfoDisplay');
            const infoContent = document.getElementById('customerInfoContent');

            let html = `
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <strong>Tổng đơn hàng:</strong> ${customer.total_orders}
                    </div>
                    <div>
                        <strong>Tổng chi tiêu:</strong> ${customer.total_spent}
                    </div>
                </div>
            `;

            if (customer.is_birthday_today) {
                html = `<div class="bg-yellow-100 text-yellow-800 p-2 rounded mb-2 text-center">
                    🎂 <strong>Hôm nay là sinh nhật khách hàng!</strong> Có thể áp dụng khuyến mại sinh nhật.
                </div>` + html;
            } else if (customer.is_birthday_this_week) {
                html = `<div class="bg-blue-100 text-blue-800 p-2 rounded mb-2 text-center">
                    🎉 Sinh nhật khách hàng trong tuần này (${customer.formatted_birthday})
                </div>` + html;
            } else if (customer.is_birthday_this_month) {
                html = `<div class="bg-green-100 text-green-800 p-2 rounded mb-2 text-center">
                    🎈 Sinh nhật khách hàng trong tháng này (${customer.formatted_birthday})
                </div>` + html;
            }

            infoContent.innerHTML = html;
            infoDisplay.classList.remove('hidden');
        }

        function handleCustomerNavigation(event) {
            const suggestions = document.querySelectorAll('.customer-suggestion-item');

            if (suggestions.length === 0) return;

            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    selectedSuggestionIndex = Math.min(selectedSuggestionIndex + 1, suggestions.length - 1);
                    updateSuggestionSelection();
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    selectedSuggestionIndex = Math.max(selectedSuggestionIndex - 1, -1);
                    updateSuggestionSelection();
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (selectedSuggestionIndex >= 0) {
                        selectCustomer(selectedSuggestionIndex);
                    }
                    break;
                case 'Escape':
                    hideCustomerSuggestions();
                    break;
            }
        }

        function updateSuggestionSelection() {
            const suggestions = document.querySelectorAll('.customer-suggestion-item');
            suggestions.forEach((item, index) => {
                if (index === selectedSuggestionIndex) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(event) {
            const phoneInput = document.getElementById('customer_phone');
            const suggestions = document.getElementById('customerSuggestions');

            if (!phoneInput.contains(event.target) && !suggestions.contains(event.target)) {
                hideCustomerSuggestions();
            }
        });

        // Multiselect Table Selection
        let selectedTables = [];
        let dropdownOpen = false;

        // Table data for calculations
        const tableData = {
            @foreach($tables as $table)
                {{ $table->id }}: {
                name: '{{ $table->name }}',
                capacity: {{ $table->capacity }},
                location: '{{ $table->location }}',
                status: '{{ $table->status == 0 ? 'available' : 'occupied' }}'
            },
            @endforeach
        };

        function toggleTableDropdown() {
            const dropdown = document.getElementById('tableDropdown');
            const btn = document.getElementById('tableDropdownBtn');

            if (dropdownOpen) {
                dropdown.classList.add('hidden');
                btn.querySelector('i').classList.remove('fa-chevron-up');
                btn.querySelector('i').classList.add('fa-chevron-down');
                dropdownOpen = false;
            } else {
                dropdown.classList.remove('hidden');
                btn.querySelector('i').classList.remove('fa-chevron-down');
                btn.querySelector('i').classList.add('fa-chevron-up');
                dropdownOpen = true;
            }
        }

        function toggleTableSelection(tableId) {
            const checkbox = document.getElementById(`table_${tableId}`);
            const option = document.querySelector(`[data-table-id="${tableId}"]`);

            if (selectedTables.includes(tableId)) {
                // Remove from selection
                selectedTables = selectedTables.filter(id => id !== tableId);
                checkbox.checked = false;
                option.classList.remove('selected');
            } else {
                // Add to selection
                selectedTables.push(tableId);
                checkbox.checked = true;
                option.classList.add('selected');
            }

            updateDropdownText();
            updateSelectedTags();
            updateTableSummary();
            updateHiddenInputs();
            updateSubmitButton();
        }

        function updateDropdownText() {
            const dropdownText = document.getElementById('dropdownText');

            if (selectedTables.length === 0) {
                dropdownText.textContent = 'Chọn bàn...';
                dropdownText.className = 'text-gray-500';
            } else if (selectedTables.length === 1) {
                const table = tableData[selectedTables[0]];
                dropdownText.textContent = `${table.name} (${table.capacity} chỗ)`;
                dropdownText.className = 'text-gray-900';
            } else {
                dropdownText.textContent = `Đã chọn ${selectedTables.length} bàn`;
                dropdownText.className = 'text-gray-900';
            }
        }

        function updateSelectedTags() {
            const tagsContainer = document.getElementById('selectedTablesTags');
            tagsContainer.innerHTML = '';

            if (selectedTables.length > 0) {
                tagsContainer.style.display = 'flex';

                selectedTables.forEach(tableId => {
                    const table = tableData[tableId];
                    const tag = document.createElement('div');
                    tag.className = 'table-tag';
                    tag.innerHTML = `
                <span>${table.name}</span>
                <span class="remove-btn" onclick="removeTableFromSelection(${tableId})">
                    <i class="fas fa-times"></i>
                </span>
            `;
                    tagsContainer.appendChild(tag);
                });
            } else {
                tagsContainer.style.display = 'none';
            }
        }

        function removeTableFromSelection(tableId) {
            toggleTableSelection(tableId);
        }

        function updateTableSummary() {
            const summary = document.getElementById('selectedTablesSummary');
            const summaryText = document.getElementById('selectedTablesText');
            const totalCapacity = document.getElementById('totalCapacity');

            if (selectedTables.length > 0) {
                const totalCap = selectedTables.reduce((sum, id) => sum + tableData[id].capacity, 0);

                summaryText.textContent = `${selectedTables.length} bàn`;
                totalCapacity.textContent = totalCap;
                summary.style.display = 'block';
            } else {
                summary.style.display = 'none';
            }
        }

        function updateHiddenInputs() {
            const container = document.getElementById('hiddenTableInputs');
            container.innerHTML = '';

            selectedTables.forEach(tableId => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'table_ids[]';
                input.value = tableId;
                container.appendChild(input);
            });
        }

        // Update submit button based on requirements
        function updateSubmitButton() {
            const submitBtn = document.getElementById('submitBtn');
            const customerName = document.getElementById('customer_name').value.trim();
            const breakfastTicketId = document.getElementById('breakfast_ticket_id').value.trim();

            // Check all required conditions
            let canSubmit = true;
            let reasons = [];

            // Customer type and service type are required
            if (!selectedCustomerType) {
                canSubmit = false;
                reasons.push('Chưa chọn loại khách hàng');
            }

            if (!selectedServiceType) {
                canSubmit = false;
                reasons.push('Chưa chọn hình thức phục vụ');
            }

            // Customer name is required
            if (!customerName) {
                canSubmit = false;
                reasons.push('Chưa nhập tên khách hàng');
            }
            // Update button state
            submitBtn.disabled = !canSubmit;

            if (canSubmit) {
                submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                submitBtn.title = '';
            } else {
                submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                submitBtn.title = reasons.join(', ');
            }
        }

        function filterDropdownTables() {
            const searchTerm = document.getElementById('tableSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            const tableOptions = document.querySelectorAll('.table-option');

            tableOptions.forEach(option => {
                const tableId = parseInt(option.dataset.tableId);
                const table = tableData[tableId];

                const matchesSearch = table.name.toLowerCase().includes(searchTerm) ||
                    table.location.toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || table.status === statusFilter;

                if (matchesSearch && matchesStatus) {
                    option.style.display = 'block';
                } else {
                    option.style.display = 'none';
                }
            });
        }

        function selectAllAvailable() {
            Object.keys(tableData).forEach(tableId => {
                const id = parseInt(tableId);
                if (tableData[id].status === 'available' && !selectedTables.includes(id)) {
                    toggleTableSelection(id);
                }
            });
        }

        function clearSelection() {
            [...selectedTables].forEach(tableId => {
                toggleTableSelection(tableId);
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('tableDropdown');
            const btn = document.getElementById('tableDropdownBtn');

            if (!dropdown.contains(event.target) && !btn.contains(event.target)) {
                if (dropdownOpen) {
                    toggleTableDropdown();
                }
            }
        });

        // Listen for form field changes to update submit button
        document.getElementById('customer_name').addEventListener('input', updateSubmitButton);

        // Initialize with old values if validation failed
        document.addEventListener('DOMContentLoaded', function() {
            // Set old customer type
            const oldCustomerType = '{{ old("customer_type") }}';
            if (oldCustomerType) {
                selectCustomerType(oldCustomerType);
            }

            // Set old service type
            const oldServiceType = '{{ old("service_type") }}';
            if (oldServiceType) {
                selectedCustomerType = oldCustomerType; // Ensure customer type is set first
                document.getElementById('serviceTypeSection').style.display = 'block';
                selectServiceType(oldServiceType);
            }

            // Set old table selection
            const oldTableIds = @json(old('table_ids', []));
            oldTableIds.forEach(tableId => {
                if (tableData[tableId]) {
                    toggleTableSelection(parseInt(tableId));
                }
            });

            // Initialize from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const defaultTableId = urlParams.get('table_id');

            if (defaultTableId && tableData[defaultTableId]) {
                toggleTableSelection(parseInt(defaultTableId));
            }

            // Initial submit button state check
            updateSubmitButton();
        });
    </script>
@endpush
