@extends('layouts.admin')

@section('title', 'Chỉnh sửa đơn hàng #' . $order->order_number)

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.orders.index') }}" class="text-gray-500 hover:text-gray-700">Đ<PERSON>n hàng</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.orders.show', $order) }}" class="text-gray-500 hover:text-gray-700">{{ $order->order_number }}</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Chỉnh sửa</span>
        </div>
    </li>
@endsection

@push('styles')
    <style>
        .food-item {
            transition: all 0.2s ease;
        }
        .food-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .selected-item {
            border-color: #4f46e5;
            background-color: #f0f9ff;
        }

        /* Customer suggestion dropdown */
        .customer-suggestions {
            position: absolute;
            z-index: 60;
            width: 100%;
            background: white;
            border: 1px solid #d1d5db;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
        }

        .customer-suggestion-item {
            padding: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.15s ease;
        }

        .customer-suggestion-item:hover {
            background-color: #f9fafb;
        }

        .customer-suggestion-item:last-child {
            border-bottom: none;
        }

        .customer-suggestion-item.selected {
            background-color: #eef2ff;
        }

        .birthday-badge {
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            color: white;
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 12px;
            font-weight: 500;
        }

        /* Multiselect Dropdown Styles (Same as Create Order) */
        .table-option {
            transition: background-color 0.15s ease;
        }

        .table-option:hover {
            background-color: #f9fafb;
        }

        .table-option.selected {
            background-color: #eef2ff;
        }

        /* Selected Table Tags */
        .table-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background: #eef2ff;
            border: 1px solid #c7d2fe;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            color: #3730a3;
            gap: 4px;
        }

        .table-tag .remove-btn {
            cursor: pointer;
            color: #6b7280;
            font-size: 10px;
            padding: 2px;
            border-radius: 50%;
            transition: all 0.15s ease;
        }

        .table-tag .remove-btn:hover {
            background: #dc2626;
            color: white;
        }

        /* Compact spacing */
        .compact-form .form-group {
            margin-bottom: 1rem;
        }

        .compact-form label {
            margin-bottom: 0.5rem;
        }

        .compact-form input,
        .compact-form select,
        .compact-form textarea {
            padding: 0.5rem;
            font-size: 0.875rem;
        }
    </style>
@endpush

@section('content')
    <form action="{{ route('admin.orders.update', $order) }}" method="POST" id="orderForm" class="space-y-6">
        @csrf
        @method('PUT')

        <!-- Order Status Alert -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Lưu ý</h3>
                    <p class="mt-1 text-sm text-yellow-700">
                        Đơn hàng này có trạng thái "{{ $order->status_text }}" - một số thay đổi có thể bị hạn chế.
                    </p>
                </div>
            </div>
        </div>

        <!-- Customer & Table Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thông tin đơn hàng</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Chọn bàn <span class="text-red-500">*</span>
                        </label>

                        @php
                            $selectedTableIds = $order->tables->pluck('id')->toArray();
                        @endphp

                            <!-- Multiselect Dropdown (Same as Create Order) -->
                        <div class="relative">
                            <button type="button"
                                    id="tableDropdownBtn"
                                    class="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                    onclick="toggleTableDropdown()">
                                <span id="dropdownText" class="text-gray-500">Chọn bàn...</span>
                                <i class="fas fa-chevron-down float-right mt-1"></i>
                            </button>

                            <!-- Dropdown Content -->
                            <div id="tableDropdown" class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg hidden">
                                <!-- Search and Filters -->
                                <div class="p-3 border-b border-gray-200">
                                    <div class="flex gap-2 mb-2">
                                        <input type="text"
                                               id="tableSearch"
                                               placeholder="Tìm bàn..."
                                               class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded"
                                               onkeyup="filterDropdownTables()">
                                        <select id="statusFilter"
                                                class="px-2 py-1 text-sm border border-gray-300 rounded"
                                                onchange="filterDropdownTables()">
                                            <option value="">Tất cả</option>
                                            <option value="available">Trống</option>
                                            <option value="occupied">Có khách</option>
                                        </select>
                                    </div>
                                    <div class="flex gap-1">
                                        <button type="button" onclick="selectAllAvailable()"
                                                class="px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600">
                                            Chọn tất cả trống
                                        </button>
                                        <button type="button" onclick="clearSelection()"
                                                class="px-2 py-1 bg-gray-500 text-white rounded text-xs hover:bg-gray-600">
                                            Bỏ chọn
                                        </button>
                                    </div>
                                </div>

                                <!-- Table List -->
                                <div class="max-h-60 overflow-y-auto">
                                    @foreach($tables as $table)
                                        <div class="table-option px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                                             data-table-id="{{ $table->id }}"
                                             data-table-name="{{ $table->name }}"
                                             data-table-capacity="{{ $table->capacity }}"
                                             data-table-location="{{ $table->location }}"
                                             data-table-status="{{ $table->status == 0 ? 'available' : 'occupied' }}"
                                             onclick="toggleTableSelection({{ $table->id }})">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <input type="checkbox"
                                                           id="table_{{ $table->id }}"
                                                           class="table-checkbox mr-2"
                                                           onchange="event.stopPropagation()">
                                                    <div>
                                                        <div class="font-medium text-sm">{{ $table->name }}</div>
                                                        <div class="text-xs text-gray-500">{{ $table->location }} • {{ $table->capacity }} chỗ</div>
                                                    </div>
                                                </div>
                                                <div class="text-xs">
                                                    @if($table->status == 0)
                                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full">Trống</span>
                                                    @else
                                                        <span class="px-2 py-1 bg-orange-100 text-orange-800 rounded-full">Có khách</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <!-- Selected Tables Tags -->
                        <div id="selectedTablesTags" class="mt-2 flex flex-wrap gap-1" style="display: none;">
                            <!-- Tags will be inserted here by JavaScript -->
                        </div>

                        <!-- Summary -->
                        <div id="selectedTablesSummary" class="mt-2 text-sm text-gray-600" style="display: none;">
                            <span id="selectedTablesText"></span> •
                            <span class="font-medium text-indigo-600">
                                <i class="fas fa-users mr-1"></i><span id="totalCapacity">0</span> chỗ ngồi
                            </span>
                        </div>

                        <!-- Hidden inputs for selected tables -->
                        <div id="hiddenTableInputs"></div>

                        @error('table_ids')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        @error('table_ids.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="relative">
                        <label for="customer_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Số điện thoại
                        </label>
                        <input type="tel" name="customer_phone" id="customer_phone"
                               value="{{ old('customer_phone', $order->customer->phone ?? '') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập số điện thoại để tìm khách hàng"
                               oninput="searchCustomers(this.value)"
                               onkeydown="handleCustomerNavigation(event)">

                        <!-- Customer suggestions dropdown -->
                        <div id="customerSuggestions" class="customer-suggestions hidden"></div>

                        @error('customer_phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Tên khách hàng <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="customer_name" id="customer_name"
                               value="{{ old('customer_name', $order->customer->name ?? '') }}" required
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập tên khách hàng">
                        @error('customer_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email
                        </label>
                        <input type="email" name="customer_email" id="customer_email"
                               value="{{ old('customer_email', $order->customer->email ?? '') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập email">
                        @error('customer_email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_birthday" class="block text-sm font-medium text-gray-700 mb-2">
                            Ngày sinh nhật
                            <span class="text-xs text-gray-500">(để tạo khuyến mại sinh nhật)</span>
                        </label>
                        <input type="date" name="customer_birthday" id="customer_birthday"
                               value="{{ old('customer_birthday', $order->customer->birthday ? $order->customer->birthday->format('Y-m-d') : '') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               >
                        @error('customer_birthday')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_address" class="block text-sm font-medium text-gray-700 mb-2">
                            Địa chỉ
                        </label>
                        <input type="text" name="customer_address" id="customer_address"
                               value="{{ old('customer_address', $order->customer->address ?? '') }}"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Nhập địa chỉ">
                        @error('customer_address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Ghi chú đơn hàng
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Ghi chú đặc biệt cho đơn hàng...">{{ old('notes', $order->notes) }}</textarea>
                    @error('notes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Customer Info Display -->
                <div id="customerInfoDisplay" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg
                    @if($order->customer && ($order->customer->isBirthdayToday() || $order->customer->isBirthdayThisWeek() || $order->customer->isBirthdayThisMonth()))
                    @else hidden @endif">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Thông tin khách hàng</h4>
                    <div id="customerInfoContent" class="text-sm text-blue-800">
                        @if($order->customer)
                            @if($order->customer->isBirthdayToday())
                                <div class="bg-yellow-100 text-yellow-800 p-2 rounded mb-2 text-center">
                                    🎂 <strong>Hôm nay là sinh nhật khách hàng!</strong> Có thể áp dụng khuyến mại sinh nhật.
                                </div>
                            @elseif($order->customer->isBirthdayThisWeek())
                                <div class="bg-blue-100 text-blue-800 p-2 rounded mb-2 text-center">
                                    🎉 Sinh nhật khách hàng trong tuần này ({{ $order->customer->formatted_birthday }})
                                </div>
                            @elseif($order->customer->isBirthdayThisMonth())
                                <div class="bg-green-100 text-green-800 p-2 rounded mb-2 text-center">
                                    🎈 Sinh nhật khách hàng trong tháng này ({{ $order->customer->formatted_birthday }})
                                </div>
                            @endif
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <strong>Tổng đơn hàng:</strong> {{ $order->customer->getTotalOrders() }}
                                </div>
                                <div>
                                    <strong>Tổng chi tiêu:</strong> {{ $order->customer->getFormattedTotalSpentAttribute() }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Order Items -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Món ăn hiện tại</h3>
                <p class="mt-1 text-sm text-gray-600">Chỉnh sửa số lượng hoặc xóa món ăn</p>
            </div>
            <div class="p-6">
                <div class="space-y-4" id="currentItemsList">
                    @foreach($order->orderItems as $index => $item)
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center space-x-4">
                                @if($item->food->image_id)
                                    <img class="h-16 w-16 rounded-lg object-cover flex-shrink-0"
                                         src="{{ \App\Helper\helper::getImageGoogleDrive($item->food->image_id) }}"
                                         alt="{{ $item->food->name }}">
                                @else
                                    <div class="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-utensils text-gray-400"></i>
                                    </div>
                                @endif
                                <div class="flex-1">
                                    <h5 class="text-sm font-medium text-gray-900">{{ $item->food->name }}</h5>
                                    <p class="text-sm text-gray-500">{{ $item->formatted_unit_price }}</p>
                                    @if($item->notes)
                                        <p class="text-xs text-gray-500 mt-1">Ghi chú: {{ $item->notes }}</p>
                                    @endif
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <button type="button" onclick="decreaseQuantity({{ $index }})"
                                            class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200">
                                        <i class="fas fa-minus text-xs"></i>
                                    </button>
                                    <input type="number" name="items[{{ $index }}][quantity]"
                                           value="{{ $item->quantity }}" min="0"
                                           class="w-16 text-center border border-gray-300 rounded px-2 py-1"
                                           onchange="updateItemTotal({{ $index }})">
                                    <button type="button" onclick="increaseQuantity({{ $index }})"
                                            class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200">
                                        <i class="fas fa-plus text-xs"></i>
                                    </button>
                                </div>
                                <div class="text-sm font-semibold text-gray-900 w-24 text-right" id="total-{{ $index }}">
                                    {{ $item->formatted_total_price }}
                                </div>
                                <button type="button" onclick="removeItem({{ $index }})"
                                        class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>

                            <!-- Hidden inputs -->
                            <input type="hidden" name="items[{{ $index }}][id]" value="{{ $item->id }}">
                            <input type="hidden" name="items[{{ $index }}][food_id]" value="{{ $item->food_id }}">
                            <input type="hidden" name="items[{{ $index }}][unit_price]" value="{{ $item->unit_price }}" id="price-{{ $index }}">
                            <input type="hidden" name="items[{{ $index }}][notes]" value="{{ $item->notes }}">
                        </div>
                    @endforeach
                </div>

                <!-- Order Summary -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-semibold text-gray-900">Tổng cộng:</span>
                        <span class="text-xl font-bold text-indigo-600" id="totalAmount">{{ $order->formatted_total_amount }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add More Items -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thêm món ăn</h3>
                <p class="mt-1 text-sm text-gray-600">Nhấp vào món ăn để thêm vào đơn hàng</p>
            </div>
            <div class="p-6">
                @foreach($categories as $categoryName => $categoryFoods)
                    <div class="mb-8">
                        <h4 class="text-md font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                            {{ $categoryName ?: 'Chưa phân loại' }}
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($categoryFoods as $food)
                                <div class="food-item border border-gray-200 rounded-lg p-4 cursor-pointer"
                                     onclick="addNewFood({{ $food->id }}, '{{ addslashes($food->name) }}', {{ $food->price }})">
                                    <div class="flex items-center space-x-4">
                                        @if($food->image_id)
                                            <img class="h-16 w-16 rounded-lg object-cover flex-shrink-0"
                                                 src="{{ \App\Helper\helper::getImageGoogleDrive($food->image_id) }}"
                                                 alt="{{ $food->name }}">
                                        @else
                                            <div class="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                                                <i class="fas fa-utensils text-gray-400"></i>
                                            </div>
                                        @endif
                                        <div class="flex-1 min-w-0">
                                            <h5 class="text-sm font-medium text-gray-900 truncate">{{ $food->name }}</h5>
                                            <p class="text-sm text-gray-500 truncate">{{ Str::limit($food->description, 50) }}</p>
                                            <p class="text-sm font-semibold text-indigo-600">{{ $food->formatted_price }}</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-plus text-indigo-600"></i>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex items-center justify-end space-x-4">
            <a href="{{ route('admin.orders.show', $order) }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-times mr-2"></i>
                Hủy
            </a>
            <button type="submit"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-save mr-2"></i>
                Cập nhật đơn hàng
            </button>
        </div>
    </form>
@endsection

@push('scripts')
    <script>
        let nextIndex = {{ $order->orderItems->count() }};
        let customerSuggestions = [];
        let selectedSuggestionIndex = -1;

        // Customer search functionality
        let searchTimeout;
        function searchCustomers(query) {
            clearTimeout(searchTimeout);

            if (query.length < 3) {
                hideCustomerSuggestions();
                return;
            }

            searchTimeout = setTimeout(() => {
                fetch(`/api/customers/search?query=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(customers => {
                        displayCustomerSuggestions(customers);
                    })
                    .catch(error => {
                        console.error('Error searching customers:', error);
                        hideCustomerSuggestions();
                    });
            }, 300);
        }

        function displayCustomerSuggestions(customers) {
            const suggestionsContainer = document.getElementById('customerSuggestions');

            if (customers.length === 0) {
                hideCustomerSuggestions();
                return;
            }

            customerSuggestions = customers;
            selectedSuggestionIndex = -1;

            let html = '';
            customers.forEach((customer, index) => {
                const birthdayBadge = customer.is_birthday_today ? '<span class="birthday-badge ml-2">🎂 Sinh nhật hôm nay!</span>' :
                    customer.is_birthday_this_week ? '<span class="birthday-badge ml-2">🎉 Sinh nhật tuần này</span>' :
                        customer.is_birthday_this_month ? '<span class="birthday-badge ml-2">🎈 Sinh nhật tháng này</span>' : '';

                html += `
            <div class="customer-suggestion-item" data-index="${index}" onclick="selectCustomer(${index})">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="font-medium text-gray-900">${customer.name}</div>
                        <div class="text-sm text-gray-600">${customer.phone}</div>
                        ${customer.email ? `<div class="text-sm text-gray-500">${customer.email}</div>` : ''}
                        ${customer.formatted_birthday ? `<div class="text-xs text-gray-500">Sinh nhật: ${customer.formatted_birthday}</div>` : ''}
                    </div>
                    <div class="text-right">
                        <div class="text-xs text-gray-500">${customer.total_orders} đơn hàng</div>
                        <div class="text-xs text-green-600 font-medium">${customer.total_spent}</div>
                        ${birthdayBadge}
                    </div>
                </div>
            </div>
        `;
            });

            suggestionsContainer.innerHTML = html;
            suggestionsContainer.classList.remove('hidden');
        }

        function hideCustomerSuggestions() {
            document.getElementById('customerSuggestions').classList.add('hidden');
            customerSuggestions = [];
            selectedSuggestionIndex = -1;
        }

        function selectCustomer(index) {
            const customer = customerSuggestions[index];

            // Fill form fields
            document.getElementById('customer_phone').value = customer.phone;
            document.getElementById('customer_name').value = customer.name;
            document.getElementById('customer_email').value = customer.email || '';
            document.getElementById('customer_birthday').value = customer.birthday || '';
            document.getElementById('customer_address').value = customer.address || '';

            // Show customer info
            displayCustomerInfo(customer);

            hideCustomerSuggestions();
        }

        function displayCustomerInfo(customer) {
            const infoDisplay = document.getElementById('customerInfoDisplay');
            const infoContent = document.getElementById('customerInfoContent');

            let html = `
        <div class="grid grid-cols-2 gap-4">
            <div>
                <strong>Tổng đơn hàng:</strong> ${customer.total_orders}
            </div>
            <div>
                <strong>Tổng chi tiêu:</strong> ${customer.total_spent}
            </div>
        </div>
    `;

            if (customer.is_birthday_today) {
                html = `<div class="bg-yellow-100 text-yellow-800 p-2 rounded mb-2 text-center">
            🎂 <strong>Hôm nay là sinh nhật khách hàng!</strong> Có thể áp dụng khuyến mại sinh nhật.
        </div>` + html;
            } else if (customer.is_birthday_this_week) {
                html = `<div class="bg-blue-100 text-blue-800 p-2 rounded mb-2 text-center">
            🎉 Sinh nhật khách hàng trong tuần này (${customer.formatted_birthday})
        </div>` + html;
            } else if (customer.is_birthday_this_month) {
                html = `<div class="bg-green-100 text-green-800 p-2 rounded mb-2 text-center">
            🎈 Sinh nhật khách hàng trong tháng này (${customer.formatted_birthday})
        </div>` + html;
            }

            infoContent.innerHTML = html;
            infoDisplay.classList.remove('hidden');
        }

        function handleCustomerNavigation(event) {
            const suggestions = document.querySelectorAll('.customer-suggestion-item');

            if (suggestions.length === 0) return;

            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    selectedSuggestionIndex = Math.min(selectedSuggestionIndex + 1, suggestions.length - 1);
                    updateSuggestionSelection();
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    selectedSuggestionIndex = Math.max(selectedSuggestionIndex - 1, -1);
                    updateSuggestionSelection();
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (selectedSuggestionIndex >= 0) {
                        selectCustomer(selectedSuggestionIndex);
                    }
                    break;
                case 'Escape':
                    hideCustomerSuggestions();
                    break;
            }
        }

        function updateSuggestionSelection() {
            const suggestions = document.querySelectorAll('.customer-suggestion-item');
            suggestions.forEach((item, index) => {
                if (index === selectedSuggestionIndex) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(event) {
            const phoneInput = document.getElementById('customer_phone');
            const suggestions = document.getElementById('customerSuggestions');

            if (!phoneInput.contains(event.target) && !suggestions.contains(event.target)) {
                hideCustomerSuggestions();
            }
        });

        function increaseQuantity(index) {
            const input = document.querySelector(`input[name="items[${index}][quantity]"]`);
            input.value = parseInt(input.value) + 1;
            updateItemTotal(index);
        }

        function decreaseQuantity(index) {
            const input = document.querySelector(`input[name="items[${index}][quantity]"]`);
            if (parseInt(input.value) > 0) {
                input.value = parseInt(input.value) - 1;
                updateItemTotal(index);
            }
        }

        function updateItemTotal(index) {
            const quantityInput = document.querySelector(`input[name="items[${index}][quantity]"]`);
            const priceInput = document.getElementById(`price-${index}`);
            const totalElement = document.getElementById(`total-${index}`);

            const quantity = parseInt(quantityInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            const total = quantity * price;

            totalElement.textContent = formatPrice(total);
            updateGrandTotal();
        }

        function removeItem(index) {
            if (confirm('Bạn có chắc chắn muốn xóa món này?')) {
                const quantityInput = document.querySelector(`input[name="items[${index}][quantity]"]`);
                quantityInput.value = 0;
                updateItemTotal(index);

                // Hide the item visually
                const itemElement = quantityInput.closest('.flex');
                itemElement.style.opacity = '0.5';
                itemElement.style.pointerEvents = 'none';
            }
        }

        function addNewFood(foodId, foodName, price) {
            // Check if food already exists in current items
            const existingInputs = document.querySelectorAll('input[name*="[food_id]"]');
            for (let input of existingInputs) {
                if (input.value == foodId) {
                    const index = input.name.match(/\[(\d+)\]/)[1];
                    const quantityInput = document.querySelector(`input[name="items[${index}][quantity]"]`);
                    quantityInput.value = parseInt(quantityInput.value) + 1;
                    updateItemTotal(index);
                    return;
                }
            }

            // Add new item
            const container = document.getElementById('currentItemsList');
            const newItem = document.createElement('div');
            newItem.className = 'flex items-center justify-between p-4 border border-gray-200 rounded-lg';
            newItem.innerHTML = `
        <div class="flex items-center space-x-4">
            <div class="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                <i class="fas fa-utensils text-gray-400"></i>
            </div>
            <div class="flex-1">
                <h5 class="text-sm font-medium text-gray-900">${foodName}</h5>
                <p class="text-sm text-gray-500">${formatPrice(price)}</p>
                <p class="text-xs text-green-600 mt-1">Món mới thêm</p>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <button type="button" onclick="decreaseQuantity(${nextIndex})"
                        class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200">
                    <i class="fas fa-minus text-xs"></i>
                </button>
                <input type="number" name="items[${nextIndex}][quantity]"
                       value="1" min="0"
                       class="w-16 text-center border border-gray-300 rounded px-2 py-1"
                       onchange="updateItemTotal(${nextIndex})">
                <button type="button" onclick="increaseQuantity(${nextIndex})"
                        class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200">
                    <i class="fas fa-plus text-xs"></i>
                </button>
            </div>
            <div class="text-sm font-semibold text-gray-900 w-24 text-right" id="total-${nextIndex}">
                ${formatPrice(price)}
            </div>
            <button type="button" onclick="removeItem(${nextIndex})"
                    class="text-red-600 hover:text-red-900">
                <i class="fas fa-trash"></i>
            </button>
        </div>

        <input type="hidden" name="items[${nextIndex}][food_id]" value="${foodId}">
        <input type="hidden" name="items[${nextIndex}][unit_price]" value="${price}" id="price-${nextIndex}">
        <input type="hidden" name="items[${nextIndex}][notes]" value="">
    `;

            container.appendChild(newItem);
            nextIndex++;
            updateGrandTotal();
        }

        function updateGrandTotal() {
            let total = 0;
            const quantityInputs = document.querySelectorAll('input[name*="[quantity]"]');

            quantityInputs.forEach((input, index) => {
                const quantity = parseInt(input.value) || 0;
                const priceInput = document.getElementById(`price-${index}`);
                if (priceInput) {
                    const price = parseFloat(priceInput.value) || 0;
                    total += quantity * price;
                }
            });

            document.getElementById('totalAmount').textContent = formatPrice(total);
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(price);
        }

        // Table selection handling (Same as Create Order)
        let selectedTables = @json($selectedTableIds);
        let isDropdownOpen = false;

        // Table data for calculations
        const tableData = {
            @foreach($tables as $table)
                {{ $table->id }}: {
                name: '{{ $table->name }}',
                capacity: {{ $table->capacity }},
                location: '{{ $table->location }}',
                status: '{{ $table->status == 0 ? 'available' : 'occupied' }}'
            },
            @endforeach
        };

        function toggleTableDropdown() {
            const dropdown = document.getElementById('tableDropdown');
            isDropdownOpen = !isDropdownOpen;

            if (isDropdownOpen) {
                dropdown.classList.remove('hidden');
                document.addEventListener('click', closeDropdownOnOutsideClick);
            } else {
                dropdown.classList.add('hidden');
                document.removeEventListener('click', closeDropdownOnOutsideClick);
            }
        }

        function closeDropdownOnOutsideClick(event) {
            const dropdown = document.getElementById('tableDropdown');
            const button = document.getElementById('tableDropdownBtn');

            if (!dropdown.contains(event.target) && !button.contains(event.target)) {
                dropdown.classList.add('hidden');
                isDropdownOpen = false;
                document.removeEventListener('click', closeDropdownOnOutsideClick);
            }
        }

        function toggleTableSelection(tableId) {
            const checkbox = document.getElementById(`table_${tableId}`);
            checkbox.checked = !checkbox.checked;

            if (checkbox.checked) {
                if (!selectedTables.includes(tableId)) {
                    selectedTables.push(tableId);
                }
            } else {
                selectedTables = selectedTables.filter(id => id !== tableId);
            }

            updateTableDisplay();
        }

        function selectAllAvailable() {
            // Clear current selection
            selectedTables = [];

            // Select all available tables
            Object.keys(tableData).forEach(tableId => {
                if (tableData[tableId].status === 'available') {
                    selectedTables.push(parseInt(tableId));
                }
            });

            updateTableDisplay();
        }

        function clearSelection() {
            selectedTables = [];
            updateTableDisplay();
        }

        function filterDropdownTables() {
            const searchTerm = document.getElementById('tableSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const tableOptions = document.querySelectorAll('.table-option');

            tableOptions.forEach(option => {
                const tableName = option.dataset.tableName.toLowerCase();
                const tableLocation = option.dataset.tableLocation.toLowerCase();
                const tableStatus = option.dataset.tableStatus;

                const matchesSearch = tableName.includes(searchTerm) || tableLocation.includes(searchTerm);
                const matchesStatus = !statusFilter || tableStatus === statusFilter;

                if (matchesSearch && matchesStatus) {
                    option.style.display = 'block';
                } else {
                    option.style.display = 'none';
                }
            });
        }

        function updateTableDisplay() {
            // Update checkboxes
            document.querySelectorAll('.table-checkbox').forEach(checkbox => {
                const tableId = parseInt(checkbox.id.replace('table_', ''));
                checkbox.checked = selectedTables.includes(tableId);
            });

            // Update dropdown options
            document.querySelectorAll('.table-option').forEach(option => {
                const tableId = parseInt(option.dataset.tableId);
                if (selectedTables.includes(tableId)) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });

            // Update dropdown button text
            updateDropdownButtonText();

            // Update tags display
            updateSelectedTablesTags();

            // Update summary
            updateTableSummary();

            // Update hidden inputs
            updateHiddenInputs();
        }

        function updateDropdownButtonText() {
            const dropdownText = document.getElementById('dropdownText');

            if (selectedTables.length === 0) {
                dropdownText.textContent = 'Chọn bàn...';
                dropdownText.className = 'text-gray-500';
            } else {
                const tableNames = selectedTables.map(id => tableData[id].name).join(', ');
                dropdownText.textContent = `Đã chọn ${selectedTables.length} bàn: ${tableNames}`;
                dropdownText.className = 'text-gray-900';
            }
        }

        function updateSelectedTablesTags() {
            const tagsContainer = document.getElementById('selectedTablesTags');
            tagsContainer.innerHTML = '';

            if (selectedTables.length > 0) {
                tagsContainer.style.display = 'flex';

                selectedTables.forEach(tableId => {
                    const table = tableData[tableId];
                    const tag = document.createElement('span');
                    tag.className = 'table-tag';
                    tag.innerHTML = `
                ${table.name}
                <span class="remove-btn" onclick="removeTableFromSelection(${tableId})">×</span>
            `;
                    tagsContainer.appendChild(tag);
                });
            } else {
                tagsContainer.style.display = 'none';
            }
        }

        function removeTableFromSelection(tableId) {
            selectedTables = selectedTables.filter(id => id !== tableId);
            updateTableDisplay();
        }

        function updateTableSummary() {
            const summaryContainer = document.getElementById('selectedTablesSummary');
            const selectedTablesText = document.getElementById('selectedTablesText');
            const totalCapacitySpan = document.getElementById('totalCapacity');

            if (selectedTables.length > 0) {
                summaryContainer.style.display = 'block';

                const totalCapacity = selectedTables.reduce((sum, tableId) => {
                    return sum + tableData[tableId].capacity;
                }, 0);

                selectedTablesText.textContent = `Đã chọn ${selectedTables.length} bàn`;
                totalCapacitySpan.textContent = totalCapacity;
            } else {
                summaryContainer.style.display = 'none';
            }
        }

        function updateHiddenInputs() {
            const container = document.getElementById('hiddenTableInputs');
            container.innerHTML = '';

            selectedTables.forEach(tableId => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'table_ids[]';
                input.value = tableId;
                container.appendChild(input);
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateTableDisplay();
            updateGrandTotal();
        });
    </script>
@endpush
