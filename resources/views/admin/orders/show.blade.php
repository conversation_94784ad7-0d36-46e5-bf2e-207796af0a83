@extends('layouts.admin')

@section('title', '<PERSON> tiết đơn hàng #' . $order->order_number)

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.orders.index') }}" class="text-gray-500 hover:text-gray-700">Đơn hàng</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">{{ $order->order_number }}</span>
        </div>
    </li>
@endsection

@section('content-header-actions')
    <div class="flex items-center space-x-3">
        <a href="{{ route('orders.print', $order) }}" target="_blank"
           class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
            <i class="fas fa-print mr-2"></i>
            In
        </a>
        @if( in_array($order->status, [3, 4]) && $order->payment_status == 0)
            <a href="{{ route('admin.orders.payment', $order) }}"
               class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                <i class="fas fa-credit-card mr-2"></i>
                Thanh toán
            </a>
        @endif

        <a href="{{ route('admin.orders.edit', $order) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <i class="fas fa-edit mr-2"></i>
            Chỉnh sửa
        </a>

        <a href="{{ route('customer.ipad', $order) }}"
           target="_blank"
           class="inline-flex items-center px-4 py-2 border border-purple-300 rounded-md shadow-sm text-sm font-medium text-purple-700 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
            <i class="fas fa-tablet-alt mr-2"></i>
            iPad Interface
        </a>

        @if($order->cancelable())
            <form action="{{ route('admin.orders.cancel', $order) }}" method="POST" class="inline"
                  onsubmit="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')">
                @csrf
                @method('PATCH')
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <i class="fas fa-times mr-2"></i>
                    Hủy đơn
                </button>
            </form>
        @endif
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Order Status Update -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Cập nhật trạng thái</h3>
            </div>
            <div class="p-6">
                <form action="{{ route('admin.orders.update-status', $order) }}" method="POST"
                      class="flex items-center space-x-4">
                    @csrf
                    @method('PATCH')
                    <select name="status" required
                            class="block px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="0" {{ $order->status == 0 ? 'selected' : '' }}>Chờ xác nhận</option>
                        <option value="1" {{ $order->status == 1 ? 'selected' : '' }}>Đã xác nhận</option>
                        <option value="2" {{ $order->status == 2 ? 'selected' : '' }}>Đang chuẩn bị</option>
                        <option value="3" {{ $order->status == 3 ? 'selected' : '' }}>Sẵn sàng</option>
                        <option value="4" {{ $order->status == 4 ? 'selected' : '' }}>Hoàn thành</option>
                    </select>
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-save mr-2"></i>
                        Cập nhật
                    </button>
                </form>
            </div>
        </div>

        <!-- Order Type Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Loại đơn hàng</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 mb-2">Loại khách hàng</dt>
                        <dd class="mt-1">
                            @if($order->customer_type === 'individual')
                                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-user mr-2"></i>Khách lẻ
                                </span>
                                <p class="text-xs text-gray-500 mt-1">Khách hàng đến trực tiếp</p>
                            @elseif($order->customer_type === 'breakfast-room')
                                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-home mr-2"></i>Ăn sáng kèm đặt phòng
                                </span>
                                <p class="text-xs text-gray-500 mt-1">Khách có đặt phòng - Miễn phí các món ăn sáng</p>
                                @if($order->breakfast_ticket_id)
                                    <div class="mt-2 text-sm">
                                        <span class="text-gray-500">Order Room ID:</span>
                                        <span class="font-mono text-blue-600 bg-blue-50 px-2 py-1 rounded">{{ $order->breakfast_ticket_id }}</span>
                                    </div>
                                @endif
                            @else
                                <span class="text-gray-400">Chưa xác định</span>
                            @endif
                        </dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500 mb-2">Hình thức phục vụ</dt>
                        <dd class="mt-1">
                            @if($order->service_type === 'buffet')
                                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                                    <i class="fas fa-coffee mr-2"></i>Buffet
                                </span>
                                <p class="text-xs text-gray-500 mt-1">Món buffet miễn phí, món ngoài menu tính tiền bình thường</p>
                            @elseif($order->service_type === 'ala-carte')
                                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                    <i class="fas fa-list mr-2"></i>Gọi món lẻ
                                </span>
                                <p class="text-xs text-gray-500 mt-1">Tất cả món đều tính tiền theo menu</p>
                            @else
                                <span class="text-gray-400">Chưa xác định</span>
                            @endif
                        </dd>
                    </div>
                </div>

                <!-- Pricing Summary Info -->
                @if($order->service_type === 'buffet' || $order->customer_type === 'breakfast-room')
                    <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-info-circle text-amber-600 mt-0.5"></i>
                            <div>
                                <h5 class="font-medium text-amber-800">Thông tin miễn phí</h5>
                                <div class="text-sm text-amber-700 mt-1">
                                    @if($order->service_type === 'buffet' && $order->customer_type === 'breakfast-room')
                                        <p>• Khách đặt phòng: Miễn phí tất cả món ăn sáng</p>
                                        <p>• Buffet: Miễn phí tất cả món thuộc danh mục buffet</p>
                                    @elseif($order->service_type === 'buffet')
                                        <p>• Buffet: Miễn phí tất cả món thuộc danh mục buffet</p>
                                    @elseif($order->customer_type === 'breakfast-room')
                                        <p>• Khách đặt phòng: Miễn phí tất cả món ăn sáng</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Order Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thông tin đơn hàng</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Mã đơn hàng</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $order->order_number }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Trạng thái</dt>
                        <dd class="mt-1">
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $order->status_color }}">
                                {{ $order->status_text }}
                            </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Thanh toán</dt>
                        <dd class="mt-1">
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $order->payment_status_color }}">
                                {{ $order->payment_status_text }}
                            </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Bàn</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            @php
                                $allTables = $order->tables->count() > 0 ? $order->tables : collect([$order->table])->filter();
                                $totalCapacity = $allTables->sum('capacity');
                            @endphp

                            @if($allTables->count() > 0)
                                <div class="flex flex-wrap gap-2">
                                    @foreach($allTables as $table)
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ $table->name }}
                                            <span class="ml-1 text-blue-600">({{ $table->capacity }})</span>
                                        </span>
                                    @endforeach
                                </div>
                                @if($allTables->count() > 1)
                                    <div class="mt-1 text-xs text-gray-500">
                                        Tổng: {{ $allTables->count() }} bàn • {{ $totalCapacity }} chỗ ngồi
                                    </div>
                                @endif
                            @else
                                <span class="text-gray-400">Mang về</span>
                            @endif
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Nhân viên tạo</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $order->user->name ?? 'Hệ thống' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Thời gian đặt</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $order->order_time->format('d/m/Y H:i') }}</dd>
                    </div>
                    @if($order->completed_at)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Thời gian hoàn thành</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order->completed_at->format('d/m/Y H:i') }}</dd>
                        </div>
                    @endif
                    @if($order->payment_method)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Phương thức thanh toán</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order->payment_method }}</dd>
                        </div>
                    @endif
                </div>

                @if($order->notes)
                    <div class="mt-6">
                        <dt class="text-sm font-medium text-gray-500">Ghi chú</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $order->notes }}</dd>
                    </div>
                @endif
            </div>
        </div>

        <!-- Customer Information -->
        @if($order->customer)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin khách hàng</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Tên khách hàng</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order->customer->name }}</dd>
                        </div>
                        @if($order->customer->phone)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Số điện thoại</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $order->customer->formatted_phone }}</dd>
                            </div>
                        @endif
                        @if($order->customer->email)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Email</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $order->customer->email }}</dd>
                            </div>
                        @endif
                        @if($order->customer->address)
                            <div class="md:col-span-2 lg:col-span-3">
                                <dt class="text-sm font-medium text-gray-500">Địa chỉ</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $order->customer->address }}</dd>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        <!-- Order Items -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Chi tiết món ăn</h3>
                    <div class="flex items-center space-x-2">
                        <button onclick="toggleBulkEdit()" id="bulkEditBtn"
                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-edit mr-2"></i>
                            Chỉnh sửa hàng loạt
                        </button>
                        <button onclick="saveAllChanges()" id="saveAllBtn"
                                class="hidden inline-flex items-center px-3 py-1.5 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-save mr-2"></i>
                            Lưu tất cả
                        </button>
                        <button onclick="cancelAllChanges()" id="cancelAllBtn"
                                class="hidden inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            <i class="fas fa-times mr-2"></i>
                            Hủy
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="selectAll"
                                   class="bulk-select-element hidden h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Món
                            ăn
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Danh
                            mục
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng
                            thái
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Đơn
                            giá
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số
                            lượng
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thành
                            tiền
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao
                            tác
                        </th>
                    </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($order->orderItems as $item)
                        <tr data-item-id="{{ $item->id }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="item_ids[]" value="{{ $item->id }}"
                                       class="bulk-select-element item-checkbox hidden h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    @if($item->food->image_id)
                                        <img class="h-12 w-12 rounded-lg object-cover mr-4"
                                             src="{{ \App\Helper\helper::getImageGoogleDrive($item->food->image_id) }}"
                                             alt="{{ $item->food->name }}">
                                    @else
                                        <div
                                            class="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center mr-4">
                                            <i class="fas fa-utensils text-gray-400"></i>
                                        </div>
                                    @endif
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $item->food->name }}</div>
                                        @if($item->notes)
                                            <div class="text-sm text-gray-500">Ghi chú: {{ $item->notes }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="space-y-1">
                                    @foreach($item->food->categories as $category)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($category->name === 'buffet')
                                                bg-orange-100 text-orange-800
                                            @elseif($category->breakfast_with_room)
                                                bg-green-100 text-green-800
                                            @else
                                                bg-blue-100 text-blue-800
                                            @endif">
                                            @if($category->name === 'buffet')
                                                <i class="fas fa-coffee mr-1"></i>
                                            @elseif($category->breakfast_with_room)
                                                <i class="fas fa-home mr-1"></i>
                                            @endif
                                            {{ $category->name }}
                                        </span>
                                    @endforeach
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <!-- Display Mode -->
                                <div class="status-display-{{ $item->id }}">
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $item->status_color }}">
                                {{ $item->status_text }}
                            </span>
                                </div>
                                <!-- Edit Mode -->
                                <div class="status-edit-{{ $item->id }} hidden">
                                    <select name="status"
                                            class="status-select text-xs border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                            data-item-id="{{ $item->id }}" data-original-value="{{ $item->status }}">
                                        <option value="0" {{ $item->status == 0 ? 'selected' : '' }}>Chờ chế biến
                                        </option>
                                        <option value="1" {{ $item->status == 1 ? 'selected' : '' }}>Đang chế biến
                                        </option>
                                        <option value="2" {{ $item->status == 2 ? 'selected' : '' }}>Sẵn sàng</option>
                                        <option value="4" {{ $item->status == 4 ? 'selected' : '' }}>Hoàn thành</option>
                                    </select>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <!-- Display Mode -->
                                <div class="price-display-{{ $item->id }} text-sm text-gray-900">
                                    @if($item->isFreeItem())
                                        <span class="text-green-600 font-medium">Miễn phí</span>
                                        <div class="text-xs text-gray-500">{{ $item->formatted_unit_price }}</div>
                                    @else
                                        {{ $item->formatted_unit_price }}
                                    @endif
                                </div>
                                <!-- Edit Mode -->
                                <div class="price-edit-{{ $item->id }} hidden">
                                    <input type="number" name="unit_price"
                                           class="price-input w-24 text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                           data-item-id="{{ $item->id }}"
                                           data-original-value="{{ $item->unit_price }}"
                                           value="{{ $item->unit_price }}"
                                           min="0"
                                           step="1000">
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="quantity-{{ $item->id }}">{{ $item->quantity }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <span class="total-price-{{ $item->id }}">
                                    @if($item->isFreeItem())
                                        <span class="text-green-600 font-medium">Miễn phí</span>
                                    @else
                                        {{ $item->formatted_total_price }}
                                    @endif
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <!-- Single Edit Mode -->
                                <div class="single-edit-actions-{{ $item->id }}">
                                    <button onclick="editSingleItem({{ $item->id }})"
                                            class="text-indigo-600 hover:text-indigo-900 mr-2">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                                <!-- Single Edit Save/Cancel -->
                                <div class="single-save-actions-{{ $item->id }} hidden">
                                    <button onclick="saveSingleItem({{ $item->id }})"
                                            class="text-green-600 hover:text-green-800 mr-2">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button onclick="cancelSingleEdit({{ $item->id }})"
                                            class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        <!-- Order Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Tổng kết đơn hàng</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Tạm tính:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $order->formatted_subtotal }}</span>
                    </div>

                    @if($order->discount_amount > 0)
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">
                                Giảm giá
                                @if($order->discount_code)
                                    <span class="text-xs text-gray-500">({{ $order->discount_code }})</span>
                                @endif
                                :
                            </span>
                            <span
                                class="text-sm font-medium text-green-600">-{{ $order->formatted_discount_amount }}</span>
                        </div>
                    @endif

                    <!-- Free Items Summary -->
                    @if($order->service_type === 'buffet' || $order->customer_type === 'breakfast-room')
                        @php
                            $freeItemsCount = $order->orderItems->where('total_price', 0)->sum('quantity');
                            $freeItemsValue = $order->orderItems->where('total_price', 0)->sum(function($item) {
                                return $item->unit_price * $item->quantity;
                            });
                        @endphp

                        @if($freeItemsCount > 0)
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">
                                    Món miễn phí ({{ $freeItemsCount }} món):
                                </span>
                                <span class="text-sm font-medium text-green-600">
                                    -{{ number_format($freeItemsValue, 0, ',', '.') }}đ
                                </span>
                            </div>
                        @endif
                    @endif

                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-semibold text-gray-900">Tổng cộng:</span>
                            <span class="text-xl font-bold text-indigo-600">{{ $order->formatted_total_amount }}</span>
                        </div>
                    </div>

                    <!-- Payment breakdown for complex orders -->
                    @if(($order->service_type === 'buffet' || $order->customer_type === 'breakfast-room') && $freeItemsCount > 0)
                        <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                            <h5 class="text-xs font-medium text-gray-700 uppercase tracking-wider mb-2">Chi tiết thanh toán</h5>
                            <div class="text-xs text-gray-600 space-y-1">
                                <div class="flex justify-between">
                                    <span>Món tính phí: {{ $order->orderItems->where('total_price', '>', 0)->sum('quantity') }} món</span>
                                    <span>{{ number_format($order->orderItems->where('total_price', '>', 0)->sum('total_price'), 0, ',', '.') }}đ</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Món miễn phí: {{ $freeItemsCount }} món</span>
                                    <span class="text-green-600">0đ</span>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script>
        let bulkEditMode = false;
        let changedItems = new Set();

        // Format currency for display
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        // Calculate and update total price for an item
        function updateTotalPrice(itemId) {
            const priceInput = document.querySelector(`input[data-item-id="${itemId}"]`);
            const quantitySpan = document.querySelector(`.quantity-${itemId}`);
            const totalSpan = document.querySelector(`.total-price-${itemId}`);

            if (priceInput && quantitySpan && totalSpan) {
                const unitPrice = parseFloat(priceInput.value) || 0;
                const quantity = parseInt(quantitySpan.textContent) || 0;
                const total = unitPrice * quantity;

                totalSpan.textContent = formatCurrency(total);
            }
        }

        // Single item edit functions
        function editSingleItem(itemId) {
            document.querySelector(`.status-display-${itemId}`).classList.add('hidden');
            document.querySelector(`.status-edit-${itemId}`).classList.remove('hidden');
            document.querySelector(`.price-display-${itemId}`).classList.add('hidden');
            document.querySelector(`.price-edit-${itemId}`).classList.remove('hidden');
            document.querySelector(`.single-edit-actions-${itemId}`).classList.add('hidden');
            document.querySelector(`.single-save-actions-${itemId}`).classList.remove('hidden');

            // Add event listener for price change
            const priceInput = document.querySelector(`.price-input[data-item-id="${itemId}"]`);
            if (priceInput) {
                priceInput.addEventListener('input', () => updateTotalPrice(itemId));
            }
        }

        function cancelSingleEdit(itemId) {
            // Restore original values
            const priceInput = document.querySelector(`.price-input[data-item-id="${itemId}"]`);
            const statusSelect = document.querySelector(`.status-select[data-item-id="${itemId}"]`);

            if (priceInput) {
                priceInput.value = priceInput.dataset.originalValue;
                updateTotalPrice(itemId);
            }
            if (statusSelect) {
                statusSelect.value = statusSelect.dataset.originalValue;
            }

            // Switch back to display mode
            document.querySelector(`.status-display-${itemId}`).classList.remove('hidden');
            document.querySelector(`.status-edit-${itemId}`).classList.add('hidden');
            document.querySelector(`.price-display-${itemId}`).classList.remove('hidden');
            document.querySelector(`.price-edit-${itemId}`).classList.add('hidden');
            document.querySelector(`.single-edit-actions-${itemId}`).classList.remove('hidden');
            document.querySelector(`.single-save-actions-${itemId}`).classList.add('hidden');
        }

        function saveSingleItem(itemId) {
            const priceInput = document.querySelector(`.price-input[data-item-id="${itemId}"]`);
            const statusSelect = document.querySelector(`.status-select[data-item-id="${itemId}"]`);

            // Prepare data for AJAX request
            const data = {
                _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                _method: 'PATCH'
            };

            if (priceInput && priceInput.value !== priceInput.dataset.originalValue) {
                data.unit_price = priceInput.value;
            }

            if (statusSelect && statusSelect.value !== statusSelect.dataset.originalValue) {
                data.status = statusSelect.value;
            }

            // Send AJAX request
            fetch(`/admin/order-items/${itemId}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': data._token
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // Update display values
                        if (data.unit_price) {
                            document.querySelector(`.price-display-${itemId}`).textContent = formatCurrency(data.unit_price);
                            priceInput.dataset.originalValue = data.unit_price;
                        }

                        if (data.status !== undefined) {
                            const statusOptions = {
                                0: {text: 'Chờ chế biến', class: 'bg-yellow-100 text-yellow-800'},
                                1: {text: 'Đang chế biến', class: 'bg-blue-100 text-blue-800'},
                                2: {text: 'Sẵn sàng', class: 'bg-green-100 text-green-800'},
                                4: {text: 'Hoàn thành', class: 'bg-gray-100 text-gray-800'}
                            };

                            const statusSpan = document.querySelector(`.status-display-${itemId} span`);
                            statusSpan.textContent = statusOptions[data.status].text;
                            statusSpan.className = `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusOptions[data.status].class}`;
                            statusSelect.dataset.originalValue = data.status;
                        }

                        // Switch back to display mode
                        cancelSingleEdit(itemId);

                        // Show success message
                        showNotification('Cập nhật thành công!', 'success');
                    } else {
                        showNotification('Có lỗi xảy ra: ' + result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Có lỗi xảy ra khi cập nhật', 'error');
                });
        }

        // Bulk edit functions
        function toggleBulkEdit() {
            bulkEditMode = !bulkEditMode;
            const elements = document.querySelectorAll('.bulk-select-element');
            const bulkEditBtn = document.getElementById('bulkEditBtn');
            const saveAllBtn = document.getElementById('saveAllBtn');
            const cancelAllBtn = document.getElementById('cancelAllBtn');

            if (bulkEditMode) {
                // Enable bulk edit mode
                elements.forEach(el => el.classList.remove('hidden'));
                bulkEditBtn.classList.add('hidden');
                saveAllBtn.classList.remove('hidden');
                cancelAllBtn.classList.remove('hidden');

                // Enable all edit fields
                document.querySelectorAll('[class*="status-display-"]').forEach(el => el.classList.add('hidden'));
                document.querySelectorAll('[class*="status-edit-"]').forEach(el => el.classList.remove('hidden'));
                document.querySelectorAll('[class*="price-display-"]').forEach(el => el.classList.add('hidden'));
                document.querySelectorAll('[class*="price-edit-"]').forEach(el => el.classList.remove('hidden'));
                document.querySelectorAll('[class*="single-edit-actions-"]').forEach(el => el.classList.add('hidden'));

                // Add event listeners for price changes
                document.querySelectorAll('.price-input').forEach(input => {
                    input.addEventListener('input', () => {
                        const itemId = input.dataset.itemId;
                        updateTotalPrice(itemId);
                        changedItems.add(itemId);
                    });
                });

                // Add event listeners for status changes
                document.querySelectorAll('.status-select').forEach(select => {
                    select.addEventListener('change', () => {
                        changedItems.add(select.dataset.itemId);
                    });
                });

            } else {
                // Disable bulk edit mode
                elements.forEach(el => el.classList.add('hidden'));
                bulkEditBtn.classList.remove('hidden');
                saveAllBtn.classList.add('hidden');
                cancelAllBtn.classList.add('hidden');

                // Disable all edit fields
                document.querySelectorAll('[class*="status-display-"]').forEach(el => el.classList.remove('hidden'));
                document.querySelectorAll('[class*="status-edit-"]').forEach(el => el.classList.add('hidden'));
                document.querySelectorAll('[class*="price-display-"]').forEach(el => el.classList.remove('hidden'));
                document.querySelectorAll('[class*="price-edit-"]').forEach(el => el.classList.add('hidden'));
                document.querySelectorAll('[class*="single-edit-actions-"]').forEach(el => el.classList.remove('hidden'));

                // Clear selections
                document.querySelectorAll('.item-checkbox').forEach(cb => cb.checked = false);
                document.getElementById('selectAll').checked = false;
                changedItems.clear();
            }
        }

        function saveAllChanges() {
            const selectedItems = document.querySelectorAll('.item-checkbox:checked');

            if (selectedItems.length === 0) {
                showNotification('Vui lòng chọn ít nhất một món để cập nhật', 'warning');
                return;
            }

            const updates = [];

            selectedItems.forEach(checkbox => {
                const itemId = checkbox.value;
                const priceInput = document.querySelector(`.price-input[data-item-id="${itemId}"]`);
                const statusSelect = document.querySelector(`.status-select[data-item-id="${itemId}"]`);

                const updateData = {id: itemId};

                if (priceInput && priceInput.value !== priceInput.dataset.originalValue) {
                    updateData.unit_price = priceInput.value;
                }

                if (statusSelect && statusSelect.value !== statusSelect.dataset.originalValue) {
                    updateData.status = statusSelect.value;
                }

                // Only add if there are changes
                if (updateData.unit_price !== undefined || updateData.status !== undefined) {
                    updates.push(updateData);
                }
            });

            if (updates.length === 0) {
                showNotification('Không có thay đổi nào để lưu', 'info');
                return;
            }

            // Send bulk update request
            fetch('/admin/order-items/bulk-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({updates: updates})
            })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showNotification('Cập nhật thành công!', 'success');
                        // Reload page to reflect changes
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        showNotification('Có lỗi xảy ra: ' + result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Có lỗi xảy ra khi cập nhật', 'error');
                });
        }

        function cancelAllChanges() {
            // Restore all original values
            document.querySelectorAll('.price-input').forEach(input => {
                input.value = input.dataset.originalValue;
                updateTotalPrice(input.dataset.itemId);
            });

            document.querySelectorAll('.status-select').forEach(select => {
                select.value = select.dataset.originalValue;
            });

            changedItems.clear();
            toggleBulkEdit();
        }

        // Select all functionality
        document.getElementById('selectAll').addEventListener('change', function () {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = this.checked;
            });
        });

        // Individual checkbox handling
        document.querySelectorAll('.item-checkbox').forEach(cb => {
            cb.addEventListener('change', function () {
                const allCheckboxes = document.querySelectorAll('.item-checkbox');
                const checkedCheckboxes = document.querySelectorAll('.item-checkbox:checked');
                document.getElementById('selectAll').checked = allCheckboxes.length === checkedCheckboxes.length;
            });
        });

        // Notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
                type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
                    type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
                        type === 'warning' ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
                            'bg-blue-100 text-blue-800 border border-blue-200'
            }`;

            notification.innerHTML = `
        <div class="flex items-center">
            <span class="mr-2">
                ${type === 'success' ? '<i class="fas fa-check-circle"></i>' :
                type === 'error' ? '<i class="fas fa-exclamation-circle"></i>' :
                    type === 'warning' ? '<i class="fas fa-exclamation-triangle"></i>' :
                        '<i class="fas fa-info-circle"></i>'}
            </span>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-sm font-medium">
                <i class="fas fa-times"></i>
            </button>
        </div>
        `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
@endsection
