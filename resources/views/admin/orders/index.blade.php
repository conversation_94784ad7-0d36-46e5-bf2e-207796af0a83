@extends('layouts.admin')

@section('title', 'Quản lý đơn hàng')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500"><PERSON><PERSON><PERSON> hàng</span>
        </div>
    </li>
@endsection

@section('content-header-actions')
    <a href="{{ route('admin.orders.create') }}"
       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
        <i class="fas fa-plus mr-2"></i>
        Tạo đơn hàng
    </a>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Filters -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <form method="GET" action="{{ route('admin.orders.index') }}" class="space-y-4 lg:space-y-0 lg:grid lg:grid-cols-6 lg:gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Tìm kiếm</label>
                    <div class="relative">
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                               placeholder="Mã đơn, khách hàng..."
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>

{{--                <div>--}}
{{--                    <label for="customer_type" class="block text-sm font-medium text-gray-700 mb-1">Loại khách</label>--}}
{{--                    <select name="customer_type" id="customer_type"--}}
{{--                            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">--}}
{{--                        <option value="">Tất cả</option>--}}
{{--                        <option value="individual" {{ request('customer_type') === 'individual' ? 'selected' : '' }}>Khách lẻ</option>--}}
{{--                        <option value="breakfast-room" {{ request('customer_type') === 'breakfast-room' ? 'selected' : '' }}>Ăn sáng kèm phòng</option>--}}
{{--                    </select>--}}
{{--                </div>--}}

{{--                <div>--}}
{{--                    <label for="service_type" class="block text-sm font-medium text-gray-700 mb-1">Hình thức</label>--}}
{{--                    <select name="service_type" id="service_type"--}}
{{--                            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">--}}
{{--                        <option value="">Tất cả</option>--}}
{{--                        <option value="buffet" {{ request('service_type') === 'buffet' ? 'selected' : '' }}>Buffet</option>--}}
{{--                        <option value="ala-carte" {{ request('service_type') === 'ala-carte' ? 'selected' : '' }}>Gọi món lẻ</option>--}}
{{--                    </select>--}}
{{--                </div>--}}

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
                    <select name="status" id="status"
                            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Tất cả</option>
                        <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Chờ xác nhận</option>
                        <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Đã xác nhận</option>
                        <option value="2" {{ request('status') === '2' ? 'selected' : '' }}>Đang chuẩn bị</option>
                        <option value="3" {{ request('status') === '3' ? 'selected' : '' }}>Sẵn sàng</option>
                        <option value="4" {{ request('status') === '4' ? 'selected' : '' }}>Hoàn thành</option>
                        <option value="5" {{ request('status') === '5' ? 'selected' : '' }}>Đã hủy</option>
                    </select>
                </div>

                <div>
                    <label for="payment_status" class="block text-sm font-medium text-gray-700 mb-1">Thanh toán</label>
                    <select name="payment_status" id="payment_status"
                            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Tất cả</option>
                        <option value="0" {{ request('payment_status') === '0' ? 'selected' : '' }}>Chưa thanh toán</option>
                        <option value="1" {{ request('payment_status') === '1' ? 'selected' : '' }}>Đã thanh toán</option>
                        <option value="2" {{ request('payment_status') === '2' ? 'selected' : '' }}>Đã hoàn tiền</option>
                    </select>
                </div>

                <div>
                    <label for="table_id" class="block text-sm font-medium text-gray-700 mb-1">Bàn</label>
                    <select name="table_id" id="table_id"
                            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Tất cả bàn</option>
                        @foreach($tables as $table)
                            <option value="{{ $table->id }}" {{ request('table_id') == $table->id ? 'selected' : '' }}>
                                {{ $table->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">Từ ngày</label>
                    <input type="date" name="date_from" id="date_from" value="{{ request('date_from') }}"
                           class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div class="flex items-end">
                    <button type="submit"
                            class="w-full bg-gray-800 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i>Lọc
                    </button>
                </div>
            </form>
        </div>

        <!-- Orders Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Đơn hàng
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Loại đơn hàng
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Khách hàng
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Bàn
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Trạng thái
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Thanh toán
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tổng tiền
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Thời gian
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider sticky right-0 bg-white shadow-sm">
                            Thao tác
                        </th>
                    </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($orders as $order)
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $order->order_number }}</div>
                                    <div class="text-sm text-gray-500">{{ $order->user->name ?? 'Hệ thống' }}</div>
                                    @if($order->breakfast_ticket_id)
                                        <div class="text-xs text-blue-600">
                                            <i class="fas fa-ticket-alt mr-1"></i>{{ $order->breakfast_ticket_id }}
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="space-y-1">
                                    <!-- Customer Type -->
                                    @if($order->customer_type === 'individual')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-user mr-1"></i>Khách lẻ
                                        </span>
                                    @elseif($order->customer_type === 'breakfast-room')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-home mr-1"></i>Ăn sáng kèm phòng
                                        </span>
                                    @endif

                                    <!-- Service Type -->
                                    @if($order->service_type === 'buffet')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                            <i class="fas fa-coffee mr-1"></i>Buffet
                                        </span>
                                    @elseif($order->service_type === 'ala-carte')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            <i class="fas fa-list mr-1"></i>Gọi món lẻ
                                        </span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($order->customer)
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $order->customer->name }}</div>
                                        @if($order->customer->phone)
                                            <div class="text-sm text-gray-500">{{ $order->customer->formatted_phone }}</div>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-gray-400">Khách vãng lai</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($order->tables && $order->tables->count() > 0)
                                    <div class="flex flex-wrap gap-1">
                                        @foreach($order->tables as $table)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $table->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                @else
                                    <span class="text-gray-400">Mang về</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $order->status_color }}">
                                    {{ $order->status_text }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $order->payment_status_color }}">
                                    {{ $order->payment_status_text }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $order->formatted_total_amount }}</div>
                                @if($order->discount_amount > 0)
                                    <div class="text-xs text-green-600">Giảm {{ $order->formatted_discount_amount }}</div>
                                @endif
                                @if($order->subtotal != $order->total_amount)
                                    <div class="text-xs text-gray-500">Gốc: {{ number_format($order->subtotal, 0, ',', '.') }}đ</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $order->order_time->format('d/m/Y H:i') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium sticky right-0 bg-white shadow-sm">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ route('admin.orders.show', $order) }}"
                                       class="text-indigo-600 hover:text-indigo-900 transition-colors duration-200"
                                       title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('orders.print', $order) }}" target="_blank"
                                       class="text-green-600 hover:text-green-900 transition-colors duration-200"
                                       title="In đơn">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    @if($order->editable())
                                        <a href="{{ route('admin.orders.edit', $order) }}"
                                           class="text-yellow-600 hover:text-yellow-900 transition-colors duration-200"
                                           title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    @endif
                                    @if(in_array($order->status, [3, 4]) && $order->payment_status == 0)
                                        <a href="{{ route('admin.orders.payment', $order) }}"
                                           class="text-green-600 hover:text-green-900 transition-colors duration-200"
                                           title="Thanh toán">
                                            <i class="fas fa-credit-card"></i>
                                        </a>
                                    @endif
                                    @canroute('admin.orders.destroy')
                                    <form method="POST" action="{{ route('admin.orders.destroy', $order) }}" style="display: inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="text-red-600 hover:text-red-900 transition-colors duration-200 bg-transparent border-none cursor-pointer"
                                                title="Xóa"
                                                onclick="return confirm('Bạn có chắc chắn muốn xóa đơn hàng này không?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    @endcanroute

                                    @if($order->cancelable())
                                        <form action="{{ route('admin.orders.cancel', $order) }}" method="POST" class="inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit"
                                                    class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                                    title="Hủy đơn">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-receipt text-gray-300 text-4xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-1">Không có đơn hàng nào</h3>
                                    <p class="text-gray-500 mb-4">Bắt đầu bằng cách tạo đơn hàng đầu tiên.</p>
                                    <a href="{{ route('admin.orders.create') }}"
                                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                                        <i class="fas fa-plus mr-2"></i>
                                        Tạo đơn hàng
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>

            @if($orders->hasPages())
                <div class="px-6 py-3 border-t border-gray-200">
                    {{ $orders->links() }}
                </div>
            @endif
        </div>
    </div>
@endsection
