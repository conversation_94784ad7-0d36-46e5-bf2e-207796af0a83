<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Print Order</title>
    <style>
        @media print {
            @page {
                margin: 0.2in;
                size: 80mm 120mm; /* Receipt paper size */
            }
            
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body { 
            font-family: 'Courier New', monospace;
            line-height: 1.3;
            color: #000;
            background: #fff;
            width: 80mm;
            margin: 0 auto;
            padding: 5mm;
            font-size: 12px;
        }

        .receipt-container {
            text-align: center;
        }

        .header {
            margin-bottom: 15px;
            border-bottom: 2px dashed #000;
            padding-bottom: 10px;
        }

        .header h1 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .qr-section {
            margin: 15px 0;
            text-align: center;
        }

        .qr-section img {
            width: 120px;
            height: 120px;
            border: 1px solid #000;
        }

        .order-info {
            text-align: left;
            margin: 15px 0;
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 10px 0;
        }

        .info-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 11px;
        }

        .info-line:last-child {
            margin-bottom: 0;
        }

        .label {
            font-weight: bold;
            width: 40%;
        }

        .value {
            width: 60%;
            text-align: right;
            word-wrap: break-word;
        }

        .footer {
            margin-top: 15px;
            text-align: center;
            font-size: 10px;
            border-top: 1px dashed #000;
            padding-top: 10px;
        }

        .print-time {
            font-size: 9px;
            margin-bottom: 10px;
            color: #666;
        }

        .no-qr {
            width: 120px;
            height: 120px;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 10px;
            color: #666;
        }

        /* Minimal spacing for receipt */
        .spacer {
            height: 5px;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <div class="print-time" id="currentDateTime"></div>

        <div class="header">
            <h1>ORDER RECEIPT</h1>
        </div>

        <div class="qr-section">
            @if(isset($order->qr_code_url))
                <img src="{{ $order->qr_code_url }}" alt="QR">
            @else
                <div class="no-qr">NO QR</div>
            @endif
        </div>

        <div class="order-info">
            <div class="info-line">
                <span class="label">ORDER:</span>
                <span class="value">{{ $order->order_number ?? 'N/A' }}</span>
            </div>
            
            <div class="info-line">
                <span class="label">CUSTOMER:</span>
                <span class="value">{{ $order->customer->name ?? 'N/A' }}</span>
            </div>
            
            <div class="info-line">
                <span class="label">DATE:</span>
                <span class="value">{{ $order->created_at ? $order->created_at->format('d/m/Y H:i') : 'N/A' }}</span>
            </div>
        </div>

        <div class="footer">
            <p>THANK YOU!</p>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set current date and time
            const now = new Date();
            const dateTimeString = now.toLocaleString('en-GB', {
                day: '2-digit',
                month: '2-digit', 
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('currentDateTime').textContent = 'Printed: ' + dateTimeString;
            
            // Auto print after a small delay
            setTimeout(() => {
                window.print();
            }, 300);
        });
    </script>
</body>
</html>