@extends('layouts.admin')

@section('title', 'Chi tiết Role')
@section('page-title', 'Chi tiết Role: ' . $role->name)

@section('content')
    <div class="max-w-6xl mx-auto space-y-6">
        <!-- Role Info Card -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Thông tin Role</h3>
                    <div class="flex space-x-2">
                        @canroute('admin.roles.edit')
                        <a href="{{ route('admin.roles.edit', $role) }}"
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                            <i class="fas fa-edit mr-2"></i>Chỉnh sửa
                        </a>
                        @endcanroute
                        <a href="{{ route('admin.roles.index') }}"
                           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>Quay lại
                        </a>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="col-span-1">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user-tag text-3xl text-green-600"></i>
                            </div>
                            <h4 class="text-xl font-semibold text-gray-900">{{ $role->name }}</h4>
                            <p class="text-sm text-gray-500">Role ID: {{ $role->id }}</p>
                        </div>
                    </div>

                    <div class="col-span-9">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">{{ $role->users()->count() }}</div>
                                <div class="text-sm text-gray-600">Users được gán</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">{{ $role->permissions->count() }}</div>
                                <div class="text-sm text-gray-600">Permissions</div>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div class="text-sm font-bold text-purple-600">{{ $role->created_at->format('d/m/Y') }}</div>
                                <div class="text-sm text-gray-600">Ngày tạo</div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h5 class="font-medium text-gray-900 mb-2">Mô tả:</h5>
                            <p class="text-gray-600">
                                Role {{ $role->name }} có {{ $role->permissions->count() }} permissions và được gán cho {{ $role->users()->count() }} users.
                                @if($role->created_at->diffInDays() < 7)
                                    <span class="text-green-600 font-medium">(Role mới tạo)</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions Card -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Permissions ({{ $role->permissions->count() }})</h3>
            </div>

            <div class="p-6">
                @if($role->permissions->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        @foreach($role->permissions as $permission)
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <i class="fas fa-key text-purple-600 mr-3"></i>
                                <span class="text-sm text-gray-700">{{ $permission->name }}</span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-key text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">Role này chưa có permissions nào</p>
                        @canroute('admin.roles.edit')
                        <a href="{{ route('admin.roles.edit', $role) }}" class="text-blue-600 hover:text-blue-800 mt-2 inline-block">
                            Thêm permissions
                        </a>
                        @endcanroute
                    </div>
                @endif
            </div>
        </div>

        <!-- Users with this Role -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Users có Role này ({{ $role->users()->count() }})</h3>
            </div>

            <div class="p-6">
                @if($role->users()->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($role->users as $user)
                            <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <img class="w-10 h-10 rounded-full mr-3"
                                     src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&background=6366f1&color=fff"
                                     alt="{{ $user->name }}">
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                </div>
                                @canroute('admin.users.show')
                                <a href="{{ route('admin.users.show', $user) }}" class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                @endcanroute
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">Chưa có user nào được gán role này</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
