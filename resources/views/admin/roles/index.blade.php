@extends('layouts.admin')

@section('title', '<PERSON>h sách Roles')
@section('page-title', 'Quản lý Roles')

@section('content')
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Danh sách Roles</h3>
                @canroute('admin.roles.create')
                <a href="{{ route('admin.roles.create') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <i class="fas fa-plus mr-2"></i>Tạo Role
                </a>
                @endcanroute
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($roles as $role)
                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex justify-between items-start mb-4">
                            <h4 class="text-lg font-medium text-gray-900">{{ $role->name }}</h4>
                            <div class="flex space-x-1">
                                @canroute('admin.roles.show')
                                <a href="{{ route('admin.roles.show', $role) }}" class="text-blue-600 hover:text-blue-800 transition-colors">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @endcanroute
                                @canroute('admin.roles.edit')
                                <a href="{{ route('admin.roles.edit', $role) }}" class="text-indigo-600 hover:text-indigo-800 transition-colors">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @endcanroute
                                @canroute('admin.roles.destroy')
                                @if($role->users()->count() == 0)
                                    <form method="POST" action="{{ route('admin.roles.destroy', $role) }}" class="inline"
                                          onsubmit="return confirm('Bạn có chắc chắn muốn xóa role này?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-800 transition-colors">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                @endif
                                @endcanroute
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Users:</span>
                                <span class="text-sm font-medium text-gray-900">{{ $role->users()->count() }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Permissions:</span>
                                <span class="text-sm font-medium text-gray-900">{{ $role->permissions->count() }}</span>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="flex flex-wrap gap-1">
                                @foreach($role->permissions->take(3) as $permission)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {{ $permission->name }}
                        </span>
                                @endforeach
                                @if($role->permissions->count() > 3)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            +{{ $role->permissions->count() - 3 }} more
                        </span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $roles->links() }}
            </div>
        </div>
    </div>
@endsection
