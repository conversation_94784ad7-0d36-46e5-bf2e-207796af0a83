@extends('layouts.admin')

@section('title', 'Tạo Role')
@section('page-title', 'Tạo Role mới')

@section('content')
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Thông tin Role</h3>
            </div>

            <form method="POST" action="{{ route('admin.roles.store') }}" class="p-6">
                @csrf

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tên Role <span class="text-red-500">*</span></label>
                    <input type="text" name="name" value="{{ old('name') }}" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                    @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Permissions Section -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Permissions</label>
                    <div class="border border-gray-200 rounded-md p-4">
                        <div class="mb-4">
                            <button type="button" onclick="selectAll()" class="text-sm text-blue-600 hover:text-blue-800 mr-4">Chọn tất cả</button>
                            <button type="button" onclick="deselectAll()" class="text-sm text-gray-600 hover:text-gray-800">Bỏ chọn tất cả</button>
                        </div>
                        <div class="max-h-60 overflow-y-auto">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                @foreach($permissions as $permission)
                                    <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                        <input type="checkbox" name="permissions[]" value="{{ $permission->name }}"
                                               class="permission-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            {{ in_array($permission->name, old('permissions', [])) ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm text-gray-700">{{ $permission->name }}</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @error('permissions')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3">
                    <a href="{{ route('admin.roles.index') }}"
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        Hủy
                    </a>
                    <button type="submit"
                            class="px-4 py-2 bg-green-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-green-700 transition-colors">
                        Tạo Role
                    </button>
                </div>
            </form>
        </div>
    </div>

    @push('scripts')
        <script>
            function selectAll() {
                document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
                    checkbox.checked = true;
                });
            }

            function deselectAll() {
                document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
                    checkbox.checked = false;
                });
            }
        </script>
    @endpush
@endsection
