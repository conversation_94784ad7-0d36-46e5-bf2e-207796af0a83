@extends('layouts.admin')

@section('title', 'Tạo mới User')

@section('breadcrumb')
    <li class="flex items-center">
        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
        <a href="{{ route('admin.users.index') }}" class="text-gray-500 hover:text-gray-700">Users</a>
    </li>
    <li class="flex items-center">
        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
        <span class="text-gray-500">Thêm mới</span>
    </li>
@endsection

@section('content-header')
    @section('subtitle', 'Thêm thông tin và quyền user')

    @section('content-header-actions')
        <div class="flex space-x-3">
            <a href="{{ route('admin.users.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Quay lại
            </a>
        </div>
    @endsection

    @section('content')
        <div class="max-w-6xl mx-auto" x-data="userCreateForm()">
            <div class="bg-white shadow-xl rounded-2xl overflow-hidden">
                <form method="POST" action="{{ route('admin.users.store') }}" class="space-y-6">
                    @csrf
                    @method('POST')

                    <div class="px-8 py-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-edit mr-2 text-indigo-500"></i>
                            Tạo tài khoản mới
                        </h3>
                        <p class="mt-1 text-sm text-gray-600">Nhập thông tin chi tiết và phân quyền cho tài khoản người dùng mới.</p>
                    </div>

                    <div class="px-8 pb-8">
                        <!-- Basic Information -->
                        <div class="mb-8">
                            <h4 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <i class="fas fa-user-circle mr-2 text-indigo-500"></i>
                                Thông tin cơ bản
                            </h4>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Left Column -->
                                <div class="space-y-6">
                                    <!-- Name -->
                                    <div class="group">
                                        <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-user mr-2 text-indigo-500"></i>Họ và tên
                                            <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               name="name"
                                               id="name"
                                               value="{{ old('name') }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('name') border-red-300 @enderror"
                                               required>
                                        @error('name')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <!-- Phone -->
                                    <div class="group">
                                        <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-phone mr-2 text-indigo-500"></i>Số điện thoại
                                        </label>
                                        <input type="tel"
                                               name="phone"
                                               id="phone"
                                               value="{{ old('phone') }}"
                                               placeholder="0365645451"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('phone') border-red-300 @enderror">
                                        @error('phone')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Right Column -->
                                <div class="space-y-6">
                                    <!-- Email -->
                                    <div class="group">
                                        <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-envelope mr-2 text-indigo-500"></i>Email
                                            <span class="text-red-500">*</span>
                                        </label>
                                        <input type="email"
                                               name="email"
                                               id="email"
                                               value="{{ old('email') }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('email') border-red-300 @enderror"
                                               required>
                                        @error('email')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <!-- Password -->
                                    <div class="group">
                                        <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-lock mr-2 text-indigo-500"></i>Mật khẩu
                                            <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <input type="password"
                                                   name="password"
                                                   id="password"
                                                   class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('password') border-red-300 @enderror"
                                                   placeholder="Nhập mật khẩu (tối thiểu 8 ký tự)"
                                                   required>
                                            <button type="button" @click="togglePassword('password')" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                <i class="fas fa-eye text-gray-400 hover:text-gray-600" x-show="!showPassword"></i>
                                                <i class="fas fa-eye-slash text-gray-400 hover:text-gray-600" x-show="showPassword"></i>
                                            </button>
                                        </div>
                                        @error('password')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <!-- Confirm Password -->
                                    <div class="group">
                                        <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-check-double mr-2 text-indigo-500"></i>Xác nhận mật khẩu
                                            <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <input type="password"
                                                   name="password_confirmation"
                                                   id="password_confirmation"
                                                   class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                   placeholder="Nhập lại mật khẩu"
                                                   required>
                                            <button type="button" @click="togglePassword('password_confirmation')" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                <i class="fas fa-eye text-gray-400 hover:text-gray-600" x-show="!showPasswordConfirm"></i>
                                                <i class="fas fa-eye-slash text-gray-400 hover:text-gray-600" x-show="showPasswordConfirm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Role Assignment -->
                        <div class="mt-8 pt-8 border-t border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <i class="fas fa-shield-alt mr-2 text-indigo-500"></i>
                                Phân quyền
                            </h4>

                            <!-- Role Selection Info -->
                            <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 text-xl mr-3 mt-1"></i>
                                    <div>
                                        <h5 class="text-blue-900 font-semibold">Hướng dẫn phân quyền</h5>
                                        <p class="text-blue-700 text-sm mt-1">
                                            Chọn một hoặc nhiều roles phù hợp với vai trò của user trong hệ thống.
                                            Mỗi role sẽ bao gồm các permissions cụ thể.
                                        </p>
                                        <div class="mt-2">
                                            <span class="text-xs text-blue-600">
                                                <strong>Lưu ý:</strong> User có thể có nhiều roles cùng lúc. Permissions sẽ được kết hợp từ tất cả roles được gán.
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Roles Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                @foreach($roles as $role)
                                    @php
                                        // Define role colors and restrictions
                                        $roleConfig = [
                                            'Super Admin' => [
                                                'color' => 'border-red-300 bg-red-50 hover:bg-red-100 text-red-900',
                                                'icon' => 'fas fa-crown text-red-600',
                                                'canAssign' => auth()->user()->hasRole('Super Admin'),
                                                'description' => 'Toàn quyền quản trị hệ thống, có thể thực hiện mọi chức năng và quản lý tất cả users.'
                                            ],
                                            'Admin' => [
                                                'color' => 'border-yellow-300 bg-yellow-50 hover:bg-yellow-100 text-yellow-900',
                                                'icon' => 'fas fa-shield-alt text-yellow-600',
                                                'canAssign' => auth()->user()->hasAnyRole(['Super Admin', 'Admin']),
                                                'description' => 'Quản trị viên với quyền quản lý users, roles và hầu hết các chức năng hệ thống.'
                                            ],
                                            'Manager' => [
                                                'color' => 'border-blue-300 bg-blue-50 hover:bg-blue-100 text-blue-900',
                                                'icon' => 'fas fa-user-tie text-blue-600',
                                                'canAssign' => auth()->user()->hasAnyRole(['Super Admin', 'Admin']),
                                                'description' => 'Quản lý với quyền hạn محدود, có thể xem và chỉnh sửa một số dữ liệu.'
                                            ],
                                            'User' => [
                                                'color' => 'border-green-300 bg-green-50 hover:bg-green-100 text-green-900',
                                                'icon' => 'fas fa-user text-green-600',
                                                'canAssign' => true,
                                                'description' => 'Người dùng thường với quyền truy cập cơ bản vào hệ thống.'
                                            ]
                                        ];

                                        $config = $roleConfig[$role->name] ?? [
                                            'color' => 'border-gray-300 bg-gray-50 hover:bg-gray-100 text-gray-900',
                                            'icon' => 'fas fa-user text-gray-600',
                                            'canAssign' => true,
                                            'description' => 'Role tùy chỉnh trong hệ thống.'
                                        ];

                                        $isChecked = in_array($role->name, old('roles', []));
                                    @endphp

                                    <div class="border rounded-xl {{ $config['color'] }} {{ $config['canAssign'] ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed' }} transition-all duration-200 {{ $config['canAssign'] ? 'hover:shadow-md' : '' }}">
                                        <label class="block p-6 {{ $config['canAssign'] ? 'cursor-pointer' : 'cursor-not-allowed' }}">
                                            <div class="flex items-start">
                                                <div class="flex items-center h-6">
                                                    <input type="checkbox"
                                                           name="roles[]"
                                                           value="{{ $role->name }}"
                                                           {{ $isChecked ? 'checked' : '' }}
                                                           {{ $config['canAssign'] ? '' : 'disabled' }}
                                                           @if($role->name === 'Super Admin')
                                                               @change="confirmSuperAdmin($event)"
                                                           @endif
                                                           class="h-5 w-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 focus:ring-2 {{ $config['canAssign'] ? '' : 'opacity-50' }}">
                                                </div>

                                                <div class="ml-4 flex-1">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <i class="{{ $config['icon'] }} mr-2"></i>
                                                            <span class="font-semibold">{{ $role->name }}</span>
                                                            @if(!$config['canAssign'])
                                                                <span class="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">Không có quyền</span>
                                                            @endif
                                                        </div>
                                                        <span class="text-sm opacity-70">{{ $role->permissions->count() }} permissions</span>
                                                    </div>

                                                    <!-- Role Description -->
                                                    <p class="mt-2 text-sm opacity-80">
                                                        {{ $config['description'] }}
                                                    </p>

                                                    <!-- Permission Preview -->
                                                    @if($role->permissions->count() > 0)
                                                        <div class="mt-4">
                                                            <details class="group">
                                                                <summary class="text-xs text-indigo-600 hover:text-indigo-800 cursor-pointer select-none flex items-center">
                                                                    <i class="fas fa-key mr-1"></i>
                                                                    Xem permissions ({{ $role->permissions->count() }})
                                                                    <i class="fas fa-chevron-down ml-1 transition-transform group-open:rotate-180"></i>
                                                                </summary>
                                                                <div class="mt-3 ml-4 text-xs opacity-70 max-h-32 overflow-y-auto">
                                                                    <div class="space-y-1">
                                                                        @foreach($role->permissions->take(8) as $permission)
                                                                            <div class="flex items-center">
                                                                                <i class="fas fa-key mr-2 opacity-50 text-xs"></i>
                                                                                <span>{{ $permission->name }}</span>
                                                                            </div>
                                                                        @endforeach
                                                                        @if($role->permissions->count() > 8)
                                                                            <div class="opacity-60 italic">
                                                                                ... và {{ $role->permissions->count() - 8 }} permissions khác
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </details>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            </div>

                            @error('roles')
                            <p class="mt-3 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                            </p>
                            @enderror

                            <!-- Selected Roles Summary -->
                            <div class="mt-6 p-4 bg-gray-50 rounded-xl" x-show="selectedRoles.length > 0">
                                <h5 class="font-medium text-gray-900 mb-2 flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    Roles đã chọn (<span x-text="selectedRoles.length"></span>)
                                </h5>
                                <div class="flex flex-wrap gap-2">
                                    <template x-for="role in selectedRoles" :key="role">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                                            <span x-text="role"></span>
                                        </span>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="mt-8 pt-6 border-t border-gray-200 flex justify-between">
                            <div>
                                <!-- Quick Role Presets -->
                                <div class="flex flex-wrap gap-2">
                                    <button type="button" @click="selectRolePreset('basic')"
                                            class="text-xs px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                        <i class="fas fa-user mr-1"></i>User cơ bản
                                    </button>
                                    @if(auth()->user()->hasAnyRole(['Super Admin', 'Admin']))
                                        <button type="button" @click="selectRolePreset('manager')"
                                                class="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                                            <i class="fas fa-user-tie mr-1"></i>Quản lý
                                        </button>
                                        <button type="button" @click="selectRolePreset('admin')"
                                                class="text-xs px-3 py-1 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 transition-colors">
                                            <i class="fas fa-shield-alt mr-1"></i>Quản trị
                                        </button>
                                    @endif
                                    <button type="button" @click="clearRoles()"
                                            class="text-xs px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors">
                                        <i class="fas fa-times mr-1"></i>Xóa tất cả
                                    </button>
                                </div>
                            </div>

                            <div class="flex space-x-3">
                                <a href="{{ route('admin.users.index') }}"
                                   class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-xl text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                                    <i class="fas fa-times mr-2"></i>
                                    Hủy bỏ
                                </a>

                                <button type="submit"
                                        class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                    <i class="fas fa-save mr-2"></i>
                                    Tạo tài khoản
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        @push('scripts')
            <script>
                function userCreateForm() {
                    return {
                        showPassword: false,
                        showPasswordConfirm: false,
                        selectedRoles: [],

                        init() {
                            // Initialize selected roles from checkboxes
                            this.updateSelectedRoles();

                            // Listen for checkbox changes
                            this.$watch('selectedRoles', () => {
                                // This will be automatically updated by checkbox changes
                            });
                        },

                        togglePassword(field) {
                            if (field === 'password') {
                                this.showPassword = !this.showPassword;
                                const input = document.getElementById('password');
                                input.type = this.showPassword ? 'text' : 'password';
                            } else if (field === 'password_confirmation') {
                                this.showPasswordConfirm = !this.showPasswordConfirm;
                                const input = document.getElementById('password_confirmation');
                                input.type = this.showPasswordConfirm ? 'text' : 'password';
                            }
                        },

                        updateSelectedRoles() {
                            const checkboxes = document.querySelectorAll('input[name="roles[]"]:checked');
                            this.selectedRoles = Array.from(checkboxes).map(cb => cb.value);
                        },

                        confirmSuperAdmin(event) {
                            if (event.target.checked) {
                                if (!confirm('Bạn có chắc chắn muốn gán role Super Admin? Đây là quyền cao nhất trong hệ thống.')) {
                                    event.target.checked = false;
                                    return;
                                }
                            }
                            this.updateSelectedRoles();
                        },

                        selectRolePreset(preset) {
                            // Clear all checkboxes first
                            this.clearRoles();

                            // Select based on preset
                            const roleMap = {
                                'basic': ['User'],
                                'manager': ['Manager', 'User'],
                                'admin': ['Admin', 'Manager', 'User']
                            };

                            const rolesToSelect = roleMap[preset] || [];
                            rolesToSelect.forEach(roleName => {
                                const checkbox = document.querySelector(`input[name="roles[]"][value="${roleName}"]`);
                                if (checkbox && !checkbox.disabled) {
                                    checkbox.checked = true;
                                }
                            });

                            this.updateSelectedRoles();
                        },

                        clearRoles() {
                            document.querySelectorAll('input[name="roles[]"]').forEach(cb => {
                                cb.checked = false;
                            });
                            this.updateSelectedRoles();
                        }
                    }
                }

                // Add event listeners to checkboxes
                document.addEventListener('DOMContentLoaded', function() {
                    document.querySelectorAll('input[name="roles[]"]').forEach(checkbox => {
                        checkbox.addEventListener('change', function() {
                            // Update Alpine.js data
                            const checkboxes = document.querySelectorAll('input[name="roles[]"]:checked');
                            // This will be handled by Alpine.js
                        });
                    });
                });
            </script>
        @endpush
    @endsection
