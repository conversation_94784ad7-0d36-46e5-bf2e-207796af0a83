@extends('layouts.admin')

@section('title', 'View User')

@section('breadcrumb')
    <li class="flex items-center">
        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
        <a href="{{ route('admin.users.index') }}" class="text-gray-500 hover:text-gray-700">Users</a>
    </li>
    <li class="flex items-center">
        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
        <span class="text-gray-500">{{ $user->name }}</span>
    </li>
@endsection

@section('content-header')
    @section('subtitle', 'View user details and account information')

    @section('content-header-actions')
        <div class="flex space-x-3">
            <a href="{{ route('admin.users.edit', $user) }}"
               class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <i class="fas fa-edit mr-2"></i>
                Edit User
            </a>
            <a href="{{ route('admin.users.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Users
            </a>
        </div>
    @endsection

    @section('content')
        <div class="max-w-6xl mx-auto">
            <!-- User Profile Header -->
            <div class="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 rounded-2xl shadow-2xl overflow-hidden mb-8">
                <div class="relative px-8 py-12">
                    <div class="absolute inset-0 bg-black/20"></div>
                    <div class="relative flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            @if($user->avatar)
                                <img src="{{ \App\Helper\helper::getImageGoogleDrive($user->avatar) }}"
                                     alt="{{ $user->name }}"
                                     class="w-32 h-32 rounded-full object-cover border-4 border-white/30 shadow-2xl">
                            @else
                                <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&size=128&background=ffffff&color=6366f1"
                                     alt="{{ $user->name }}"
                                     class="w-32 h-32 rounded-full object-cover border-4 border-white/30 shadow-2xl">
                            @endif
                        </div>

                        <!-- User Info -->
                        <div class="text-center md:text-left text-white flex-1">
                            <h1 class="text-3xl font-bold mb-2">{{ $user->name }}</h1>
                            <p class="text-white/80 text-lg mb-4">{{ $user->email }}</p>

                            <!-- Contact Info -->
                            <div class="flex flex-wrap justify-center md:justify-start gap-4 mb-4">
                                @if($user->phone)
                                    <span class="text-white/80 flex items-center">
                                <i class="fas fa-phone mr-2"></i>
                                {{ $user->phone }}
                            </span>
                                @endif

                                @if($user->location)
                                    <span class="text-white/80 flex items-center">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                {{ $user->location }}
                            </span>
                                @endif

                                @if($user->website)
                                    <a href="{{ $user->website }}" target="_blank" class="text-white/80 hover:text-white flex items-center transition-colors">
                                        <i class="fas fa-globe mr-2"></i>
                                        {{ str_replace(['http://', 'https://'], '', $user->website) }}
                                        <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                    </a>
                                @endif
                            </div>

                            <!-- Status Badges -->
                            <div class="flex flex-wrap justify-center md:justify-start gap-3">
                                @if($user->is_admin)
                                    <span class="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-medium">
                                <i class="fas fa-crown mr-1"></i>Administrator
                            </span>
                                @else
                                    <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
                                <i class="fas fa-user mr-1"></i>User
                            </span>
                                @endif

                                @if($user->email_verified_at)
                                    <span class="bg-green-400 text-green-900 px-3 py-1 rounded-full text-sm font-medium">
                                <i class="fas fa-check-circle mr-1"></i>Verified
                            </span>
                                @else
                                    <span class="bg-red-400 text-red-900 px-3 py-1 rounded-full text-sm font-medium">
                                <i class="fas fa-exclamation-circle mr-1"></i>Unverified
                            </span>
                                @endif

                                @if($user->id === auth()->id())
                                    <span class="bg-blue-400 text-blue-900 px-3 py-1 rounded-full text-sm font-medium">
                                <i class="fas fa-star mr-1"></i>You
                            </span>
                                @endif
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="flex flex-col space-y-3">
                            <a href="{{ route('admin.users.edit', $user) }}"
                               class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-all duration-200 text-center">
                                <i class="fas fa-edit mr-2"></i>Edit
                            </a>
                            @if($user->id !== auth()->id())
                                <button onclick="deleteUser()"
                                        class="bg-red-500/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-red-600/80 transition-all duration-200">
                                    <i class="fas fa-trash mr-2"></i>Delete
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- User Details -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- About Section -->
                    @if($user->bio)
                        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-info-circle mr-2 text-indigo-500"></i>
                                    About
                                </h3>
                            </div>
                            <div class="p-6">
                                <p class="text-gray-700 leading-relaxed">{{ $user->bio }}</p>
                            </div>
                        </div>
                    @endif

                    <!-- Account Information -->
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-user-circle mr-2 text-indigo-500"></i>
                                Account Information
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Basic Info -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Full Name</label>
                                        <p class="text-gray-900 font-medium">{{ $user->name }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Email Address</label>
                                        <div class="flex items-center">
                                            <p class="text-gray-900">{{ $user->email }}</p>
                                            @if($user->email_verified_at)
                                                <i class="fas fa-check-circle text-green-500 ml-2" title="Verified"></i>
                                            @else
                                                <i class="fas fa-exclamation-circle text-yellow-500 ml-2" title="Unverified"></i>
                                            @endif
                                        </div>
                                    </div>

                                    @if($user->phone)
                                        <div>
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Phone Number</label>
                                            <p class="text-gray-900">{{ $user->phone }}</p>
                                        </div>
                                    @endif
                                </div>

                                <!-- Additional Info -->
                                <div class="space-y-4">
                                    @if($user->location)
                                        <div>
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Location</label>
                                            <p class="text-gray-900">{{ $user->location }}</p>
                                        </div>
                                    @endif

                                    @if($user->website)
                                        <div>
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Website</label>
                                            <a href="{{ $user->website }}"
                                               target="_blank"
                                               class="text-indigo-600 hover:text-indigo-800 flex items-center">
                                                {{ str_replace(['http://', 'https://'], '', $user->website) }}
                                                <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                            </a>
                                        </div>
                                    @endif

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Account Type</label>
                                        @if($user->is_admin)
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                        <i class="fas fa-crown mr-1"></i>Administrator
                                    </span>
                                        @else
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-user mr-1"></i>Regular User
                                    </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Timeline -->
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-clock mr-2 text-indigo-500"></i>
                                Activity Timeline
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="flow-root">
                                <ul class="-mb-8">
                                    <li class="relative pb-8">
                                        <div class="relative flex space-x-3">
                                            <div class="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 ring-8 ring-white">
                                                <i class="fas fa-user-plus text-white text-sm"></i>
                                            </div>
                                            <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                                <div>
                                                    <p class="text-sm text-gray-500">Account created</p>
                                                </div>
                                                <div class="whitespace-nowrap text-right text-sm text-gray-500">
                                                    <time datetime="{{ $user->created_at->toISOString() }}">
                                                        {{ $user->created_at->format('M j, Y g:i A') }}
                                                    </time>
                                                </div>
                                            </div>
                                        </div>
                                    </li>

                                    @if($user->email_verified_at)
                                        <li class="relative pb-8">
                                            <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                                            <div class="relative flex space-x-3">
                                                <div class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500 ring-8 ring-white">
                                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                                </div>
                                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                                    <div>
                                                        <p class="text-sm text-gray-500">Email verified</p>
                                                    </div>
                                                    <div class="whitespace-nowrap text-right text-sm text-gray-500">
                                                        <time datetime="{{ $user->email_verified_at->toISOString() }}">
                                                            {{ $user->email_verified_at->format('M j, Y g:i A') }}
                                                        </time>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    @endif

                                    @if($user->last_login_at)
                                        <li class="relative">
                                            <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                                            <div class="relative flex space-x-3">
                                                <div class="flex h-8 w-8 items-center justify-center rounded-full bg-purple-500 ring-8 ring-white">
                                                    <i class="fas fa-sign-in-alt text-white text-sm"></i>
                                                </div>
                                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                                    <div>
                                                        <p class="text-sm text-gray-500">Last login</p>
                                                    </div>
                                                    <div class="whitespace-nowrap text-right text-sm text-gray-500">
                                                        <time datetime="{{ $user->last_login_at->toISOString() }}">
                                                            {{ $user->last_login_at->format('M j, Y g:i A') }}
                                                        </time>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Stats -->
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-chart-bar mr-2 text-indigo-500"></i>
                                Quick Stats
                            </h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-500">Member Since</span>
                                <span class="text-sm font-medium text-gray-900">{{ $user->created_at->diffForHumans() }}</span>
                            </div>

                            @if($user->last_login_at)
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">Last Login</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $user->last_login_at->diffForHumans() }}</span>
                                </div>
                            @endif

                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-500">Email Status</span>
                                @if($user->email_verified_at)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>Verified
                            </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-clock mr-1"></i>Pending
                            </span>
                                @endif
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-500">Account ID</span>
                                <span class="text-sm font-mono text-gray-900">#{{ $user->id }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-bolt mr-2 text-indigo-500"></i>
                                Actions
                            </h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <a href="{{ route('admin.users.edit', $user) }}"
                               class="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl text-center block">
                                <i class="fas fa-edit mr-2"></i>Edit User
                            </a>

                            @if(!$user->email_verified_at)
                                <button class="w-full bg-blue-600 text-white px-4 py-3 rounded-xl font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                    <i class="fas fa-envelope mr-2"></i>Send Verification
                                </button>
                            @endif

                            @if($user->id !== auth()->id())
                                <button onclick="deleteUser()"
                                        class="w-full bg-gradient-to-r from-red-600 to-pink-600 text-white px-4 py-3 rounded-xl font-semibold hover:from-red-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                    <i class="fas fa-trash mr-2"></i>Delete User
                                </button>
                            @endif
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-server mr-2 text-indigo-500"></i>
                                System Info
                            </h3>
                        </div>
                        <div class="p-6 space-y-4 text-xs">
                            <div class="flex justify-between">
                                <span class="text-gray-500">Created:</span>
                                <span class="text-gray-900 font-mono">{{ $user->created_at->toISOString() }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500">Updated:</span>
                                <span class="text-gray-900 font-mono">{{ $user->updated_at->toISOString() }}</span>
                            </div>
                            @if($user->email_verified_at)
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Verified:</span>
                                    <span class="text-gray-900 font-mono">{{ $user->email_verified_at->toISOString() }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hidden Delete Form -->
        @if($user->id !== auth()->id())
            <form id="delete-form" method="POST" action="{{ route('admin.users.destroy', $user) }}" class="hidden">
                @csrf
                @method('DELETE')
            </form>
        @endif
    @endsection

    @push('scripts')
        <script>
            function deleteUser() {
                if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                    document.getElementById('delete-form').submit();
                }
            }
        </script>
    @endpush
