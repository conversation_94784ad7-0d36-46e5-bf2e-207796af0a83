@extends('layouts.admin')

@section('title', 'Sửa thông tin User')

@section('breadcrumb')
    <li class="flex items-center">
        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
        <a href="{{ route('admin.users.index') }}" class="text-gray-500 hover:text-gray-700">Users</a>
    </li>
    <li class="flex items-center">
        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
        <span class="text-gray-500">Sửa thông tin {{ $user->name }}</span>
    </li>
@endsection

@section('content-header')
    @section('subtitle', 'Cập nhật thông tin và quyền user')

    @section('content-header-actions')
        <div class="flex space-x-3">
            <a href="{{ route('admin.users.show', $user) }}"
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <i class="fas fa-eye mr-2"></i>
                Xem chi tiết
            </a>
            <a href="{{ route('admin.users.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Quay lại
            </a>
        </div>
    @endsection

    @section('content')
        <div class="max-w-6xl mx-auto" x-data="userEditForm()">
            <!-- User Info Header -->
            <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl shadow-2xl overflow-hidden mb-8">
                <div class="relative px-8 py-6">
                    <div class="absolute inset-0 bg-black/20"></div>
                    <div class="relative flex items-center space-x-6">
                        <div class="flex-shrink-0">
                            @if($user->avatar)
                                <img src="{{ \App\Helper\helper::getImageGoogleDrive($user->avatar) }}"
                                     alt="{{ $user->name }}"
                                     class="w-20 h-20 rounded-full object-cover border-4 border-white/30 shadow-lg">
                            @else
                                <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&size=80&background=ffffff&color=6366f1"
                                     alt="{{ $user->name }}"
                                     class="w-20 h-20 rounded-full object-cover border-4 border-white/30 shadow-lg">
                            @endif
                        </div>
                        <div class="text-white">
                            <h1 class="text-2xl font-bold">{{ $user->name }}</h1>
                            <p class="text-white/80">{{ $user->email }}</p>
                            <div class="flex items-center space-x-3 mt-2">
                                @foreach($user->roles as $role)
                                    @php
                                        $roleColors = [
                                            'Super Admin' => 'bg-red-400 text-red-900',
                                            'Admin' => 'bg-yellow-400 text-yellow-900',
                                            'Manager' => 'bg-blue-400 text-blue-900',
                                            'User' => 'bg-green-400 text-green-900'
                                        ];
                                        $colorClass = $roleColors[$role->name] ?? 'bg-gray-400 text-gray-900';
                                    @endphp
                                    <span class="{{ $colorClass }} px-3 py-1 rounded-full text-xs font-medium">
                                        @if($role->name === 'Super Admin')
                                            <i class="fas fa-crown mr-1"></i>
                                        @elseif($role->name === 'Admin')
                                            <i class="fas fa-shield-alt mr-1"></i>
                                        @elseif($role->name === 'Manager')
                                            <i class="fas fa-user-tie mr-1"></i>
                                        @else
                                            <i class="fas fa-user mr-1"></i>
                                        @endif
                                        {{ $role->name }}
                                    </span>
                                @endforeach
                                @if($user->email_verified_at)
                                    <span class="bg-emerald-400 text-emerald-900 px-3 py-1 rounded-full text-xs font-medium">
                                        <i class="fas fa-check-circle mr-1"></i>Verified
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit Form -->
            <div class="bg-white shadow-xl rounded-2xl overflow-hidden">
                <form method="POST" action="{{ route('admin.users.update', $user) }}" class="space-y-6">
                    @csrf
                    @method('PUT')

                    <div class="px-8 py-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-edit mr-2 text-indigo-500"></i>
                            Cập nhật thông tin
                        </h3>
                        <p class="mt-1 text-sm text-gray-600">Chỉnh sửa thông tin chi tiết và phân quyền của tài khoản người dùng.</p>
                    </div>

                    <div class="px-8 pb-8">
                        <!-- Basic Information -->
                        <div class="mb-8">
                            <h4 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <i class="fas fa-user-circle mr-2 text-indigo-500"></i>
                                Thông tin cơ bản
                            </h4>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Left Column -->
                                <div class="space-y-6">
                                    <!-- Name -->
                                    <div class="group">
                                        <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-user mr-2 text-indigo-500"></i>Họ và tên
                                            <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               name="name"
                                               id="name"
                                               value="{{ old('name', $user->name) }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('name') border-red-300 @enderror"
                                               required>
                                        @error('name')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <!-- Phone -->
                                    <div class="group">
                                        <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-phone mr-2 text-indigo-500"></i>Số điện thoại
                                        </label>
                                        <input type="tel"
                                               name="phone"
                                               id="phone"
                                               value="{{ old('phone', $user->phone) }}"
                                               placeholder="0365645451"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('phone') border-red-300 @enderror">
                                        @error('phone')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Right Column -->
                                <div class="space-y-6">
                                    <!-- Email -->
                                    <div class="group">
                                        <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-envelope mr-2 text-indigo-500"></i>Email
                                            <span class="text-red-500">*</span>
                                        </label>
                                        <input type="email"
                                               name="email"
                                               id="email"
                                               value="{{ old('email', $user->email) }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('email') border-red-300 @enderror"
                                               required>
                                        @error('email')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <!-- Password -->
                                    <div class="group">
                                        <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-lock mr-2 text-indigo-500"></i>Mật khẩu mới
                                        </label>
                                        <div class="relative">
                                            <input type="password"
                                                   name="password"
                                                   id="password"
                                                   class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('password') border-red-300 @enderror"
                                                   placeholder="Để trống để giữ mật khẩu hiện tại">
                                            <button type="button" @click="togglePassword('password')" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                <i class="fas fa-eye text-gray-400 hover:text-gray-600" x-show="!showPassword"></i>
                                                <i class="fas fa-eye-slash text-gray-400 hover:text-gray-600" x-show="showPassword"></i>
                                            </button>
                                        </div>
                                        @error('password')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <!-- Confirm Password -->
                                    <div class="group">
                                        <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-check-double mr-2 text-indigo-500"></i>Xác nhận mật khẩu mới
                                        </label>
                                        <div class="relative">
                                            <input type="password"
                                                   name="password_confirmation"
                                                   id="password_confirmation"
                                                   class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                   placeholder="Xác nhận mật khẩu mới">
                                            <button type="button" @click="togglePassword('password_confirmation')" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                <i class="fas fa-eye text-gray-400 hover:text-gray-600" x-show="!showPasswordConfirm"></i>
                                                <i class="fas fa-eye-slash text-gray-400 hover:text-gray-600" x-show="showPasswordConfirm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Role Assignment -->
                        <div class="mt-8 pt-8 border-t border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <i class="fas fa-shield-alt mr-2 text-indigo-500"></i>
                                Phân quyền
                            </h4>

                            <!-- Current Roles Display -->
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mb-6">
                                <h5 class="text-blue-900 font-semibold mb-3 flex items-center">
                                    <i class="fas fa-info-circle mr-2"></i>Roles hiện tại
                                </h5>
                                <div class="flex flex-wrap gap-3">
                                    @foreach($user->roles as $role)
                                        @php
                                            $roleColors = [
                                                'Super Admin' => 'bg-red-100 text-red-800 border-red-200',
                                                'Admin' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
                                                'Manager' => 'bg-blue-100 text-blue-800 border-blue-200',
                                                'User' => 'bg-green-100 text-green-800 border-green-200'
                                            ];
                                            $colorClass = $roleColors[$role->name] ?? 'bg-gray-100 text-gray-800 border-gray-200';
                                        @endphp
                                        <div class="flex items-center {{ $colorClass }} px-4 py-2 rounded-lg border">
                                            @if($role->name === 'Super Admin')
                                                <i class="fas fa-crown mr-2"></i>
                                            @elseif($role->name === 'Admin')
                                                <i class="fas fa-shield-alt mr-2"></i>
                                            @elseif($role->name === 'Manager')
                                                <i class="fas fa-user-tie mr-2"></i>
                                            @else
                                                <i class="fas fa-user mr-2"></i>
                                            @endif
                                            <span class="font-medium">{{ $role->name }}</span>
                                            <span class="ml-2 text-xs">({{ $role->permissions->count() }} permissions)</span>
                                        </div>
                                    @endforeach
                                    @if($user->roles->count() === 0)
                                        <span class="text-gray-500 italic">Chưa có role nào được gán</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Roles Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                @foreach($roles as $role)
                                    @php
                                        // Define role colors and restrictions
                                        $roleConfig = [
                                            'Super Admin' => [
                                                'color' => 'border-red-300 bg-red-50 hover:bg-red-100 text-red-900',
                                                'icon' => 'fas fa-crown text-red-600',
                                                'canAssign' => auth()->user()->hasRole('Super Admin'),
                                                'description' => 'Toàn quyền quản trị hệ thống, có thể thực hiện mọi chức năng và quản lý tất cả users.'
                                            ],
                                            'Admin' => [
                                                'color' => 'border-yellow-300 bg-yellow-50 hover:bg-yellow-100 text-yellow-900',
                                                'icon' => 'fas fa-shield-alt text-yellow-600',
                                                'canAssign' => auth()->user()->hasAnyRole(['Super Admin', 'Admin']),
                                                'description' => 'Quản trị viên với quyền quản lý users, roles và hầu hết các chức năng hệ thống.'
                                            ],
                                            'Manager' => [
                                                'color' => 'border-blue-300 bg-blue-50 hover:bg-blue-100 text-blue-900',
                                                'icon' => 'fas fa-user-tie text-blue-600',
                                                'canAssign' => auth()->user()->hasAnyRole(['Super Admin', 'Admin']),
                                                'description' => 'Quản lý với quyền hạn محدود, có thể xem và chỉnh sửa một số dữ liệu.'
                                            ],
                                            'User' => [
                                                'color' => 'border-green-300 bg-green-50 hover:bg-green-100 text-green-900',
                                                'icon' => 'fas fa-user text-green-600',
                                                'canAssign' => true,
                                                'description' => 'Người dùng thường với quyền truy cập cơ bản vào hệ thống.'
                                            ]
                                        ];

                                        $config = $roleConfig[$role->name] ?? [
                                            'color' => 'border-gray-300 bg-gray-50 hover:bg-gray-100 text-gray-900',
                                            'icon' => 'fas fa-user text-gray-600',
                                            'canAssign' => true,
                                            'description' => 'Role tùy chỉnh trong hệ thống.'
                                        ];

                                        $isChecked = in_array($role->name, old('roles', $userRoles));
                                    @endphp

                                    <div class="border rounded-xl {{ $config['color'] }} {{ $config['canAssign'] ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed' }} transition-all duration-200 {{ $config['canAssign'] ? 'hover:shadow-md' : '' }}">
                                        <label class="block p-6 {{ $config['canAssign'] ? 'cursor-pointer' : 'cursor-not-allowed' }}">
                                            <div class="flex items-start">
                                                <div class="flex items-center h-6">
                                                    <input type="checkbox"
                                                           name="roles[]"
                                                           value="{{ $role->name }}"
                                                           {{ $isChecked ? 'checked' : '' }}
                                                           {{ $config['canAssign'] ? '' : 'disabled' }}
                                                           @if($role->name === 'Super Admin')
                                                               @change="confirmSuperAdmin($event)"
                                                           @endif
                                                           @change="updateSelectedRoles"
                                                           class="h-5 w-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 focus:ring-2 {{ $config['canAssign'] ? '' : 'opacity-50' }}">
                                                </div>

                                                <div class="ml-4 flex-1">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <i class="{{ $config['icon'] }} mr-2"></i>
                                                            <span class="font-semibold">{{ $role->name }}</span>
                                                            @if(!$config['canAssign'])
                                                                <span class="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">Không có quyền</span>
                                                            @endif
                                                        </div>
                                                        <span class="text-sm opacity-70">{{ $role->permissions->count() }} permissions</span>
                                                    </div>

                                                    <!-- Role Description -->
                                                    <p class="mt-2 text-sm opacity-80">
                                                        {{ $config['description'] }}
                                                    </p>

                                                    <!-- Permission Preview -->
                                                    @if($role->permissions->count() > 0)
                                                        <div class="mt-4">
                                                            <details class="group">
                                                                <summary class="text-xs text-indigo-600 hover:text-indigo-800 cursor-pointer select-none flex items-center">
                                                                    <i class="fas fa-key mr-1"></i>
                                                                    Xem permissions ({{ $role->permissions->count() }})
                                                                    <i class="fas fa-chevron-down ml-1 transition-transform group-open:rotate-180"></i>
                                                                </summary>
                                                                <div class="mt-3 ml-4 text-xs opacity-70 max-h-32 overflow-y-auto">
                                                                    <div class="space-y-1">
                                                                        @foreach($role->permissions->take(8) as $permission)
                                                                            <div class="flex items-center">
                                                                                <i class="fas fa-key mr-2 opacity-50 text-xs"></i>
                                                                                <span>{{ $permission->name }}</span>
                                                                            </div>
                                                                        @endforeach
                                                                        @if($role->permissions->count() > 8)
                                                                            <div class="opacity-60 italic">
                                                                                ... và {{ $role->permissions->count() - 8 }} permissions khác
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </details>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            </div>

                            @error('roles')
                            <p class="mt-3 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                            </p>
                            @enderror

                            <!-- Selected Roles Summary -->
                            <div class="mt-6 p-4 bg-gray-50 rounded-xl" x-show="selectedRoles.length > 0">
                                <h5 class="font-medium text-gray-900 mb-2 flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    Roles được chọn (<span x-text="selectedRoles.length"></span>)
                                </h5>
                                <div class="flex flex-wrap gap-2">
                                    <template x-for="role in selectedRoles" :key="role">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                                            <span x-text="role"></span>
                                        </span>
                                    </template>
                                </div>
                            </div>

                            <!-- Role Change Warning -->
                            @if($user->roles->count() > 0)
                                <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <div class="flex">
                                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-3 mt-1"></i>
                                        <div class="text-sm text-yellow-800">
                                            <h4 class="font-semibold">Lưu ý khi thay đổi roles:</h4>
                                            <ul class="mt-2 list-disc list-inside space-y-1">
                                                <li>Thay đổi role sẽ ảnh hưởng đến quyền truy cập của user ngay lập tức</li>
                                                <li>User có thể mất quyền truy cập một số tính năng nếu role bị thu hồi</li>
                                                <li>Super Admin chỉ có thể được gán bởi Super Admin khác</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Form Actions -->
                        <div class="mt-8 pt-6 border-t border-gray-200 flex justify-between">
                            <div class="flex space-x-3">
                                <!-- Quick Role Presets -->
                                <div class="flex flex-wrap gap-2">
                                    <button type="button" @click="selectRolePreset('basic')"
                                            class="text-xs px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                        <i class="fas fa-user mr-1"></i>User cơ bản
                                    </button>
                                    @if(auth()->user()->hasAnyRole(['Super Admin', 'Admin']))
                                        <button type="button" @click="selectRolePreset('manager')"
                                                class="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                                            <i class="fas fa-user-tie mr-1"></i>Quản lý
                                        </button>
                                        <button type="button" @click="selectRolePreset('admin')"
                                                class="text-xs px-3 py-1 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 transition-colors">
                                            <i class="fas fa-shield-alt mr-1"></i>Quản trị
                                        </button>
                                    @endif
                                    <button type="button" @click="clearRoles()"
                                            class="text-xs px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors">
                                        <i class="fas fa-times mr-1"></i>Xóa tất cả
                                    </button>
                                </div>
                            </div>

                            <div class="flex space-x-3">
                                <a href="{{ route('admin.users.index') }}"
                                   class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                                    <i class="fas fa-times mr-2"></i>
                                    Hủy bỏ
                                </a>

                                @if($user->id !== auth()->id())
                                    <button type="button"
                                            @click="showDeleteModal = true"
                                            class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-xl font-semibold hover:from-red-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                        <i class="fas fa-trash mr-2"></i>
                                        Xóa user
                                    </button>
                                @endif

                                <button type="submit"
                                        class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                    <i class="fas fa-save mr-2"></i>
                                    Cập nhật thông tin
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Delete Confirmation Modal -->
            @if($user->id !== auth()->id())
                <div x-show="showDeleteModal"
                     x-transition.opacity
                     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
                     @click.self="showDeleteModal = false">
                    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-xl bg-white">
                        <div class="mt-3 text-center">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Xác nhận xóa user</h3>
                            <div class="mt-2 px-7 py-3">
                                <p class="text-sm text-gray-500 mb-4">
                                    Bạn có chắc chắn muốn xóa user <strong>{{ $user->name }}</strong>?
                                    <br><br>
                                    <span class="text-red-600 font-medium">Hành động này không thể hoàn tác!</span>
                                </p>

                                @if($user->hasRole('Super Admin'))
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                                        <div class="flex">
                                            <i class="fas fa-crown text-red-500 mr-2 mt-0.5"></i>
                                            <div class="text-sm text-red-800">
                                                <strong>Cảnh báo:</strong> Đây là Super Admin!
                                                Việc xóa có thể ảnh hưởng nghiêm trọng đến hệ thống.
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="flex gap-3 justify-center">
                                <button @click="showDeleteModal = false"
                                        class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-lg hover:bg-gray-400 transition-colors">
                                    Hủy bỏ
                                </button>
                                <form method="POST" action="{{ route('admin.users.destroy', $user) }}" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors">
                                        <i class="fas fa-trash mr-1"></i>Xác nhận xóa
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        @push('scripts')
            <script>
                function userEditForm() {
                    return {
                        showPassword: false,
                        showPasswordConfirm: false,
                        showDeleteModal: false,
                        selectedRoles: [],

                        init() {
                            // Initialize selected roles from checkboxes
                            this.updateSelectedRoles();
                        },

                        togglePassword(field) {
                            if (field === 'password') {
                                this.showPassword = !this.showPassword;
                                const input = document.getElementById('password');
                                input.type = this.showPassword ? 'text' : 'password';
                            } else if (field === 'password_confirmation') {
                                this.showPasswordConfirm = !this.showPasswordConfirm;
                                const input = document.getElementById('password_confirmation');
                                input.type = this.showPasswordConfirm ? 'text' : 'password';
                            }
                        },

                        updateSelectedRoles() {
                            const checkboxes = document.querySelectorAll('input[name="roles[]"]:checked');
                            this.selectedRoles = Array.from(checkboxes).map(cb => cb.value);
                        },

                        confirmSuperAdmin(event) {
                            if (event.target.checked) {
                                if (!confirm('Bạn có chắc chắn muốn gán role Super Admin? Đây là quyền cao nhất trong hệ thống.')) {
                                    event.target.checked = false;
                                    this.updateSelectedRoles();
                                    return;
                                }
                            }
                            this.updateSelectedRoles();
                        },

                        selectRolePreset(preset) {
                            // Clear all checkboxes first
                            this.clearRoles();

                            // Select based on preset
                            const roleMap = {
                                'basic': ['User'],
                                'manager': ['Manager', 'User'],
                                'admin': ['Admin', 'Manager', 'User']
                            };

                            const rolesToSelect = roleMap[preset] || [];
                            rolesToSelect.forEach(roleName => {
                                const checkbox = document.querySelector(`input[name="roles[]"][value="${roleName}"]`);
                                if (checkbox && !checkbox.disabled) {
                                    checkbox.checked = true;
                                }
                            });

                            this.updateSelectedRoles();
                        },

                        clearRoles() {
                            document.querySelectorAll('input[name="roles[]"]').forEach(cb => {
                                cb.checked = false;
                            });
                            this.updateSelectedRoles();
                        }
                    }
                }
            </script>
        @endpush
    @endsection
