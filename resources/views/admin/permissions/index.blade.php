@extends('layouts.admin')

@section('title', 'Danh sách Permissions')
@section('page-title', 'Quản lý Permissions')

@section('content')
    <div class="space-y-6">
        <!-- Route Sync Card -->
        <div class="bg-white rounded-lg shadow" x-data="routeSyncManager()">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Route Sync Management</h3>
                <p class="text-sm text-gray-600 mt-1">Tự động tạo permissions từ route names trong ứng dụng</p>
            </div>

            <div class="p-6">
                <!-- Sync Status -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600" x-text="stats.total_admin_routes || '-'"></div>
                        <div class="text-sm text-gray-600">Total Admin Routes</div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-600" x-text="stats.existing_permissions || '-'"></div>
                        <div class="text-sm text-gray-600">Existing Permissions</div>
                    </div>
                    <div class="bg-yellow-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-600" x-text="stats.missing_permissions || '-'"></div>
                        <div class="text-sm text-gray-600">Missing Permissions</div>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-purple-600">{{ $permissions->count() }}</div>
                        <div class="text-sm text-gray-600">Total Permissions</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3 mb-6">
                    <button @click="loadStats()"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Kiểm tra Status
                    </button>

                    <form method="POST" action="{{ route('admin.permissions.sync-routes') }}" class="inline"
                          onsubmit="return confirm('Bạn có muốn sync tất cả route permissions? Thao tác này sẽ tạo permissions mới từ routes.')">
                        @csrf
                        <button type="submit"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                            <i class="fas fa-download mr-2"></i>Sync Routes
                        </button>
                    </form>

                    <form method="POST" action="{{ route('admin.permissions.cleanup') }}" class="inline"
                          onsubmit="return confirm('Bạn có muốn cleanup permissions không sử dụng? Thao tác này sẽ xóa các permissions không còn route tương ứng.')">
                        @csrf
                        <button type="submit"
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                            <i class="fas fa-trash-alt mr-2"></i>Cleanup Orphaned
                        </button>
                    </form>

                    @canroute('admin.permissions.create')
                    <a href="{{ route('admin.permissions.create') }}"
                       class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Tạo Permission
                    </a>
                    @endcanroute
                </div>

                <!-- Missing Routes Alert -->
                <div x-show="stats.missing_permissions > 0"
                     class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-3 mt-1"></i>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-800">Missing Route Permissions</h4>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p x-text="`Có ${stats.missing_permissions} routes chưa có permissions tương ứng.`"></p>
                                <div x-show="stats.missing_routes && stats.missing_routes.length > 0" class="mt-2">
                                    <p class="font-medium">Missing routes:</p>
                                    <ul class="list-disc list-inside mt-1 max-h-32 overflow-y-auto">
                                        <template x-for="route in stats.missing_routes" :key="route">
                                            <li x-text="route" class="text-xs"></li>
                                        </template>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Success Messages -->
                @if(session('sync_result'))
                    <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <i class="fas fa-check-circle text-green-600 mr-3 mt-1"></i>
                            <div class="text-sm text-green-700">
                                @php $result = session('sync_result'); @endphp
                                <p><strong>Sync Result:</strong></p>
                                <ul class="mt-1">
                                    <li>• New permissions: {{ count($result['new_permissions']) }}</li>
                                    <li>• Existing permissions: {{ count($result['existing_permissions']) }}</li>
                                    <li>• Total routes processed: {{ $result['total_routes'] }}</li>
                                    @if(!empty($result['updated_roles']))
                                        <li>• Updated roles: {{ implode(', ', $result['updated_roles']) }}</li>
                                    @endif
                                </ul>
                                @if(!empty($result['new_permissions']))
                                    <details class="mt-2">
                                        <summary class="cursor-pointer font-medium">View new permissions ({{ count($result['new_permissions']) }})</summary>
                                        <ul class="mt-1 ml-4 text-xs max-h-32 overflow-y-auto">
                                            @foreach($result['new_permissions'] as $permission)
                                                <li>• {{ $permission }}</li>
                                            @endforeach
                                        </ul>
                                    </details>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Permissions List -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Danh sách Permissions</h3>
                    <div class="text-sm text-gray-600">
                        Tổng: {{ $permissions->total() }} permissions
                    </div>
                </div>
            </div>

            <div class="p-6">
                <!-- Search -->
                <div class="mb-6">
                    <input type="text" placeholder="Tìm kiếm permissions..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- Permissions Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permission Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($permissions as $permission)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        @if(str_starts_with($permission->name, 'admin.'))
                                            <i class="fas fa-route text-blue-600 mr-3"></i>
                                        @else
                                            <i class="fas fa-key text-purple-600 mr-3"></i>
                                        @endif
                                        <div class="text-sm font-medium text-gray-900">{{ $permission->name }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if(str_starts_with($permission->name, 'admin.'))
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Route Permission
                                    </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Custom Permission
                                    </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex flex-wrap gap-1">
                                        @foreach($permission->roles->take(3) as $role)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ $role->name }}
                                    </span>
                                        @endforeach
                                        @if($permission->roles->count() > 3)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        +{{ $permission->roles->count() - 3 }} more
                                    </span>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $permission->created_at->format('d/m/Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        @canroute('admin.permissions.show')
                                        <a href="{{ route('admin.permissions.show', $permission) }}" class="text-blue-600 hover:text-blue-900 transition-colors">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endcanroute
                                        @canroute('admin.permissions.edit')
                                        <a href="{{ route('admin.permissions.edit', $permission) }}" class="text-indigo-600 hover:text-indigo-900 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcanroute
                                        @canroute('admin.permissions.destroy')
                                        @if($permission->roles()->count() == 0)
                                            <form method="POST" action="{{ route('admin.permissions.destroy', $permission) }}" class="inline"
                                                  onsubmit="return confirm('Bạn có chắc chắn muốn xóa permission này?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900 transition-colors">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                        @endcanroute
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $permissions->links() }}
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            function routeSyncManager() {
                return {
                    stats: {},

                    async loadStats() {
                        try {
                            const response = await fetch('{{ route("admin.permissions.sync-status") }}');
                            this.stats = await response.json();
                        } catch (error) {
                            console.error('Failed to load sync stats:', error);
                        }
                    },

                    init() {
                        this.loadStats();
                    }
                }
            }
        </script>
    @endpush
@endsection
