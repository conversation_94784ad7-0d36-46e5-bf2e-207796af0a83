@extends('layouts.admin')

@section('title', 'Chi tiết Permission')
@section('page-title', 'Chi tiết Permission: ' . $permission->name)

@section('content')
    <div class="max-w-6xl mx-auto space-y-6">
        <!-- Permission Info Card -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Thông tin Permission</h3>
                    <div class="flex space-x-2">
                        @canroute('admin.permissions.edit')
                        <a href="{{ route('admin.permissions.edit', $permission) }}"
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                            <i class="fas fa-edit mr-2"></i>Chỉnh sửa
                        </a>
                        @endcanroute
                        <a href="{{ route('admin.permissions.index') }}"
                           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>Quay lại
                        </a>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="col-span-1">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-key text-3xl text-purple-600"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 break-all">{{ $permission->name }}</h4>
                            <p class="text-sm text-gray-500">Permission ID: {{ $permission->id }}</p>
                        </div>
                    </div>

                    <div class="col-span-3">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">{{ $permission->roles()->count() }}</div>
                                <div class="text-sm text-gray-600">Roles có permission</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">
                                    {{ $permission->roles()->withCount('users')->get()->sum('users_count') }}
                                </div>
                                <div class="text-sm text-gray-600">Users qua roles</div>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                                <div class="text-sm font-bold text-yellow-600">{{ $permission->created_at->format('d/m/Y') }}</div>
                                <div class="text-sm text-gray-600">Ngày tạo</div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h5 class="font-medium text-gray-900 mb-2">Mô tả:</h5>
                            <p class="text-gray-600">
                                Permission "{{ $permission->name }}" được sử dụng bởi {{ $permission->roles()->count() }} roles.
                                @if(str_starts_with($permission->name, 'admin.'))
                                    <span class="text-blue-600 font-medium">(Route permission)</span>
                                @endif
                                @if($permission->created_at->diffInDays() < 7)
                                    <span class="text-green-600 font-medium">(Permission mới tạo)</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Roles with this Permission -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Roles có Permission này ({{ $permission->roles()->count() }})</h3>
            </div>

            <div class="p-6">
                @if($permission->roles()->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($permission->roles as $role)
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user-tag text-green-600"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $role->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $role->users()->count() }} users</div>
                                    </div>
                                </div>
                                @canroute('admin.roles.show')
                                <a href="{{ route('admin.roles.show', $role) }}" class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                @endcanroute
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-user-tag text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">Chưa có role nào sử dụng permission này</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Route Information (if applicable) -->
        @if(str_starts_with($permission->name, 'admin.'))
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">Thông tin Route</h3>
                </div>

                <div class="p-6">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="font-medium text-gray-900 mb-2">Route Name:</h5>
                                <code class="text-sm bg-white px-2 py-1 rounded border">{{ $permission->name }}</code>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900 mb-2">URL Pattern:</h5>
                                <code class="text-sm bg-white px-2 py-1 rounded border">
                                    @php
                                        $routeName = $permission->name;
                                        $urlPattern = str_replace(['admin.', '.'], ['admin/', '/'], $routeName);
                                        if (str_ends_with($urlPattern, 'index')) {
                                            $urlPattern = str_replace('/index', '', $urlPattern);
                                        } elseif (str_ends_with($urlPattern, 'create')) {
                                            $urlPattern .= '';
                                        } elseif (str_ends_with($urlPattern, 'edit')) {
                                            $urlPattern = str_replace('/edit', '/{id}/edit', $urlPattern);
                                        } elseif (str_ends_with($urlPattern, 'show')) {
                                            $urlPattern = str_replace('/show', '/{id}', $urlPattern);
                                        } elseif (str_ends_with($urlPattern, 'destroy')) {
                                            $urlPattern = str_replace('/destroy', '/{id}', $urlPattern);
                                        }
                                    @endphp
                                    /{{ $urlPattern }}
                                </code>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h5 class="font-medium text-gray-900 mb-2">Mô tả chức năng:</h5>
                            <p class="text-sm text-gray-600">
                                @php
                                    $description = match(true) {
                                        str_ends_with($permission->name, '.index') => 'Xem danh sách',
                                        str_ends_with($permission->name, '.create') => 'Tạo mới',
                                        str_ends_with($permission->name, '.store') => 'Lưu dữ liệu mới',
                                        str_ends_with($permission->name, '.show') => 'Xem chi tiết',
                                        str_ends_with($permission->name, '.edit') => 'Chỉnh sửa',
                                        str_ends_with($permission->name, '.update') => 'Cập nhật dữ liệu',
                                        str_ends_with($permission->name, '.destroy') => 'Xóa',
                                        $permission->name === 'admin.dashboard' => 'Truy cập trang chủ admin',
                                        default => 'Chức năng khác'
                                    };
                                @endphp
                                {{ $description }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection
