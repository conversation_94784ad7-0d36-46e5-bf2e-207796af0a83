@extends('layouts.admin')

@section('title', 'Tạo Permission')
@section('page-title', 'Tạo Permission mới')

@section('content')
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Thông tin Permission</h3>
            </div>

            <form method="POST" action="{{ route('admin.permissions.store') }}" class="p-6">
                @csrf

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tên Permission <span class="text-red-500">*</span></label>
                    <input type="text" name="name" value="{{ old('name') }}" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror"
                           placeholder="admin.module.action">
                    @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500">
                        Định dạng khuyến nghị: admin.module.action (ví dụ: admin.users.create, admin.posts.edit)
                    </p>
                </div>

                <!-- Permission Examples -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Ví dụ các Permission thường dùng</label>
                    <div class="bg-gray-50 rounded-md p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <h5 class="font-medium text-gray-900 mb-2">User Management:</h5>
                                <ul class="space-y-1 text-gray-600">
                                    <li>• admin.users.index</li>
                                    <li>• admin.users.create</li>
                                    <li>• admin.users.edit</li>
                                    <li>• admin.users.destroy</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900 mb-2">Content Management:</h5>
                                <ul class="space-y-1 text-gray-600">
                                    <li>• admin.posts.index</li>
                                    <li>• admin.posts.create</li>
                                    <li>• admin.posts.publish</li>
                                    <li>• admin.posts.destroy</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Templates -->
                <div class="mb-6" x-data="permissionTemplates()">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Templates nhanh</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <select x-model="selectedModule" @change="updatePermissionName()"
                                class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Chọn module</option>
                            <option value="users">Users</option>
                            <option value="roles">Roles</option>
                            <option value="permissions">Permissions</option>
                            <option value="posts">Posts</option>
                            <option value="categories">Categories</option>
                            <option value="settings">Settings</option>
                        </select>

                        <select x-model="selectedAction" @change="updatePermissionName()"
                                class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Chọn action</option>
                            <option value="index">Index (xem danh sách)</option>
                            <option value="create">Create (tạo mới)</option>
                            <option value="store">Store (lưu dữ liệu)</option>
                            <option value="show">Show (xem chi tiết)</option>
                            <option value="edit">Edit (chỉnh sửa)</option>
                            <option value="update">Update (cập nhật)</option>
                            <option value="destroy">Destroy (xóa)</option>
                        </select>

                        <button type="button" @click="applyTemplate()"
                                :disabled="!selectedModule || !selectedAction"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                            Áp dụng
                        </button>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3">
                    <a href="{{ route('admin.permissions.index') }}"
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        Hủy
                    </a>
                    <button type="submit"
                            class="px-4 py-2 bg-purple-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-purple-700 transition-colors">
                        Tạo Permission
                    </button>
                </div>
            </form>
        </div>
    </div>

    @push('scripts')
        <script>
            function permissionTemplates() {
                return {
                    selectedModule: '',
                    selectedAction: '',

                    updatePermissionName() {
                        // This function updates in real-time but doesn't apply until button is clicked
                        // You can add visual feedback here if needed
                    },

                    applyTemplate() {
                        if (this.selectedModule && this.selectedAction) {
                            const permissionName = `admin.${this.selectedModule}.${this.selectedAction}`;
                            document.querySelector('input[name="name"]').value = permissionName;
                        }
                    }
                }
            }
        </script>
    @endpush
@endsection
