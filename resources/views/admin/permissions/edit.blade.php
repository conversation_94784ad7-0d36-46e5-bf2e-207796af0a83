@extends('layouts.admin')

@section('title', 'Chỉnh sửa Permission')
@section('page-title', 'Chỉnh sửa Permission: ' . $permission->name)

@section('content')
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Thông tin Permission</h3>
            </div>

            <form method="POST" action="{{ route('admin.permissions.update', $permission) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tên Permission <span class="text-red-500">*</span></label>
                    <input type="text" name="name" value="{{ old('name', $permission->name) }}" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                    @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500">
                        Định dạng khuyến nghị: admin.module.action (ví dụ: admin.users.create)
                    </p>
                </div>

                <!-- Current Permission Info -->
                <div class="mb-6 p-4 bg-gray-50 rounded-md">
                    <h4 class="font-medium text-gray-900 mb-3">Thông tin hiện tại</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-600">Roles sử dụng:</span>
                            <span class="text-gray-900">{{ $permission->roles()->count() }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-600">Tổng Users:</span>
                            <span class="text-gray-900">{{ $permission->roles()->withCount('users')->get()->sum('users_count') }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-600">Ngày tạo:</span>
                            <span class="text-gray-900">{{ $permission->created_at->format('d/m/Y H:i') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Warning if permission is used -->
                @if($permission->roles()->count() > 0)
                    <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div class="flex">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800">Cảnh báo</h4>
                                <p class="text-sm text-yellow-700 mt-1">
                                    Permission này đang được sử dụng bởi {{ $permission->roles()->count() }} roles.
                                    Việc thay đổi tên có thể ảnh hưởng đến quyền truy cập của users.
                                </p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Roles using this permission -->
                @if($permission->roles()->count() > 0)
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Roles đang sử dụng permission này</label>
                        <div class="border border-gray-200 rounded-md p-4 max-h-40 overflow-y-auto">
                            <div class="flex flex-wrap gap-2">
                                @foreach($permission->roles as $role)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            {{ $role->name }}
                            <span class="ml-1 text-green-600">({{ $role->users()->count() }} users)</span>
                        </span>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3">
                    <a href="{{ route('admin.permissions.index') }}"
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        Hủy
                    </a>
                    <button type="submit"
                            class="px-4 py-2 bg-purple-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-purple-700 transition-colors">
                        Cập nhật Permission
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
