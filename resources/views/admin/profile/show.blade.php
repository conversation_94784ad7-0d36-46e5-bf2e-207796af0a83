@extends('layouts.admin')

@section('title', 'Profile')

@section('breadcrumb')
    <li class="flex items-center">
        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
        <span class="text-gray-500">Profile</span>
    </li>
@endsection

@section('content-header')
    @section('subtitle', 'View your profile information')

    @section('content-header-actions')
        <a href="{{ route('admin.profile.edit') }}"
           class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            <i class="fas fa-edit mr-2"></i>
            Edit Profile
        </a>
    @endsection

    @section('content')
        <div class="max-w-4xl mx-auto">
            <!-- Profile Header -->
            <div class="bg-white shadow-admin rounded-lg overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-32"></div>
                <div class="relative px-6 pb-6">
                    <div class="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                        <div class="relative -mt-16 mb-4 sm:mb-0">
                            @if(auth()->user()->avatar)
                                <img src="{{ \App\Helper\helper::getImageGoogleDrive(auth()->user()->avatar) }}"
                                     alt="{{ auth()->user()->name }}"
                                     class="h-32 w-32 rounded-full object-cover border-4 border-white shadow-lg">
                            @else
                                <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&size=128&background=6366f1&color=fff"
                                     alt="{{ auth()->user()->name }}"
                                     class="h-32 w-32 rounded-full object-cover border-4 border-white shadow-lg">
                            @endif
                        </div>

                        <div class="flex-1 min-w-0">
                            <h1 class="text-2xl font-bold text-gray-900">{{ auth()->user()->name }}</h1>
                            <div class="flex flex-wrap items-center mt-2 space-x-4">
                        <span class="text-gray-600">
                            <i class="fas fa-envelope mr-1"></i>
                            {{ auth()->user()->email }}
                        </span>

                                @if(auth()->user()->phone)
                                    <span class="text-gray-600">
                                <i class="fas fa-phone mr-1"></i>
                                {{ auth()->user()->phone }}
                            </span>
                                @endif

                                @if(auth()->user()->location)
                                    <span class="text-gray-600">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                {{ auth()->user()->location }}
                            </span>
                                @endif
                            </div>

                            <div class="flex flex-wrap items-center mt-3 space-x-2">
                                {!! auth()->user()->role_badge !!}
                                {!! auth()->user()->verification_badge !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Details -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Information -->
                <div class="lg:col-span-2">
                    <div class="bg-white shadow-admin rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i class="fas fa-info-circle mr-2 text-gray-500"></i>
                                About
                            </h3>
                        </div>

                        <div class="p-6">
                            @if(auth()->user()->bio)
                                <div class="mb-6">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Bio</h4>
                                    <p class="text-gray-900 leading-relaxed">{{ auth()->user()->bio }}</p>
                                </div>
                            @endif

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Email</h4>
                                    <div class="flex items-center">
                                        <span class="text-gray-900">{{ auth()->user()->email }}</span>
                                        @if(auth()->user()->hasVerifiedEmail())
                                            <i class="fas fa-check-circle text-green-500 ml-2" title="Verified"></i>
                                        @else
                                            <i class="fas fa-exclamation-circle text-yellow-500 ml-2" title="Unverified"></i>
                                        @endif
                                    </div>
                                </div>

                                @if(auth()->user()->phone)
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">Phone</h4>
                                        <p class="text-gray-900">{{ auth()->user()->phone }}</p>
                                    </div>
                                @endif

                                @if(auth()->user()->location)
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">Location</h4>
                                        <p class="text-gray-900">{{ auth()->user()->location }}</p>
                                    </div>
                                @endif

                                @if(auth()->user()->website)
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">Website</h4>
                                        <a href="{{ auth()->user()->website }}"
                                           target="_blank"
                                           class="text-indigo-600 hover:text-indigo-800">
                                            {{ str_replace(['http://', 'https://'], '', auth()->user()->website) }}
                                            <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Information -->
                <div class="space-y-6">
                    <!-- Account Details -->
                    <div class="bg-white shadow-admin rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i class="fas fa-user-cog mr-2 text-gray-500"></i>
                                Account Details
                            </h3>
                        </div>

                        <div class="p-6 space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-1">Member Since</h4>
                                <p class="text-gray-900">{{ auth()->user()->created_at->format('F j, Y') }}</p>
                                <p class="text-xs text-gray-500">{{ auth()->user()->created_at->diffForHumans() }}</p>
                            </div>

                            @if(auth()->user()->last_login_at)
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-1">Last Login</h4>
                                    <p class="text-gray-900">{{ auth()->user()->last_login_at->format('M j, Y g:i A') }}</p>
                                    <p class="text-xs text-gray-500">{{ auth()->user()->last_login_at->diffForHumans() }}</p>
                                </div>
                            @endif

                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-1">Account Type</h4>
                                <div>
                                    {!! auth()->user()->role_badge !!}
                                </div>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-1">Email Status</h4>
                                <div>
                                    {!! auth()->user()->verification_badge !!}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white shadow-admin rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i class="fas fa-bolt mr-2 text-gray-500"></i>
                                Quick Actions
                            </h3>
                        </div>

                        <div class="p-6 space-y-3">
                            <a href="{{ route('admin.profile.edit') }}"
                               class="block w-full text-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-edit mr-2"></i>
                                Edit Profile
                            </a>

                            <a href="{{ route('admin.dashboard') }}"
                               class="block w-full text-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-tachometer-alt mr-2"></i>
                                Dashboard
                            </a>

                            <a href="{{ route('admin.settings') }}"
                               class="block w-full text-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-cog mr-2"></i>
                                Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endsection
