@extends('layouts.admin')

@section('title', 'Cài đặt tài khoản')

@section('breadcrumb')
    <li class="flex items-center">
        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
        <span class="text-gray-500">Thông tin tài khoản</span>
    </li>
@endsection

@section('content-header')
    @section('subtitle', 'Thông tin tài khoản và bảo mật')

    @section('content')
        <div class="max-w-6xl mx-auto" x-data="profileManager()">
            <!-- Hero Section with Avatar -->
            <div class="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 rounded-2xl shadow-2xl overflow-hidden mb-8">
                <div class="relative px-8 py-12">
                    <div class="absolute inset-0 bg-black/20"></div>
                    <div class="relative flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
                        <!-- Avatar Upload Section -->
                        <div class="relative group">
                            <div class="relative">
                                @if($user->avatar)
                                    <img id="avatarPreview" src="{{ \App\Helper\helper::getImageGoogleDrive($user->avatar) }}"
                                         alt="{{ $user->name }}"
                                         class="w-32 h-32 rounded-full object-cover border-4 border-white/30 shadow-2xl group-hover:scale-105 transition-all duration-300">
                                @else
                                    <img id="avatarPreview" src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&size=128&background=ffffff&color=6366f1"
                                         alt="{{ $user->name }}"
                                         class="w-32 h-32 rounded-full object-cover border-4 border-white/30 shadow-2xl group-hover:scale-105 transition-all duration-300">
                                @endif

                                <!-- Upload Overlay -->
                                <label for="avatar" class="absolute inset-0 flex items-center justify-center bg-black/60 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 cursor-pointer">
                                    <i class="fas fa-camera text-white text-xl"></i>
                                    <input type="file" id="avatar" name="avatar" class="hidden" accept="image/*" @change="previewAvatar">
                                </label>
                            </div>

                            <!-- Avatar Actions -->
                            <div class="absolute -bottom-2 -right-2 flex space-x-1">
                                <button type="button" @click="$refs.avatarInput.click()"
                                        class="bg-white text-indigo-600 p-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110">
                                    <i class="fas fa-edit text-sm"></i>
                                </button>
                                @if($user->avatar)
                                    <button type="button" @click="removeAvatar()"
                                            class="bg-red-500 text-white p-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110">
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                @endif
                            </div>
                        </div>

                        <!-- User Info -->
                        <div class="text-center md:text-left text-white">
                            <h1 class="text-3xl font-bold mb-2">{{ $user->name }}</h1>
                            <p class="text-white/80 text-lg mb-4">{{ $user->email }}</p>
                            <div class="flex flex-wrap justify-center md:justify-start gap-3">
                                @if($user->is_admin)
                                    <span class="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-medium">
                                        <i class="fas fa-crown mr-1"></i>Admin
                                    </span>
                                @else
                                    <span class="bg-yellow-400 text-info-900 px-3 py-1 rounded-full text-sm font-medium">
                                        Nhân viên
                                    </span>
                                @endif
                                @if($user->email_verified_at)
                                    <span class="bg-green-400 text-green-900 px-3 py-1 rounded-full text-sm font-medium">
                                        <i class="fas fa-check-circle mr-1"></i>Đang hoạt động
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Tabs -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                <!-- Tab Navigation -->
                <div class="border-b border-gray-200">
                    <nav class="flex space-x-8 px-8" aria-label="Tabs">
                        <button @click="activeTab = 'profile'"
                                :class="activeTab === 'profile' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200">
                            <i class="fas fa-user mr-2"></i>Thông tin cơ bản
                        </button>
                        <button @click="activeTab = 'security'"
                                :class="activeTab === 'security' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200">
                            <i class="fas fa-shield-alt mr-2"></i>Mật khẩu
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-8">
                    <div x-show="activeTab === 'profile'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 translate-y-4" x-transition:enter-end="opacity-100 translate-y-0">
                        <form method="POST" action="{{ route('admin.profile.update') }}" enctype="multipart/form-data">
                            @csrf
                            @method('patch')

                            <input type="file" x-ref="avatarInput" name="avatar" class="hidden" accept="image/*" @change="previewAvatar">

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Left Column -->
                                <div class="space-y-6">
                                    <!-- Name -->
                                    <div class="group">
                                        <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-user mr-2 text-indigo-500"></i>Họ và tên
                                        </label>
                                        <input type="text" name="name" id="name"
                                               value="{{ old('name', $user->name) }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('name') border-red-300 @enderror"
                                               required>
                                        @error('name')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <!-- Email -->
                                    <div class="group">
                                        <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-envelope mr-2 text-indigo-500"></i>Email
                                        </label>
                                        <input type="email" name="email" id="email"
                                               value="{{ old('email', $user->email) }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('email') border-red-300 @enderror"
                                               required>
                                        @error('email')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                </div>
                                <div class="space-y-6">
                                    <div class="group">
                                        <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">
                                            <i class="fas fa-phone mr-2 text-indigo-500"></i>Số điện thoại
                                        </label>
                                        <input type="tel" name="phone" id="phone"
                                               value="{{ old('phone', $user->phone) }}"
                                               placeholder="0365645451"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('phone') border-red-300 @enderror">
                                        @error('phone')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                        </p>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-center pt-6 border-t border-gray-200">
                                <button type="submit" class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                    <i class="fas fa-save mr-2"></i>Cập nhật thông tin
                                </button>
                            </div>
                        </form>
                    </div>
                    <div x-show="activeTab === 'security'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 translate-y-4" x-transition:enter-end="opacity-100 translate-y-0">
                        <form method="POST" action="{{ route('admin.profile.password') }}">
                            @csrf
                            @method('put')

                            <div class="max-w-2xl">
                                <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6">
                                    <div class="flex items-center">
                                        <i class="fas fa-info-circle text-blue-500 text-xl mr-3"></i>
                                        <div>
                                            <h3 class="text-blue-900 font-semibold">Bảo mật mật khẩu</h3>
                                            <p class="text-blue-700 text-sm mt-1">Sử dụng mật khẩu mạnh với ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký hiệu.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- Current Password -->
                                <div class="group mb-6">
                                    <label for="current_password" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-lock mr-2 text-indigo-500"></i>Mật khẩu hiện tại
                                    </label>
                                    <input type="password" name="current_password" id="current_password"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('current_password', 'updatePassword') border-red-300 @enderror"
                                           required>
                                    @error('current_password', 'updatePassword')
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <!-- New Password -->
                                <div class="group mb-6">
                                    <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-key mr-2 text-indigo-500"></i>Mật khẩu mới
                                    </label>
                                    <input type="password" name="password" id="password"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 @error('password', 'updatePassword') border-red-300 @enderror"
                                           required>
                                    @error('password', 'updatePassword')
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <!-- Confirm Password -->
                                <div class="group mb-6">
                                    <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-check-double mr-2 text-indigo-500"></i>Xác nhận mật khẩu mới
                                    </label>
                                    <input type="password" name="password_confirmation" id="password_confirmation"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                           required>
                                </div>

                                <!-- Submit Button -->
                                <div class="flex justify-end">
                                    <button type="submit" class="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                        <i class="fas fa-shield-alt mr-2"></i>Cập nhật mật khẩu
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail())
            <form id="send-verification" method="post" action="{{ route('verification.send') }}">
                @csrf
            </form>
        @endif
    @endsection

    @push('scripts')
        <script>
            function profileManager() {
                return {
                    activeTab: 'profile',
                    showDeleteModal: false,

                    previewAvatar(event) {
                        const file = event.target.files[0];
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                document.getElementById('avatarPreview').src = e.target.result;
                            };
                            reader.readAsDataURL(file);
                        }
                    },

                    removeAvatar() {
                        if (confirm('Are you sure you want to remove your avatar?')) {
                            // You can implement AJAX call here or submit a form
                            fetch('{{ route("admin.profile.remove-avatar") }}', {
                                method: 'DELETE',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                    'Accept': 'application/json',
                                }
                            }).then(response => {
                                if (response.ok) {
                                    location.reload();
                                }
                            });
                        }
                    }
                }
            }

            // Bio character counter
            document.addEventListener('DOMContentLoaded', function() {
                const bioTextarea = document.getElementById('bio');
                if (bioTextarea) {
                    bioTextarea.addEventListener('input', function() {
                        const counter = this.parentElement.querySelector('[x-text*="length"]');
                        if (counter) {
                            counter.textContent = this.value.length + '/500';
                        }
                    });
                }
            });
        </script>
    @endpush
