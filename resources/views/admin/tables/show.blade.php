@extends('layouts.admin')

@section('title', $table->name)

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.tables.index') }}" class="text-gray-500 hover:text-gray-700">Bàn</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">{{ $table->name }}</span>
        </div>
    </li>
@endsection

@section('content-header-actions')
    <div class="flex items-center space-x-3">
        @if($table->isAvailable())
            <a href="{{ route('admin.orders.create', ['table_id' => $table->id]) }}"
               class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                <i class="fas fa-plus mr-2"></i>
                Tạo đơn hàng
            </a>
        @endif

        <a href="{{ route('admin.tables.edit', $table) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <i class="fas fa-edit mr-2"></i>
            Chỉnh sửa
        </a>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Table Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thông tin bàn</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Tên bàn</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $table->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Trạng thái</dt>
                        <dd class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $table->status_color }}">
                                {{ $table->status_text }}
                            </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Số chỗ ngồi</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $table->capacity }} chỗ</dd>
                    </div>
                    @if($table->location)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Vị trí</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $table->location }}</dd>
                        </div>
                    @endif
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Ngày tạo</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $table->created_at->format('d/m/Y H:i') }}</dd>
                    </div>
                </div>

                @if($table->description)
                    <div class="mt-6">
                        <dt class="text-sm font-medium text-gray-500">Mô tả</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $table->description }}</dd>
                    </div>
                @endif
            </div>
        </div>

        <!-- QR Code -->
{{--        <div class="bg-white rounded-lg shadow-sm border border-gray-200">--}}
{{--            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">--}}
{{--                <h3 class="text-lg font-medium text-gray-900">Mã QR</h3>--}}
{{--                <form action="{{ route('admin.tables.regenerate-qr', $table) }}" method="POST" class="inline">--}}
{{--                    @csrf--}}
{{--                    <button type="submit"--}}
{{--                            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">--}}
{{--                        <i class="fas fa-sync-alt mr-2"></i>--}}
{{--                        Tạo lại QR--}}
{{--                    </button>--}}
{{--                </form>--}}
{{--            </div>--}}
{{--            <div class="p-6">--}}
{{--                <div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">--}}
{{--                    @if($table->qr_code_url)--}}
{{--                        <div class="flex-shrink-0">--}}
{{--                            <img src="{{ $table->qr_code_url }}" alt="QR Code for {{ $table->name }}" class="w-32 h-32 border border-gray-200 rounded-lg">--}}
{{--                        </div>--}}
{{--                    @endif--}}
{{--                    <div class="flex-1">--}}
{{--                        <h4 class="text-sm font-medium text-gray-900 mb-2">Hướng dẫn sử dụng</h4>--}}
{{--                        <ul class="text-sm text-gray-600 space-y-1">--}}
{{--                            <li>• Khách hàng quét mã QR để truy cập menu đặt món</li>--}}
{{--                            <li>• Mã QR sẽ tự động nhận diện bàn {{ $table->name }}</li>--}}
{{--                            <li>• Khách có thể đặt món trực tiếp từ điện thoại</li>--}}
{{--                            <li>• Đơn hàng sẽ được gửi trực tiếp đến hệ thống quản lý</li>--}}
{{--                        </ul>--}}
{{--                        <div class="mt-4">--}}
{{--                            <p class="text-xs text-gray-500">URL: {{ $table->generateQrCode() }}</p>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}

        <!-- Current Order -->
        @if($table->currentOrder->first())
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Đơn hàng hiện tại</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">{){ $table->currentOrder->first()->order_number }}</h4>
                            <p class="text-sm text-gray-600">{{ $table->currentOrder->first()->customer->name ?? 'Khách vãng lai' }}</p>
                            <p class="text-xs text-gray-500">{{ $table->currentOrder->first()->order_time->format('d/m/Y H:i') }}</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $table->currentOrder->first()->status_color }}">
                                {{ $table->currentOrder->first()->status_text }}
                            </span>
                            <p class="text-sm font-medium text-gray-900 mt-1">{{ $table->currentOrder->first()->formatted_total_amount }}</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="{{ route('admin.orders.show', $table->currentOrder->first()) }}"
                           class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                            <i class="fas fa-eye mr-2"></i>
                            Xem chi tiết đơn hàng
                        </a>
                    </div>
                </div>
            </div>
        @endif

        <!-- Recent Orders -->
        @if($table->orders->count() > 0)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Lịch sử đơn hàng gần đây</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã đơn</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Khách hàng</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tổng tiền</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($table->orders as $order)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $order->order_number }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $order->customer->name ?? 'Khách vãng lai' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $order->status_color }}">
                                            {{ $order->status_text }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $order->formatted_total_amount }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $order->order_time->format('d/m/Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="{{ route('admin.orders.show', $order) }}"
                                           class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
    </div>
@endsection
