@extends('layouts.admin')

@section('title', 'Quản lý bàn')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">B<PERSON>n</span>
        </div>
    </li>
@endsection

@section('content-header-actions')
    <a href="{{ route('admin.tables.create') }}"
       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
        <i class="fas fa-plus mr-2"></i>
        Thêm bàn
    </a>
@endsection

@section('content')
    <a href="https://api.vietqr.io/image/970416-********-zahTi5g.jpg?accountName=NGUYEN%20THI%20PHUONG%20THAO%20&amount=1000000">aa</a>
    <div class="space-y-6">
        <!-- Filters -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <form method="GET" action="{{ route('admin.tables.index') }}" class="space-y-4 lg:space-y-0 lg:grid lg:grid-cols-4 lg:gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Tìm kiếm</label>
                    <div class="relative">
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                               placeholder="Tên bàn, mã bàn..."
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
                    <select name="status" id="status"
                            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Tất cả trạng thái</option>
                        <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Trống</option>
                        <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Có khách</option>
                        <option value="2" {{ request('status') === '2' ? 'selected' : '' }}>Đã đặt</option>
                        <option value="3" {{ request('status') === '3' ? 'selected' : '' }}>Bảo trì</option>
                    </select>
                </div>

                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Vị trí</label>
                    <select name="location" id="location"
                            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Tất cả vị trí</option>
                        @foreach($locations as $location)
                            <option value="{{ $location }}" {{ request('location') == $location ? 'selected' : '' }}>
                                {{ $location }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit"
                            class="w-full bg-gray-800 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i>Lọc
                    </button>
                </div>
            </form>
        </div>

        <!-- Tables Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            @forelse($tables as $table)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-lg font-semibold text-gray-900">{{ $table->name }}</h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $table->status_color }}">
                                {{ $table->status_text }}
                            </span>
                        </div>

                        <div class="flex items-center gap-3 mb-3" style="font-size: 13px;">
                            <div>
                                <i class="fas fa-users w-4"></i>
                                <span>{{ $table->capacity }} chỗ</span>
                            </div>
                            @if($table->location)
                                <div>
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>{{ $table->location }}</span>
                                </div>
                            @endif
                        </div>

                        @if($table->description)
                            <p class="text-sm text-gray-500 mb-3">{{ Str::limit($table->description, 60) }}</p>
                        @endif

                        <div class="flex items-center justify-between">
                            <div class="flex gap-1 space-x-2" style="font-size: 13px">
                                <a href="{{ route('admin.tables.show', $table) }}"
                                   class="text-indigo-600 hover:text-indigo-900 transition-colors duration-200"
                                   title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.tables.edit', $table) }}"
                                   class="text-yellow-600 hover:text-yellow-900 transition-colors duration-200"
                                   title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.tables.destroy', $table) }}" method="POST" class="inline"
                                      onsubmit="return confirm('Bạn có chắc chắn muốn xóa bàn này?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                            title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>

                            @if($table->isAvailable())
                                <a href="{{ route('admin.orders.create', ['table_id' => $table->id]) }}"
                                   class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700">
                                    <i class="fas fa-plus mr-1"></i>
                                    Đặt bàn
                                </a>
                            @elseif($table->currentOrder->first())
                                <a href="{{ route('admin.orders.show', $table->currentOrder->first()) }}"
                                   class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700">
                                    <i class="fas fa-receipt mr-1"></i>
                                    Xem đơn
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="text-center py-12">
                        <i class="fas fa-chair text-gray-300 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">Không có bàn nào</h3>
                        <p class="text-gray-500 mb-4">Bắt đầu bằng cách thêm bàn đầu tiên của bạn.</p>
                        <a href="{{ route('admin.tables.create') }}"
                           class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                            <i class="fas fa-plus mr-2"></i>
                            Thêm bàn
                        </a>
                    </div>
                </div>
            @endforelse
        </div>

        @if($tables->hasPages())
            <div class="bg-white px-6 py-3 border border-gray-200 rounded-lg">
                {{ $tables->links() }}
            </div>
        @endif
    </div>
@endsection
