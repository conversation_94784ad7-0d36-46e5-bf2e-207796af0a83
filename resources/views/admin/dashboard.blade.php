@extends('layouts.admin')

@section('title', 'Dashboard')

@section('content-header')
@section('subtitle', 'Welcome to your admin dashboard')

@section('content')
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Users -->
    <div class="bg-white overflow-hidden shadow-admin rounded-lg">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                        Total Users
                    </div>
                    <div class="text-2xl font-bold text-gray-900">
                        {{ number_format($stats['total_users']) }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Users -->
    <div class="bg-white overflow-hidden shadow-admin rounded-lg">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-check text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                        Active Users (30d)
                    </div>
                    <div class="text-2xl font-bold text-gray-900">
                        {{ number_format($stats['active_users']) }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Users Today -->
    <div class="bg-white overflow-hidden shadow-admin rounded-lg">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-plus text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                        New Today
                    </div>
                    <div class="text-2xl font-bold text-gray-900">
                        {{ number_format($stats['new_users_today']) }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Users This Week -->
    <div class="bg-white overflow-hidden shadow-admin rounded-lg">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                        New This Week
                    </div>
                    <div class="text-2xl font-bold text-gray-900">
                        {{ number_format($stats['new_users_week']) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Recent Users -->
    <div class="bg-white shadow-admin rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i class="fas fa-users mr-2 text-gray-500"></i>
                Recent Users
            </h3>
        </div>
        <div class="overflow-hidden">
            <div class="px-6 py-4">
                @if($recentUsers->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentUsers as $user)
                        <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                <img class="h-10 w-10 rounded-full object-cover"
                                     src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&background=6366f1&color=fff"
                                     alt="{{ $user->name }}">
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    {{ $user->name }}
                                </p>
                                <p class="text-sm text-gray-500 truncate">
                                    {{ $user->email }}
                                </p>
                            </div>
                            <div class="flex-shrink-0 text-sm text-gray-500">
                                {{ $user->created_at->diffForHumans() }}
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-8">No users found.</p>
                @endif
            </div>
        </div>
        <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
            <div class="text-sm text-right">
                <a href="{{ route('admin.users.index') }}" class="text-indigo-600 hover:text-indigo-900">
                    View all users →
                </a>
            </div>
        </div>
    </div>

    <!-- Registration Chart -->
    <div class="bg-white shadow-admin rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i class="fas fa-chart-bar mr-2 text-gray-500"></i>
                User Registrations (Last 7 Days)
            </h3>
        </div>
        <div class="p-6">
            <div class="h-64 flex items-end space-x-2">
                @php
                    $maxCount = $userRegistrations->max('count') ?: 1;
                @endphp
                @forelse($userRegistrations as $registration)
                    <div class="flex-1 flex flex-col items-center">
                        <div class="w-full bg-indigo-500 rounded-t"
                             style="height: {{ ($registration->count / $maxCount) * 200 }}px; min-height: 10px;">
                        </div>
                        <div class="text-xs text-gray-500 mt-2 text-center">
                            {{ Carbon\Carbon::parse($registration->date)->format('M j') }}
                        </div>
                        <div class="text-xs font-medium text-gray-900">
                            {{ $registration->count }}
                        </div>
                    </div>
                @empty
                    <div class="flex-1 text-center text-gray-500 py-8">
                        No registration data available
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="mt-8">
    <div class="bg-white shadow-admin rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i class="fas fa-bolt mr-2 text-gray-500"></i>
                Quick Actions
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{{ route('admin.users.create') }}"
                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-user-plus mr-2"></i>
                    Add User
                </a>

                <a href="{{ route('admin.users.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-list mr-2"></i>
                    Manage Users
                </a>

                <a href="#"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-cog mr-2"></i>
                    Settings
                </a>

                <a href="#"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Reports
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
