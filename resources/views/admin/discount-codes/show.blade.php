@extends('layouts.admin')

@section('title', 'Chi tiết mã giảm giá')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.discount-codes.index') }}" class="text-gray-500 hover:text-gray-700">Mã giảm giá</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Chi tiết</span>
        </div>
    </li>
@endsection

@section('content-header-actions')
    <div class="flex space-x-3">
        <a href="{{ route('admin.discount-codes.edit', $discountCode) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            Chỉnh sửa
        </a>
        <form action="{{ route('admin.discount-codes.destroy', $discountCode) }}" method="POST" class="inline"
              onsubmit="return confirm('Bạn có chắc chắn muốn xóa mã giảm giá này?')">
            @csrf
            @method('DELETE')
            <button type="submit"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-trash mr-2"></i>
                Xóa mã
            </button>
        </form>
    </div>
@endsection

@section('content')
    <div class="max-w-7xl mx-auto space-y-6">
        <!-- Main Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <!-- Discount Code Display -->
                <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-8 text-white mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold mb-2">{{ $discountCode->code }}</h1>
                            <p class="text-purple-100">{{ $discountCode->type_text }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-5xl font-bold">{{ $discountCode->discount_text }}</div>
                            <div class="text-sm text-purple-100 mt-1">Giá trị giảm</div>
                        </div>
                    </div>

                    <div class="mt-6 pt-6 border-t border-purple-400">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-lg font-medium">{{ $discountCode->start_time->format('d/m/Y H:i') }}</div>
                                <div class="text-sm text-purple-100">Bắt đầu</div>
                            </div>
                            <div>
                                <div class="text-lg font-medium">{{ $discountCode->expire_time->format('d/m/Y H:i') }}</div>
                                <div class="text-sm text-purple-100">Kết thúc</div>
                            </div>
                            <div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $discountCode->status_color }}">
                                {{ $discountCode->status }}
                            </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Details Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin cơ bản</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">ID:</span>
                        <span class="text-sm text-gray-900">#{{ $discountCode->id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Mã giảm giá:</span>
                        <span class="text-sm font-mono font-bold text-gray-900 bg-gray-100 px-2 py-1 rounded">{{ $discountCode->code }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Loại giảm giá:</span>
                        <span class="text-sm text-gray-900 font-medium">{{ $discountCode->type_text }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Giá trị:</span>
                        <span class="text-sm text-gray-900 font-semibold text-lg {{ $discountCode->type == 1 ? 'text-green-600' : 'text-blue-600' }}">
                        {{ $discountCode->discount_text }}
                    </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Trạng thái:</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $discountCode->status_color }}">
                        {{ $discountCode->status }}
                    </span>
                    </div>
                </div>
            </div>

            <!-- Time Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thời gian áp dụng</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Thời gian bắt đầu:</span>
                        <div class="text-right">
                            <div class="text-sm text-gray-900">{{ $discountCode->start_time->format('d/m/Y') }}</div>
                            <div class="text-xs text-gray-500">{{ $discountCode->start_time->format('H:i:s') }}</div>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Thời gian kết thúc:</span>
                        <div class="text-right">
                            <div class="text-sm text-gray-900">{{ $discountCode->expire_time->format('d/m/Y') }}</div>
                            <div class="text-xs text-gray-500">{{ $discountCode->expire_time->format('H:i:s') }}</div>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Thời gian hoạt động:</span>
                        <span class="text-sm text-gray-900">
                        {{ $discountCode->start_time->diffInDays($discountCode->expire_time) }} ngày
                    </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Còn lại:</span>
                        <span class="text-sm text-gray-900">
                        @if($discountCode->status === 'Đang hoạt động')
                                {{ $discountCode->expire_time->diffForHumans() }}
                            @elseif($discountCode->status === 'Chưa bắt đầu')
                                {{ $discountCode->start_time->diffForHumans() }}
                            @else
                                Đã hết hạn
                            @endif
                    </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Timeline -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Timeline mã giảm giá</h3>
            </div>
            <div class="px-6 py-4">
                <div class="relative">
                    <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                    <!-- Created -->
                    <div class="relative flex items-center space-x-4 pb-6">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-plus text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">Mã được tạo</h4>
                            <p class="text-sm text-gray-500">{{ $discountCode->created_at->format('d/m/Y H:i:s') }}</p>
                            <p class="text-xs text-gray-400">{{ $discountCode->created_at->diffForHumans() }}</p>
                        </div>
                    </div>

                    <!-- Start Time -->
                    <div class="relative flex items-center space-x-4 pb-6">
                        <div class="flex-shrink-0 w-8 h-8 {{ now() >= $discountCode->start_time ? 'bg-green-500' : 'bg-gray-300' }} rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">Bắt đầu hoạt động</h4>
                            <p class="text-sm text-gray-500">{{ $discountCode->start_time->format('d/m/Y H:i:s') }}</p>
                            <p class="text-xs text-gray-400">{{ $discountCode->start_time->diffForHumans() }}</p>
                        </div>
                    </div>

                    <!-- End Time -->
                    <div class="relative flex items-center space-x-4">
                        <div class="flex-shrink-0 w-8 h-8 {{ now() > $discountCode->expire_time ? 'bg-red-500' : 'bg-gray-300' }} rounded-full flex items-center justify-center">
                            <i class="fas fa-stop text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">Hết hạn</h4>
                            <p class="text-sm text-gray-500">{{ $discountCode->expire_time->format('d/m/Y H:i:s') }}</p>
                            <p class="text-xs text-gray-400">{{ $discountCode->expire_time->diffForHumans() }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Statistics (placeholder) -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Thống kê sử dụng</h3>
                <p class="mt-1 text-sm text-gray-600">Thông tin về việc sử dụng mã giảm giá này</p>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-gray-900">0</div>
                        <div class="text-sm text-gray-500">Lượt sử dụng</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-gray-900">0 VNĐ</div>
                        <div class="text-sm text-gray-500">Tổng tiết kiệm</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-gray-900">0</div>
                        <div class="text-sm text-gray-500">Đơn hàng</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-gray-900">0 VNĐ</div>
                        <div class="text-sm text-gray-500">Doanh thu</div>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                    <p class="text-sm text-gray-600 text-center">
                        <i class="fas fa-info-circle mr-1"></i>
                        Thống kê sử dụng sẽ được cập nhật khi có tích hợp với hệ thống đặt hàng
                    </p>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4">
                <div class="flex flex-wrap gap-3">
                    <a href="{{ route('admin.discount-codes.edit', $discountCode) }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i>
                        Chỉnh sửa mã giảm giá
                    </a>

                    <button onclick="copyToClipboard('{{ $discountCode->code }}')"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-copy mr-2"></i>
                        Sao chép mã
                    </button>

                    <a href="{{ route('admin.discount-codes.create') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Tạo mã mới
                    </a>

                    <a href="{{ route('admin.discount-codes.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Quay lại danh sách
                    </a>

                    <form action="{{ route('admin.discount-codes.destroy', $discountCode) }}" method="POST" class="inline"
                          onsubmit="return confirm('Bạn có chắc chắn muốn xóa mã giảm giá này? Hành động này không thể hoàn tác!')">
                        @csrf
                        @method('DELETE')
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>
                            Xóa mã giảm giá
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function copyToClipboard(text) {

            navigator.clipboard.writeText(text).then(() => {
                console.log(event, 'text');

                // Show success message
                const originalText = event.target.innerHTML;
                event.target.innerHTML = '<i class="fas fa-check mr-2"></i>Đã sao chép!';
                event.target.classList.add('bg-green-50', 'text-green-700', 'border-green-300');

                setTimeout(() => {
                    event.target.innerHTML = '<i class="fas fa-copy mr-2"></i>Sao chép mã';
                    event.target.classList.remove('bg-green-50', 'text-green-700', 'border-green-300');
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('Không thể sao chép mã. Vui lòng thử lại!');
            });
        }
    </script>
@endpush
