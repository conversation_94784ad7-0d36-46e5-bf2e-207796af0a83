@extends('layouts.admin')

@section('title', 'Thêm mã giảm giá mới')

@section('breadcrumb')
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{{ route('admin.discount-codes.index') }}" class="text-gray-500 hover:text-gray-700">Mã giảm giá</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="text-gray-500">Thêm mới</span>
        </div>
    </li>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <form action="{{ route('admin.discount-codes.store') }}" method="POST" class="space-y-6" x-data="{
        discountType: '{{ old('type', '1') }}',
        startTime: '{{ old('start_time') }}',
        expireTime: '{{ old('expire_time') }}',
        validateDates() {
            if (this.startTime && this.expireTime) {
                if (new Date(this.startTime) >= new Date(this.expireTime)) {
                    alert('Thời gian kết thúc phải sau thời gian bắt đầu!');
                    return false;
                }
            }
            return true;
        }
    }" @submit="return validateDates()">
            @csrf

            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin mã giảm giá</h3>
                    <p class="mt-1 text-sm text-gray-600">Nhập thông tin chi tiết về mã giảm giá</p>
                </div>

                <div class="px-6 py-4 space-y-6">
                    <!-- Discount Code -->
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-1">
                            Mã giảm giá <span class="text-red-500">*</span>
                        </label>
                        <div class="flex">
                            <input type="text"
                                   name="code"
                                   id="code"
                                   value="{{ old('code') }}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('code') border-red-300 @enderror font-mono"
                                   placeholder="Nhập mã giảm giá..."
                                   required>
                            <button type="button"
                                    onclick="generateCode()"
                                    class="px-4 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md text-sm text-gray-600 hover:bg-gray-200 transition-colors duration-200">
                                <i class="fas fa-random mr-1"></i>
                                Tự động
                            </button>
                        </div>
                        @error('code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Mã phải là duy nhất và không trùng với mã đã có</p>
                    </div>

                    <!-- Discount Type and Value -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-1">
                                Loại giảm giá <span class="text-red-500">*</span>
                            </label>
                            <select name="type"
                                    id="type"
                                    x-model="discountType"
                                    class="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('type') border-red-300 @enderror"
                                    required>
                                <option value="1">Giảm theo phần trăm (%)</option>
                                <option value="2">Giảm theo số tiền cố định (VNĐ)</option>
                            </select>
                            @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="discount" class="block text-sm font-medium text-gray-700 mb-1">
                                Giá trị giảm <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="number"
                                       name="discount"
                                       id="discount"
                                       value="{{ old('discount') }}"
                                       :min="discountType === '1' ? '0' : '1000'"
                                       :max="discountType === '1' ? '100' : '10000000'"
                                       :step="discountType === '1' ? '0.01' : '1000'"
                                       class="block w-full px-3 py-2 pr-12 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('discount') border-red-300 @enderror"
                                       placeholder="0"
                                       required>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm" x-text="discountType === '1' ? '%' : 'VNĐ'"></span>
                                </div>
                            </div>
                            @error('discount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500" x-show="discountType === '1'">
                                Giá trị từ 0% đến 100%
                            </p>
                            <p class="mt-1 text-sm text-gray-500" x-show="discountType === '2'">
                                Số tiền giảm tối thiểu 1,000 VNĐ
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Settings -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thời gian áp dụng</h3>
                    <p class="mt-1 text-sm text-gray-600">Thiết lập thời gian bắt đầu và kết thúc cho mã giảm giá</p>
                </div>

                <div class="px-6 py-4 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="start_time" class="block text-sm font-medium text-gray-700 mb-1">
                                Thời gian bắt đầu <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local"
                                   name="start_time"
                                   id="start_time"
                                   x-model="startTime"
                                   value="{{ old('start_time') }}"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('start_time') border-red-300 @enderror"
                                   required>
                            @error('start_time')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="expire_time" class="block text-sm font-medium text-gray-700 mb-1">
                                Thời gian kết thúc <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local"
                                   name="expire_time"
                                   id="expire_time"
                                   x-model="expireTime"
                                   value="{{ old('expire_time') }}"
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('expire_time') border-red-300 @enderror"
                                   required>
                            @error('expire_time')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Quick Time Presets -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Thiết lập nhanh</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                            <button type="button"
                                    onclick="setTimePreset(1)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                1 ngày
                            </button>
                            <button type="button"
                                    onclick="setTimePreset(7)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                1 tuần
                            </button>
                            <button type="button"
                                    onclick="setTimePreset(30)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                1 tháng
                            </button>
                            <button type="button"
                                    onclick="setTimePreset(90)"
                                    class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                3 tháng
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Xem trước</h3>
                    <p class="mt-1 text-sm text-gray-600">Xem trước thông tin mã giảm giá</p>
                </div>

                <div class="px-6 py-4">
                    <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-xl font-bold">Mã giảm giá</h4>
                                <p class="text-purple-100 mt-1">Áp dụng cho đơn hàng</p>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold" id="preview-discount">0%</div>
                                <div class="text-sm text-purple-100" id="preview-type">Giảm theo %</div>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-purple-400">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-mono text-lg font-bold" id="preview-code">DISCOUNT</div>
                                    <div class="text-sm text-purple-100">Mã giảm giá</div>
                                </div>
                                <div class="text-right text-sm">
                                    <div id="preview-validity">Còn hiệu lực</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="{{ route('admin.discount-codes.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Hủy
                </a>
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Lưu mã giảm giá
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function generateCode() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let code = '';
            for (let i = 0; i < 8; i++) {
                code += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('code').value = code;
            updatePreview();
        }

        function setTimePreset(days) {
            const now = new Date();
            const start = new Date(now.getTime() + (1 * 60 * 60 * 1000)); // 1 hour from now
            const end = new Date(start.getTime() + (days * 24 * 60 * 60 * 1000));

            document.getElementById('start_time').value = formatDateTimeLocal(start);
            document.getElementById('expire_time').value = formatDateTimeLocal(end);

            updatePreview();
        }

        function formatDateTimeLocal(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        function updatePreview() {
            const code = document.getElementById('code').value || 'DISCOUNT';
            const type = document.getElementById('type').value;
            const discount = document.getElementById('discount').value || '0';
            const startTime = document.getElementById('start_time').value;
            const expireTime = document.getElementById('expire_time').value;

            document.getElementById('preview-code').textContent = code;
            document.getElementById('preview-type').textContent = type === '1' ? 'Giảm theo %' : 'Giảm theo số tiền';
            document.getElementById('preview-discount').textContent = type === '1' ? discount + '%' : formatNumber(discount) + ' VNĐ';

            if (startTime && expireTime) {
                const start = new Date(startTime);
                const end = new Date(expireTime);
                const now = new Date();

                if (now < start) {
                    document.getElementById('preview-validity').textContent = 'Chưa bắt đầu';
                } else if (now > end) {
                    document.getElementById('preview-validity').textContent = 'Đã hết hạn';
                } else {
                    document.getElementById('preview-validity').textContent = 'Đang hoạt động';
                }
            }
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('vi-VN').format(num);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for real-time preview updates
            ['code', 'type', 'discount', 'start_time', 'expire_time'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updatePreview);
                    element.addEventListener('change', updatePreview);
                }
            });

            // Initial preview update
            updatePreview();

            // Set default start time to 1 hour from now
            if (!document.getElementById('start_time').value) {
                const now = new Date();
                const defaultStart = new Date(now.getTime() + (1 * 60 * 60 * 1000));
                document.getElementById('start_time').value = formatDateTimeLocal(defaultStart);
            }
        });
    </script>
@endpush
