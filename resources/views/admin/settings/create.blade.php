@extends('layouts.admin')

@section('title', 'Create Setting')

@section('content')
    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">Create New Setting</h2>
                            <p class="mt-1 text-sm text-gray-600">Add a new system setting or configuration</p>
                        </div>
                        <a href="{{ route('admin.settings.index') }}"
                           class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Back to Settings
                        </a>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form method="POST" action="{{ route('admin.settings.store') }}" x-data="{ type: 'text' }">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                                <input type="text"
                                       name="name"
                                       id="name"
                                       value="{{ old('name') }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('name') border-red-500 @enderror"
                                       placeholder="e.g., site_name, max_upload_size"
                                       required>
                                @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Unique identifier for this setting</p>
                            </div>

                            <!-- Type -->
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700">Type</label>
                                <select name="type"
                                        id="type"
                                        x-model="type"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('type') border-red-500 @enderror"
                                        required>
                                    <option value="text" {{ old('type') === 'text' ? 'selected' : '' }}>Text</option>
                                    <option value="number" {{ old('type') === 'number' ? 'selected' : '' }}>Number
                                    </option>
                                    <option value="boolean" {{ old('type') === 'boolean' ? 'selected' : '' }}>Boolean
                                    </option>
                                    <option value="json" {{ old('type') === 'json' ? 'selected' : '' }}>JSON</option>
                                </select>
                                @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Data type for this setting</p>
                            </div>

                            <!-- Value -->
                            <div class="md:col-span-2">
                                <label for="setting_value" class="block text-sm font-medium text-gray-700">Value</label>

                                <!-- Single input that changes attributes -->
                                <template x-if="type !== 'json' && type !== 'boolean'">
                                    <input :type="type === 'number' ? 'number' : 'text'"
                                           name="value"
                                           id="setting_value"
                                           value="{{ old('value') }}"
                                           step="any"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('value') border-red-500 @enderror"
                                           :placeholder="type === 'number' ? 'Enter numeric value' : 'Enter text value'">
                                </template>

                                <template x-if="type === 'boolean'">
                                    <select name="value"
                                            id="setting_value_boolean"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('value') border-red-500 @enderror">
                                        <option value="0" {{ old('value', '0') === '0' ? 'selected' : '' }}>False</option>
                                        <option value="1" {{ old('value') === '1' ? 'selected' : '' }}>True</option>
                                    </select>
                                </template>

                                <template x-if="type === 'json'">
        <textarea name="value"
                  id="setting_value_json"
                  rows="6"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('value') border-red-500 @enderror"
                  placeholder='{"key": "value", "array": [1, 2, 3]}'>{{ old('value') }}</textarea>
                                </template>

                                @error('value')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">The value for this setting</p>
                            </div>

                            <!-- Description -->
                            <div class="md:col-span-2">
                                <label for="description"
                                       class="block text-sm font-medium text-gray-700">Description</label>
                                <textarea name="description"
                                          id="description"
                                          rows="3"
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('description') border-red-500 @enderror"
                                          placeholder="Describe what this setting does...">{{ old('description') }}</textarea>
                                @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Optional description to help understand this
                                    setting's purpose</p>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                            <a href="{{ route('admin.settings.index') }}"
                               class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150">
                                Cancel
                            </a>
                            <button type="submit"
                                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M5 13l4 4L19 7"></path>
                                </svg>
                                Create Setting
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
