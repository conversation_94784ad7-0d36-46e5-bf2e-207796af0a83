@extends('layouts.admin')

@section('title', 'Setting Details')

@section('content')
    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">Setting Details</h2>
                            <p class="mt-1 text-sm text-gray-600">View details for: <strong>{{ $setting->name }}</strong></p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.settings.edit', $setting) }}"
                               class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit Setting
                            </a>
                            <a href="{{ route('admin.settings.index') }}"
                               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                                Back to Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Setting Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                <div class="mt-1 p-3 bg-gray-50 rounded-md">
                                    <code class="text-sm font-mono text-gray-900">{{ $setting->name }}</code>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Type</label>
                                <div class="mt-1">
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                                    @if($setting->type === 'text') bg-blue-100 text-blue-800
                                    @elseif($setting->type === 'number') bg-green-100 text-green-800
                                    @elseif($setting->type === 'boolean') bg-purple-100 text-purple-800
                                    @elseif($setting->type === 'json') bg-orange-100 text-orange-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($setting->type) }}
                                </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Created</label>
                                <div class="mt-1 text-sm text-gray-900">
                                    {{ $setting->created_at->format('F j, Y \a\t g:i A') }}
                                    <span class="text-gray-500">({{ $setting->created_at->diffForHumans() }})</span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                                <div class="mt-1 text-sm text-gray-900">
                                    {{ $setting->updated_at->format('F j, Y \a\t g:i A') }}
                                    <span class="text-gray-500">({{ $setting->updated_at->diffForHumans() }})</span>
                                </div>
                            </div>
                        </div>

                        <!-- Value Display -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Current Value</label>
                                <div class="mt-1">
                                    @if($setting->type === 'boolean')
                                        <div class="flex items-center space-x-2">
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                                            @if($setting->value) bg-green-100 text-green-800 @else bg-red-100 text-red-800 @endif">
                                            {{ $setting->value ? 'True' : 'False' }}
                                        </span>
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       {{ $setting->value ? 'checked' : '' }}
                                                       disabled
                                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            </div>
                                        </div>
                                    @elseif($setting->type === 'json')
                                        <div class="p-3 bg-gray-50 rounded-md">
                                            <pre class="text-sm text-gray-900 overflow-x-auto">{{ json_encode(json_decode($setting->value), JSON_PRETTY_PRINT) }}</pre>
                                        </div>
                                    @else
                                        <div class="p-3 bg-gray-50 rounded-md">
                                            <div class="text-sm text-gray-900 break-all">{{ $setting->value ?: 'No value set' }}</div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            @if($setting->type === 'text' || $setting->type === 'number')
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Parsed Value</label>
                                    <div class="mt-1 p-3 bg-gray-50 rounded-md">
                                        <code class="text-sm font-mono text-gray-900">
                                            {{ \App\Models\Setting::get($setting->name) }}
                                        </code>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    @if($setting->description)
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <div class="p-4 bg-blue-50 rounded-md">
                                <p class="text-sm text-gray-900">{{ $setting->description }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Actions</h3>
                        <div class="flex space-x-3">
                            <a href="{{ route('admin.settings.edit', $setting) }}"
                               class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit Setting
                            </a>
                            <form method="POST" action="{{ route('admin.settings.destroy', $setting) }}"
                                  class="inline"
                                  onsubmit="return confirm('Are you sure you want to delete this setting? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Delete Setting
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
