<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Đặt Món Ăn - Mobile</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        [x-cloak] { display: none !important; }
        .smooth-scroll { scroll-behavior: smooth; }
        .category-sticky { position: sticky; top: 80px; z-index: 10; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen" x-data="orderApp()">
<!-- Header with Navigation -->
<div class="bg-white shadow-sm sticky top-0 z-20">
    <div class="p-4">
        <h1 class="text-xl font-bold text-gray-800 mb-1">Đặt Món</h1>
        <p class="text-sm text-gray-600">
            Khách hàng: <span x-text="config.customer.name"></span> |
            Giới hạn: <span x-text="config.max_ticket_quantity"></span> món |
            Ticket ID: <span x-text="config.breakfast_ticket_id"></span>
        </p>
    </div>

    <!-- Tab Navigation -->
    <div class="flex border-b border-gray-200">
        <button
            @click="currentTab = 'menu'"
            :class="currentTab === 'menu' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500'"
            class="flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors"
        >
            Thực đơn
        </button>
        <button
            @click="currentTab = 'orders'; loadOrderHistory()"
            :class="currentTab === 'orders' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500'"
            class="flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors relative"
        >
            Đơn hàng đã đặt
            <span x-show="orderHistory.length > 0"
                  class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                  x-text="orderHistory.length"></span>
        </button>
    </div>
</div>

<!-- Menu Tab -->
<div x-show="currentTab === 'menu'" class="p-4 pb-24">
    <!-- Loading State -->
    <template x-if="isLoading">
        <div class="flex items-center justify-center py-12">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p class="text-gray-500">Đang tải thực đơn...</p>
            </div>
        </div>
    </template>

    <!-- Error State -->
    <template x-if="apiError && !isLoading">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <p class="text-sm text-red-800 font-medium">Không thể tải thực đơn</p>
                    <p class="text-xs text-red-600 mt-1" x-text="apiError"></p>
                    <button
                        @click="loadMenuData()"
                        class="mt-2 text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded transition-colors"
                    >
                        Thử lại
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- Menu Content -->
    <div x-show="!isLoading && !apiError">
        <!-- Empty State -->
        <template x-if="!menuData.success || Object.keys(groupedItems).length === 0">
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <p class="text-gray-500 text-lg mb-2">Chưa có món ăn nào</p>
                <p class="text-gray-400 text-sm">Thực đơn sẽ được cập nhật sớm!</p>
            </div>
        </template>

        <!-- Categories -->
        <template x-for="(category, categoryId) in groupedItems" :key="categoryId">
            <div class="mb-6">
                <!-- Category Header -->
                <div class="category-sticky bg-white py-3 border-b border-gray-100 mb-4">
                    <h2 class="text-lg font-semibold text-gray-800" x-text="category.name"></h2>
                </div>

                <!-- Menu Items -->
                <div class="space-y-4">
                    <template x-for="item in category.items" :key="item.id">
                        <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3">
                            <div class="flex gap-3">
                                <!-- Image -->
                                <div class="w-16 h-16 bg-gray-100 rounded-lg flex-shrink-0 flex items-center justify-center">
                                    <template x-if="item.image">
                                        <img :src="item.image" :alt="item.name" class="w-full h-full object-cover rounded-lg">
                                    </template>
                                    <template x-if="!item.image">
                                        <span class="text-xs text-gray-400">No Image</span>
                                    </template>
                                </div>

                                <!-- Content -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="font-medium text-gray-900 text-sm leading-tight mb-1" x-text="item.name"></h3>
                                    <p class="text-xs text-gray-600 mb-2 leading-tight line-clamp-2" x-text="item.description"></p>

                                    <div class="flex items-center justify-between">
                                        <!-- Categories Tags -->
                                        <div class="flex flex-wrap gap-1">
                                            <div x-show="item && item.categories">
                                                <template x-for="(cat, index) in item.categories || []" :key="`${index}-${cat.id || 'unknown'}`">
                                                    <span x-show="cat.name === category.name"
                                                          class="px-2 py-1 bg-blue-50 text-blue-600 rounded text-xs mr-2"
                                                          x-text="cat.name"></span>
                                                </template>
                                            </div>
                                        </div>

                                        <!-- Add Button & Quantity -->
                                        <div class="flex items-center gap-2">
                                            <template x-if="getItemQuantity(item.id) > 0">
                                                <div class="flex items-center gap-1">
                                                    <button
                                                        @click="removeFromCart(item.id)"
                                                        class="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                                                    >
                                                        −
                                                    </button>
                                                    <span class="w-6 text-center text-sm font-medium" x-text="getItemQuantity(item.id)"></span>
                                                </div>
                                            </template>
                                            <button
                                                @click="addToCart(item)"
                                                class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
                                            >
                                                +
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </template>
    </div>
</div>

<!-- Order History Tab -->
<div x-show="currentTab === 'orders'" class="p-4 pb-24">
    <!-- Loading State -->
    <template x-if="isLoadingOrders">
        <div class="flex items-center justify-center py-12">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p class="text-gray-500">Đang tải đơn hàng...</p>
            </div>
        </div>
    </template>

    <!-- Error State -->
    <template x-if="orderLoadError && !isLoadingOrders">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <p class="text-sm text-red-800 font-medium">Không thể tải đơn hàng</p>
                    <p class="text-xs text-red-600 mt-1" x-text="orderLoadError"></p>
                    <button
                        @click="loadOrderHistory()"
                        class="mt-2 text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded transition-colors"
                    >
                        Thử lại
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- Order Content -->
    <div x-show="!isLoadingOrders && !orderLoadError">
        <template x-if="orderHistory.length === 0">
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <p class="text-gray-500 text-lg mb-2">Chưa có đơn hàng nào</p>
                <p class="text-gray-400 text-sm">Hãy đặt món đầu tiên của bạn!</p>
            </div>
        </template>

        <div x-show="orderHistory.length > 0" class="space-y-4">
            <template x-for="(order, index) in orderHistory" :key="order.id">
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
                    <!-- Order Header -->
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium text-gray-900">
                                Đơn hàng #<span x-text="order.id"></span>
                            </h3>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                    <span x-text="getStatusText(order.status)"></span>
                                </span>
                        </div>
                        <div class="text-xs text-gray-500 space-y-1">
                            <p>Thời gian: <span x-text="formatDateTime(order.created_at)"></span></p>
                            <p>Tổng số món: <span class="font-medium text-gray-900" x-text="order.total_quantity"></span></p>
                            <template x-if="order.notes">
                                <p>Ghi chú: <span x-text="order.notes"></span></p>
                            </template>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="p-4">
                        <div class="space-y-3">
                            <template x-for="item in order.order_items" :key="item.food_id">
                                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                    <!-- Image -->
                                    <div class="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0 flex items-center justify-center">
                                        <template x-if="item.image">
                                            <img :src="item.image" :alt="item.name" class="w-full h-full object-cover rounded-lg">
                                        </template>
                                        <template x-if="!item.image">
                                            <span class="text-xs text-gray-400">No Image</span>
                                        </template>
                                    </div>

                                    <!-- Item Info -->
                                    <div class="flex-1 min-w-0">
                                        <p class="font-medium text-sm text-gray-900" x-text="item.name"></p>
                                        <p class="text-xs text-gray-500">Food ID: <span x-text="item.food_id"></span></p>
                                    </div>

                                    <!-- Quantity -->
                                    <div class="text-sm font-medium text-gray-900">
                                        x<span x-text="item.quantity"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<!-- Floating Cart Button -->
<div x-show="getTotalQuantity() > 0 && currentTab === 'menu'"
     x-transition
     class="fixed bottom-4 right-4 z-50">
    <button
        @click="showCart = true"
        class="w-14 h-14 bg-green-500 text-white rounded-full shadow-lg flex items-center justify-center hover:bg-green-600 transition-colors relative"
    >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.68 4.32M7 13l1.68-4.32M19 13v6a2 2 0 01-2 2H7a2 2 0 01-2-2v-6m14 0v-6a2 2 0 00-2-2H7a2 2 0 00-2-2"></path>
        </svg>
        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center"
              x-text="getTotalQuantity()"></span>
    </button>
</div>

<!-- Cart Modal -->
<div x-show="showCart"
     x-cloak
     class="fixed inset-0 z-50 overflow-y-auto"
     @click.self="showCart = false">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Backdrop -->
        <div x-show="showCart"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>

        <!-- Modal -->
        <div x-show="showCart"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             class="inline-block align-bottom bg-white rounded-t-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:rounded-lg w-full">

            <!-- Header -->
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Giỏ hàng của bạn</h3>
                    <button @click="showCart = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Cart Items -->
                <div class="max-h-64 overflow-y-auto">
                    <template x-if="cart.length === 0">
                        <div class="text-center py-8">
                            <p class="text-gray-500">Giỏ hàng trống</p>
                        </div>
                    </template>

                    <div x-show="cart.length > 0" class="space-y-3">
                        <template x-for="item in cart" :key="item.id">
                            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                                <div class="flex-1">
                                    <p class="font-medium text-sm" x-text="item.name"></p>
                                    <p class="text-xs text-gray-500" x-text="item.categories.map(cat => cat.name).join(', ')"></p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <button
                                        @click="removeFromCart(item.id)"
                                        class="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                                    >
                                        −
                                    </button>
                                    <span class="w-8 text-center font-medium" x-text="item.quantity"></span>
                                    <button
                                        @click="addToCart(item)"
                                        class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-blue-600"
                                    >
                                        +
                                    </button>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Total and Actions -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex justify-between items-center mb-3">
                        <span class="font-medium">Tổng số món:</span>
                        <span class="font-bold"
                              :class="isOverLimit() ? 'text-red-500' : 'text-green-600'"
                              x-text="`${getTotalQuantity()} / ${config.max_ticket_quantity}`"></span>
                    </div>

                    <!-- Over limit warning -->
                    <div x-show="isOverLimit()"
                         class="bg-red-50 border border-red-200 rounded-md p-3 mb-3">
                        <div class="flex">
                            <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="ml-3">
                                <p class="text-sm text-red-800">
                                    <strong>Vượt quá giới hạn!</strong><br>
                                    Bạn chỉ được phép đặt tối đa <span x-text="config.max_ticket_quantity"></span> món.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Confirm Button -->
                    <button
                        @click="confirmOrder()"
                        :disabled="cart.length === 0 || isOverLimit() || isSubmittingOrder"
                        :class="cart.length === 0 || isOverLimit() || isSubmittingOrder ? 'bg-gray-300 cursor-not-allowed' : 'bg-green-500 hover:bg-green-600'"
                        class="w-full py-3 px-4 rounded-lg text-white font-medium transition-colors flex items-center justify-center"
                    >
                        <template x-if="isSubmittingOrder">
                            <div class="flex items-center">
                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                <span>Đang xử lý...</span>
                            </div>
                        </template>
                        <template x-if="!isSubmittingOrder">
                            <span>Xác nhận đơn hàng</span>
                        </template>
                    </button>

                    <!-- Submit Error -->
                    <div x-show="submitError"
                         class="bg-red-50 border border-red-200 rounded-md p-3 mt-3">
                        <div class="flex">
                            <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="ml-3">
                                <p class="text-sm text-red-800">
                                    <strong>Lỗi đặt hàng!</strong><br>
                                    <span x-text="submitError"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div x-show="showSuccess"
     x-cloak
     class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Đặt hàng thành công!</h3>
                    <p class="text-sm text-gray-500 mb-4">
                        Đơn hàng của bạn đã được xác nhận với
                        <span x-text="successOrderQuantity"></span> món.
                    </p>
                    <div class="flex gap-2">
                        <button
                            @click="closeSuccess()"
                            class="w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors"
                        >
                            Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal đã được xóa -->

<script>
    const url_s15 = '{{env('APP_URL')}}'
    function orderApp() {
        return {
            // Configuration
            config: {
                breakfast_ticket_id: '{{$orderId}}',
                customer: @json($customer),
                max_ticket_quantity: Number('{{$totalTicket}}')
            },

            // Menu data - sẽ được load từ API
            menuData: {
                success: false,
                data: {
                    categories: {},
                    menu_items: [],
                    total_results: 0
                }
            },
            isLoading: true,
            apiError: null,
            isSubmittingOrder: false,
            submitError: null,
            isLoadingOrders: false,
            orderLoadError: null,

            // State
            currentTab: 'menu',
            cart: [],
            showCart: false,
            showSuccess: false,
            successOrderQuantity: 0,
            orderHistory: [],

            // Computed properties
            get groupedItems() {
                const grouped = {};
                const addedItems = new Set(); // Track items đã được thêm

                this.menuData.data.menu_items.forEach(item => {
                    // Chỉ xử lý item một lần duy nhất
                    if (addedItems.has(item.id)) {
                        return;
                    }
                    addedItems.add(item.id);

                    // Lấy category đầu tiên làm category chính cho item này
                    const primaryCategory = item.categories[0];
                    if (primaryCategory) {
                        if (!grouped[primaryCategory.id]) {
                            grouped[primaryCategory.id] = {
                                name: primaryCategory.name,
                                items: []
                            };
                        }
                        grouped[primaryCategory.id].items.push(item);
                    }
                });

                return grouped;
            },

            // Methods
            addToCart(item) {
                const existingItem = this.cart.find(cartItem => cartItem.id === item.id);
                if (existingItem) {
                    existingItem.quantity++;
                } else {
                    this.cart.push({ ...item, quantity: 1 });
                }
            },

            removeFromCart(itemId) {
                const itemIndex = this.cart.findIndex(item => item.id === itemId);
                if (itemIndex > -1) {
                    this.cart[itemIndex].quantity--;
                    if (this.cart[itemIndex].quantity <= 0) {
                        this.cart.splice(itemIndex, 1);
                    }
                }
            },

            getItemQuantity(itemId) {
                const item = this.cart.find(cartItem => cartItem.id === itemId);
                return item ? item.quantity : 0;
            },

            getTotalQuantity() {
                return this.cart.reduce((total, item) => total + item.quantity, 0);
            },

            isOverLimit() {
                return this.getTotalQuantity() > this.config.max_ticket_quantity;
            },

            confirmOrder() {
                if (this.cart.length === 0 || this.isOverLimit() || this.isSubmittingOrder) return;

                this.submitOrder();
            },

            async submitOrder() {
                this.isSubmittingOrder = true;
                this.submitError = null;

                try {
                    // Prepare order data theo format API
                    const quantity = this.cart.reduce((total, item) => total + item.quantity, 0)
                    const orderData = {
                        breakfast_ticket_id: this.config.breakfast_ticket_id,
                        notes: "",
                        name: this.config.customer.name,
                        phone: this.config.customer.phone,
                        email: this.config.customer.email,
                        address: "",
                        quantity: quantity,
                        order_items: this.cart.map(item => ({
                            food_id: item.id,
                            quantity: item.quantity,
                            notes: ""
                        }))
                    };

                    console.log('Submitting order:', orderData);
                    const response = await fetch(`/api/v1/order/create-by-breakfast-ticket`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(orderData)
                    });


                    const responseData = await response.json();

                    if (!response.ok) {
                        throw new Error(responseData.message || `HTTP ${response.status}: ${response.statusText}`);
                    }

                    if (responseData.success) {
                        // Refresh order history sau khi tạo order thành công
                        await this.loadOrderHistory();

                        // Show success
                        this.successOrderQuantity = this.getTotalQuantity();
                        this.showCart = false;
                        this.showSuccess = true;

                        console.log('Order created successfully:', responseData);
                    } else {
                        throw new Error(responseData.message || 'API trả về success: false');
                    }
                    window.location.reload()
                } catch (error) {
                    console.error('Error submitting order:', error);
                    this.submitError = error.message || 'Không thể tạo đơn hàng';
                } finally {
                    this.isSubmittingOrder = false;
                }
            },

            closeSuccess() {
                this.showSuccess = false;
                this.cart = [];
                this.submitError = null; // Clear submit error when closing
            },

            // API Methods
            async loadMenuData() {
                this.isLoading = true;
                this.apiError = null;

                try {
                    const response = await fetch(`/api/v1/menu`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();

                    if (data.success) {
                        this.menuData = data;
                    } else {
                        throw new Error('API trả về success: false');
                    }
                } catch (error) {
                    console.error('Error loading menu:', error);
                    this.apiError = error.message || 'Không thể kết nối đến server';
                } finally {
                    this.isLoading = false;
                }
            },

            async loadOrderHistory() {
                this.isLoadingOrders = true;
                this.orderLoadError = null;

                try {
                    const response = await fetch(`/api/v1/order/get-order-by-breakfast-ticket?breakfast_ticket_id=${this.config.breakfast_ticket_id}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();

                    if (data.success && data.data) {
                        // Convert API response to our format
                        const order = data.data;
                        order.total_quantity = order.order_items.reduce((sum, item) => sum + item.quantity, 0);
                        this.orderHistory = [order]; // API trả về 1 order duy nhất
                    } else if (data.success && !data.data) {
                        // No order found
                        this.orderHistory = [];
                    } else {
                        throw new Error('API trả về success: false');
                    }
                } catch (error) {
                    console.error('Error loading order history:', error);
                    this.orderLoadError = error.message || 'Không thể kết nối đến server';
                    // Fallback to empty array on error
                    this.orderHistory = [];
                } finally {
                    this.isLoadingOrders = false;
                }
            },

            // Helper methods for order display
            getStatusText(status) {
                const statusMap = {
                    0: 'Chờ xử lý',
                    1: 'Đã xác nhận',
                    2: 'Đang chuẩn bị',
                    4: 'Hoàn thành',
                    5: 'Đã hủy'
                };
                return statusMap[status] || 'Không xác định';
            },

            formatDateTime(datetime) {
                try {
                    return new Date(datetime).toLocaleString('vi-VN');
                } catch (error) {
                    return datetime;
                }
            },

            init() {
                // Load menu data from API
                this.loadMenuData();

                // Load order history when component initializes
                this.loadOrderHistory();
            }
        }
    }
</script>
</body>
</html>
