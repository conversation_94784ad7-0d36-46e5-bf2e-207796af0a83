<div class="fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 overflow-x-auto"
     :class="{ 'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen }">
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between h-16 bg-gray-900 px-4 sticky top-0 z-0">
        <div class="flex items-center">
            <h1 class="text-xl font-bold text-white">{{ config('app.name') }}</h1>
        </div>
        <button @click="sidebarOpen = false" class="lg:hidden text-gray-400 hover:text-white">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- Sidebar Menu -->
    <nav class="mt-6 px-3 space-y-1 overflow-y-auto">
        <!-- Dashboard -->
        <a href="{{ route('admin.dashboard') }}"
           class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.dashboard') ? 'bg-gray-700 text-white' : '' }}">
            <i class="fas fa-tachometer-alt w-5 mr-3 text-gray-400 group-hover:text-white"></i>
            Dashboard
        </a>
        <!-- Restaurant Management Section -->
        <div class="mt-6">
            <p class="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Quản lý nhà hàng
            </p>

            @canroute('admin.tables.index')
            <a href="{{ route('admin.tables.index') }}"
               class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.tables.*') ? 'bg-gray-700 text-white' : '' }}">
                <i class="fas fa-chair w-5 mr-3 text-gray-400 group-hover:text-white"></i>
                Quản lý bàn
            </a>
            @endcanroute()

            @canroute('admin.orders.index')
            <a href="{{ route('admin.orders.index') }}"
               class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.orders.*') ? 'bg-gray-700 text-white' : '' }}">
                <i class="fas fa-receipt w-5 mr-3 text-gray-400 group-hover:text-white"></i>
                Đơn hàng
            </a>
            @endcanroute()

        </div>

        @canroute('admin.orderFood')
        <a href="{{ route('admin.orderFood') }}"
           class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.orderFood') ? 'bg-gray-700 text-white' : '' }}">
            <i class="fas fa-utensils w-5 mr-3 text-gray-400 group-hover:text-white"></i>
            Reception Dashboard
        </a>
        @endcanroute()



        <!-- Food Management Section -->
        <div class="mt-6">
            <p class="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Quản lý thực phẩm
            </p>

            @canroute('admin.foods.index')
                <a href="{{ route('admin.foods.index') }}"
                   class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.foods.*') ? 'bg-gray-700 text-white' : '' }}">
                    <i class="fas fa-utensils w-5 mr-3 text-gray-400 group-hover:text-white"></i>
                    Món ăn
                </a>
            @endcanroute()

            @canroute('admin.food-categories.index')
            <a href="{{ route('admin.food-categories.index') }}"
               class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.food-categories.*') ? 'bg-gray-700 text-white' : '' }}">
                <i class="fas fa-tags w-5 mr-3 text-gray-400 group-hover:text-white"></i>
                Danh mục
            </a>
            @endcanroute()

            @canroute('admin.discount-codes.index')
            <a href="{{ route('admin.discount-codes.index') }}"
               class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.discount-codes.*') ? 'bg-gray-700 text-white' : '' }}">
                <i class="fas fa-ticket-alt w-5 mr-3 text-gray-400 group-hover:text-white"></i>
                Mã giảm giá
            </a>
            @endcanroute()

            @canroute('admin.date-locks.index')
            <a href="{{ route('admin.date-locks.index') }}"
               class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.date-locks.*') ? 'bg-gray-700 text-white' : '' }}">
                <i class="fas fa-calendar-times w-5 mr-3 text-gray-400 group-hover:text-white"></i>
                Khóa ngày
            </a>
            @endcanroute()
        </div>


        <div class="border-t border-gray-600 my-4"></div>
        @hasanyrole('Super Admin|Admin')
            <a href="{{route('admin.roles.index')}}"
               class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.date-locks.*') ? 'bg-gray-700 text-white' : '' }}">
                <i class="fas fa-user-tag mr-3"></i>
                Danh sách Roles
            </a>
        @endhasanyrole()
        @hasanyrole('Super Admin|Admin')
        <a href="{{route('admin.permissions.index')}}"
           class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.date-locks.*') ? 'bg-gray-700 text-white' : '' }}">
            <i class="fas fa-key text-white-600 mr-3"></i>
            Danh sách Permissions
        </a>
        @endhasanyrole()


        <!-- Divider -->
        <div class="border-t border-gray-600 my-4"></div>

        <!-- Users -->
        @canroute('admin.users.index')
        <a href="{{ route('admin.users.index') }}"
           class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.users.*') ? 'bg-gray-700 text-white' : '' }}">
            <i class="fas fa-users w-5 mr-3 text-gray-400 group-hover:text-white"></i>
            Tài khoản
        </a>
        @endcanroute()

        @canroute('admin.settings.index')
        <a href="{{ route('admin.settings.index') }}"
           class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.settings.*') ? 'bg-gray-700 text-white' : '' }}">
            <i class="fas fa-cog w-5 mr-3 text-gray-400 group-hover:text-white"></i>
            Settings
        </a>
        @endcanroute()

        @canroute('admin.vietqr.index')
        <a href="{{ route('admin.vietqr.index') }}"
           class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.vietqr.*') ? 'bg-gray-700 text-white' : '' }}">
            <i class="fas fa-cog w-5 mr-3 text-gray-400 group-hover:text-white"></i>
            Config vietqr
        </a>
        @endcanroute()

        <!-- Logout -->
        <form method="POST" action="{{ route('logout') }}">
            @csrf
            <button type="submit"
                    class="group flex items-center w-full px-3 py-2.5 text-sm font-medium rounded-lg text-gray-200 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                <i class="fas fa-sign-out-alt w-5 mr-3 text-gray-400 group-hover:text-white"></i>
                Logout
            </button>
        </form>
    </nav>
</div>
