<nav x-data="{ open: false }" class="bg-white/90 backdrop-blur-md border-b border-white/20 shadow-lg sticky top-0 z-50">
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-20">
            <div class="flex">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('dashboard') }}" class="group flex items-center space-x-3 transition-all duration-300 hover:scale-105">
                        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300">
                            <x-application-logo class="block h-8 w-auto fill-current text-white" />
                        </div>
                        <span class="text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                            {{ config('app.name', 'Laravel') }}
                        </span>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden space-x-2 sm:-my-px sm:ms-10 sm:flex items-center">
                    <x-nav-link :href="route('admin.dashboard')" :active="request()->routeIs('dashboard')"
                                class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-xl transition-all duration-300 hover:bg-blue-50 hover:text-blue-700 {{ request()->routeIs('dashboard') ? 'bg-blue-100 text-blue-700 shadow-md' : 'text-gray-600' }}">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        {{ __('Dashboard Admin') }}
                    </x-nav-link>
                </div>

                <div class="hidden sm:-my-px sm:ms-10 sm:flex items-center">
                    <x-nav-link :href="route('kitchen')" :active="request()->routeIs('kitchen')"
                                class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-xl transition-all duration-300 hover:bg-blue-50 hover:text-blue-700 {{ request()->routeIs('dashboard') ? 'bg-blue-100 text-blue-700 shadow-md' : 'text-gray-600' }}">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        {{ __('Dashboard Kitchen') }}
                    </x-nav-link>
                </div>
            </div>

            <!-- Settings Dropdown -->
            <!-- Hamburger -->
            <div class="-me-2 flex items-center sm:hidden">
                <button @click="open = ! open" class="inline-flex items-center justify-center p-3 rounded-xl text-gray-600 hover:text-gray-900 hover:bg-white/80 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 shadow-md hover:shadow-lg">
                    <svg class="h-6 w-6 transition-transform duration-300" :class="{ 'rotate-90': open }" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{'block': open, 'hidden': ! open}"
         class="hidden sm:hidden bg-white/95 backdrop-blur-md border-t border-white/20 shadow-lg"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2">

        <div class="pt-4 pb-3 space-y-2 px-4">
            <x-responsive-nav-link :href="route('admin.dashboard')" :active="request()->routeIs('admin.dashboard')"
                                   class="flex items-center px-4 py-3 text-base font-medium rounded-xl transition-all duration-300 {{ request()->routeIs('admin.dashboard') ? 'bg-blue-100 text-blue-700 shadow-md' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700' }}">
                <i class="fas fa-tachometer-alt mr-3 text-gray-400"></i>
                {{ __('Dashboard Admin') }}
            </x-responsive-nav-link>
        </div>
    </div>
</nav>
