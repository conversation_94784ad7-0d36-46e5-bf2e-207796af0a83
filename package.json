{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/forms": "^0.5.2", "@tailwindcss/postcss": "^4.1.9", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "pusher-js": "^8.4.0", "tailwindcss": "^3.1.0", "vite": "^6.2.4"}, "dependencies": {"@alpinejs/focus": "^3.14.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@vitejs/plugin-react": "^4.5.2", "alpinejs": "^3.14.9", "antd": "^5.26.0", "laravel-echo": "^1.19.0", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-dom": "^19.1.0"}}