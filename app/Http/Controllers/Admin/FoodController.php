<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Food;
use App\Models\FoodCat;
use App\Services\GoogleDrive\GoogleDriveUploadService;
use Faker\Extension\Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class FoodController extends Controller
{
    public function index(Request $request)
    {
        $query = Food::with('categories');

        // Filter by status
        if ($request->has('status') && isset($request->status)) {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->whereHas('categories', function ($q) use ($request) {
                $q->where('cat_food_id', $request->category);
            });
        }

        // Search by name
        if ($request->has('search') && isset($request->search)) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $foods = $query->latest()->paginate(10)->withQueryString();
        $categories = FoodCat::active()->get();

        return view('admin.foods.index', compact('foods', 'categories'));
    }

    public function create()
    {
        $categories = FoodCat::active()->get();
        return view('admin.foods.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_english' => 'nullable|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'categories' => 'required|array',
            'status' => 'required|in:0,1',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['name', 'name_english', 'description', 'price', 'status']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $files = $request->file('image');
            $driveService = new GoogleDriveUploadService();
            $uploadResults = $driveService->uploadMultiple([$files], '1t4GFR65lja0oy9GsMcAox5sTHAd-6LLn', new FoodCat());
            $images = array_map(function ($file) {
                return $file['fileId'];
            }, $uploadResults['files'] ?? []);

            $data['image_id'] = $images[0] ?? '';
        }

        $food = Food::create($data);
        $food->categories()->attach($request->categories);

        return redirect()->route('admin.foods.index')
            ->with('success', 'Thêm món ăn thành công!');
    }

    public function show(Food $food)
    {
        $food->load('categories', 'dateLocks');
        return view('admin.foods.show', compact('food'));
    }

    public function edit(Food $food)
    {
        $categories = FoodCat::active()->get();
        return view('admin.foods.edit', compact('food', 'categories'));
    }

    public function update(Request $request, Food $food)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_english' => 'nullable|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'categories' => 'required|array',
            'status' => 'required|in:0,1',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['name', 'name_english', 'description', 'price', 'status']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $files = $request->file('image');
            $driveService = new GoogleDriveUploadService();
            $uploadResults = $driveService->uploadMultiple([$files], '1t4GFR65lja0oy9GsMcAox5sTHAd-6LLn', new FoodCat());
            $images = array_map(function ($file) {
                return $file['fileId'];
            }, $uploadResults['files'] ?? []);

            $data['image_id'] = $images[0] ?? '';
        }

        $food->update($data);
        $food->categories()->sync($request->categories);

        return redirect()->route('admin.foods.index')
            ->with('success', 'Cập nhật món ăn thành công!');
    }

    public function destroy(Food $food)
    {
        // Delete image if exists
        if ($food->image_id && Storage::disk('public')->exists($food->image_id)) {
            Storage::disk('public')->delete($food->image_id);
        }

        $food->delete();

        return redirect()->route('admin.foods.index')
            ->with('success', 'Xóa món ăn thành công!');
    }

    public function toggleStatus(Food $food)
    {
        $food->update(['status' => $food->status == 0 ? 1 : 0]);

        return redirect()->back()
            ->with('success', 'Cập nhật trạng thái thành công!');
    }

    /**
     * API endpoint to get foods for customer interface
     */
    public function apiIndex()
    {
        $foods = Food::with('categories')->available()
            ->orderBy('name')
            ->get()
            ->map(function ($food) {
                return [
                    'id' => $food->id,
                    'name' => $food->name,
                    'name_english' => $food->name_english,
                    'description' => $food->description,
                    'price' => $food->price,
                    'category' => $food->categories->map(fn($category) => $category->name)->implode(', '),
                    'category_id' => $food->cat_id,
                    'image' => $food->image_id ? \App\Helper\helper::getImageGoogleDrive($food->image_id) : null,
                    'is_available' => $food->status == 0 // status 0 = available
                ];
            });

        return response()->json([
            'success' => true,
            'foods' => $foods
        ]);
    }
}
