<?php

namespace App\Http\Controllers\Api;

use App\Helper\helper;
use App\Http\Controllers\Controller;
use App\Models\EatingTicket;
use App\Models\Food;
use App\Models\Order;
use App\Models\Customer;
use App\Models\FoodCat;
use App\Services\EatTicketService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class MenuApiController extends Controller
{
    /**
     * Get menu items and categories
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getMenu(Request $request, $orderID = null): JsonResponse
    {
        try {
            // Get locale from request header or default to Vietnamese
            $locale = $request->header('Accept-Language', 'vi');
            $categoriesQuery = FoodCat::active()
                ->with(['foods' => function ($query) {
                    $query->available()
                        ->select([
                            'id',
                            'name',
                            'name_english',
                            'description',
                            'price',
                            'image_id',
                            'cat_id',
                            'status',
                            'created_at'
                        ]);
                }])
                ->select(['id', 'name', 'status', 'image_id', 'hide_price', 'breakfast_with_room']);

            $order = Order::query()->find($orderID ?? 0);
            if (!$order || !$order?->isBreakfastRoomCustomer()) {
                $categoriesQuery->where('breakfast_with_room', 0);
            }

            $categories = $categoriesQuery->orderBy('name') ->get();

            // Get all available menu items
            $menuItems = Food::available()
                ->with(['categories'])
                ->select([
                    'foods.id',
                    'foods.name',
                    'foods.description',
                    'foods.price',
                    'foods.image_id',
                    'foods.cat_id',
                    'foods.status',
                    'foods.created_at'
                ])
                ->orderBy('foods.name')
                ->get()
                ->map(function ($food) {
                   $hidePrice = $food->categories->where('hide_price', 1)->first();

                    return [
                        'id' => $food->id,
                        'name' => $food->name,
                        'description' => $food->description,
                        'price' => $food->price,
                        'formatted_price' => $food->formatted_price,
                        'hide_price' => (bool)$hidePrice,
                        'image' => $food->image_id ? helper::getImageGoogleDrive($food->image_id) : null,
                        'categories' => $food->categories->map(fn($category) => [
                            'id' => $category->id,
                            'name' => $category->name,
                            'hide_price' => $category->hide_price ?? false,
                            'breakfast_with_room' => $category->breakfast_with_room ?? false,
                        ]),
                        'available' => true,
                        'status' => $food->status,
                        'is_new' => $this->isNewItem($food->created_at),
                        'rating' => $this->getItemRating($food->id),
                        'review_count' => $this->getReviewCount($food->id),
                        'tags' => $this->getItemTags($food),
                        'options' => $this->getItemOptions($food->id),
                        'discount' => null, // Add if you have discount system
                        'original_price' => null, // Add if you have discount system
                    ];
                });

            // Format categories
            $formattedCategories = $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'image' => $category->image_id ? $this->getImageUrl($category->image_id) : null,
                    'food_count' => $category->foods->count(),
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Menu data retrieved successfully',
                'data' => [
                    'menu_items' => $menuItems,
                    'categories' => $formattedCategories,
                    'total_items' => $menuItems->count(),
                    'total_categories' => $formattedCategories->count(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving menu data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get menu items by category
     *
     * @param Request $request
     * @param int $categoryId
     * @return JsonResponse
     */
    public function getMenuByCategory(Request $request, int $categoryId): JsonResponse
    {
        try {
            $category = FoodCat::active()->findOrFail($categoryId);

            $menuItems = Food::available()
                ->where('cat_id', $categoryId)
                ->with(['categories'])
                ->select([
                    'foods.id',
                    'foods.name',
                    'foods.description',
                    'foods.price',
                    'foods.image_id',
                    'foods.cat_id',
                    'foods.status',
                    'foods.created_at'
                ])
                ->orderBy('name')
                ->get()
                ->map(function ($food) {
                    return [
                        'id' => $food->id,
                        'name' => $food->name,
                        'description' => $food->description,
                        'price' => $food->price,
                        'formatted_price' => $food->formatted_price,
                        'image' => $food->image_id ? $this->getImageUrl($food->image_id) : null,
                        'categories' => $food->categories->map(fn($category) => [
                            'id' => $category->id,
                            'name' => $category->name,
                        ]),
                        'available' => true,
                        'is_popular' => $this->isPopularItem($food->id),
                        'is_new' => $this->isNewItem($food->created_at),
                        'rating' => $this->getItemRating($food->id),
                        'review_count' => $this->getReviewCount($food->id),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'category' => [
                        'id' => $category->id,
                        'name' => $category->name,
                    ],
                    'menu_items' => $menuItems,
                    'total_items' => $menuItems->count(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving category menu data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search menu items
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchMenu(Request $request): JsonResponse
    {
        try {
            $searchTerm = $request->get('q', '');

            if (empty($searchTerm)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Search term is required'
                ], 400);
            }

            $menuItems = Food::available()
                ->where(function ($query) use ($searchTerm) {
                    $query->where('name', 'LIKE', "%{$searchTerm}%")
                        ->orWhere('description', 'LIKE', "%{$searchTerm}%");
                })
                ->with(['categories'])
                ->select([
                    'foods.id',
                    'foods.name',
                    'foods.description',
                    'foods.price',
                    'foods.image_id',
                    'foods.cat_id',
                    'foods.status'
                ])
                ->orderBy('name')
                ->get()
                ->map(function ($food) {
                    return [
                        'id' => $food->id,
                        'name' => $food->name,
                        'description' => $food->description,
                        'price' => $food->price,
                        'formatted_price' => $food->formatted_price,
                        'image' => $food->image_id ? $this->getImageUrl($food->image_id) : null,
                        'categories' => $food->categories->map(fn($category) => [
                            'id' => $category->id,
                            'name' => $category->name,
                        ]),
                        'available' => true,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'menu_items' => $menuItems,
                    'search_term' => $searchTerm,
                    'total_results' => $menuItems->count(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching menu items',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get image URL from image_id
     *
     * @param int $imageId
     * @return string|null
     */
    private function getImageUrl($imageId): ?string
    {
        // Adjust this based on your image storage system
        // Example implementations:

        // If you store images in storage/app/public/images/
        // return $imageId ? asset("storage/images/{$imageId}.jpg") : null;

        // If you have an images table
        // $image = \App\Models\Image::find($imageId);
        // return $image ? asset("storage/{$image->path}") : null;

        // If you store image paths directly
        return $imageId ? asset("images/{$imageId}") : null;
    }

    /**
     * Check if item is popular (you can implement your own logic)
     *
     * @param int $foodId
     * @return bool
     */
    private function isPopularItem($foodId): bool
    {
        // Implement your logic here
        // Example: check order count in last 30 days
        // return \App\Models\OrderItem::where('food_id', $foodId)
        //     ->whereHas('order', function($q) {
        //         $q->where('created_at', '>=', now()->subDays(30));
        //     })
        //     ->count() > 10;

        return false; // Default implementation
    }

    /**
     * Check if item is new (created in last 7 days)
     *
     * @param $createdAt
     * @return bool
     */
    private function isNewItem($createdAt): bool
    {
        return $createdAt && $createdAt->diffInDays(now()) <= 7;
    }

    /**
     * Get item rating (implement based on your review system)
     *
     * @param int $foodId
     * @return float|null
     */
    private function getItemRating($foodId): ?float
    {
        // Implement your rating logic here
        // Example:
        // return \App\Models\Review::where('food_id', $foodId)->avg('rating');

        return null; // Default implementation
    }

    /**
     * Get review count
     *
     * @param int $foodId
     * @return int
     */
    private function getReviewCount($foodId): int
    {
        // Implement your review count logic here
        // Example:
        // return \App\Models\Review::where('food_id', $foodId)->count();

        return 0; // Default implementation
    }

    /**
     * Get item tags
     *
     * @param Food $food
     * @return array
     */
    private function getItemTags($food): array
    {
        $tags = [];

        // Add tags based on food properties
        if ($this->isPopularItem($food->id)) {
            $tags[] = 'Phổ biến';
        }

        if ($this->isNewItem($food->created_at)) {
            $tags[] = 'Mới';
        }

        // Add more tags based on your business logic
        // if ($food->is_vegetarian) $tags[] = 'Chay';
        // if ($food->is_spicy) $tags[] = 'Cay';

        return $tags;
    }

    /**
     * Get item options (size, toppings, etc.)
     *
     * @param int $foodId
     * @return array
     */
    private function getItemOptions($foodId): array
    {
        // Implement your options logic here
        // Example:
        // return \App\Models\FoodOption::where('food_id', $foodId)->get()->toArray();

        return []; // Default implementation
    }
    /**
     * Get menu for breakfast ticket
     * request have order ticket id
     * response have categories and menu items
     * @param Request $request
     * @return JsonResponse
     */

    public function getMenuforBreakfastTicket(Request $request)
    {
        try {
            $categories = FoodCat::where('breakfast_with_room', 1)->get()->pluck('name', 'id')->toArray();
            $menuItems = Food::available()
                ->with(['categories'])
                ->whereHas('categories', function ($query) use ($categories) {
                    $query->whereIn('food_cats.id', array_keys($categories));
                })
                ->select([
                    'foods.id',
                    'foods.name',
                    'foods.description',
                    'foods.image_id',
                    'foods.cat_id',
                    'foods.status'
                ])
                ->orderBy('name')
                ->get()
                ->map(function ($food) {
                    return [
                        'id' => $food->id,
                        'name' => $food->name,
                        'description' => $food->description,
                        'image' => $food->image_id ? helper::getImageGoogleDrive($food->image_id) : null,
                        'categories' => $food->categories->unique()->map(fn($category) => [
                            'id' => $category->id,
                            'name' => $category->name,
                        ]),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'menu_items' => $menuItems,
                    'total_results' => $menuItems->count(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving menu data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    /**
     * Create order by breakfast ticket
     *
     * @param Request $request
     * @return JsonResponse
     */

    public function createByBreakfastTicket(Request $request)
    {
        try {
            DB::beginTransaction();
            $eatService = new EatTicketService();
            $eatService->useTicket($request->breakfast_ticket_id, $request->quantity);
            $listOrderItem = $request->order_items;
            $foodIds = array_column($listOrderItem, 'food_id');
            $foods = Food::whereIn('id', $foodIds)->get()->keyBy('id');
            $orderItems = [];
            foreach ($listOrderItem as $orderItem) {
                if (!isset($foods[$orderItem['food_id']])) {
                    continue;
                }
                $orderItems = array_merge($orderItems, array_fill(0, $orderItem['quantity'], [
                    'food_id' => $orderItem['food_id'],
                    'quantity' => 1,
                    'notes' => $orderItem['notes'] ?? null,
                    'unit_price' => $foods[$orderItem['food_id']]->price,
                    'total_price' => $foods[$orderItem['food_id']]->price * $orderItem['quantity'],
                ]));
            }

            $order = Order::where('breakfast_ticket_id', $request->breakfast_ticket_id)->first();
            if (!$order) {
                $customer = Customer::create([
                    'name' => $request->name ?? 'order by breakfast ticket',
                    'phone' => $request->phone ?? null,
                    'email' => $request->email ?? null,
                    'address' => $request->address ?? null,
                ]);

                $order = Order::create([
                    'customer_id' => $customer->id,
                    'user_id' => $request->user_id ?? null,
                    'status' => Order::STATUS_CONFIRMED, // Pending
                    'payment_status' => 0, // Unpaid
                    'notes' => $request->notes ?? null,
                    'breakfast_ticket_id' => $request->breakfast_ticket_id,
                ]);
                $url = route('customer.ticket', ['orderRoomId' => $request->breakfast_ticket_id]);
                helper::generateOrderQrCode($order, $url);
            }
            $order->orderItems()->createMany($orderItems);
            $order->updateTotals();
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => $order,
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error creating order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getOrderByBreakfastTicket(Request $request)
    {
        try {
            $order = Order::select([
                'id',
                'customer_id',
                'user_id',
                'status',
                'notes',
                'breakfast_ticket_id',
                'created_at',
                'updated_at',
            ])->with('orderItems.food')->where('breakfast_ticket_id', $request->breakfast_ticket_id)->first();

            if($order) {
                $orderItems = $order->orderItems->groupBy('food_id')
                ->map(function ($items, $foodId) {
                    return (object) [
                        'food_id' => $foodId,
                        'quantity' => $items->sum('quantity'),
                        'name' => $items->first()->food->name,
                        'image' => $items->first()->food->image_id ? helper::getImageGoogleDrive($items->first()->food->image_id) : null,
                    ];
                })
                ->values();
            $order = $order->toArray();
            $order['order_items'] = $orderItems->toArray();
            }

            return response()->json([
                'success' => true,
                'data' => $order,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting order',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

