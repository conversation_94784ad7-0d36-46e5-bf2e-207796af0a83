<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Food extends Model
{
    use HasFactory;
    protected $table = 'foods';

    protected $fillable = [
        'name',
        'name_english',
        'status',
        'description',
        'price',
        'image_id',
        'cat_id'
    ];

    protected $casts = [
        'status' => 'boolean',
        'price' => 'integer',
    ];

    public function categories()
    {
        return $this->belongsToMany(FoodCat::class, 'cat_food_food', 'food_id', 'cat_food_id');
    }

    public function scopeBreakfastWithRoomItems($query)
    {
        return $query->whereHas('categories', function ($categoryQuery) {
            $categoryQuery->where('breakfast_with_room', 1);
        });
    }

    public function scopeBuffetItems($query)
    {
        return $query->whereHas('categories', function ($categoryQuery) {
            $categoryQuery->where('name', 'buffet');
        });
    }
    public function scopeRegularItems($query)
    {
        return $query->whereDoesntHave('categories', function ($categoryQuery) {
            $categoryQuery->where('name', 'buffet');
        });
    }

    public function isBuffetItem(): bool
    {
        return $this->categories()->where('name', 'buffet')->exists();
    }

    public function isBreakfastWithRoomItem(): bool
    {
        return $this->categories()->where('breakfast_with_room', true)->exists();
    }


    public function dateLocks()
    {
        return $this->hasMany(FoodDateLock::class);
    }
    public function scopeAvailable($query)
    {
        return $query->where('status', 0)
            ->whereDoesntHave('dateLocks', function ($q) {
                $q->where('date', Carbon::today()->toDateString());
            });
    }

    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 1);
    }

    public function getStatusTextAttribute()
    {
        return $this->status == 0 ? 'Hoạt động' : 'Không hoạt động';
    }

    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 0, '.', ',') . ' VNĐ';
    }

    /**
     * Get localized name based on current locale
     * @param string $locale
     * @return string
     */
    public function getLocalizedName($locale = 'vi')
    {
        if ($locale === 'en' && !empty($this->name_english)) {
            return $this->name_english;
        }

        return $this->name;
    }

    /**
     * Get the localized name attribute
     * @return string
     */
    public function getLocalizedNameAttribute()
    {
        $locale = app()->getLocale();
        return $this->getLocalizedName($locale);
    }

    public function isFreeItem(string $customerType, string $serviceType): bool
    {
        // Buffet items are free for buffet orders
        if ($serviceType === 'buffet' && $this->isBuffetItem()) {
            return true;
        }

        // Breakfast with room items are free for breakfast-room customers
        if ($customerType === 'breakfast-room' && $this->isBreakfastWithRoomItem()) {
            return true;
        }

        return false;
    }
}
