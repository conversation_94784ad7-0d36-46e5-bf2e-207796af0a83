const CACHE_NAME = 'restaurant-app-v2';
const STATIC_CACHE = 'static-v2';
const DYNAMIC_CACHE = 'dynamic-v2';

// Essential files to cache
const staticAssets = [
  '/manifest.json',
  '/offline.html',
  '/images/icon-192x192.png',
  '/images/icon-512x512.png'
];

// External resources to cache
const externalAssets = [
  'https://fonts.bunny.net/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

// Install event
self.addEventListener('install', event => {
  console.log('Service Worker installing...');
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE).then(cache => {
        console.log('Caching static assets...');
        return Promise.all(
          staticAssets.map(url => {
            return cache.add(url).catch(err => {
              console.warn('Failed to cache:', url, err);
            });
          })
        );
      }),
      // Cache external assets
      caches.open(CACHE_NAME).then(cache => {
        console.log('Caching external assets...');
        return Promise.all(
          externalAssets.map(url => {
            return fetch(url).then(response => {
              if (response.ok) {
                return cache.put(url, response);
              }
            }).catch(err => {
              console.warn('Failed to cache external asset:', url, err);
            });
          })
        );
      })
    ]).then(() => {
      console.log('Service Worker installed successfully');
      self.skipWaiting();
    })
  );
});

// Fetch event

// Helper function to determine what to cache
function shouldCache(url) {
  // Cache API responses and static assets
  return url.includes('/api/') ||
         url.includes('/storage/') ||
         url.includes('.css') ||
         url.includes('.js') ||
         url.includes('.png') ||
         url.includes('.jpg') ||
         url.includes('.jpeg') ||
         url.includes('.svg');
}

// Activate event
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME &&
                cacheName !== STATIC_CACHE &&
                cacheName !== DYNAMIC_CACHE) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Take control of all clients
      self.clients.claim()
    ]).then(() => {
      console.log('Service Worker activated successfully');
    })
  );
});

// Background sync for offline orders
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync-order') {
    event.waitUntil(syncOrders());
  }
});

async function syncOrders() {
  try {
    const orders = await getOfflineOrders();
    for (const order of orders) {
      await submitOrder(order);
      await removeOfflineOrder(order.id);
    }
  } catch (error) {
    console.error('Error syncing orders:', error);
  }
}

async function getOfflineOrders() {
  return new Promise((resolve) => {
    const request = indexedDB.open('RestaurantDB', 1);
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(['orders'], 'readonly');
      const store = transaction.objectStore('orders');
      const getAll = store.getAll();
      getAll.onsuccess = () => resolve(getAll.result);
    };
  });
}

async function submitOrder(order) {
  const response = await fetch('/api/customer/orders', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': order.csrf_token
    },
    body: JSON.stringify(order)
  });
  return response.json();
}

async function removeOfflineOrder(orderId) {
  return new Promise((resolve) => {
    const request = indexedDB.open('RestaurantDB', 1);
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(['orders'], 'readwrite');
      const store = transaction.objectStore('orders');
      const deleteRequest = store.delete(orderId);
      deleteRequest.onsuccess = () => resolve();
    };
  });
}
