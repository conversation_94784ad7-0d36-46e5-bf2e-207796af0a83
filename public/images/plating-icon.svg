<svg width="192" height="192" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg">
    <!-- Background circle -->
    <circle cx="96" cy="96" r="88" fill="#f8fafc" stroke="#e2e8f0" stroke-width="4"/>

    <!-- Gradient definitions -->
    <defs>
        <linearGradient id="plateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="foodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="handleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#6b7280;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#4b5563;stop-opacity:1" />
        </linearGradient>
    </defs>

    <!-- Main plate -->
    <ellipse cx="96" cy="120" rx="55" ry="45" fill="url(#plateGradient)" stroke="#d1d5db" stroke-width="2"/>
    <ellipse cx="96" cy="118" rx="50" ry="40" fill="none" stroke="#e5e7eb" stroke-width="1"/>

    <!-- Food items on plate -->
    <!-- Main dish (chicken/meat) -->
    <ellipse cx="85" cy="115" rx="18" ry="12" fill="url(#foodGradient)"/>
    <ellipse cx="85" cy="113" rx="15" ry="10" fill="#fed7aa"/>

    <!-- Side dish 1 (vegetables) -->
    <circle cx="110" cy="110" r="8" fill="#22c55e"/>
    <circle cx="115" cy="115" r="6" fill="#16a34a"/>
    <circle cx="105" cy="120" r="5" fill="#15803d"/>

    <!-- Side dish 2 (garnish) -->
    <ellipse cx="70" cy="125" rx="8" ry="5" fill="#ef4444"/>
    <ellipse cx="75" cy="130" rx="6" ry="4" fill="#dc2626"/>

    <!-- Chef's hand holding spatula -->
    <g transform="translate(40, 30)">
        <!-- Arm -->
        <ellipse cx="15" cy="45" rx="8" ry="25" fill="#fbbf24" transform="rotate(-20 15 45)"/>

        <!-- Hand -->
        <circle cx="8" cy="25" r="12" fill="#fed7aa"/>

        <!-- Spatula handle -->
        <rect x="0" y="15" width="3" height="25" fill="url(#handleGradient)" transform="rotate(-15 1.5 27.5)"/>

        <!-- Spatula head -->
        <ellipse cx="-2" cy="12" rx="8" ry="4" fill="#9ca3af" transform="rotate(-15 -2 12)"/>
    </g>

    <!-- Steam lines (indicating hot food) -->
    <g opacity="0.6">
        <path d="M 70 90 Q 72 85 70 80 Q 68 75 70 70" stroke="#94a3b8" stroke-width="2" fill="none" stroke-linecap="round"/>
        <path d="M 80 85 Q 82 80 80 75 Q 78 70 80 65" stroke="#94a3b8" stroke-width="2" fill="none" stroke-linecap="round"/>
        <path d="M 90 88 Q 92 83 90 78 Q 88 73 90 68" stroke="#94a3b8" stroke-width="2" fill="none" stroke-linecap="round"/>
    </g>

    <!-- Decorative elements -->
    <!-- Fork on the side -->
    <g transform="translate(140, 90)">
        <rect x="0" y="0" width="2" height="20" fill="url(#handleGradient)"/>
        <rect x="-1" y="-2" width="1" height="8" fill="url(#handleGradient)"/>
        <rect x="1" y="-2" width="1" height="8" fill="url(#handleGradient)"/>
        <rect x="3" y="-2" width="1" height="8" fill="url(#handleGradient)"/>
    </g>

    <!-- Knife -->
    <g transform="translate(150, 95)">
        <rect x="0" y="0" width="2" height="18" fill="url(#handleGradient)"/>
        <rect x="-1" y="-3" width="4" height="2" fill="#9ca3af"/>
    </g>

    <!-- Small garnish elements -->
    <circle cx="55" cy="140" r="2" fill="#22c55e"/>
    <circle cx="135" cy="135" r="2" fill="#ef4444"/>
    <circle cx="60" cy="105" r="1.5" fill="#fbbf24"/>

    <!-- Shadow under plate -->
    <ellipse cx="96" cy="165" rx="45" ry="8" fill="#000000" opacity="0.1"/>
</svg>
