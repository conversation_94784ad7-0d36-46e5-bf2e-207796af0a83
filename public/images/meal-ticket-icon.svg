<svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <!-- Gradient cho background -->
        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
        </linearGradient>

        <!-- Gradient cho ticket -->
        <linearGradient id="ticketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#F1F5F9;stop-opacity:1" />
        </linearGradient>

        <!-- Gradient cho đĩa ăn -->
        <linearGradient id="plateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
        </linearGradient>

        <!-- Shadow filter -->
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.15"/>
        </filter>
    </defs>

    <!-- Background -->
    <rect width="512" height="512" rx="96" fill="url(#bgGradient)"/>

    <!-- Ticket body với shadow -->
    <g filter="url(#shadow)">
        <!-- Main ticket rectangle -->
        <rect x="80" y="160" width="352" height="192" rx="16" fill="url(#ticketGradient)" stroke="#E2E8F0" stroke-width="2"/>

        <!-- Perforated edge (left) -->
        <circle cx="80" cy="200" r="12" fill="url(#bgGradient)"/>
        <circle cx="80" cy="230" r="12" fill="url(#bgGradient)"/>
        <circle cx="80" cy="260" r="12" fill="url(#bgGradient)"/>
        <circle cx="80" cy="290" r="12" fill="url(#bgGradient)"/>
        <circle cx="80" cy="320" r="12" fill="url(#bgGradient)"/>

        <!-- Perforated edge (right) -->
        <circle cx="432" cy="200" r="12" fill="url(#bgGradient)"/>
        <circle cx="432" cy="230" r="12" fill="url(#bgGradient)"/>
        <circle cx="432" cy="260" r="12" fill="url(#bgGradient)"/>
        <circle cx="432" cy="290" r="12" fill="url(#bgGradient)"/>
        <circle cx="432" cy="320" r="12" fill="url(#bgGradient)"/>
    </g>

    <!-- Plate icon -->
    <circle cx="180" cy="256" r="36" fill="url(#plateGradient)" stroke="#FFFFFF" stroke-width="3"/>
    <circle cx="180" cy="256" r="24" fill="#FFFFFF" opacity="0.9"/>

    <!-- Fork icon -->
    <g transform="translate(140, 220)">
        <rect x="0" y="32" width="4" height="40" rx="2" fill="#64748B"/>
        <rect x="-6" y="28" width="4" height="8" rx="2" fill="#64748B"/>
        <rect x="-2" y="28" width="4" height="8" rx="2" fill="#64748B"/>
        <rect x="2" y="28" width="4" height="8" rx="2" fill="#64748B"/>
        <rect x="6" y="28" width="4" height="8" rx="2" fill="#64748B"/>
    </g>

    <!-- Spoon icon -->
    <g transform="translate(215, 220)">
        <rect x="0" y="32" width="4" height="40" rx="2" fill="#64748B"/>
        <circle cx="2" cy="28" r="8" fill="#64748B"/>
    </g>

    <!-- Ticket text lines -->
    <rect x="260" y="200" width="120" height="8" rx="4" fill="#CBD5E1"/>
    <rect x="260" y="220" width="80" height="6" rx="3" fill="#E2E8F0"/>
    <rect x="260" y="235" width="100" height="6" rx="3" fill="#E2E8F0"/>

    <!-- Ticket number -->
    <rect x="260" y="280" width="60" height="20" rx="10" fill="#3B82F6"/>
    <text x="290" y="295" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF" text-anchor="middle">#001</text>

    <!-- Hotel/Restaurant icon (top) -->
    <g transform="translate(256, 80)">
        <!-- Building base -->
        <rect x="-20" y="20" width="40" height="40" rx="4" fill="#FFFFFF" stroke="#3B82F6" stroke-width="2"/>
        <!-- Windows -->
        <rect x="-15" y="25" width="8" height="8" rx="1" fill="#3B82F6"/>
        <rect x="-2" y="25" width="8" height="8" rx="1" fill="#3B82F6"/>
        <rect x="9" y="25" width="8" height="8" rx="1" fill="#3B82F6"/>
        <rect x="-15" y="38" width="8" height="8" rx="1" fill="#3B82F6"/>
        <rect x="-2" y="38" width="8" height="8" rx="1" fill="#3B82F6"/>
        <rect x="9" y="38" width="8" height="8" rx="1" fill="#3B82F6"/>
        <!-- Roof -->
        <polygon points="0,10 -25,25 25,25" fill="#F59E0B"/>
        <!-- Flag -->
        <rect x="-2" y="0" width="4" height="15" fill="#64748B"/>
        <polygon points="2,5 2,0 15,2.5" fill="#EF4444"/>
    </g>
</svg>
