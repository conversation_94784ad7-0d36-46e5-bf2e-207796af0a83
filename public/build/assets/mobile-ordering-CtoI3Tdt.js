import{t as Mt,r as c,C as dt,o as Et,b1 as Vt,a as ut,$ as It,a3 as V,a4 as I,b2 as mt,j as e,aD as Te,aB as F,a2 as O,aC as Oe,b3 as ht,b4 as zt,c as Ht,b0 as Dt}from"./index-CHvake0r.js";import{f as qe,e as At,h as Lt,i as P,b as Ft,d as Pe,L as Me,I as Ut,r as ge,R as ft,c as Bt}from"./formatter-Blxj4gZL.js";import{T as fe}from"./index-Bbq37T-v.js";import{s as ke}from"./index-CIWcONm8.js";import{M as ue,a as xe,F as Kt}from"./index-Cqw7XJJF.js";import{S as xt}from"./index-D_dKmeqG.js";import{E as me,S as Ee,a as Yt,b as pt}from"./EyeOutlined-D5ZQtqsR.js";import{a as gt,u as vt,L as Gt,S as Qt,R as yt}from"./DeleteOutlined-C56jyccF.js";import{D as bt}from"./index-DIqWL9-0.js";import{R as Ve}from"./ClockCircleOutlined-B61igLBq.js";import{R as Xt}from"./CheckCircleOutlined-DMtaSJBb.js";import{R as Wt,a as Jt,b as Zt}from"./RocketOutlined-BtbtbzoE.js";import{R as er,B as de}from"./FireOutlined-B7wZholJ.js";import"./EditOutlined-t_UPdXXr.js";import"./ActionButton-BdVp3AS3.js";function tr(t,n,r){return typeof r=="boolean"?r:t.length?!0:Mt(n).some(o=>o.type===gt)}var jt=function(t,n){var r={};for(var l in t)Object.prototype.hasOwnProperty.call(t,l)&&n.indexOf(l)<0&&(r[l]=t[l]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,l=Object.getOwnPropertySymbols(t);o<l.length;o++)n.indexOf(l[o])<0&&Object.prototype.propertyIsEnumerable.call(t,l[o])&&(r[l[o]]=t[l[o]]);return r};function pe({suffixCls:t,tagName:n,displayName:r}){return l=>c.forwardRef((a,d)=>c.createElement(l,Object.assign({ref:d,suffixCls:t,tagName:n},a)))}const Re=c.forwardRef((t,n)=>{const{prefixCls:r,suffixCls:l,className:o,tagName:a}=t,d=jt(t,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:u}=c.useContext(dt),m=u("layout",r),[g,b,_]=vt(m),N=l?`${m}-${l}`:m;return g(c.createElement(a,Object.assign({className:ut(r||N,o,b,_),ref:n},d)))}),rr=c.forwardRef((t,n)=>{const{direction:r}=c.useContext(dt),[l,o]=c.useState([]),{prefixCls:a,className:d,rootClassName:u,children:m,hasSider:g,tagName:b,style:_}=t,N=jt(t,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),B=Et(N,["suffixCls"]),{getPrefixCls:z,className:w,style:D}=Vt("layout"),j=z("layout",a),C=tr(l,m,g),[T,k,y]=vt(j),R=ut(j,{[`${j}-has-sider`]:C,[`${j}-rtl`]:r==="rtl"},w,d,u,k,y),S=c.useMemo(()=>({siderHook:{addSider:s=>{o(h=>[].concat(It(h),[s]))},removeSider:s=>{o(h=>h.filter(v=>v!==s))}}}),[]);return T(c.createElement(Gt.Provider,{value:S},c.createElement(b,Object.assign({ref:n,className:R,style:Object.assign(Object.assign({},D),_)},B),m)))}),ar=pe({tagName:"div",displayName:"Layout"})(rr),nr=pe({suffixCls:"header",tagName:"header",displayName:"Header"})(Re),sr=pe({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(Re),lr=pe({suffixCls:"content",tagName:"main",displayName:"Content"})(Re),U=ar;U.Header=nr;U.Footer=sr;U.Content=lr;U.Sider=gt;U._InternalSiderContext=Qt;var cr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"}}]},name:"appstore",theme:"outlined"},or=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:cr}))},ve=c.forwardRef(or),ir={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"},dr=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:ir}))},ur=c.forwardRef(dr),mr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"},hr=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:mr}))},Nt=c.forwardRef(hr),fr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"},xr=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:fr}))},pr=c.forwardRef(xr),gr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9zM512 814.8S156 586.7 156 385.5C156 283.6 240.3 201 344.3 201c73.1 0 136.5 40.8 167.7 100.4C543.2 241.8 606.6 201 679.7 201c104 0 188.3 82.6 188.3 184.5 0 201.2-356 429.3-356 429.3z"}}]},name:"heart",theme:"outlined"},vr=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:gr}))},Ie=c.forwardRef(vr),yr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M536.1 273H488c-4.4 0-8 3.6-8 8v275.3c0 2.6 1.2 5 3.3 6.5l165.3 120.7c3.6 2.6 8.6 1.9 11.2-1.7l28.6-39c2.7-3.7 1.9-8.7-1.7-11.2L544.1 528.5V281c0-4.4-3.6-8-8-8zm219.8 75.2l156.8 38.3c5 1.2 9.9-2.6 9.9-7.7l.8-161.5c0-6.7-7.7-10.5-12.9-6.3L752.9 334.1a8 8 0 003 14.1zm167.7 301.1l-56.7-19.5a8 8 0 00-10.1 4.8c-1.9 5.1-3.9 10.1-6 15.1-17.8 42.1-43.3 80-75.9 112.5a353 353 0 01-112.5 75.9 352.18 352.18 0 01-137.7 27.8c-47.8 0-94.1-9.3-137.7-27.8a353 353 0 01-112.5-75.9c-32.5-32.5-58-70.4-75.9-112.5A353.44 353.44 0 01171 512c0-47.8 9.3-94.2 27.8-137.8 17.8-42.1 43.3-80 75.9-112.5a353 353 0 01112.5-75.9C430.6 167.3 477 158 524.8 158s94.1 9.3 137.7 27.8A353 353 0 01775 261.7c10.2 10.3 19.8 21 28.6 32.3l59.8-46.8C784.7 146.6 662.2 81.9 524.6 82 285 82.1 92.6 276.7 95 516.4 97.4 751.9 288.9 942 524.8 942c185.5 0 343.5-117.6 403.7-282.3 1.5-4.2-.7-8.9-4.9-10.4z"}}]},name:"history",theme:"outlined"},br=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:yr}))},jr=c.forwardRef(br),Nr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},_r=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:Nr}))},Cr=c.forwardRef(_r),Sr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"},$r=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:Sr}))},_t=c.forwardRef($r),wr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},Tr=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:wr}))},ze=c.forwardRef(Tr),Pr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"},Or=function(n,r){return c.createElement(V,I({},n,{ref:r,icon:Pr}))},kr=c.forwardRef(Or),X={},ye={exports:{}},He;function A(){return He||(He=1,function(t){function n(r){return r&&r.__esModule?r:{default:r}}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}(ye)),ye.exports}var W={},De;function Rr(){if(De)return W;De=1,Object.defineProperty(W,"__esModule",{value:!0}),W.default=void 0;var t={items_per_page:"/ trang",jump_to:"Đến",jump_to_confirm:"xác nhận",page:"Trang",prev_page:"Trang Trước",next_page:"Trang Kế",prev_5:"Về 5 Trang Trước",next_5:"Đến 5 Trang Kế",prev_3:"Về 3 Trang Trước",next_3:"Đến 3 Trang Kế",page_size:"kích thước trang"};return W.default=t,W}var J={},Z={},ee={},be={exports:{}},je={exports:{}},Ne={exports:{}},_e={exports:{}},Ae;function Ct(){return Ae||(Ae=1,function(t){function n(r){"@babel/helpers - typeof";return t.exports=n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(l){return typeof l}:function(l){return l&&typeof Symbol=="function"&&l.constructor===Symbol&&l!==Symbol.prototype?"symbol":typeof l},t.exports.__esModule=!0,t.exports.default=t.exports,n(r)}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}(_e)),_e.exports}var Ce={exports:{}},Le;function qr(){return Le||(Le=1,function(t){var n=Ct().default;function r(l,o){if(n(l)!="object"||!l)return l;var a=l[Symbol.toPrimitive];if(a!==void 0){var d=a.call(l,o||"default");if(n(d)!="object")return d;throw new TypeError("@@toPrimitive must return a primitive value.")}return(o==="string"?String:Number)(l)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}(Ce)),Ce.exports}var Fe;function Mr(){return Fe||(Fe=1,function(t){var n=Ct().default,r=qr();function l(o){var a=r(o,"string");return n(a)=="symbol"?a:a+""}t.exports=l,t.exports.__esModule=!0,t.exports.default=t.exports}(Ne)),Ne.exports}var Ue;function Er(){return Ue||(Ue=1,function(t){var n=Mr();function r(l,o,a){return(o=n(o))in l?Object.defineProperty(l,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):l[o]=a,l}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}(je)),je.exports}var Be;function St(){return Be||(Be=1,function(t){var n=Er();function r(o,a){var d=Object.keys(o);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(o);a&&(u=u.filter(function(m){return Object.getOwnPropertyDescriptor(o,m).enumerable})),d.push.apply(d,u)}return d}function l(o){for(var a=1;a<arguments.length;a++){var d=arguments[a]!=null?arguments[a]:{};a%2?r(Object(d),!0).forEach(function(u){n(o,u,d[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(d)):r(Object(d)).forEach(function(u){Object.defineProperty(o,u,Object.getOwnPropertyDescriptor(d,u))})}return o}t.exports=l,t.exports.__esModule=!0,t.exports.default=t.exports}(be)),be.exports}var te={},Ke;function $t(){return Ke||(Ke=1,Object.defineProperty(te,"__esModule",{value:!0}),te.commonLocale=void 0,te.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),te}var Ye;function Vr(){if(Ye)return ee;Ye=1;var t=A().default;Object.defineProperty(ee,"__esModule",{value:!0}),ee.default=void 0;var n=t(St()),r=$t(),l=(0,n.default)((0,n.default)({},r.commonLocale),{},{locale:"vi_VN",today:"Hôm nay",now:"Bây giờ",backToToday:"Trở về hôm nay",ok:"OK",clear:"Xóa",week:"Tuần",month:"Tháng",year:"Năm",timeSelect:"Chọn thời gian",dateSelect:"Chọn ngày",weekSelect:"Chọn tuần",monthSelect:"Chọn tháng",yearSelect:"Chọn năm",decadeSelect:"Chọn thập kỷ",dateFormat:"D/M/YYYY",dateTimeFormat:"D/M/YYYY HH:mm:ss",previousMonth:"Tháng trước (PageUp)",nextMonth:"Tháng sau (PageDown)",previousYear:"Năm trước (Control + left)",nextYear:"Năm sau (Control + right)",previousDecade:"Thập kỷ trước",nextDecade:"Thập kỷ sau",previousCentury:"Thế kỷ trước",nextCentury:"Thế kỷ sau"});return ee.default=l,ee}var re={},Ge;function wt(){if(Ge)return re;Ge=1,Object.defineProperty(re,"__esModule",{value:!0}),re.default=void 0;const t={placeholder:"Chọn thời gian",rangePlaceholder:["Bắt đầu","Kết thúc"]};return re.default=t,re}var Qe;function Tt(){if(Qe)return Z;Qe=1;var t=A().default;Object.defineProperty(Z,"__esModule",{value:!0}),Z.default=void 0;var n=t(Vr()),r=t(wt());const l={lang:Object.assign({placeholder:"Chọn thời điểm",yearPlaceholder:"Chọn năm",quarterPlaceholder:"Chọn quý",monthPlaceholder:"Chọn tháng",weekPlaceholder:"Chọn tuần",rangePlaceholder:["Ngày bắt đầu","Ngày kết thúc"],rangeYearPlaceholder:["Năm bắt đầu","Năm kết thúc"],rangeQuarterPlaceholder:["Quý bắt đầu","Quý kết thúc"],rangeMonthPlaceholder:["Tháng bắt đầu","Tháng kết thúc"],rangeWeekPlaceholder:["Tuần bắt đầu","Tuần kết thúc"]},n.default),timePickerLocale:Object.assign({},r.default)};return Z.default=l,Z}var Xe;function Ir(){if(Xe)return J;Xe=1;var t=A().default;Object.defineProperty(J,"__esModule",{value:!0}),J.default=void 0;var n=t(Tt());return J.default=n.default,J}var We;function zr(){if(We)return X;We=1;var t=A().default;Object.defineProperty(X,"__esModule",{value:!0}),X.default=void 0;var n=t(Rr()),r=t(Ir()),l=t(Tt()),o=t(wt());const a="${label} không phải kiểu ${type} hợp lệ",d={locale:"vi",Pagination:n.default,DatePicker:l.default,TimePicker:o.default,Calendar:r.default,global:{placeholder:"Vui lòng chọn",close:"Đóng"},Table:{filterTitle:"Bộ lọc",filterConfirm:"Đồng ý",filterReset:"Bỏ lọc",filterEmptyText:"Không có bộ lọc",filterCheckAll:"Chọn tất cả",filterSearchPlaceholder:"Tìm kiếm bộ lọc",emptyText:"Trống",selectAll:"Chọn tất cả",selectInvert:"Chọn ngược lại",selectNone:"Bỏ chọn tất cả",selectionAll:"Chọn tất cả",sortTitle:"Sắp xếp",expand:"Mở rộng dòng",collapse:"Thu gọn dòng",triggerDesc:"Nhấp để sắp xếp giảm dần",triggerAsc:"Nhấp để sắp xếp tăng dần",cancelSort:"Nhấp để hủy sắp xếp"},Tour:{Next:"Tiếp",Previous:"Trước",Finish:"Hoàn thành"},Modal:{okText:"Đồng ý",cancelText:"Hủy",justOkText:"OK"},Popconfirm:{okText:"Đồng ý",cancelText:"Hủy"},Transfer:{titles:["",""],searchPlaceholder:"Tìm ở đây",itemUnit:"mục",itemsUnit:"mục",remove:"Gỡ bỏ",selectCurrent:"Chọn trang hiện tại",removeCurrent:"Gỡ bỏ trang hiện tại",selectAll:"Chọn tất cả",removeAll:"Gỡ bỏ tất cả",selectInvert:"Đảo ngược trang hiện tại"},Upload:{uploading:"Đang tải lên...",removeFile:"Gỡ bỏ tập tin",uploadError:"Lỗi tải lên",previewFile:"Xem trước tập tin",downloadFile:"Tải tập tin"},Empty:{description:"Trống"},Icon:{icon:"icon"},Text:{edit:"Chỉnh sửa",copy:"Sao chép",copied:"Đã sao chép",expand:"Mở rộng"},Form:{optional:"(Tùy chọn)",defaultValidateMessages:{default:"${label} không đáp ứng điều kiện quy định",required:"Hãy nhập thông tin cho trường ${label}",enum:"${label} phải có giá trị nằm trong tập [${enum}]",whitespace:"${label} không được chứa khoảng trắng",date:{format:"${label} sai định dạng ngày tháng",parse:"Không thể chuyển ${label} sang kiểu Ngày tháng",invalid:"${label} không phải giá trị Ngày tháng hợp lệ"},types:{string:a,method:a,array:a,object:a,number:a,date:a,boolean:a,integer:a,float:a,regexp:a,email:a,url:a,hex:a},string:{len:"${label} phải dài đúng ${len} ký tự",min:"Độ dài tối thiểu trường ${label} là ${min} ký tự",max:"Độ dài tối đa trường ${label} là ${max} ký tự",range:"Độ dài trường ${label} phải từ ${min} đến ${max} ký tự"},number:{len:"${label} phải bằng ${len}",min:"${label} phải lớn hơn hoặc bằng ${min}",max:"${label} phải nhỏ hơn hoặc bằng ${max}",range:"${label} phải nằm trong khoảng ${min}-${max}"},array:{len:"Mảng ${label} phải có ${len} phần tử ",min:"Mảng ${label} phải chứa tối thiểu ${min} phần tử ",max:"Mảng ${label} phải chứa tối đa ${max} phần tử ",range:"Mảng ${label} phải chứa từ ${min}-${max} phần tử"},pattern:{mismatch:"${label} không thỏa mãn mẫu kiểm tra ${pattern}"}}},Image:{preview:"Xem trước"},QRCode:{expired:"Mã QR hết hạn",refresh:"Làm mới"}};return X.default=d,X}var Se,Je;function Hr(){return Je||(Je=1,Se=zr()),Se}var Dr=Hr();const Ar=mt(Dr);var ae={},ne={},Ze;function Lr(){if(Ze)return ne;Ze=1,Object.defineProperty(ne,"__esModule",{value:!0}),ne.default=void 0;var t={items_per_page:"/ page",jump_to:"Go to",jump_to_confirm:"confirm",page:"Page",prev_page:"Previous Page",next_page:"Next Page",prev_5:"Previous 5 Pages",next_5:"Next 5 Pages",prev_3:"Previous 3 Pages",next_3:"Next 3 Pages",page_size:"Page Size"};return ne.default=t,ne}var se={},le={},ce={},et;function Fr(){if(et)return ce;et=1;var t=A().default;Object.defineProperty(ce,"__esModule",{value:!0}),ce.default=void 0;var n=t(St()),r=$t(),l=(0,n.default)((0,n.default)({},r.commonLocale),{},{locale:"en_US",today:"Today",now:"Now",backToToday:"Back to today",ok:"OK",clear:"Clear",week:"Week",month:"Month",year:"Year",timeSelect:"select time",dateSelect:"select date",weekSelect:"Choose a week",monthSelect:"Choose a month",yearSelect:"Choose a year",decadeSelect:"Choose a decade",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"Previous month (PageUp)",nextMonth:"Next month (PageDown)",previousYear:"Last year (Control + left)",nextYear:"Next year (Control + right)",previousDecade:"Last decade",nextDecade:"Next decade",previousCentury:"Last century",nextCentury:"Next century"});return ce.default=l,ce}var oe={},tt;function Pt(){if(tt)return oe;tt=1,Object.defineProperty(oe,"__esModule",{value:!0}),oe.default=void 0;const t={placeholder:"Select time",rangePlaceholder:["Start time","End time"]};return oe.default=t,oe}var rt;function Ot(){if(rt)return le;rt=1;var t=A().default;Object.defineProperty(le,"__esModule",{value:!0}),le.default=void 0;var n=t(Fr()),r=t(Pt());const l={lang:Object.assign({placeholder:"Select date",yearPlaceholder:"Select year",quarterPlaceholder:"Select quarter",monthPlaceholder:"Select month",weekPlaceholder:"Select week",rangePlaceholder:["Start date","End date"],rangeYearPlaceholder:["Start year","End year"],rangeQuarterPlaceholder:["Start quarter","End quarter"],rangeMonthPlaceholder:["Start month","End month"],rangeWeekPlaceholder:["Start week","End week"]},n.default),timePickerLocale:Object.assign({},r.default)};return le.default=l,le}var at;function Ur(){if(at)return se;at=1;var t=A().default;Object.defineProperty(se,"__esModule",{value:!0}),se.default=void 0;var n=t(Ot());return se.default=n.default,se}var nt;function Br(){if(nt)return ae;nt=1;var t=A().default;Object.defineProperty(ae,"__esModule",{value:!0}),ae.default=void 0;var n=t(Lr()),r=t(Ur()),l=t(Ot()),o=t(Pt());const a="${label} is not a valid ${type}",d={locale:"en",Pagination:n.default,DatePicker:l.default,TimePicker:o.default,Calendar:r.default,global:{placeholder:"Please select",close:"Close"},Table:{filterTitle:"Filter menu",filterConfirm:"OK",filterReset:"Reset",filterEmptyText:"No filters",filterCheckAll:"Select all items",filterSearchPlaceholder:"Search in filters",emptyText:"No data",selectAll:"Select current page",selectInvert:"Invert current page",selectNone:"Clear all data",selectionAll:"Select all data",sortTitle:"Sort",expand:"Expand row",collapse:"Collapse row",triggerDesc:"Click to sort descending",triggerAsc:"Click to sort ascending",cancelSort:"Click to cancel sorting"},Tour:{Next:"Next",Previous:"Previous",Finish:"Finish"},Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Popconfirm:{okText:"OK",cancelText:"Cancel"},Transfer:{titles:["",""],searchPlaceholder:"Search here",itemUnit:"item",itemsUnit:"items",remove:"Remove",selectCurrent:"Select current page",removeCurrent:"Remove current page",selectAll:"Select all data",deselectAll:"Deselect all data",removeAll:"Remove all data",selectInvert:"Invert current page"},Upload:{uploading:"Uploading...",removeFile:"Remove file",uploadError:"Upload error",previewFile:"Preview file",downloadFile:"Download file"},Empty:{description:"No data"},Icon:{icon:"icon"},Text:{edit:"Edit",copy:"Copy",copied:"Copied",expand:"Expand",collapse:"Collapse"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:a,method:a,array:a,object:a,number:a,date:a,boolean:a,integer:a,float:a,regexp:a,email:a,url:a,hex:a},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}},Image:{preview:"Preview"},QRCode:{expired:"QR code expired",refresh:"Refresh",scanned:"Scanned"},ColorPicker:{presetEmpty:"Empty",transparent:"Transparent",singleColor:"Single",gradientColor:"Gradient"}};return ae.default=d,ae}var $e,st;function Kr(){return st||(st=1,$e=Br()),$e}var Yr=Kr();const Gr=mt(Yr),Qr={order:"Order",menu:"Menu",cart:"Cart",history:"History",orderPlacedSuccess:"Order placed successfully!",cannotUpdateOrderStatus:"Cannot update order status.",paymentRequestSent:"Payment request sent. Please wait for staff.",cannotSendPaymentRequest:"Cannot send payment request.",paymentRequestError:"Error sending payment request.",requestPayment:"Request Payment",requestPaymentTitle:"Request Payment",enterDiscountCode:"Enter discount code (if any)",confirmPaymentRequest:"Are you sure you want to call staff to pay for this order?",cancel:"Cancel",loading:"Loading...",error:"Error",success:"Success",confirm:"Confirm",language:"Language",english:"English",vietnamese:"Vietnamese",cartEmpty:"Cart is empty",addItemsFromMenu:"Add items from menu",cartItems:"Cart ({count} items)",clearAll:"Clear all",removeFromCart:"Remove from cart?",confirmRemoveItem:'Are you sure you want to remove "{name}" from cart?',remove:"Remove",removeAllItems:"Remove all items?",confirmClearCart:"Are you sure you want to remove all items from cart?",orderSummary:"Order Summary",placeOrder:"Place Order ({count} items)",orderPlacedSuccessfully:"Order placed successfully!",orderError:"An error occurred while placing the order",cartEmptyWarning:"Cart is empty",perItem:"/ item",all:"All",searchPlaceholder:"Search dishes...",addToCart:"Add to cart",add:"Add",hot:"Hot",outOfStock:"Out of stock",noOrders:"No orders yet",orderDetails:"Order Details",emptyOrder:"Empty order",paymentDetails:"Payment Details",subtotal:"Subtotal:",discount:"Discount:",total:"Total:",payment:"Payment:",itemTypes:"Item types",totalQuantity:"Total quantity",pending:"Pending",confirmed:"Confirmed",preparing:"Preparing",ready:"Ready",served:"Served",completed:"Completed",cancelled:"Cancelled"},Xr={order:"Order",menu:"Thực đơn",cart:"Giỏ hàng",history:"Lịch sử",orderPlacedSuccess:"Đặt món thành công!",cannotUpdateOrderStatus:"Không thể cập nhật trạng thái đơn hàng.",paymentRequestSent:"Đã gửi yêu cầu thanh toán. Vui lòng đợi nhân viên.",cannotSendPaymentRequest:"Không thể gửi yêu cầu thanh toán.",paymentRequestError:"Lỗi khi gửi yêu cầu thanh toán.",requestPayment:"Gọi thanh toán",requestPaymentTitle:"Gọi thanh toán",enterDiscountCode:"Nhập mã giảm giá (nếu có)",confirmPaymentRequest:"Bạn có chắc muốn gọi nhân viên để thanh toán cho đơn hàng này?",cancel:"Hủy",loading:"Đang tải...",error:"Lỗi",success:"Thành công",confirm:"Xác nhận",language:"Ngôn ngữ",english:"Tiếng Anh",vietnamese:"Tiếng Việt",cartEmpty:"Giỏ hàng trống",addItemsFromMenu:"Hãy thêm món ăn từ thực đơn",cartItems:"Giỏ hàng ({count} món)",clearAll:"Xóa tất cả",removeFromCart:"Xóa món khỏi giỏ hàng?",confirmRemoveItem:'Bạn có chắc muốn xóa "{name}" khỏi giỏ hàng?',remove:"Xóa",removeAllItems:"Xóa tất cả món?",confirmClearCart:"Bạn có chắc muốn xóa tất cả món khỏi giỏ hàng?",orderSummary:"Tổng đơn hàng",placeOrder:"Đặt món ({count} món)",orderPlacedSuccessfully:"Đặt món thành công!",orderError:"Có lỗi xảy ra khi đặt món",cartEmptyWarning:"Giỏ hàng trống",perItem:"/ món",all:"Tất cả",searchPlaceholder:"Tìm kiếm món ăn...",addToCart:"Thêm vào giỏ",add:"Thêm",hot:"Hot",outOfStock:"Tạm hết",noOrders:"Chưa có đơn hàng nào",orderDetails:"Đơn hàng",emptyOrder:"Đơn hàng trống",paymentDetails:"Chi tiết thanh toán",subtotal:"Tạm tính:",discount:"Giảm giá:",total:"Tổng cộng:",payment:"Thanh toán:",itemTypes:"Loại món",totalQuantity:"Tổng số lượng",pending:"Đang xử lý",confirmed:"Đã xác nhận",preparing:"Đang chuẩn bị",ready:"Sẵn sàng",served:"Đã phục vụ",completed:"Hoàn thành",cancelled:"Đã hủy"},he={en:Qr,vi:Xr},kt="vi",Rt=()=>{const t=localStorage.getItem("locale");return t&&he[t]?t:kt},Wr=t=>he[t]?(localStorage.setItem("locale",t),!0):!1,qt=c.createContext(),Jr=({children:t})=>{const[n,r]=c.useState(Rt()),a={locale:n,changeLocale:d=>{Wr(d)&&(r(d),window.location.reload())},t:(d,u=d)=>{var g,b;return((g=he[n])==null?void 0:g[d])||((b=he[kt])==null?void 0:b[d])||u}};return e.jsx(qt.Provider,{value:a,children:t})},ie=()=>{const t=c.useContext(qt);if(!t)throw new Error("useTranslation must be used within a TranslationProvider");return t},{Title:lt,Text:$}=fe,Zr=({orderId:t,onOrderCountChange:n,activeTab:r,handleRequestPayment:l,paymentLoading:o})=>{const{t:a}=ie(),d=i=>{const x={0:e.jsx(Ve,{}),1:e.jsx(Pe,{}),2:e.jsx(kr,{spin:!0}),3:e.jsx(Wt,{}),4:e.jsx(Xt,{}),5:e.jsx(Ft,{}),default:e.jsx(Ve,{})};return x[i]||x.default},[u,m]=c.useState(null),[g,b]=c.useState([]),[_,N]=c.useState(!0),[B,z]=c.useState(null),[w,D]=c.useState(0),[j,C]=ke.useMessage(),[T,k]=ue.useModal();c.useEffect(()=>{r==="history"&&y()},[t,r]);const y=async()=>{var i,x,q;try{N(!0);const H=await(await fetch(`/api/orders/current?since=${t}`)).json();H.success&&(m(H.orders),b(((i=H.orders)==null?void 0:i.order_items)||[]),n&&n(((q=(x=H.orders)==null?void 0:x.order_items)==null?void 0:q.length)||0))}catch(L){console.error("Error fetching current order:",L)}finally{N(!1)}},R=async(i,x)=>{var q;if(x<1){S(i);return}try{(await(await fetch(`/api/orders/items/${i}`,{method:"PATCH",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":(q=document.querySelector('meta[name="csrf-token"]'))==null?void 0:q.content},body:JSON.stringify({quantity:x})})).json()).success?(b(G=>G.map(Q=>Q.id===i?{...Q,quantity:x}:Q)),j.success("Đã cập nhật số lượng")):j.error("Không thể cập nhật số lượng")}catch{j.error("Lỗi khi cập nhật số lượng")}},S=async i=>{console.log("handleRemoveItem called with itemId:",i),T.confirm({title:"Xóa món khỏi đơn hàng?",content:"Bạn có chắc muốn xóa món này khỏi đơn hàng?",onOk:async()=>{var x;try{(await(await fetch(`/api/orders/items/${i}`,{method:"DELETE",headers:{"X-CSRF-TOKEN":(x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.content}})).json()).success?(b(H=>H.filter(G=>G.id!==i)),j.success("Đã xóa món khỏi đơn hàng")):j.error("Không thể xóa món")}catch{j.error("Lỗi khi xóa món")}}})},s=c.useMemo(()=>g.map(i=>`${i.id}-${i.quantity}-${i.unit_price}-${i.status}`).join("|"),[g]),h=c.useMemo(()=>g.reduce((i,x)=>i+(x.status===3?0:x.quantity*x.unit_price||0),0),[s]),v=c.useMemo(()=>g.reduce((i,x)=>i+(x.quantity*x.unit_price||0),0),[s]),Y=u&&[0].includes(u.status);return _?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(xt,{size:"large"})}):u?e.jsxs("div",{className:"order-history-container p-4 bg-gray-50 min-h-screen pb-24",children:[C,k,e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{children:[e.jsx(lt,{level:3,className:"mb-1 text-gray-800",children:a("orderDetails")}),e.jsx($,{className:"text-gray-500",children:qe(u.created_at)})]}),e.jsx(Te,{color:Lt(u.status),icon:d(u.status),className:"rounded-full px-3 py-1",children:At(u.status,a)})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[e.jsxs(F,{size:"small",className:"text-center",children:[e.jsx("div",{className:"font-bold text-blue-600",children:g.length}),e.jsx("div",{className:"text-xs text-gray-500",children:a("itemTypes")})]}),e.jsxs(F,{size:"small",className:"text-center",children:[e.jsx("div",{className:"font-bold text-orange-600",children:g.reduce((i,x)=>i+x.quantity,0)}),e.jsx("div",{className:"text-xs text-gray-500",children:a("totalQuantity")})]})]}),Y&&e.jsx("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-3 rounded",children:e.jsx($,{className:"text-blue-800 text-sm",children:"💡 Bạn có thể chỉnh sửa đơn hàng này vì chưa được xác nhận"})})]}),g.length===0?e.jsx(me,{description:a("emptyOrder"),className:"mt-12"}):e.jsx("div",{className:"space-y-3 mb-6",children:g.map(i=>{var x;return e.jsx(F,{className:"border border-gray-200",children:e.jsx("div",{className:"flex gap-3",children:e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"w-full flex justify-between",children:e.jsx($,{strong:!0,className:"text-gray-800",children:(x=i==null?void 0:i.food)==null?void 0:x.name})}),e.jsxs("div",{className:"text-sm text-gray-600",children:[P(i==null?void 0:i.unit_price)," ",a("perItem")]})]}),Y&&e.jsx(O,{type:"text",size:"small",icon:e.jsx(yt,{}),onClick:()=>S(i.id),className:"text-red-500 hover:text-red-700 w-8 h-8"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"flex items-center gap-2",children:Y?e.jsxs("div",{className:"flex items-center bg-gray-50 rounded-lg p-1",children:[e.jsx(O,{type:"text",size:"small",icon:e.jsx(_t,{}),onClick:()=>R(i.id,i.quantity-1),className:"w-8 h-8 rounded-md hover:bg-white border-0"}),e.jsx("div",{className:"mx-2 min-w-[40px] text-center",children:e.jsx($,{strong:!0,className:"text-base",children:i.quantity})}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(Oe,{}),onClick:()=>R(i.id,i.quantity+1),className:"w-8 h-8 rounded-md hover:bg-white border-0"})]}):e.jsxs($,{className:"text-gray-600",children:["Số lượng: ",i.quantity]})}),e.jsx($,{strong:!0,className:"text-green-600",children:P(i.unit_price*i.quantity)})]}),e.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["Đặt lúc: ",qe(i.created_at)]})]})})},i.id)})}),e.jsxs(F,{className:"mt-4 shadow-lg",children:[e.jsx(lt,{level:5,className:"mb-3",children:a("paymentDetails")}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx($,{children:a("subtotal")}),e.jsx($,{children:P(v)})]}),u.discount_amount>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx($,{children:a("discount")}),e.jsx($,{children:P(u.discount_amount)})]}),e.jsx(bt,{className:"my-2"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx($,{strong:!0,className:"text-lg",children:a("total")}),e.jsx($,{strong:!0,className:"text-lg text-green-600",children:u.payment_method?P(u.total_amount):P(h)})]})]}),u.payment_method&&e.jsx("div",{className:"mt-3 pt-3 border-t border-gray-100",children:e.jsxs($,{className:"text-sm text-gray-600",children:[a("payment")," ",u.payment_method]})}),(u==null?void 0:u.id)&&![0,4,5,6].includes(u.status)&&e.jsx(O,{type:"success",shape:"round",icon:e.jsx(Nt,{}),size:"large",loading:o,onClick:l,className:"shadow-lg bg-green-600 hover:bg-green-700 text-base font-semibold text-white w-full mt-3",children:a("requestPayment")})]})]}):e.jsx("div",{className:"order-history-container p-4 bg-gray-50 min-h-screen",children:e.jsx(me,{description:a("noOrders"),className:"mt-20"})})},{Title:Sa,Text:we,Paragraph:ea}=fe,{Search:$a}=xe,ta=({order:t,onAddToCart:n,cartItems:r=[]})=>{const{t:l}=ie(),[o,a]=c.useState([]),[d,u]=c.useState([]),[m,g]=c.useState(!0),[b,_]=c.useState(""),[N,B]=c.useState("all"),[z,w]=c.useState([]);c.useEffect(()=>{D()},[]),c.useEffect(()=>{D()},[l]);const D=async()=>{try{g(!0);const s=localStorage.getItem("locale")||"vi",v=await(await fetch(`/api/menu/${t==null?void 0:t.id}?lang=${s}`)).json();v.success&&(a(v.data.menu_items||[]),u(v.data.categories||[]))}catch(s){console.error("Error fetching menu:",s)}finally{g(!1)}},j=()=>{let s=o;if(N!=="all"&&(s=s.filter(h=>h.categories.some(v=>v.id===N))),b){const h=ge(b);s=s.filter(v=>ge(v.name).includes(h)||ge(v.description||"").includes(h))}return s},C=s=>{const h=r.find(v=>v.id===s);return h?h.quantity:0},T=s=>z.includes(s),k=s=>{w(h=>h.includes(s)?h.filter(v=>v!==s):[...h,s])},y=s=>({"Đồ uống":e.jsx(Jt,{}),"Món chính":e.jsx(ve,{}),"Tráng miệng":e.jsx(Ie,{}),"Khai vị":e.jsx(ze,{})})[s]||e.jsx(ve,{}),R=[{key:"all",label:e.jsxs(Ee,{children:[e.jsx(ve,{}),l("all")]})},...d.map(s=>({key:s.id,label:e.jsxs(Ee,{children:[y(s.name),s.name]})}))];if(m)return e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(xt,{size:"large"})});const S=j();return e.jsxs("div",{className:"menu-view-container p-4 bg-gray-50 min-h-screen pb-20",children:[e.jsx("div",{className:"mb-4",children:e.jsx(xe,{placeholder:l("searchPlaceholder"),allowClear:!0,size:"large",prefix:e.jsx(Yt,{}),value:b,onChange:s=>_(s.target.value),className:"rounded-lg"})}),e.jsx("div",{className:"mb-4",children:e.jsx(ht,{activeKey:N,onChange:B,size:"small",className:"category-tabs",items:R})}),S.length===0?e.jsx(me,{description:"Không tìm thấy món ăn nào",className:"mt-12"}):e.jsx(Me,{dataSource:S,renderItem:s=>e.jsx(Me.Item,{className:"mb-3 p-0",children:e.jsx(F,{className:"w-full menu-item-card hover:shadow-md transition-shadow",children:e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("div",{className:"flex gap-4",children:[s.image&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-20 h-20 rounded-lg overflow-hidden",children:e.jsx(Ut,{src:s.image,alt:s.name,className:"w-full h-full object-cover",preview:!1})})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1 pr-2",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1 flex-wrap",children:[e.jsx(we,{strong:!0,className:"text-base text-gray-800 leading-tight",children:s.name}),s.is_popular&&e.jsxs(Te,{color:"red",size:"small",children:[e.jsx(er,{className:"mr-1"}),l("hot")]})]}),s.description&&e.jsx(ea,{className:"text-sm text-gray-600 mb-2 leading-tight",ellipsis:{rows:2},children:s.description}),s.rating&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ze,{className:"text-yellow-500 text-xs"}),e.jsxs(we,{className:"text-xs text-gray-600",children:[s.rating," (",s.review_count||0,")"]})]})]}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(Ie,{className:T(s.id)?"text-red-500":"text-gray-400"}),onClick:()=>k(s.id),className:"flex-shrink-0 w-8 h-8"})]})})]}),e.jsxs("div",{className:"flex justify-between items-center pt-2 border-t border-gray-100",children:[e.jsx("div",{children:e.jsx(we,{strong:!0,className:"text-lg text-green-600",children:P(s.price)})}),e.jsxs("div",{className:"flex items-center gap-2",children:[C(s.id)>0&&e.jsx(de,{count:C(s.id),size:"small",className:"mr-1"}),e.jsx(O,{type:"primary",size:"small",icon:e.jsx(Oe,{}),onClick:()=>n(s),disabled:!s.available,className:"bg-blue-500 hover:bg-blue-600 rounded-full px-3 h-8 text-sm font-medium",children:l("add")})]})]}),!s.available&&e.jsx("div",{children:e.jsx(Te,{color:"red",size:"small",children:l("outOfStock")})})]})})})})]})},{Title:ct,Text:E}=fe,{TextArea:wa}=xe,ra=({items:t=[],table:n,onUpdateQuantity:r,onRemoveItem:l,onClearCart:o,onPlaceOrder:a,total:d,isHiddenPriceCart:u})=>{const{t:m}=ie(),[g,b]=ue.useModal(),[_,N]=ke.useMessage(),[B,z]=c.useState(!1),[w]=Kt.useForm(),[D,j]=c.useState(!1),C=d,T=C,k=(s,h)=>{if(h<1){y(s);return}r(s,h)},y=s=>{const h=t.find(v=>v.id===s);g.confirm({title:m("removeFromCart"),content:m("confirmRemoveItem").replace("{name}",(h==null?void 0:h.name)||""),icon:e.jsx(Pe,{}),okText:m("remove"),okType:"danger",cancelText:m("cancel"),onOk:()=>l(s)})},R=()=>{g.confirm({title:m("removeAllItems"),content:m("confirmClearCart"),icon:e.jsx(Pe,{}),okText:m("clearAll"),okType:"danger",cancelText:m("cancel"),onOk:o})},S=async s=>{try{z(!0);const h=await a({...s,subtotal:C,total:T});h.success?(_.success(m("orderPlacedSuccessfully")),j(!1),w.resetFields()):_.error(h.message||m("orderError"))}catch{_.error(m("orderError"))}finally{z(!1)}};return t.length===0?e.jsx("div",{className:"cart-view-container p-4 bg-gray-50 min-h-screen",children:e.jsx(me,{image:e.jsx(ft,{className:"text-6xl text-gray-300"}),description:e.jsxs("div",{children:[e.jsx(E,{className:"text-gray-500",children:m("cartEmpty")}),e.jsx("br",{}),e.jsx(E,{className:"text-sm text-gray-400",children:m("addItemsFromMenu")})]}),className:"mt-20"})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"cart-view-container p-4 bg-gray-50 min-h-screen pb-32",children:[b,N,e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx(ct,{level:4,className:"mb-0",children:m("cartItems").replace("{count}",t.length)}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(ur,{}),onClick:R,className:"text-red-500 hover:text-red-700",children:m("clearAll")})]}),e.jsx("div",{className:"space-y-3 mb-6",children:t.map(s=>e.jsx(F,{className:"cart-item-card border border-gray-200 shadow-sm",children:e.jsx("div",{className:"flex gap-3",children:e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{className:"flex-1 pr-2",children:[e.jsx(E,{strong:!0,className:"text-base text-gray-800 block leading-tight",children:s.name}),!s.hide_price&&e.jsxs(E,{className:"text-sm text-gray-600",children:[!s.hide_price&&P(s.price)," ",m("perItem")]})]}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(yt,{}),onClick:()=>y(s.id),className:"text-red-500 hover:text-red-700 w-8 h-8 flex-shrink-0"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center bg-gray-50 rounded-lg p-1",children:[e.jsx(O,{type:"text",size:"small",icon:e.jsx(_t,{}),onClick:()=>k(s.id,s.quantity-1),className:"w-8 h-8 rounded-md hover:bg-white border-0 flex items-center justify-center"}),e.jsx("div",{className:"mx-2 min-w-[40px] text-center",children:e.jsx(E,{strong:!0,className:"text-base",children:s.quantity})}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(Oe,{}),onClick:()=>k(s.id,s.quantity+1),className:"w-8 h-8 rounded-md hover:bg-white border-0 flex items-center justify-center"})]}),e.jsx(E,{strong:!0,className:"text-lg text-green-600",children:!s.hide_price&&P(s.price*s.quantity)})]})]})})},s.id))}),e.jsxs(F,{className:"mt-4 order-summary-card sticky bottom-20 z-10",children:[e.jsx(ct,{level:5,className:"mb-3",children:m("orderSummary")}),!u&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(E,{children:m("subtotal")}),e.jsx(E,{children:P(C)})]}),e.jsx(bt,{className:"my-2"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(E,{strong:!0,className:"text-lg",children:m("total")}),e.jsx(E,{strong:!0,className:"text-lg text-green-600",children:P(T)})]})]}),e.jsx(O,{type:"primary",size:"large",block:!0,icon:e.jsx(zt,{}),onClick:S,className:"mt-4 bg-blue-500 hover:bg-blue-600 h-12 text-lg font-medium",children:m("placeOrder").replace("{count}",t.length)})]})]})})},{Option:ot}=pt,aa=({className:t=""})=>{const{locale:n,changeLocale:r,t:l}=ie(),o=a=>{r(a)};return e.jsxs(pt,{value:n,onChange:o,className:`language-switcher ${t}`,style:{minWidth:120},suffixIcon:e.jsx(pr,{}),size:"small",children:[e.jsx(ot,{value:"vi",children:l("vietnamese")}),e.jsx(ot,{value:"en",children:l("english")})]})},{Title:na}=fe,{Content:sa}=U,la=({table:t,activeOrder:n})=>{const{t:r}=ie(),[l,o]=c.useState("menu"),[a,d]=c.useState([]),[u,m]=c.useState(0),[g,b]=c.useState([]),[_,N]=c.useState(!1),[B,z]=ue.useModal(),[w,D]=ke.useMessage(),[j,C]=c.useState(!1),[T,k]=c.useState(""),[y,R]=c.useState(window.activeOrderData||{}),S=c.useCallback(async()=>{if(y!=null&&y.id)try{const p=await(await fetch(`/api/orders/${y.id}/details`)).json();p.order&&R(p.order)}catch(f){console.error("Failed to fetch active order",f),w.error(r("cannotUpdateOrderStatus"))}},[y.id,w]);c.useEffect(()=>{const f=setInterval(()=>{S()},3e4);return()=>clearInterval(f)},[S]);const s=()=>a.reduce((f,p)=>f+(p.quantity||0),0),h=a.some(f=>f.hide_price===!0),v=()=>a.reduce((f,p)=>f+(p.price||0)*(p.quantity||0),0),Y=f=>{d(p=>p.find(M=>M.id===f.id)?p.map(M=>M.id===f.id?{...M,quantity:M.quantity+1}:M):[...p,{...f,quantity:1}])},i=f=>{d(p=>p.filter(K=>K.id!==f))},x=(f,p)=>{if(p<=0){i(f);return}d(K=>K.map(M=>M.id===f?{...M,quantity:p}:M))},q=()=>{d([])},L=async()=>{try{const p=await(await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content},body:JSON.stringify({items:a,existing_order_id:y.id})})).json();return p.success&&(q(),o("history"),b(K=>[...K,{type:"success",message:r("orderPlacedSuccess"),timestamp:new Date}])),p}catch(f){return console.error("Error placing order:",f),{success:!1,error:"Network error"}}},H=async()=>{C(!0)},G=async()=>{N(!0);try{const p=await(await fetch(`/api/orders/${y.id}/request-payment`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content},body:JSON.stringify({discount_code:T})})).json();p.success?(p.qr_payment_url&&window.open(p.qr_payment_url,"_blank"),w.success(r("paymentRequestSent")),S(),C(!1)):w.error(p.message||r("cannotSendPaymentRequest"))}catch(f){console.error("Error requesting payment:",f),w.error(r("paymentRequestError"))}finally{N(!1)}},Q=[{key:"menu",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(Cr,{className:"text-lg mb-1"}),e.jsx("span",{className:"text-xs",children:r("menu")})]}),children:e.jsx(ta,{order:y,onAddToCart:Y,cartItems:a})},{key:"cart",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(de,{count:s(),size:"small",offset:[10,-5],children:e.jsx(ft,{className:"text-lg mb-1"})}),e.jsx("span",{className:"text-xs",children:r("cart")})]}),children:e.jsx(ra,{items:a,table:t,onUpdateQuantity:x,onRemoveItem:i,onClearCart:q,onPlaceOrder:L,total:v(),isHiddenPriceCart:h})},{key:"history",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(de,{count:u,size:"small",offset:[10,-5],children:e.jsx(jr,{className:"text-lg mb-1"})}),e.jsx("span",{className:"text-xs",children:r("history")})]}),children:e.jsx(Zr,{orderId:y.id,activeTab:l,onOrderCountChange:m,paymentLoading:_,handleRequestPayment:H})}];return e.jsxs(e.Fragment,{children:[e.jsxs(U,{className:"mobile-ordering-layout min-h-screen bg-gray-50",style:{marginBottom:"58px"},children:[D,z,e.jsx("div",{className:"sticky top-0 z-50 bg-white shadow-sm border-b",children:e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center",children:e.jsx(Zt,{className:"text-white text-lg"})}),e.jsx("div",{children:e.jsxs("div",{className:"font-semibold text-gray-800",children:[r("order")," ",y.order_number]})})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(aa,{className:"mr-2"}),e.jsx(de,{count:g.length,size:"small",children:e.jsx(Bt,{className:"text-xl text-gray-600"})})]})]})}),e.jsx(sa,{className:"flex-1",children:e.jsx(ht,{activeKey:l,onChange:o,centered:!0,size:"large",className:"[&_.ant-tabs-tab]:!px-3 [&_.ant-tabs-tab]:!py-2 [&_.ant-tabs-tab]:!min-w-[80px] [&_.ant-tabs-tab]:!flex-1 [&_.ant-tabs-tab-btn]:!w-full [&_.ant-tabs-tab-btn]:!flex [&_.ant-tabs-tab-btn]:!flex-col [&_.ant-tabs-tab-btn]:!items-center [&_.ant-tabs-tab-btn]:!justify-center [&_.ant-tabs-tab-btn]:!text-xs [&_.ant-tabs-ink-bar]:!h-1 [&_.ant-tabs-ink-bar]:!rounded [&_.ant-tabs-content-holder]:!p-0 [&_.ant-tabs-tabpane]:!p-0",tabBarStyle:{backgroundColor:"white",margin:0,padding:"0 8px",borderTop:"1px solid #f0f0f0",position:"fixed",width:"100%",height:"58px",bottom:"0",zIndex:40},items:Q})})]}),e.jsxs(ue,{title:r("requestPaymentTitle"),visible:j,onOk:G,onCancel:()=>C(!1),okText:r("requestPayment"),cancelText:r("cancel"),confirmLoading:_,children:[e.jsx("div",{style:{marginBottom:16},children:e.jsx(xe,{prefix:e.jsx(Nt,{}),className:"w-full mb-2 border border-gray-300 rounded-md p-2",placeholder:r("enterDiscountCode"),value:T,onChange:f=>k(f.target.value)})}),e.jsx("div",{children:e.jsx(na,{level:5,strong:!0,children:r("confirmPaymentRequest")})})]})]})},ca=window.tableData,oa=window.activeOrderData,ia=()=>{const n=Rt()==="en"?Gr:Ar;return e.jsx(Jr,{children:e.jsx(Dt,{locale:n,children:e.jsx(la,{table:ca,activeOrder:oa})})})},it=document.getElementById("mobile-ordering-root");it&&Ht.createRoot(it).render(e.jsx(ia,{}));
