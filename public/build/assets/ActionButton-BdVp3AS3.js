import{r as s,bY as x,a2 as y,bq as E}from"./index-CHvake0r.js";function a(n){return!!(n!=null&&n.then)}const P=n=>{const{type:d,children:v,prefixCls:p,buttonProps:m,close:l,autoFocus:b,emitEvent:h,isSilent:i,quitOnNullishReturnValue:C,actionFn:o}=n,r=s.useRef(!1),f=s.useRef(null),[R,c]=x(!1),u=(...t)=>{l==null||l.apply(void 0,t)};s.useEffect(()=>{let t=null;return b&&(t=setTimeout(()=>{var e;(e=f.current)===null||e===void 0||e.focus({preventScroll:!0})})),()=>{t&&clearTimeout(t)}},[]);const g=t=>{a(t)&&(c(!0),t.then((...e)=>{c(!1,!0),u.apply(void 0,e),r.current=!1},e=>{if(c(!1,!0),r.current=!1,!(i!=null&&i()))return Promise.reject(e)}))},k=t=>{if(r.current)return;if(r.current=!0,!o){u();return}let e;if(h){if(e=o(t),C&&!a(e)){r.current=!1,u(t);return}}else if(o.length)e=o(l),r.current=!1;else if(e=o(),!a(e)){u();return}g(e)};return s.createElement(y,Object.assign({},E(d),{onClick:k,loading:R,prefixCls:p},m,{ref:f}),v)};export{P as A};
