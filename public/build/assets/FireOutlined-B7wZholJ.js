import{i as nt,m as $t,br as rt,p as st,au as z,e as j,r as s,C as U,bs as it,a as w,b as at,ap as yt,a3 as St,a4 as Ot}from"./index-CHvake0r.js";const xt=new z("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),Nt=new z("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),wt=new z("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),jt=new z("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),Et=new z("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),It=new z("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),Pt=t=>{const{componentCls:e,iconCls:i,antCls:n,badgeShadowSize:o,textFontSize:d,textFontSizeSM:a,statusSize:h,dotSize:v,textFontWeight:C,indicatorHeight:l,indicatorHeightSM:c,marginXS:m,calc:g}=t,u=`${n}-scroll-number`,$=rt(t,(f,{darkColor:y})=>({[`&${e} ${e}-color-${f}`]:{background:y,[`&:not(${e}-count)`]:{color:y},"a:hover &":{background:y}}}));return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},st(t)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${e}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:t.indicatorZIndex,minWidth:l,height:l,color:t.badgeTextColor,fontWeight:C,fontSize:d,lineHeight:j(l),whiteSpace:"nowrap",textAlign:"center",background:t.badgeColor,borderRadius:g(l).div(2).equal(),boxShadow:`0 0 0 ${j(o)} ${t.badgeShadowColor}`,transition:`background ${t.motionDurationMid}`,a:{color:t.badgeTextColor},"a:hover":{color:t.badgeTextColor},"a:hover &":{background:t.badgeColorHover}},[`${e}-count-sm`]:{minWidth:c,height:c,fontSize:a,lineHeight:j(c),borderRadius:g(c).div(2).equal()},[`${e}-multiple-words`]:{padding:`0 ${j(t.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${e}-dot`]:{zIndex:t.indicatorZIndex,width:v,minWidth:v,height:v,background:t.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${j(o)} ${t.badgeShadowColor}`},[`${e}-count, ${e}-dot, ${u}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${i}-spin`]:{animationName:It,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${e}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${e}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:h,height:h,verticalAlign:"middle",borderRadius:"50%"},[`${e}-status-success`]:{backgroundColor:t.colorSuccess},[`${e}-status-processing`]:{overflow:"visible",color:t.colorInfo,backgroundColor:t.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:o,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:xt,animationDuration:t.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${e}-status-default`]:{backgroundColor:t.colorTextPlaceholder},[`${e}-status-error`]:{backgroundColor:t.colorError},[`${e}-status-warning`]:{backgroundColor:t.colorWarning},[`${e}-status-text`]:{marginInlineStart:m,color:t.colorText,fontSize:t.fontSize}}}),$),{[`${e}-zoom-appear, ${e}-zoom-enter`]:{animationName:Nt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`${e}-zoom-leave`]:{animationName:wt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`&${e}-not-a-wrapper`]:{[`${e}-zoom-appear, ${e}-zoom-enter`]:{animationName:jt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`${e}-zoom-leave`]:{animationName:Et,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`&:not(${e}-status)`]:{verticalAlign:"middle"},[`${u}-custom-component, ${e}-count`]:{transform:"none"},[`${u}-custom-component, ${u}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[u]:{overflow:"hidden",transition:`all ${t.motionDurationMid} ${t.motionEaseOutBack}`,[`${u}-only`]:{position:"relative",display:"inline-block",height:l,transition:`all ${t.motionDurationSlow} ${t.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${u}-only-unit`]:{height:l,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${u}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${e}-count, ${e}-dot, ${u}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}},lt=t=>{const{fontHeight:e,lineWidth:i,marginXS:n,colorBorderBg:o}=t,d=e,a=i,h=t.colorTextLightSolid,v=t.colorError,C=t.colorErrorHover;return $t(t,{badgeFontHeight:d,badgeShadowSize:a,badgeTextColor:h,badgeColor:v,badgeColorHover:C,badgeShadowColor:o,badgeProcessingDuration:"1.2s",badgeRibbonOffset:n,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},ct=t=>{const{fontSize:e,lineHeight:i,fontSizeSM:n,lineWidth:o}=t;return{indicatorZIndex:"auto",indicatorHeight:Math.round(e*i)-2*o,indicatorHeightSM:e,dotSize:n/2,textFontSize:n,textFontSizeSM:n,textFontWeight:"normal",statusSize:n/2}},zt=nt("Badge",t=>{const e=lt(t);return Pt(e)},ct),Rt=t=>{const{antCls:e,badgeFontHeight:i,marginXS:n,badgeRibbonOffset:o,calc:d}=t,a=`${e}-ribbon`,h=`${e}-ribbon-wrapper`,v=rt(t,(C,{darkColor:l})=>({[`&${a}-color-${C}`]:{background:l,color:l}}));return{[h]:{position:"relative"},[a]:Object.assign(Object.assign(Object.assign(Object.assign({},st(t)),{position:"absolute",top:n,padding:`0 ${j(t.paddingXS)}`,color:t.colorPrimary,lineHeight:j(i),whiteSpace:"nowrap",backgroundColor:t.colorPrimary,borderRadius:t.borderRadiusSM,[`${a}-text`]:{color:t.badgeTextColor},[`${a}-corner`]:{position:"absolute",top:"100%",width:o,height:o,color:"currentcolor",border:`${j(d(o).div(2).equal())} solid`,transform:t.badgeRibbonCornerTransform,transformOrigin:"top",filter:t.badgeRibbonCornerFilter}}),v),{[`&${a}-placement-end`]:{insetInlineEnd:d(o).mul(-1).equal(),borderEndEndRadius:0,[`${a}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${a}-placement-start`]:{insetInlineStart:d(o).mul(-1).equal(),borderEndStartRadius:0,[`${a}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},Tt=nt(["Badge","Ribbon"],t=>{const e=lt(t);return Rt(e)},ct),Bt=t=>{const{className:e,prefixCls:i,style:n,color:o,children:d,text:a,placement:h="end",rootClassName:v}=t,{getPrefixCls:C,direction:l}=s.useContext(U),c=C("ribbon",i),m=`${c}-wrapper`,[g,u,$]=Tt(c,m),f=it(o,!1),y=w(c,`${c}-placement-${h}`,{[`${c}-rtl`]:l==="rtl",[`${c}-color-${o}`]:f},e),b={},O={};return o&&!f&&(b.background=o,O.color=o),g(s.createElement("div",{className:w(m,v,u,$)},d,s.createElement("div",{className:w(y,u),style:Object.assign(Object.assign({},b),n)},s.createElement("span",{className:`${c}-text`},a),s.createElement("div",{className:`${c}-corner`,style:O}))))},ot=t=>{const{prefixCls:e,value:i,current:n,offset:o=0}=t;let d;return o&&(d={position:"absolute",top:`${o}00%`,left:0}),s.createElement("span",{style:d,className:w(`${e}-only-unit`,{current:n})},i)};function Ft(t,e,i){let n=t,o=0;for(;(n+10)%10!==e;)n+=i,o+=i;return o}const Dt=t=>{const{prefixCls:e,count:i,value:n}=t,o=Number(n),d=Math.abs(i),[a,h]=s.useState(o),[v,C]=s.useState(d),l=()=>{h(o),C(d)};s.useEffect(()=>{const g=setTimeout(l,1e3);return()=>clearTimeout(g)},[o]);let c,m;if(a===o||Number.isNaN(o)||Number.isNaN(a))c=[s.createElement(ot,Object.assign({},t,{key:o,current:!0}))],m={transition:"none"};else{c=[];const g=o+10,u=[];for(let b=o;b<=g;b+=1)u.push(b);const $=v<d?1:-1,f=u.findIndex(b=>b%10===a);c=($<0?u.slice(0,f+1):u.slice(f)).map((b,O)=>{const W=b%10;return s.createElement(ot,Object.assign({},t,{key:b,value:W,offset:$<0?O-f:O,current:O===f}))}),m={transform:`translateY(${-Ft(a,o,$)}00%)`}}return s.createElement("span",{className:`${e}-only`,style:m,onTransitionEnd:l},c)};var Wt=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(i[n[o]]=t[n[o]]);return i};const Ht=s.forwardRef((t,e)=>{const{prefixCls:i,count:n,className:o,motionClassName:d,style:a,title:h,show:v,component:C="sup",children:l}=t,c=Wt(t,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:m}=s.useContext(U),g=m("scroll-number",i),u=Object.assign(Object.assign({},c),{"data-show":v,style:a,className:w(g,o,d),title:h});let $=n;if(n&&Number(n)%1===0){const f=String(n).split("");$=s.createElement("bdi",null,f.map((y,b)=>s.createElement(Dt,{prefixCls:g,count:Number(n),value:y,key:f.length-b})))}return a!=null&&a.borderColor&&(u.style=Object.assign(Object.assign({},a),{boxShadow:`0 0 0 1px ${a.borderColor} inset`})),l?at(l,f=>({className:w(`${g}-custom-component`,f==null?void 0:f.className,d)})):s.createElement(C,Object.assign({},u,{ref:e}),$)});var Mt=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(i[n[o]]=t[n[o]]);return i};const Zt=s.forwardRef((t,e)=>{var i,n,o,d,a;const{prefixCls:h,scrollNumberPrefixCls:v,children:C,status:l,text:c,color:m,count:g=null,overflowCount:u=99,dot:$=!1,size:f="default",title:y,offset:b,style:O,className:W,rootClassName:dt,classNames:N,styles:S,showZero:H=!1}=t,Y=Mt(t,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:K,direction:M,badge:r}=s.useContext(U),p=K("badge",h),[G,ut,mt]=zt(p),Z=g>u?`${u}+`:g,R=Z==="0"||Z===0,J=g===null||R&&!H,_=(l!=null||m!=null)&&J,bt=l!=null||!R,T=$&&!R,E=T?"":Z,I=s.useMemo(()=>(E==null||E===""||R&&!H)&&!T,[E,R,H,T]),Q=s.useRef(g);I||(Q.current=g);const P=Q.current,k=s.useRef(E);I||(k.current=E);const A=k.current,tt=s.useRef(T);I||(tt.current=T);const B=s.useMemo(()=>{if(!b)return Object.assign(Object.assign({},r==null?void 0:r.style),O);const x={marginTop:b[1]};return M==="rtl"?x.left=parseInt(b[0],10):x.right=-parseInt(b[0],10),Object.assign(Object.assign(Object.assign({},x),r==null?void 0:r.style),O)},[M,b,O,r==null?void 0:r.style]),gt=y??(typeof P=="string"||typeof P=="number"?P:void 0),ft=I||!c?null:s.createElement("span",{className:`${p}-status-text`},c),pt=!P||typeof P!="object"?void 0:at(P,x=>({style:Object.assign(Object.assign({},B),x.style)})),F=it(m,!1),vt=w(N==null?void 0:N.indicator,(i=r==null?void 0:r.classNames)===null||i===void 0?void 0:i.indicator,{[`${p}-status-dot`]:_,[`${p}-status-${l}`]:!!l,[`${p}-color-${m}`]:F}),V={};m&&!F&&(V.color=m,V.background=m);const et=w(p,{[`${p}-status`]:_,[`${p}-not-a-wrapper`]:!C,[`${p}-rtl`]:M==="rtl"},W,dt,r==null?void 0:r.className,(n=r==null?void 0:r.classNames)===null||n===void 0?void 0:n.root,N==null?void 0:N.root,ut,mt);if(!C&&_&&(c||bt||!J)){const x=B.color;return G(s.createElement("span",Object.assign({},Y,{className:et,style:Object.assign(Object.assign(Object.assign({},S==null?void 0:S.root),(o=r==null?void 0:r.styles)===null||o===void 0?void 0:o.root),B)}),s.createElement("span",{className:vt,style:Object.assign(Object.assign(Object.assign({},S==null?void 0:S.indicator),(d=r==null?void 0:r.styles)===null||d===void 0?void 0:d.indicator),V)}),c&&s.createElement("span",{style:{color:x},className:`${p}-status-text`},c)))}return G(s.createElement("span",Object.assign({ref:e},Y,{className:et,style:Object.assign(Object.assign({},(a=r==null?void 0:r.styles)===null||a===void 0?void 0:a.root),S==null?void 0:S.root)}),C,s.createElement(yt,{visible:!I,motionName:`${p}-zoom`,motionAppear:!1,motionDeadline:1e3},({className:x})=>{var L,q;const Ct=K("scroll-number",v),X=tt.current,ht=w(N==null?void 0:N.indicator,(L=r==null?void 0:r.classNames)===null||L===void 0?void 0:L.indicator,{[`${p}-dot`]:X,[`${p}-count`]:!X,[`${p}-count-sm`]:f==="small",[`${p}-multiple-words`]:!X&&A&&A.toString().length>1,[`${p}-status-${l}`]:!!l,[`${p}-color-${m}`]:F});let D=Object.assign(Object.assign(Object.assign({},S==null?void 0:S.indicator),(q=r==null?void 0:r.styles)===null||q===void 0?void 0:q.indicator),B);return m&&!F&&(D=D||{},D.background=m),s.createElement(Ht,{prefixCls:Ct,show:!I,motionClassName:x,className:ht,count:A,title:gt,style:D,key:"scrollNumber"},pt)}),ft))}),_t=Zt;_t.Ribbon=Bt;var At={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M834.1 469.2A347.49 347.49 0 00751.2 354l-29.1-26.7a8.09 8.09 0 00-13 3.3l-13 37.3c-8.1 23.4-23 47.3-44.1 70.8-1.4 1.5-3 1.9-4.1 2-1.1.1-2.8-.1-4.3-1.5-1.4-1.2-2.1-3-2-4.8 3.7-60.2-14.3-128.1-53.7-202C555.3 171 510 123.1 453.4 89.7l-41.3-24.3c-5.4-3.2-12.3 1-12 7.3l2.2 48c1.5 32.8-2.3 61.8-11.3 85.9-11 29.5-26.8 56.9-47 81.5a295.64 295.64 0 01-47.5 46.1 352.6 352.6 0 00-100.3 121.5A347.75 347.75 0 00160 610c0 47.2 9.3 92.9 27.7 136a349.4 349.4 0 0075.5 110.9c32.4 32 70 57.2 111.9 74.7C418.5 949.8 464.5 959 512 959s93.5-9.2 136.9-27.3A348.6 348.6 0 00760.8 857c32.4-32 57.8-69.4 75.5-110.9a344.2 344.2 0 0027.7-136c0-48.8-10-96.2-29.9-140.9zM713 808.5c-53.7 53.2-125 82.4-201 82.4s-147.3-29.2-201-82.4c-53.5-53.1-83-123.5-83-198.4 0-43.5 9.8-85.2 29.1-124 18.8-37.9 46.8-71.8 80.8-97.9a349.6 349.6 0 0058.6-56.8c25-30.5 44.6-64.5 58.2-101a240 240 0 0012.1-46.5c24.1 22.2 44.3 49 61.2 80.4 33.4 62.6 48.8 118.3 45.8 165.7a74.01 74.01 0 0024.4 59.8 73.36 73.36 0 0053.4 18.8c19.7-1 37.8-9.7 51-24.4 13.3-14.9 24.8-30.1 34.4-45.6 14 17.9 25.7 37.4 35 58.4 15.9 35.8 24 73.9 24 113.1 0 74.9-29.5 145.4-83 198.4z"}}]},name:"fire",theme:"outlined"},Vt=function(e,i){return s.createElement(St,Ot({},e,{ref:i,icon:At}))},qt=s.forwardRef(Vt);export{_t as B,qt as R};
