import{r as o,a6 as K,a as g,b as U,i as J,m as Q,p as Y,au as B,b1 as Z}from"./index-CHvake0r.js";function k(e,t,i){var n=i||{},a=n.noTrailing,r=a===void 0?!1:a,l=n.noLeading,c=l===void 0?!1:l,d=n.debounceMode,m=d===void 0?void 0:d,u,h=!1,p=0;function S(){u&&clearTimeout(u)}function E(v){var $=v||{},f=$.upcomingOnly,C=f===void 0?!1:f;S(),h=!C}function x(){for(var v=arguments.length,$=new Array(v),f=0;f<v;f++)$[f]=arguments[f];var C=this,N=Date.now()-p;if(h)return;function s(){p=Date.now(),t.apply(C,$)}function b(){u=void 0}!c&&m&&!u&&s(),S(),m===void 0&&N>e?c?(p=Date.now(),r||(u=setTimeout(m?b:s,e))):s():r!==!0&&(u=setTimeout(m?b:s,m===void 0?e-N:e))}return x.cancel=E,x}function ee(e,t,i){var n={},a=n.atBegin,r=a===void 0?!1:a;return k(e,t,{debounceMode:r!==!1})}const z=100,G=z/5,_=z/2-G/2,M=_*2*Math.PI,q=50,j=e=>{const{dotClassName:t,style:i,hasCircleCls:n}=e;return o.createElement("circle",{className:g(`${t}-circle`,{[`${t}-circle-bg`]:n}),r:_,cx:q,cy:q,strokeWidth:G,style:i})},te=({percent:e,prefixCls:t})=>{const i=`${t}-dot`,n=`${i}-holder`,a=`${n}-hidden`,[r,l]=o.useState(!1);K(()=>{e!==0&&l(!0)},[e!==0]);const c=Math.max(Math.min(e,100),0);if(!r)return null;const d={strokeDashoffset:`${M/4}`,strokeDasharray:`${M*c/100} ${M*(100-c)/100}`};return o.createElement("span",{className:g(n,`${i}-progress`,c<=0&&a)},o.createElement("svg",{viewBox:`0 0 ${z} ${z}`,role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":c},o.createElement(j,{dotClassName:i,hasCircleCls:!0}),o.createElement(j,{dotClassName:i,style:d})))};function ie(e){const{prefixCls:t,percent:i=0}=e,n=`${t}-dot`,a=`${n}-holder`,r=`${a}-hidden`;return o.createElement(o.Fragment,null,o.createElement("span",{className:g(a,i>0&&r)},o.createElement("span",{className:g(n,`${t}-dot-spin`)},[1,2,3,4].map(l=>o.createElement("i",{className:`${t}-dot-item`,key:l})))),o.createElement(te,{prefixCls:t,percent:i}))}function ne(e){const{prefixCls:t,indicator:i,percent:n}=e,a=`${t}-dot`;return i&&o.isValidElement(i)?U(i,{className:g(i.props.className,a),percent:n}):o.createElement(ie,{prefixCls:t,percent:n})}const oe=new B("antSpinMove",{to:{opacity:1}}),ae=new B("antRotate",{to:{transform:"rotate(405deg)"}}),se=e=>{const{componentCls:t,calc:i}=e;return{[t]:Object.assign(Object.assign({},Y(e)),{position:"absolute",display:"none",color:e.colorPrimary,fontSize:0,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"relative",display:"inline-block",opacity:1},[`${t}-text`]:{fontSize:e.fontSize,paddingTop:i(i(e.dotSize).sub(e.fontSize)).div(2).add(2).equal()},"&-fullscreen":{position:"fixed",width:"100vw",height:"100vh",backgroundColor:e.colorBgMask,zIndex:e.zIndexPopupBase,inset:0,display:"flex",alignItems:"center",flexDirection:"column",justifyContent:"center",opacity:0,visibility:"hidden",transition:`all ${e.motionDurationMid}`,"&-show":{opacity:1,visibility:"visible"},[t]:{[`${t}-dot-holder`]:{color:e.colorWhite},[`${t}-text`]:{color:e.colorTextLightSolid}}},"&-nested-loading":{position:"relative",[`> div > ${t}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${t}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:i(e.dotSize).mul(-1).div(2).equal()},[`${t}-text`]:{position:"absolute",top:"50%",width:"100%",textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${t}-show-text ${t}-dot`]:{marginTop:i(e.dotSize).div(2).mul(-1).sub(10).equal()},"&-sm":{[`${t}-dot`]:{margin:i(e.dotSizeSM).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:i(i(e.dotSizeSM).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:i(e.dotSizeSM).div(2).mul(-1).sub(10).equal()}},"&-lg":{[`${t}-dot`]:{margin:i(e.dotSizeLG).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:i(i(e.dotSizeLG).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:i(e.dotSizeLG).div(2).mul(-1).sub(10).equal()}}},[`${t}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${t}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${t}-dot-holder`]:{width:"1em",height:"1em",fontSize:e.dotSize,display:"inline-block",transition:`transform ${e.motionDurationSlow} ease, opacity ${e.motionDurationSlow} ease`,transformOrigin:"50% 50%",lineHeight:1,color:e.colorPrimary,"&-hidden":{transform:"scale(0.3)",opacity:0}},[`${t}-dot-progress`]:{position:"absolute",inset:0},[`${t}-dot`]:{position:"relative",display:"inline-block",fontSize:e.dotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:i(e.dotSize).sub(i(e.marginXXS).div(2)).div(2).equal(),height:i(e.dotSize).sub(i(e.marginXXS).div(2)).div(2).equal(),background:"currentColor",borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:oe,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0,animationDelay:"0s"},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:ae,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"},"&-circle":{strokeLinecap:"round",transition:["stroke-dashoffset","stroke-dasharray","stroke","stroke-width","opacity"].map(n=>`${n} ${e.motionDurationSlow} ease`).join(","),fillOpacity:0,stroke:"currentcolor"},"&-circle-bg":{stroke:e.colorFillSecondary}},[`&-sm ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeSM}},[`&-sm ${t}-dot-holder`]:{i:{width:i(i(e.dotSizeSM).sub(i(e.marginXXS).div(2))).div(2).equal(),height:i(i(e.dotSizeSM).sub(i(e.marginXXS).div(2))).div(2).equal()}},[`&-lg ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeLG}},[`&-lg ${t}-dot-holder`]:{i:{width:i(i(e.dotSizeLG).sub(e.marginXXS)).div(2).equal(),height:i(i(e.dotSizeLG).sub(e.marginXXS)).div(2).equal()}},[`&${t}-show-text ${t}-text`]:{display:"block"}})}},re=e=>{const{controlHeightLG:t,controlHeight:i}=e;return{contentHeight:400,dotSize:t/2,dotSizeSM:t*.35,dotSizeLG:i}},le=J("Spin",e=>{const t=Q(e,{spinDotDefault:e.colorTextDescription});return[se(t)]},re),ce=200,X=[[30,.05],[70,.03],[96,.01]];function de(e,t){const[i,n]=o.useState(0),a=o.useRef(null),r=t==="auto";return o.useEffect(()=>(r&&e&&(n(0),a.current=setInterval(()=>{n(l=>{const c=100-l;for(let d=0;d<X.length;d+=1){const[m,u]=X[d];if(l<=m)return l+c*u}return l})},ce)),()=>{clearInterval(a.current)}),[r,e]),r?i:t}var ue=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(i[n[a]]=e[n[a]]);return i};let H;function me(e,t){return!!e&&!!t&&!Number.isNaN(Number(t))}const pe=e=>{var t;const{prefixCls:i,spinning:n=!0,delay:a=0,className:r,rootClassName:l,size:c="default",tip:d,wrapperClassName:m,style:u,children:h,fullscreen:p=!1,indicator:S,percent:E}=e,x=ue(e,["prefixCls","spinning","delay","className","rootClassName","size","tip","wrapperClassName","style","children","fullscreen","indicator","percent"]),{getPrefixCls:v,direction:$,className:f,style:C,indicator:N}=Z("spin"),s=v("spin",i),[b,I,D]=le(s),[y,P]=o.useState(()=>n&&!me(n,a)),R=de(y,E);o.useEffect(()=>{if(n){const w=ee(a,()=>{P(!0)});return w(),()=>{var O;(O=w==null?void 0:w.cancel)===null||O===void 0||O.call(w)}}P(!1)},[a,n]);const L=o.useMemo(()=>typeof h<"u"&&!p,[h,p]),A=g(s,f,{[`${s}-sm`]:c==="small",[`${s}-lg`]:c==="large",[`${s}-spinning`]:y,[`${s}-show-text`]:!!d,[`${s}-rtl`]:$==="rtl"},r,!p&&l,I,D),F=g(`${s}-container`,{[`${s}-blur`]:y}),V=(t=S??N)!==null&&t!==void 0?t:H,W=Object.assign(Object.assign({},C),u),T=o.createElement("div",Object.assign({},x,{style:W,className:A,"aria-live":"polite","aria-busy":y}),o.createElement(ne,{prefixCls:s,indicator:V,percent:R}),d&&(L||p)?o.createElement("div",{className:`${s}-text`},d):null);return b(L?o.createElement("div",Object.assign({},x,{className:g(`${s}-nested-loading`,m,I,D)}),y&&o.createElement("div",{key:"loading"},T),o.createElement("div",{className:F,key:"container"},h)):p?o.createElement("div",{className:g(`${s}-fullscreen`,{[`${s}-fullscreen-show`]:y},l,I,D)},T):T)};pe.setDefaultIndicator=e=>{H=e};export{pe as S};
