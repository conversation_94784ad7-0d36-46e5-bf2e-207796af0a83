const s=async(e={})=>{try{const o=await fetch("/api/kitchen/orderFoods",{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}});if(!o.ok)throw new Error("Failed to load foods");return await o.json()}catch(o){throw console.error("Error loading foods:",o),o}},c=async(e,o,r=null)=>{var a;try{const t=await fetch(`/api/kitchen/foods/${e}/status`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`,"X-CSRF-TOKEN":(a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.content},body:JSON.stringify({status:o,notes:r})});if(!t.ok)throw new Error("Failed to update food status");return await t.json()}catch(t){throw console.error("Error updating food status:",t),t}},i=async(e,o,r)=>{var a;try{const t=await fetch("/api/kitchen/foods/updateStatus",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`,"X-CSRF-TOKEN":(a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.content},body:JSON.stringify({foodName:e,quantity:o,status:r})});if(!t.ok)throw new Error("Failed to update food quantity");return await t.json()}catch(t){throw console.error("Error updating food quantity:",t),t}},d=async e=>{if(console.log(e),e.length!==0)try{const o=new(window.AudioContext||window.webkitAudioContext);o.state==="suspended"&&(await o.resume(),console.log("AudioContext resumed successfully"));const r=await fetch(`/api/mp3?q=${encodeURIComponent(e)}`,{method:"GET",headers:{Accept:"audio/mpeg"}});if(!r.ok)throw new Error(`Failed to fetch MP3: ${r.statusText}`);const a=await r.arrayBuffer(),t=await o.decodeAudioData(a),n=o.createBufferSource();n.buffer=t,n.connect(o.destination),n.start(),n.onended=()=>{console.log("Audio playback finished"),o.close()}}catch(o){console.error("Error in loudSpeaker:",o)}};export{i as a,s as g,d as l,c as u};
