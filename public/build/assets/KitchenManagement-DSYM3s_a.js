import{r as i,a3 as De,a4 as te,al as Ye,am as Ze,ac as L,Z as xt,a5 as de,a6 as St,aG as yt,w as be,a as Q,a0 as Je,aH as Nt,aI as wt,_ as $t,aJ as je,aK as Ct,aL as It,F as jt,i as Et,m as Rt,aM as Ot,aN as Dt,p as Ee,aO as et,aP as Mt,e as W,aQ as _t,aR as Ft,aS as kt,aT as Bt,aU as Vt,aV as At,aW as Lt,q as zt,C as Tt,z as qt,a1 as Ht,aX as Pt,aA as Wt,aY as Gt,aZ as Ut,a_ as He,d as Pe,a$ as Kt,b0 as Xt,j as a,a2 as me,aB as fe,aD as We,T as Qt}from"./index-CHvake0r.js";import{R as Yt,S as Zt,b as tt}from"./EyeOutlined-D5ZQtqsR.js";import{R as Jt}from"./ReloadOutlined-BPMc8QqU.js";import{a as en,F as Re,M as tn}from"./index-Cqw7XJJF.js";import{g as nn,l as rn,u as an,a as sn}from"./kitchent-0R1q2rXv.js";import{R as pe}from"./CheckCircleOutlined-DMtaSJBb.js";import{B as xe,R as ln}from"./FireOutlined-B7wZholJ.js";import{R as nt}from"./ClockCircleOutlined-B61igLBq.js";import{s as on}from"./index-CC0TaQ9p.js";import{S as cn}from"./index-D_dKmeqG.js";var un={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},dn=function(t,n){return i.createElement(De,te({},t,{ref:n,icon:un}))},fn=i.forwardRef(dn);function Oe(){return typeof BigInt=="function"}function rt(e){return!e&&e!==0&&!Number.isNaN(e)||!String(e).trim()}function ee(e){var t=e.trim(),n=t.startsWith("-");n&&(t=t.slice(1)),t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,""),t.startsWith(".")&&(t="0".concat(t));var r=t||"0",s=r.split("."),o=s[0]||"0",h=s[1]||"0";o==="0"&&h==="0"&&(n=!1);var d=n?"-":"";return{negative:n,negativeStr:d,trimStr:r,integerStr:o,decimalStr:h,fullStr:"".concat(d).concat(r)}}function Me(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function J(e){var t=String(e);if(Me(e)){var n=Number(t.slice(t.indexOf("e-")+2)),r=t.match(/\.(\d+)/);return r!=null&&r[1]&&(n+=r[1].length),n}return t.includes(".")&&_e(t)?t.length-t.indexOf(".")-1:0}function Se(e){var t=String(e);if(Me(e)){if(e>Number.MAX_SAFE_INTEGER)return String(Oe()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(Oe()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(J(t))}return ee(t).fullStr}function _e(e){return typeof e=="number"?!Number.isNaN(e):e?/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e):!1}var mn=function(){function e(t){if(Ze(this,e),L(this,"origin",""),L(this,"negative",void 0),L(this,"integer",void 0),L(this,"decimal",void 0),L(this,"decimalLen",void 0),L(this,"empty",void 0),L(this,"nan",void 0),rt(t)){this.empty=!0;return}if(this.origin=String(t),t==="-"||Number.isNaN(t)){this.nan=!0;return}var n=t;if(Me(n)&&(n=Number(n)),n=typeof n=="string"?n:Se(n),_e(n)){var r=ee(n);this.negative=r.negative;var s=r.trimStr.split(".");this.integer=BigInt(s[0]);var o=s[1]||"0";this.decimal=BigInt(o),this.decimalLen=o.length}else this.nan=!0}return Ye(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(n){var r="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(n,"0"));return BigInt(r)}},{key:"negate",value:function(){var n=new e(this.toString());return n.negative=!n.negative,n}},{key:"cal",value:function(n,r,s){var o=Math.max(this.getDecimalStr().length,n.getDecimalStr().length),h=this.alignDecimal(o),d=n.alignDecimal(o),p=r(h,d).toString(),b=s(o),f=ee(p),E=f.negativeStr,S=f.trimStr,$="".concat(E).concat(S.padStart(b+1,"0"));return new e("".concat($.slice(0,-b),".").concat($.slice(-b)))}},{key:"add",value:function(n){if(this.isInvalidate())return new e(n);var r=new e(n);return r.isInvalidate()?this:this.cal(r,function(s,o){return s+o},function(s){return s})}},{key:"multi",value:function(n){var r=new e(n);return this.isInvalidate()||r.isInvalidate()?new e(NaN):this.cal(r,function(s,o){return s*o},function(s){return s*2})}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(n){return this.toString()===(n==null?void 0:n.toString())}},{key:"lessEquals",value:function(n){return this.add(n.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return n?this.isInvalidate()?"":ee("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),e}(),gn=function(){function e(t){if(Ze(this,e),L(this,"origin",""),L(this,"number",void 0),L(this,"empty",void 0),rt(t)){this.empty=!0;return}this.origin=String(t),this.number=Number(t)}return Ye(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(n){if(this.isInvalidate())return new e(n);var r=Number(n);if(Number.isNaN(r))return this;var s=this.number+r;if(s>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(s<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var o=Math.max(J(this.number),J(r));return new e(s.toFixed(o))}},{key:"multi",value:function(n){var r=Number(n);if(this.isInvalidate()||Number.isNaN(r))return new e(NaN);var s=this.number*r;if(s>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(s<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var o=Math.max(J(this.number),J(r));return new e(s.toFixed(o))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(n){return this.toNumber()===(n==null?void 0:n.toNumber())}},{key:"lessEquals",value:function(n){return this.add(n.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return n?this.isInvalidate()?"":Se(this.number):this.origin}}]),e}();function K(e){return Oe()?new mn(e):new gn(e)}function ve(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e==="")return"";var s=ee(e),o=s.negativeStr,h=s.integerStr,d=s.decimalStr,p="".concat(t).concat(d),b="".concat(o).concat(h);if(n>=0){var f=Number(d[n]);if(f>=5&&!r){var E=K(e).add("".concat(o,"0.").concat("0".repeat(n)).concat(10-f));return ve(E.toString(),t,n,r)}return n===0?b:"".concat(b).concat(t).concat(d.padEnd(n,"0").slice(0,n))}return p===".0"?b:"".concat(b).concat(p)}function hn(e,t){return typeof Proxy<"u"&&e?new Proxy(e,{get:function(r,s){if(t[s])return t[s];var o=r[s];return typeof o=="function"?o.bind(r):o}}):e}function pn(e,t){var n=i.useRef(null);function r(){try{var o=e.selectionStart,h=e.selectionEnd,d=e.value,p=d.substring(0,o),b=d.substring(h);n.current={start:o,end:h,value:d,beforeTxt:p,afterTxt:b}}catch{}}function s(){if(e&&n.current&&t)try{var o=e.value,h=n.current,d=h.beforeTxt,p=h.afterTxt,b=h.start,f=o.length;if(o.startsWith(d))f=d.length;else if(o.endsWith(p))f=o.length-n.current.afterTxt.length;else{var E=d[b-1],S=o.indexOf(E,b-1);S!==-1&&(f=S+1)}e.setSelectionRange(f,f)}catch($){xt(!1,"Something warning of cursor restore. Please fire issue about this: ".concat($.message))}}return[r,s]}var vn=function(){var t=i.useState(!1),n=de(t,2),r=n[0],s=n[1];return St(function(){s(yt())},[]),r},bn=200,xn=600;function Sn(e){var t=e.prefixCls,n=e.upNode,r=e.downNode,s=e.upDisabled,o=e.downDisabled,h=e.onStep,d=i.useRef(),p=i.useRef([]),b=i.useRef();b.current=h;var f=function(){clearTimeout(d.current)},E=function(y,c){y.preventDefault(),f(),b.current(c);function m(){b.current(c),d.current=setTimeout(m,bn)}d.current=setTimeout(m,xn)};i.useEffect(function(){return function(){f(),p.current.forEach(function(F){return be.cancel(F)})}},[]);var S=vn();if(S)return null;var $="".concat(t,"-handler"),R=Q($,"".concat($,"-up"),L({},"".concat($,"-up-disabled"),s)),V=Q($,"".concat($,"-down"),L({},"".concat($,"-down-disabled"),o)),O=function(){return p.current.push(be(f))},D={unselectable:"on",role:"button",onMouseUp:O,onMouseLeave:O};return i.createElement("div",{className:"".concat($,"-wrap")},i.createElement("span",te({},D,{onMouseDown:function(y){E(y,!0)},"aria-label":"Increase Value","aria-disabled":s,className:R}),n||i.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-up-inner")})),i.createElement("span",te({},D,{onMouseDown:function(y){E(y,!1)},"aria-label":"Decrease Value","aria-disabled":o,className:V}),r||i.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-down-inner")})))}function Ge(e){var t=typeof e=="number"?Se(e):ee(e).fullStr,n=t.includes(".");return n?ee(t.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}const yn=function(){var e=i.useRef(0),t=function(){be.cancel(e.current)};return i.useEffect(function(){return t},[]),function(n){t(),e.current=be(function(){n()})}};var Nn=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],wn=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],Ue=function(t,n){return t||n.isEmpty()?n.toString():n.toNumber()},Ke=function(t){var n=K(t);return n.isInvalidate()?null:n},$n=i.forwardRef(function(e,t){var n=e.prefixCls,r=e.className,s=e.style,o=e.min,h=e.max,d=e.step,p=d===void 0?1:d,b=e.defaultValue,f=e.value,E=e.disabled,S=e.readOnly,$=e.upHandler,R=e.downHandler,V=e.keyboard,O=e.changeOnWheel,D=O===void 0?!1:O,F=e.controls,y=F===void 0?!0:F;e.classNames;var c=e.stringMode,m=e.parser,x=e.formatter,N=e.precision,I=e.decimalSeparator,T=e.onChange,q=e.onInput,H=e.onPressEnter,z=e.onStep,P=e.changeOnBlur,Z=P===void 0?!0:P,ne=e.domRef,Y=Je(e,Nn),l="".concat(n,"-input"),w=i.useRef(null),C=i.useState(!1),k=de(C,2),A=k[0],U=k[1],B=i.useRef(!1),_=i.useRef(!1),G=i.useRef(!1),re=i.useState(function(){return K(f??b)}),ge=de(re,2),M=ge[0],ae=ge[1];function st(g){f===void 0&&ae(g)}var ye=i.useCallback(function(g,u){if(!u)return N>=0?N:Math.max(J(g),J(p))},[N,p]),Ne=i.useCallback(function(g){var u=String(g);if(m)return m(u);var j=u;return I&&(j=j.replace(I,".")),j.replace(/[^\w.-]+/g,"")},[m,I]),we=i.useRef(""),Fe=i.useCallback(function(g,u){if(x)return x(g,{userTyping:u,input:String(we.current)});var j=typeof g=="number"?Se(g):g;if(!u){var v=ye(j,u);if(_e(j)&&(I||v>=0)){var X=I||".";j=ve(j,X,v)}}return j},[x,ye,I]),lt=i.useState(function(){var g=b??f;return M.isInvalidate()&&["string","number"].includes($t(g))?Number.isNaN(g)?"":g:Fe(M.toString(),!1)}),ke=de(lt,2),oe=ke[0],Be=ke[1];we.current=oe;function ce(g,u){Be(Fe(g.isInvalidate()?g.toString(!1):g.toString(!u),u))}var ie=i.useMemo(function(){return Ke(h)},[h,N]),se=i.useMemo(function(){return Ke(o)},[o,N]),Ve=i.useMemo(function(){return!ie||!M||M.isInvalidate()?!1:ie.lessEquals(M)},[ie,M]),Ae=i.useMemo(function(){return!se||!M||M.isInvalidate()?!1:M.lessEquals(se)},[se,M]),ot=pn(w.current,A),Le=de(ot,2),ct=Le[0],ut=Le[1],ze=function(u){return ie&&!u.lessEquals(ie)?ie:se&&!se.lessEquals(u)?se:null},$e=function(u){return!ze(u)},he=function(u,j){var v=u,X=$e(v)||v.isEmpty();if(!v.isEmpty()&&!j&&(v=ze(v)||v,X=!0),!S&&!E&&X){var ue=v.toString(),Ie=ye(ue,j);return Ie>=0&&(v=K(ve(ue,".",Ie)),$e(v)||(v=K(ve(ue,".",Ie,!0)))),v.equals(M)||(st(v),T==null||T(v.isEmpty()?null:Ue(c,v)),f===void 0&&ce(v,j)),v}return M},dt=yn(),Te=function g(u){if(ct(),we.current=u,Be(u),!_.current){var j=Ne(u),v=K(j);v.isNaN()||he(v,!0)}q==null||q(u),dt(function(){var X=u;m||(X=u.replace(/。/g,".")),X!==u&&g(X)})},ft=function(){_.current=!0},mt=function(){_.current=!1,Te(w.current.value)},gt=function(u){Te(u.target.value)},Ce=function(u){var j;if(!(u&&Ve||!u&&Ae)){B.current=!1;var v=K(G.current?Ge(p):p);u||(v=v.negate());var X=(M||K(0)).add(v.toString()),ue=he(X,!1);z==null||z(Ue(c,ue),{offset:G.current?Ge(p):p,type:u?"up":"down"}),(j=w.current)===null||j===void 0||j.focus()}},qe=function(u){var j=K(Ne(oe)),v;j.isNaN()?v=he(M,u):v=he(j,u),f!==void 0?ce(M,!1):v.isNaN()||ce(v,!1)},ht=function(){B.current=!0},pt=function(u){var j=u.key,v=u.shiftKey;B.current=!0,G.current=v,j==="Enter"&&(_.current||(B.current=!1),qe(!1),H==null||H(u)),V!==!1&&!_.current&&["Up","ArrowUp","Down","ArrowDown"].includes(j)&&(Ce(j==="Up"||j==="ArrowUp"),u.preventDefault())},vt=function(){B.current=!1,G.current=!1};i.useEffect(function(){if(D&&A){var g=function(v){Ce(v.deltaY<0),v.preventDefault()},u=w.current;if(u)return u.addEventListener("wheel",g,{passive:!1}),function(){return u.removeEventListener("wheel",g)}}});var bt=function(){Z&&qe(!1),U(!1),B.current=!1};return je(function(){M.isInvalidate()||ce(M,!1)},[N,x]),je(function(){var g=K(f);ae(g);var u=K(Ne(oe));(!g.equals(u)||!B.current||x)&&ce(g,B.current)},[f]),je(function(){x&&ut()},[oe]),i.createElement("div",{ref:ne,className:Q(n,r,L(L(L(L(L({},"".concat(n,"-focused"),A),"".concat(n,"-disabled"),E),"".concat(n,"-readonly"),S),"".concat(n,"-not-a-number"),M.isNaN()),"".concat(n,"-out-of-range"),!M.isInvalidate()&&!$e(M))),style:s,onFocus:function(){U(!0)},onBlur:bt,onKeyDown:pt,onKeyUp:vt,onCompositionStart:ft,onCompositionEnd:mt,onBeforeInput:ht},y&&i.createElement(Sn,{prefixCls:n,upNode:$,downNode:R,upDisabled:Ve,downDisabled:Ae,onStep:Ce}),i.createElement("div",{className:"".concat(l,"-wrap")},i.createElement("input",te({autoComplete:"off",role:"spinbutton","aria-valuemin":o,"aria-valuemax":h,"aria-valuenow":M.isInvalidate()?null:M.toString(),step:p},Y,{ref:Ct(w,t),className:l,value:oe,onChange:gt,disabled:E,readOnly:S}))))}),Cn=i.forwardRef(function(e,t){var n=e.disabled,r=e.style,s=e.prefixCls,o=s===void 0?"rc-input-number":s,h=e.value,d=e.prefix,p=e.suffix,b=e.addonBefore,f=e.addonAfter,E=e.className,S=e.classNames,$=Je(e,wn),R=i.useRef(null),V=i.useRef(null),O=i.useRef(null),D=function(y){O.current&&wt(O.current,y)};return i.useImperativeHandle(t,function(){return hn(O.current,{focus:D,nativeElement:R.current.nativeElement||V.current})}),i.createElement(Nt,{className:E,triggerFocus:D,prefixCls:o,value:h,disabled:n,style:r,prefix:d,suffix:p,addonAfter:f,addonBefore:b,classNames:S,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:R},i.createElement($n,te({prefixCls:o,disabled:n,ref:O,domRef:V,className:S==null?void 0:S.input},$)))});const In=e=>{var t;const n=(t=e.handleVisible)!==null&&t!==void 0?t:"auto",r=e.controlHeightSM-e.lineWidth*2;return Object.assign(Object.assign({},It(e)),{controlWidth:90,handleWidth:r,handleFontSize:e.fontSize/2,handleVisible:n,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new jt(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:n===!0?1:0,handleVisibleWidth:n===!0?r:0})},Xe=({componentCls:e,borderRadiusSM:t,borderRadiusLG:n},r)=>{const s=r==="lg"?n:t;return{[`&-${r}`]:{[`${e}-handler-wrap`]:{borderStartEndRadius:s,borderEndEndRadius:s},[`${e}-handler-up`]:{borderStartEndRadius:s},[`${e}-handler-down`]:{borderEndEndRadius:s}}}},jn=e=>{const{componentCls:t,lineWidth:n,lineType:r,borderRadius:s,inputFontSizeSM:o,inputFontSizeLG:h,controlHeightLG:d,controlHeightSM:p,colorError:b,paddingInlineSM:f,paddingBlockSM:E,paddingBlockLG:S,paddingInlineLG:$,colorIcon:R,motionDurationMid:V,handleHoverColor:O,handleOpacity:D,paddingInline:F,paddingBlock:y,handleBg:c,handleActiveBg:m,colorTextDisabled:x,borderRadiusSM:N,borderRadiusLG:I,controlWidth:T,handleBorderColor:q,filledHandleBg:H,lineHeightLG:z,calc:P}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Ee(e)),et(e)),{display:"inline-block",width:T,margin:0,padding:0,borderRadius:s}),Mt(e,{[`${t}-handler-wrap`]:{background:c,[`${t}-handler-down`]:{borderBlockStart:`${W(n)} ${r} ${q}`}}})),_t(e,{[`${t}-handler-wrap`]:{background:H,[`${t}-handler-down`]:{borderBlockStart:`${W(n)} ${r} ${q}`}},"&:focus-within":{[`${t}-handler-wrap`]:{background:c}}})),Ft(e,{[`${t}-handler-wrap`]:{background:c,[`${t}-handler-down`]:{borderBlockStart:`${W(n)} ${r} ${q}`}}})),kt(e)),{"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:h,lineHeight:z,borderRadius:I,[`input${t}-input`]:{height:P(d).sub(P(n).mul(2)).equal(),padding:`${W(S)} ${W($)}`}},"&-sm":{padding:0,fontSize:o,borderRadius:N,[`input${t}-input`]:{height:P(p).sub(P(n).mul(2)).equal(),padding:`${W(E)} ${W(f)}`}},"&-out-of-range":{[`${t}-input-wrap`]:{input:{color:b}}},"&-group":Object.assign(Object.assign(Object.assign({},Ee(e)),Vt(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:I,fontSize:e.fontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:N}}},At(e)),Lt(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${t}-input`]:{cursor:"not-allowed"},[t]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},Ee(e)),{width:"100%",padding:`${W(y)} ${W(F)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:s,outline:0,transition:`all ${V} linear`,appearance:"textfield",fontSize:"inherit"}),Bt(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,appearance:"none"}})},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1}})},{[t]:Object.assign(Object.assign(Object.assign({[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleVisibleWidth,opacity:D,height:"100%",borderStartStartRadius:0,borderStartEndRadius:s,borderEndEndRadius:s,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${V}`,overflow:"hidden",[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:R,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${W(n)} ${r} ${q}`,transition:`all ${V} linear`,"&:active":{background:m},"&:hover":{height:"60%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{color:O}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},zt()),{color:R,transition:`all ${V} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:s},[`${t}-handler-down`]:{borderEndEndRadius:s}},Xe(e,"lg")),Xe(e,"sm")),{"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`
          ${t}-handler-up-disabled,
          ${t}-handler-down-disabled
        `]:{cursor:"not-allowed"},[`
          ${t}-handler-up-disabled:hover &-handler-up-inner,
          ${t}-handler-down-disabled:hover &-handler-down-inner
        `]:{color:x}})}]},En=e=>{const{componentCls:t,paddingBlock:n,paddingInline:r,inputAffixPadding:s,controlWidth:o,borderRadiusLG:h,borderRadiusSM:d,paddingInlineLG:p,paddingInlineSM:b,paddingBlockLG:f,paddingBlockSM:E,motionDurationMid:S}=e;return{[`${t}-affix-wrapper`]:Object.assign(Object.assign({[`input${t}-input`]:{padding:`${W(n)} 0`}},et(e)),{position:"relative",display:"inline-flex",alignItems:"center",width:o,padding:0,paddingInlineStart:r,"&-lg":{borderRadius:h,paddingInlineStart:p,[`input${t}-input`]:{padding:`${W(f)} 0`}},"&-sm":{borderRadius:d,paddingInlineStart:b,[`input${t}-input`]:{padding:`${W(E)} 0`}},[`&:not(${t}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${t}-disabled`]:{background:"transparent"},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:s},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:r,marginInlineStart:s,transition:`margin ${S}`}},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1},[`&:not(${t}-affix-wrapper-without-controls):hover ${t}-suffix`]:{marginInlineEnd:e.calc(e.handleWidth).add(r).equal()}})}},Rn=Et("InputNumber",e=>{const t=Rt(e,Ot(e));return[jn(t),En(t),Dt(t)]},In,{unitless:{handleOpacity:!0}});var On=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};const at=i.forwardRef((e,t)=>{const{getPrefixCls:n,direction:r}=i.useContext(Tt),s=i.useRef(null);i.useImperativeHandle(t,()=>s.current);const{className:o,rootClassName:h,size:d,disabled:p,prefixCls:b,addonBefore:f,addonAfter:E,prefix:S,suffix:$,bordered:R,readOnly:V,status:O,controls:D,variant:F}=e,y=On(e,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),c=n("input-number",b),m=qt(c),[x,N,I]=Rn(c,m),{compactSize:T,compactItemClassnames:q}=Ht(c,r);let H=i.createElement(fn,{className:`${c}-handler-up-inner`}),z=i.createElement(Yt,{className:`${c}-handler-down-inner`});const P=typeof D=="boolean"?D:void 0;typeof D=="object"&&(H=typeof D.upIcon>"u"?H:i.createElement("span",{className:`${c}-handler-up-inner`},D.upIcon),z=typeof D.downIcon>"u"?z:i.createElement("span",{className:`${c}-handler-down-inner`},D.downIcon));const{hasFeedback:Z,status:ne,isFormItemInput:Y,feedbackIcon:l}=i.useContext(Pt),w=Kt(ne,O),C=Wt(M=>{var ae;return(ae=d??T)!==null&&ae!==void 0?ae:M}),k=i.useContext(Gt),A=p??k,[U,B]=Ut("inputNumber",F,R),_=Z&&i.createElement(i.Fragment,null,l),G=Q({[`${c}-lg`]:C==="large",[`${c}-sm`]:C==="small",[`${c}-rtl`]:r==="rtl",[`${c}-in-form-item`]:Y},N),re=`${c}-group`,ge=i.createElement(Cn,Object.assign({ref:s,disabled:A,className:Q(I,m,o,h,q),upHandler:H,downHandler:z,prefixCls:c,readOnly:V,controls:P,prefix:S,suffix:_||$,addonBefore:f&&i.createElement(Pe,{form:!0,space:!0},f),addonAfter:E&&i.createElement(Pe,{form:!0,space:!0},E),classNames:{input:G,variant:Q({[`${c}-${U}`]:B},He(c,w,Z)),affixWrapper:Q({[`${c}-affix-wrapper-sm`]:C==="small",[`${c}-affix-wrapper-lg`]:C==="large",[`${c}-affix-wrapper-rtl`]:r==="rtl",[`${c}-affix-wrapper-without-controls`]:D===!1||A},N),wrapper:Q({[`${re}-rtl`]:r==="rtl"},N),groupWrapper:Q({[`${c}-group-wrapper-sm`]:C==="small",[`${c}-group-wrapper-lg`]:C==="large",[`${c}-group-wrapper-rtl`]:r==="rtl",[`${c}-group-wrapper-${U}`]:B},He(`${c}-group-wrapper`,w,Z),N)}},y));return x(ge)}),it=at,Dn=e=>i.createElement(Xt,{theme:{components:{InputNumber:{handleVisible:!0}}}},i.createElement(at,Object.assign({},e)));it._InternalPanelDoNotUseOrYouWillBeFired=Dn;var Mn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M839.6 433.8L749 150.5a9.24 9.24 0 00-8.9-6.5h-77.4c-4.1 0-7.6 2.6-8.9 6.5l-91.3 283.3c-.3.9-.5 1.9-.5 2.9 0 5.1 4.2 9.3 9.3 9.3h56.4c4.2 0 7.8-2.8 9-6.8l17.5-61.6h89l17.3 61.5c1.1 4 4.8 6.8 9 6.8h61.2c1 0 1.9-.1 2.8-.4 2.4-.8 4.3-2.4 5.5-4.6 1.1-2.2 1.3-4.7.6-7.1zM663.3 325.5l32.8-116.9h6.3l32.1 116.9h-71.2zm143.5 492.9H677.2v-.4l132.6-188.9c1.1-1.6 1.7-3.4 1.7-5.4v-36.4c0-5.1-4.2-9.3-9.3-9.3h-204c-5.1 0-9.3 4.2-9.3 9.3v43c0 5.1 4.2 9.3 9.3 9.3h122.6v.4L587.7 828.9a9.35 9.35 0 00-1.7 5.4v36.4c0 5.1 4.2 9.3 9.3 9.3h211.4c5.1 0 9.3-4.2 9.3-9.3v-43a9.2 9.2 0 00-9.2-9.3zM416 702h-76V172c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v530h-76c-6.7 0-10.5 7.8-6.3 13l112 141.9a8 8 0 0012.6 0l112-141.9c4.1-5.2.4-13-6.3-13z"}}]},name:"sort-ascending",theme:"outlined"},_n=function(t,n){return i.createElement(De,te({},t,{ref:n,icon:Mn}))},Fn=i.forwardRef(_n),kn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M839.6 433.8L749 150.5a9.24 9.24 0 00-8.9-6.5h-77.4c-4.1 0-7.6 2.6-8.9 6.5l-91.3 283.3c-.3.9-.5 1.9-.5 2.9 0 5.1 4.2 9.3 9.3 9.3h56.4c4.2 0 7.8-2.8 9-6.8l17.5-61.6h89l17.3 61.5c1.1 4 4.8 6.8 9 6.8h61.2c1 0 1.9-.1 2.8-.4 2.4-.8 4.3-2.4 5.5-4.6 1.1-2.2 1.3-4.7.6-7.1zM663.3 325.5l32.8-116.9h6.3l32.1 116.9h-71.2zm143.5 492.9H677.2v-.4l132.6-188.9c1.1-1.6 1.7-3.4 1.7-5.4v-36.4c0-5.1-4.2-9.3-9.3-9.3h-204c-5.1 0-9.3 4.2-9.3 9.3v43c0 5.1 4.2 9.3 9.3 9.3h122.6v.4L587.7 828.9a9.35 9.35 0 00-1.7 5.4v36.4c0 5.1 4.2 9.3 9.3 9.3h211.4c5.1 0 9.3-4.2 9.3-9.3v-43a9.2 9.2 0 00-9.2-9.3zM310.3 167.1a8 8 0 00-12.6 0L185.7 309c-4.2 5.3-.4 13 6.3 13h76v530c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V322h76c6.7 0 10.5-7.8 6.3-13l-112-141.9z"}}]},name:"sort-descending",theme:"outlined"},Bn=function(t,n){return i.createElement(De,te({},t,{ref:n,icon:kn}))},Vn=i.forwardRef(Bn);const An=({lastUpdate:e,loading:t,onReload:n})=>a.jsxs("div",{className:"flex justify-between items-center mb-4",children:[a.jsxs("h1",{className:"text-3xl font-bold text-gray-800 flex items-center",children:["🍳 Kitchen Dashboard",a.jsxs("span",{className:"ml-3 text-sm text-gray-500 font-normal",children:["Cập nhật: ",e.toLocaleTimeString()]})]}),a.jsx(Zt,{children:a.jsx(me,{icon:a.jsx(Jt,{}),onClick:n,title:"Refresh",loading:t})})]}),{Option:Yn}=tt,{Search:Zn}=en,le={0:{text:"Chờ chế biến",color:"#1890ff",bgColor:"bg-blue-50",icon:"ClockCircleOutlined"},1:{text:"Đang chế biến",color:"#fa8c16",bgColor:"bg-orange-50",icon:"FireOutlined"},2:{text:"Sẵn sàng",color:"#52c41a",bgColor:"bg-green-50",icon:"CheckCircleOutlined"},3:{text:"Đã phục vụ",color:"#d9d9d9",bgColor:"bg-gray-50",icon:"CheckCircleOutlined"}},Ln=({food:e,isLocked:t=!1,onStatusChange:n,currentStatus:r})=>{const[s,o]=i.useState(!1),[h]=Re.useForm(),[d,p]=i.useState(!1),b=()=>{const y="transition-all duration-200 border-l-4 mb-3",c="border-l-blue-500",m=t?"opacity-60 cursor-not-allowed":"";return t?`${y} ${c} bg-white ${m} border-dashed`:`${y} ${c} bg-white hover:shadow-lg`},f=y=>{try{const[c,m]=y.split(" "),[x,N,I]=c.split("/"),[T,q]=m.split(":"),H=new Date(I,N-1,x,T,q),P=Math.floor((new Date-H)/6e4);return Math.max(0,P)}catch{return 0}},E=()=>{switch(r){case 0:return 1;case 1:return 2;default:return null}},S=()=>{switch(r){case 0:return"Chế biến";case 1:return"Hoàn thành";default:return"Completed"}},$=()=>{switch(r){case 0:return"Xác nhận bắt đầu chế biến";case 1:return"Xác nhận hoàn thành món ăn";default:return"Xác nhận thay đổi trạng thái"}},R=()=>{switch(r){case 0:return a.jsx(nt,{style:{color:"#1890ff"}});case 1:return a.jsx(pe,{style:{color:"#52c41a"}});default:return a.jsx(pe,{})}},V=()=>{t||(h.setFieldsValue({quantity:e.total_quantity}),o(!0))},O=()=>{o(!1),h.resetFields()},D=y=>y<15?"green":y<30?"orange":"red",F=async()=>{try{p(!0);const y=await h.validateFields(),c=E();c!==null&&n&&await n(e.name,c,y.quantity),o(!1),h.resetFields()}catch(y){console.error("Validation failed:",y)}finally{p(!1)}};return a.jsxs(a.Fragment,{children:[a.jsx(fe,{size:"small",className:b(),title:a.jsx("div",{className:"flex justify-between items-center",children:a.jsxs("div",{className:"flex items-center space-x-2 flex-wrap",children:[a.jsx("span",{className:"font-semibold text-sm",children:e.name}),t&&a.jsx(We,{color:"orange",size:"small",className:"animate-pulse",children:"🔒 Đang xử lý"})]})}),extra:a.jsx("div",{className:"flex items-center space-x-1 flex-wrap",children:a.jsx(xe,{count:e.total_quantity,style:{backgroundColor:"#52c41a"},title:"Số lượng"})}),children:a.jsx("div",{className:"space-y-2",children:a.jsxs("div",{className:"flex justify-between items-center text-xs",children:[a.jsx("div",{className:"flex items-center text-gray-500",children:(()=>{const y=f(e.order_time);return a.jsxs(We,{color:D(y),children:[y," phút"]})})()}),a.jsx("div",{className:"text-gray-600 flex items-center justify-between text-xs",children:E()!==null&&a.jsxs(me,{size:"small",type:"primary",onClick:V,disabled:t,className:"ml-2",children:[a.jsx(pe,{}),S()]})})]})})}),a.jsx(tn,{title:a.jsxs("div",{className:"flex items-center space-x-2",children:[R(),a.jsx("span",{children:$()})]}),open:s,onCancel:O,footer:[a.jsx(me,{onClick:O,children:"Hủy"},"cancel"),a.jsx(me,{type:"primary",loading:d,onClick:F,children:"Xác nhận"},"confirm")],width:400,centered:!0,children:a.jsxs("div",{className:"py-4",children:[a.jsxs("div",{className:"mb-4 p-3 bg-gray-50 rounded-lg",children:[a.jsxs("div",{className:"flex justify-between items-center mb-2",children:[a.jsx("span",{className:"font-medium text-gray-700",children:"Món ăn:"}),a.jsx("span",{className:"font-semibold",children:e.name})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"font-medium text-gray-700",children:"Tổng số lượng:"}),a.jsx(xe,{count:e.total_quantity,style:{backgroundColor:"#52c41a"}})]})]}),a.jsxs(Re,{form:h,layout:"vertical",children:[a.jsx(Re.Item,{label:"Số lượng cần xử lý",name:"quantity",rules:[{required:!0,message:"Vui lòng nhập số lượng!"},{type:"number",min:1,max:e.total_quantity,message:`Số lượng phải từ 1 đến ${e.total_quantity}!`}],children:a.jsx(it,{min:1,max:e.total_quantity,style:{width:"100%"},placeholder:"Nhập số lượng",addonAfter:"phần"})}),a.jsxs("div",{className:"text-sm text-gray-500",children:["💡 Mặc định sẽ xử lý tất cả ",e.total_quantity," phần"]})]})]})})]})},zn={ClockCircleOutlined:a.jsx(nt,{}),FireOutlined:a.jsx(ln,{}),CheckCircleOutlined:a.jsx(pe,{})},{Option:Jn}=tt,Qe=({status:e,title:t,foods:n,lockedFoods:r=new Set,sortBy:s=null,sortOrder:o="asc",onStatusChange:h})=>{var y,c;const[d,p]=i.useState(null),[b,f]=i.useState("asc"),E=n.reduce((m,x)=>m+x.total_quantity,0),S=n.filter(m=>r.has(m.id)).length,$=d||s,R=d?b:o,V=i.useMemo(()=>$?[...n].sort((m,x)=>{let N,I;switch($){case"table":N=m.tables&&m.tables.length>0?m.tables[0]:"",I=x.tables&&x.tables.length>0?x.tables[0]:"";break;case"name":N=m.name||"",I=x.name||"";break;case"order_time":N=new Date(m.order_time||0),I=new Date(x.order_time||0);break;case"quantity":N=m.quantity||0,I=x.quantity||0;break;case"category":N=m.category||"",I=x.category||"";break;case"order_code":N=m.order_code||"",I=x.order_code||"";break;default:N=new Date(m.order_time||0),I=new Date(x.order_time||0)}if($==="order_time")return R==="asc"?N-I:I-N;if($==="quantity")return R==="asc"?N-I:I-N;{const T=N.localeCompare(I,"vi",{numeric:!0,sensitivity:"base"});return R==="asc"?T:-T}}):[...n].sort((m,x)=>new Date(m.order_time)-new Date(x.order_time)),[n,$,R]),O=()=>{var N;const m="flex-1 min-w-80 rounded-lg p-4 transition-all duration-200 relative",x=((N=le[e])==null?void 0:N.bgColor)||"bg-gray-50";return`${m} ${x} border-2 border-solid border-gray-200`},D=()=>$?`${d?"🔧":"🌐"} ${{table:"Bàn",name:"Tên món"}[$]} ${R==="asc"?"↑":"↓"}`:null,F=()=>{f(m=>m==="asc"?"desc":"asc")};return a.jsxs("div",{className:O(),style:{minHeight:"600px"},children:[a.jsxs("div",{className:"flex items-center justify-between mb-4",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"text-gray-600",children:zn[(y=le[e])==null?void 0:y.icon]}),a.jsx("h3",{className:"font-semibold text-gray-800",children:t}),a.jsx(xe,{count:E,style:{backgroundColor:(c=le[e])==null?void 0:c.color}}),S>0&&a.jsx(xe,{count:`${S} đang xử lý`,style:{backgroundColor:"#fa8c16"},className:"animate-pulse"})]}),a.jsx("div",{className:"flex items-center space-x-1",children:$&&a.jsx(Qt,{title:"Đảo thứ tự sắp xếp",children:a.jsx(me,{size:"small",icon:R==="asc"?a.jsx(Fn,{}):a.jsx(Vn,{}),onClick:F,type:"text",className:"opacity-70 hover:opacity-100",disabled:!d})})})]}),$&&a.jsx("div",{className:"mb-3",children:a.jsxs("div",{className:`text-xs px-2 py-1 rounded inline-flex items-center ${d?"text-blue-600 bg-blue-50 border border-blue-200":"text-gray-500 bg-gray-100"}`,children:[a.jsx("span",{className:"mr-1",children:"📊"}),"Sắp xếp: ",D(),d&&a.jsx("span",{className:"ml-1 text-xs",children:"(Riêng)"}),!d&&s&&a.jsx("span",{className:"ml-1 text-xs",children:"(Chung)"})]})}),a.jsxs("div",{className:"space-y-3",children:[V.map(m=>a.jsx(Ln,{food:m,isLocked:r.has(m.id),onStatusChange:h,currentStatus:e},m.id)),n.length===0&&a.jsxs("div",{className:"text-center text-gray-400 py-8",children:[a.jsx("div",{className:"text-4xl mb-2",children:"📭"}),a.jsx("div",{children:"Không có món ăn"})]})]})]})},er=()=>{const[e,t]=on.useNotification(),[n,r]=i.useState([]),[s,o]=i.useState(!1),[h,d]=i.useState(new Date),[p,b]=i.useState({}),[f,E]=i.useState(""),[S,$]=i.useState(null),[R,V]=i.useState("asc"),[O,D]=i.useState(new Set),[F,y]=i.useState(new Set),c=i.useRef(new Map),m=i.useRef([]),x=i.useRef(!1),[N,I]=i.useState([]),[T,q]=i.useState([]);i.useEffect(()=>{H();const l=setInterval(()=>{O.size===0&&!x.current&&z()},6e4);return()=>clearInterval(l)},[O.size]);const H=async()=>{o(!0);try{await z()}catch{e.error({message:"Lỗi tải dữ liệu"})}finally{o(!1)}},z=async()=>{try{const l=await nn();O.size===0&&!x.current&&(r((l==null?void 0:l.foods)||[]),d(new Date),I((l==null?void 0:l.waitingFood)||[]),q((l==null?void 0:l.preparingFood)||[]),(l==null?void 0:l.waitingFood.length)>0&&P([...l==null?void 0:l.waitingFood,...l==null?void 0:l.preparingFood]))}catch(l){console.error("Error loading foods:",l)}},P=l=>{const w=l.map(C=>`${C.name} ${C.total_quantity} suất`).join(", ");rn(w)};i.useCallback(async()=>{if(!(x.current||m.current.length===0)){for(x.current=!0;m.current.length>0;){const l=m.current.shift();await Z(l)}x.current=!1}},[O.size]);const Z=async({foodId:l,food:w,newStatus:C,originalStatus:k})=>{var U;const A=`${l}-${Date.now()}`;try{if(!(w!=null&&w.id))throw new Error(`Invalid food object for ${l}`);D(_=>new Set(_).add(A)),y(_=>new Set(_).add(l));const B={...w};c.current.set(l,B),await an(l,C),r(_=>_.map(G=>G.id===l?{...G,status:C}:G)),e.success({message:"Cập nhật thành công",description:`${B.name} đã được chuyển sang ${(U=le[C])==null?void 0:U.text}`,placement:"topRight",duration:2}),c.current.delete(l)}catch{const _=c.current.get(l);_&&(r(G=>G.map(re=>re.id===l?_:re)),c.current.delete(l)),e.error({message:"❌ Cập nhật thất bại",description:"Đã khôi phục trạng thái cũ",placement:"topRight",duration:4})}finally{D(B=>{const _=new Set(B);return _.delete(A),_}),y(B=>{const _=new Set(B);return _.delete(l),_}),d(new Date)}},ne=i.useCallback(async(l,w,C)=>{(await sn(l,C,w)).success?e.success({message:"✅ Cập nhật thành công",placement:"topRight",duration:4}):e.error({message:"❌ Cập nhật thất bại",placement:"topRight",duration:4}),z()},[n]);i.useMemo(()=>({caregories:[...new Set(n.flatMap(l=>l.categories||[]))].sort(),table:[...new Set(n.flatMap(l=>l.tables||[]))].sort(),order_code:[...new Set(n.map(l=>l.order_code))].sort(),name:[...new Set(n.map(l=>l.name))].sort()}),[n]);const Y=i.useMemo(()=>{let l=n;return Object.entries(p).forEach(([w,C])=>{C&&(l=l.filter(k=>w==="table"?k.tables&&k.tables.includes(C):w==="caregories"?k.categories&&k.categories.includes(C):k[w]===C))}),f&&(l=l.filter(w=>w.name.toLowerCase().includes(f.toLowerCase())||w.caregories.toLowerCase().includes(f.toLowerCase())||w.tables&&w.tables.some(C=>C.toLowerCase().includes(f.toLowerCase()))||w.order_code.toLowerCase().includes(f.toLowerCase()))),S&&(l=[...l].sort((w,C)=>{let k,A;switch(S){case"table":k=w.tables&&w.tables.length>0?w.tables[0]:"",A=C.tables&&C.tables.length>0?C.tables[0]:"";break;case"name":k=w.name||"",A=C.name||"";break;default:return 0}if(S==="order_time")return R==="asc"?k-A:A-k;if(S==="quantity")return R==="asc"?k-A:A-k;{const U=k.localeCompare(A,"vi",{numeric:!0});return R==="asc"?U:-U}})),l},[n,p,f,S,R]);return a.jsxs("div",{className:"w-100 flex justify-center",children:[t,a.jsx("div",{className:"p-6 bg-gray-100 min-h-screen container",children:a.jsxs(cn,{spinning:s,children:[a.jsx("div",{className:"mb-6",children:a.jsx(An,{lastUpdate:h,loading:s||O.size>0,onReload:z,pendingCount:O.size})}),a.jsxs("div",{className:"mb-4 grid grid-cols-1 md:grid-cols-4 gap-4",children:[a.jsxs(fe,{size:"small",className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:Y.filter(l=>(l.status!==void 0?l.status:0)===0).length}),a.jsx("div",{className:"text-gray-500",children:"Chờ chế biến"})]}),a.jsxs(fe,{size:"small",className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-orange-600",children:Y.filter(l=>(l.status!==void 0?l.status:0)===1).length}),a.jsx("div",{className:"text-gray-500",children:"Đang chế biến"})]}),a.jsxs(fe,{size:"small",className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-green-600",children:Y.filter(l=>(l.status!==void 0?l.status:0)===2).length}),a.jsx("div",{className:"text-gray-500",children:"Sẵn sàng"})]}),a.jsxs(fe,{size:"small",className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-purple-600",children:Y.reduce((l,w)=>l+w.quantity,0)}),a.jsx("div",{className:"text-gray-500",children:"Tổng số phần"})]})]}),a.jsxs("div",{className:"flex space-x-4 pb-4",children:[a.jsx(Qe,{status:0,title:le[0].text,foods:N||[],lockedFoods:F,sortBy:S,sortOrder:R,onStatusChange:ne}),a.jsx(Qe,{status:1,title:le[1].text,foods:T||[],lockedFoods:F,sortBy:S,sortOrder:R,onStatusChange:ne})]})]})})]})};export{er as K};
