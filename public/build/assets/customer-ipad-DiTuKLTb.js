import{r as a,a3 as Q,a4 as $,j as e,aE as W,aF as S,aB as v,aD as w,a2 as D,c as M}from"./index-CHvake0r.js";import{R as y,L as C,g as L,a as H,I as F,b as K,c as N,d as P}from"./formatter-Blxj4gZL.js";import{T as V}from"./index-Bbq37T-v.js";import{s as X}from"./index-CIWcONm8.js";import{B as R,R as G}from"./FireOutlined-B7wZholJ.js";import{S as _}from"./EyeOutlined-D5ZQtqsR.js";import{R as g}from"./CheckCircleOutlined-DMtaSJBb.js";import{D as J}from"./index-DIqWL9-0.js";import{R as z}from"./QrcodeOutlined-BwfiqOGi.js";import{R as I}from"./ClockCircleOutlined-B61igLBq.js";import"./index-D_dKmeqG.js";import"./EditOutlined-t_UPdXXr.js";var U={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M744 62H280c-35.3 0-64 28.7-64 64v768c0 35.3 28.7 64 64 64h464c35.3 0 64-28.7 64-64V126c0-35.3-28.7-64-64-64zm-8 824H288V134h448v752zM472 784a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"mobile",theme:"outlined"},Y=function(m,r){return a.createElement(Q,$({},m,{ref:r,icon:U}))},B=a.forwardRef(Y);const{Title:d,Text:s,Paragraph:k}=V,Z=()=>{const[x,m]=X.useMessage(),[r,p]=a.useState(()=>window.orderData||{id:1,order_number:"ORD-001",status:1,total_amount:25e4,formatted_total_amount:"250,000đ",created_at:new Date().toISOString(),qr_code_url:null,order_items:[{food_name:"Phở bò tái",quantity:2,unit_price:8e4,notes:"Ít hành"},{food_name:"Cà phê sữa đá",quantity:1,unit_price:25e3,notes:""},{food_name:"Bánh mì thịt nướng",quantity:1,unit_price:35e3,notes:"Không rau thơm"}]}),[E,j]=a.useState(!1),[c,u]=a.useState(!1);a.useEffect(()=>{var o;const n=(o=r==null?void 0:r.order_items)==null?void 0:o.every(l=>l.status!==3);u(n)},[r]);const i=n=>({0:{text:"Chờ xác nhận",color:"warning",icon:e.jsx(I,{}),badgeStatus:"warning"},1:{text:"Đã xác nhận",color:"processing",icon:e.jsx(P,{}),badgeStatus:"processing"},2:{text:"Đang chuẩn bị",color:"processing",icon:e.jsx(G,{}),badgeStatus:"processing"},3:{text:"Sẵn sàng",color:"success",icon:e.jsx(N,{}),badgeStatus:"success"},4:{text:"Hoàn thành",color:"success",icon:e.jsx(g,{}),badgeStatus:"success"},5:{text:"Đã hủy",color:"error",icon:e.jsx(K,{}),badgeStatus:"error"}})[n]||{text:"Không xác định",color:"default",icon:e.jsx(I,{}),badgeStatus:"default"},O=()=>{const n={width:window.innerWidth<768?200:240,height:window.innerWidth<768?200:240,backgroundColor:"#000",borderRadius:16,padding:8,boxShadow:"0 20px 40px rgba(0,0,0,0.3)"},o={width:"100%",height:"100%",backgroundColor:"#fff",borderRadius:12,display:"flex",alignItems:"center",justifyContent:"center"};return r.qr_code_url?e.jsx("a",{href:`/orders/${r.id}/print`,target:"_blank",rel:"noopener noreferrer",style:{textDecoration:"none"},children:e.jsx("div",{style:n,children:e.jsx("div",{style:o,children:e.jsx(F,{src:r.qr_code_url,alt:"QR Code",style:{width:window.innerWidth<768?180:220,height:window.innerWidth<768?180:220,borderRadius:8},preview:!1,fallback:e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(z,{style:{fontSize:48,marginBottom:8,color:"#ccc"}}),e.jsx("br",{}),e.jsx(s,{type:"secondary",style:{fontSize:12},children:"QR Code"}),e.jsx("br",{}),e.jsx(s,{type:"secondary",style:{fontSize:12},children:"Scan to Order"})]})})})})}):e.jsx("div",{style:n,children:e.jsx("div",{style:o,children:e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(z,{style:{fontSize:48,marginBottom:8,color:"#ccc"}}),e.jsx("br",{}),e.jsx(s,{type:"secondary",style:{fontSize:12},children:"QR Code"}),e.jsx("br",{}),e.jsx(s,{type:"secondary",style:{fontSize:12},children:"Scan to Order"})]})})})},q=async()=>{var n;j(!0);try{const o=await fetch(`/api/orders/${r.id}/confirm`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.content)||""}});if(o.ok){const l=await o.json();u(!0),p(h=>({...h,status:1})),x.success("Đơn hàng đã được xác nhận!")}else throw new Error("Failed to confirm order")}catch(o){console.error("Error confirming order:",o),x.error("Có lỗi xảy ra khi xác nhận đơn hàng")}finally{j(!1)}},b=async()=>{var n;try{const o=await fetch(`/api/orders/${r.id}/items`,{headers:{"X-CSRF-TOKEN":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.content)||""}});if(o.ok){const l=await o.json();let h=!1;l.order_items.forEach(f=>{f.status===3&&(h=!0)}),h&&u(!1),p(f=>({...f,order_items:l.order_items,total_amount:l.total_amount,formatted_total_amount:l.formatted_total_amount,status:l.status}))}}catch(o){console.error("Error fetching order items:",o)}};a.useEffect(()=>{b();const n=setInterval(b,5e3);return()=>{clearInterval(n)}},[r.id]);const[t,A]=a.useState(window.innerWidth<768);return a.useEffect(()=>{const n=()=>{A(window.innerWidth<768)};return window.addEventListener("resize",n),()=>window.removeEventListener("resize",n)},[]),e.jsxs("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)"},children:[m,e.jsxs(W,{style:{minHeight:"100vh"},children:[e.jsx(S,{xs:24,md:12,lg:10,xl:8,children:e.jsxs("div",{style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",minHeight:"100vh",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:t?"32px 24px":"48px 32px",color:"white"},children:[t&&e.jsxs("div",{style:{textAlign:"center",marginBottom:24},children:[e.jsx(d,{level:2,style:{color:"white",marginBottom:8},children:"Chào mừng quý khách!"}),e.jsx(s,{style:{color:"rgba(255,255,255,0.8)",fontSize:16},children:"Quét mã QR để gọi thêm món"})]}),e.jsx(v,{style:{marginBottom:t?24:32,borderRadius:24,boxShadow:"0 20px 40px rgba(0,0,0,0.1)",backgroundColor:"rgba(255,255,255,0.95)",backdropFilter:"blur(10px)",border:"none"},children:e.jsxs("div",{style:{textAlign:"center",padding:t?16:24},children:[e.jsx("div",{style:{marginBottom:t?16:24,display:"flex",justifyContent:"center"},children:e.jsx(O,{})}),e.jsx(d,{level:t?3:2,style:{marginBottom:8},children:r.order_number}),e.jsx(k,{style:{color:"#6b7280",marginBottom:t?16:24,fontSize:t?14:16},children:"Quét mã để gọi thêm món"})]})}),t&&e.jsx("div",{style:{marginBottom:24},children:e.jsx(R,{status:i(r.status).badgeStatus,text:e.jsx(w,{color:i(r.status).color,icon:i(r.status).icon,style:{border:"none",color:"white",backgroundColor:"rgba(255,255,255,0.2)"},children:i(r.status).text})})}),!t&&e.jsxs("div",{style:{textAlign:"center",maxWidth:400},children:[e.jsx(d,{level:1,style:{color:"white",marginBottom:16,fontSize:window.innerWidth>1200?48:36},children:"Chào mừng quý khách!"}),e.jsx(k,{style:{color:"rgba(255,255,255,0.9)",marginBottom:32,fontSize:18,lineHeight:1.6},children:"Quét mã QR để gọi thêm món hoặc theo dõi trạng thái đơn hàng của bạn"}),e.jsxs(_,{size:"large",children:[e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx("div",{style:{width:48,height:48,backgroundColor:"rgba(255,255,255,0.2)",borderRadius:16,display:"flex",alignItems:"center",justifyContent:"center",marginBottom:12,margin:"0 auto 12px auto"},children:e.jsx(B,{style:{fontSize:24,color:"white"}})}),e.jsx(s,{style:{color:"rgba(255,255,255,0.8)",fontSize:14},children:"Quét QR"})]}),e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx("div",{style:{width:48,height:48,backgroundColor:"rgba(255,255,255,0.2)",borderRadius:16,display:"flex",alignItems:"center",justifyContent:"center",marginBottom:12,margin:"0 auto 12px auto"},children:e.jsx(y,{style:{fontSize:24,color:"white"}})}),e.jsx(s,{style:{color:"rgba(255,255,255,0.8)",fontSize:14},children:"Gọi thêm món"})]}),e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx("div",{style:{width:48,height:48,backgroundColor:"rgba(255,255,255,0.2)",borderRadius:16,display:"flex",alignItems:"center",justifyContent:"center",marginBottom:12,margin:"0 auto 12px auto"},children:e.jsx(g,{style:{fontSize:24,color:"white"}})}),e.jsx(s,{style:{color:"rgba(255,255,255,0.8)",fontSize:14},children:"Theo dõi"})]})]})]}),t&&e.jsxs(_,{size:"large",style:{marginTop:24},children:[e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(B,{style:{fontSize:24,marginBottom:4,display:"block"}}),e.jsx(s,{style:{color:"rgba(255,255,255,0.8)",fontSize:12},children:"Quét QR"})]}),e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(y,{style:{fontSize:24,marginBottom:4,display:"block"}}),e.jsx(s,{style:{color:"rgba(255,255,255,0.8)",fontSize:12},children:"Gọi món"})]}),e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(g,{style:{fontSize:24,marginBottom:4,display:"block"}}),e.jsx(s,{style:{color:"rgba(255,255,255,0.8)",fontSize:12},children:"Theo dõi"})]})]})]})}),e.jsx(S,{xs:24,md:12,lg:14,xl:16,children:e.jsx("div",{style:{minHeight:"100vh",overflow:"auto",padding:t?24:48},children:e.jsxs("div",{style:{maxWidth:800,margin:"0 auto"},children:[e.jsxs("div",{style:{display:"flex",flexDirection:t?"column":"row",justifyContent:"space-between",alignItems:t?"flex-start":"center",marginBottom:t?24:32},children:[e.jsx(d,{level:2,style:{marginBottom:t?8:0},children:"Chi tiết đơn hàng"}),!t&&e.jsx(R,{status:i(r.status).badgeStatus,text:e.jsx(w,{color:i(r.status).color,icon:i(r.status).icon,style:{padding:"8px 16px",fontSize:14,fontWeight:500},children:i(r.status).text})})]}),e.jsxs(v,{style:{boxShadow:"0 10px 30px rgba(0,0,0,0.1)",border:"none",borderRadius:t?16:24,marginBottom:t?24:32},children:[e.jsxs("div",{style:{display:"flex",flexDirection:t?"column":"row",justifyContent:"space-between",alignItems:"flex-start",marginBottom:t?16:24},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:12,marginBottom:t?12:0},children:[e.jsx("div",{style:{width:t?40:48,height:t?40:48,backgroundColor:"#f3e8ff",borderRadius:t?12:16,display:"flex",alignItems:"center",justifyContent:"center"},children:i(r.status).icon}),e.jsxs("div",{children:[e.jsx(d,{level:t?4:3,style:{marginBottom:4},children:r.order_number}),e.jsxs(s,{style:{color:"#6b7280",fontSize:t?12:14},children:["Đặt lúc ",new Date(r.created_at).toLocaleTimeString("vi-VN",{hour:"2-digit",minute:"2-digit"})]})]})]}),e.jsx("div",{style:{textAlign:t?"left":"right"},children:e.jsx(d,{level:3,style:{marginBottom:0,color:"#7c3aed"},children:r.formatted_total_amount})})]}),e.jsx(J,{style:{margin:t?"16px 0":"24px 0"}}),r.order_items&&r.order_items.length>0?e.jsx(C,{dataSource:r.order_items,renderItem:n=>e.jsx(C.Item,{style:{padding:t?"12px 0":"16px 0",borderRadius:12,transition:"all 0.3s ease",cursor:"default"},onMouseEnter:o=>{t||(o.currentTarget.style.backgroundColor="#f9fafb",o.currentTarget.style.padding="16px",o.currentTarget.style.margin="0 -16px")},onMouseLeave:o=>{t||(o.currentTarget.style.backgroundColor="transparent",o.currentTarget.style.padding="16px 0",o.currentTarget.style.margin="0")},children:e.jsxs("div",{style:{width:"100%"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:4},children:[e.jsx(d,{level:5,style:{marginBottom:0,fontSize:t?14:16},children:n.food_name}),e.jsxs(s,{strong:!0,style:{color:"#7c3aed",fontSize:t?16:18},children:[(n.quantity*n.unit_price).toLocaleString(),"đ"]})]}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(s,{className:`rounded-full px-2 py-1 text-xs ${H(n.status)}`,children:L(n.status)||"Chờ xác nhận"}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8,marginLeft:"auto"},children:[e.jsxs(s,{style:{color:"#6b7280",fontSize:t?12:14},children:[n.unit_price.toLocaleString(),"đ"]}),e.jsxs(s,{style:{backgroundColor:"#f3e8ff",color:"#7c3aed",padding:t?"2px 8px":"4px 12px",borderRadius:20,fontSize:t?12:14,fontWeight:500},children:["x",n.quantity]})]})]})]})})}):e.jsxs("div",{style:{textAlign:"center",padding:t?32:48},children:[e.jsx(y,{style:{fontSize:t?48:64,color:"#d1d5db",marginBottom:16}}),e.jsx(s,{style:{color:"#9ca3af",fontSize:t?16:18},children:"Chưa có món nào được chọn"})]})]}),e.jsx(D,{type:"primary",size:"large",onClick:q,disabled:c,loading:E,icon:c?e.jsx(g,{}):null,style:{width:t?"100%":"auto",height:t?48:56,borderRadius:t?12:16,fontSize:t?16:18,fontWeight:500,padding:t?"0 32px":"0 48px",boxShadow:"0 10px 30px rgba(16, 185, 129, 0.3)",backgroundColor:c?"#d9d9d9":"#10b981",borderColor:c?"#d9d9d9":"#10b981"},children:c?"Đã xác nhận":"Xác nhận đơn hàng"})]})})})]})]})},T=document.getElementById("ipad-interface-root");T&&M.createRoot(T).render(e.jsx(Z,{}));
