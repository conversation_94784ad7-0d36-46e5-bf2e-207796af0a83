import{t as it,r as n,C as Ye,o as lt,b1 as ot,a as Xe,$ as dt,a3 as H,a4 as I,b2 as ut,j as e,aD as ye,aB as A,a2 as S,aC as be,b3 as Ge,b4 as ht,c as mt,b0 as xt}from"./index-CHvake0r.js";import{f as Ce,e as ft,h as gt,i as O,b as pt,d as ve,L as _e,I as yt,r as le,R as Qe,c as vt}from"./formatter-B1SRHu05.js";import{T as ae}from"./index-Bbq37T-v.js";import{s as je}from"./index-CIWcONm8.js";import{M as re,a as ne,F as bt}from"./index-Cqw7XJJF.js";import{S as Ue}from"./index-D_dKmeqG.js";import{E as se,S as we,a as jt}from"./EyeOutlined-D5ZQtqsR.js";import{a as Je,u as We,L as Nt,S as Ct,R as Ze}from"./DeleteOutlined-C56jyccF.js";import{D as et}from"./index-DIqWL9-0.js";import{R as Te}from"./ClockCircleOutlined-B61igLBq.js";import{R as _t}from"./CheckCircleOutlined-DMtaSJBb.js";import{R as wt,a as Tt,b as $t}from"./RocketOutlined-BtbtbzoE.js";import{R as Ot,B as te}from"./FireOutlined-B7wZholJ.js";import"./EditOutlined-t_UPdXXr.js";import"./ActionButton-BdVp3AS3.js";function St(t,a,s){return typeof s=="boolean"?s:t.length?!0:it(a).some(i=>i.type===Je)}var tt=function(t,a){var s={};for(var c in t)Object.prototype.hasOwnProperty.call(t,c)&&a.indexOf(c)<0&&(s[c]=t[c]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,c=Object.getOwnPropertySymbols(t);i<c.length;i++)a.indexOf(c[i])<0&&Object.prototype.propertyIsEnumerable.call(t,c[i])&&(s[c[i]]=t[c[i]]);return s};function ce({suffixCls:t,tagName:a,displayName:s}){return c=>n.forwardRef((l,o)=>n.createElement(c,Object.assign({ref:o,suffixCls:t,tagName:a},l)))}const Ne=n.forwardRef((t,a)=>{const{prefixCls:s,suffixCls:c,className:i,tagName:l}=t,o=tt(t,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:g}=n.useContext(Ye),x=g("layout",s),[b,N,j]=We(x),E=c?`${x}-${c}`:x;return b(n.createElement(l,Object.assign({className:Xe(s||E,i,N,j),ref:a},o)))}),kt=n.forwardRef((t,a)=>{const{direction:s}=n.useContext(Ye),[c,i]=n.useState([]),{prefixCls:l,className:o,rootClassName:g,children:x,hasSider:b,tagName:N,style:j}=t,E=tt(t,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),q=lt(E,["suffixCls"]),{getPrefixCls:T,className:D,style:C}=ot("layout"),v=T("layout",l),$=St(c,x,b),[k,p,V]=We(v),_=Xe(v,{[`${v}-has-sider`]:$,[`${v}-rtl`]:s==="rtl"},D,o,g,p,V),r=n.useMemo(()=>({siderHook:{addSider:u=>{i(y=>[].concat(dt(y),[u]))},removeSider:u=>{i(y=>y.filter(L=>L!==u))}}}),[]);return k(n.createElement(Nt.Provider,{value:r},n.createElement(N,Object.assign({ref:a,className:_,style:Object.assign(Object.assign({},C),j)},q),x)))}),Vt=ce({tagName:"div",displayName:"Layout"})(kt),Rt=ce({suffixCls:"header",tagName:"header",displayName:"Header"})(Ne),Pt=ce({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(Ne),Mt=ce({suffixCls:"content",tagName:"main",displayName:"Content"})(Ne),B=Vt;B.Header=Rt;B.Footer=Pt;B.Content=Mt;B.Sider=Je;B._InternalSiderContext=Ct;var qt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"}}]},name:"appstore",theme:"outlined"},zt=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:qt}))},oe=n.forwardRef(zt),Ht={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"},It=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:Ht}))},Et=n.forwardRef(It),Dt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"},Lt=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:Dt}))},rt=n.forwardRef(Lt),Ft={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9zM512 814.8S156 586.7 156 385.5C156 283.6 240.3 201 344.3 201c73.1 0 136.5 40.8 167.7 100.4C543.2 241.8 606.6 201 679.7 201c104 0 188.3 82.6 188.3 184.5 0 201.2-356 429.3-356 429.3z"}}]},name:"heart",theme:"outlined"},At=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:Ft}))},$e=n.forwardRef(At),Bt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M536.1 273H488c-4.4 0-8 3.6-8 8v275.3c0 2.6 1.2 5 3.3 6.5l165.3 120.7c3.6 2.6 8.6 1.9 11.2-1.7l28.6-39c2.7-3.7 1.9-8.7-1.7-11.2L544.1 528.5V281c0-4.4-3.6-8-8-8zm219.8 75.2l156.8 38.3c5 1.2 9.9-2.6 9.9-7.7l.8-161.5c0-6.7-7.7-10.5-12.9-6.3L752.9 334.1a8 8 0 003 14.1zm167.7 301.1l-56.7-19.5a8 8 0 00-10.1 4.8c-1.9 5.1-3.9 10.1-6 15.1-17.8 42.1-43.3 80-75.9 112.5a353 353 0 01-112.5 75.9 352.18 352.18 0 01-137.7 27.8c-47.8 0-94.1-9.3-137.7-27.8a353 353 0 01-112.5-75.9c-32.5-32.5-58-70.4-75.9-112.5A353.44 353.44 0 01171 512c0-47.8 9.3-94.2 27.8-137.8 17.8-42.1 43.3-80 75.9-112.5a353 353 0 01112.5-75.9C430.6 167.3 477 158 524.8 158s94.1 9.3 137.7 27.8A353 353 0 01775 261.7c10.2 10.3 19.8 21 28.6 32.3l59.8-46.8C784.7 146.6 662.2 81.9 524.6 82 285 82.1 92.6 276.7 95 516.4 97.4 751.9 288.9 942 524.8 942c185.5 0 343.5-117.6 403.7-282.3 1.5-4.2-.7-8.9-4.9-10.4z"}}]},name:"history",theme:"outlined"},Kt=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:Bt}))},Yt=n.forwardRef(Kt),Xt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},Gt=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:Xt}))},Qt=n.forwardRef(Gt),Ut={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"},Jt=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:Ut}))},st=n.forwardRef(Jt),Wt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},Zt=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:Wt}))},Oe=n.forwardRef(Zt),er={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"},tr=function(a,s){return n.createElement(H,I({},a,{ref:s,icon:er}))},rr=n.forwardRef(tr),G={},de={exports:{}},Se;function ie(){return Se||(Se=1,function(t){function a(s){return s&&s.__esModule?s:{default:s}}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports}(de)),de.exports}var Q={},ke;function sr(){if(ke)return Q;ke=1,Object.defineProperty(Q,"__esModule",{value:!0}),Q.default=void 0;var t={items_per_page:"/ trang",jump_to:"Đến",jump_to_confirm:"xác nhận",page:"Trang",prev_page:"Trang Trước",next_page:"Trang Kế",prev_5:"Về 5 Trang Trước",next_5:"Đến 5 Trang Kế",prev_3:"Về 3 Trang Trước",next_3:"Đến 3 Trang Kế",page_size:"kích thước trang"};return Q.default=t,Q}var U={},J={},W={},ue={exports:{}},he={exports:{}},me={exports:{}},xe={exports:{}},Ve;function at(){return Ve||(Ve=1,function(t){function a(s){"@babel/helpers - typeof";return t.exports=a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(c){return typeof c}:function(c){return c&&typeof Symbol=="function"&&c.constructor===Symbol&&c!==Symbol.prototype?"symbol":typeof c},t.exports.__esModule=!0,t.exports.default=t.exports,a(s)}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports}(xe)),xe.exports}var fe={exports:{}},Re;function ar(){return Re||(Re=1,function(t){var a=at().default;function s(c,i){if(a(c)!="object"||!c)return c;var l=c[Symbol.toPrimitive];if(l!==void 0){var o=l.call(c,i||"default");if(a(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(c)}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}(fe)),fe.exports}var Pe;function nr(){return Pe||(Pe=1,function(t){var a=at().default,s=ar();function c(i){var l=s(i,"string");return a(l)=="symbol"?l:l+""}t.exports=c,t.exports.__esModule=!0,t.exports.default=t.exports}(me)),me.exports}var Me;function cr(){return Me||(Me=1,function(t){var a=nr();function s(c,i,l){return(i=a(i))in c?Object.defineProperty(c,i,{value:l,enumerable:!0,configurable:!0,writable:!0}):c[i]=l,c}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}(he)),he.exports}var qe;function ir(){return qe||(qe=1,function(t){var a=cr();function s(i,l){var o=Object.keys(i);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(i);l&&(g=g.filter(function(x){return Object.getOwnPropertyDescriptor(i,x).enumerable})),o.push.apply(o,g)}return o}function c(i){for(var l=1;l<arguments.length;l++){var o=arguments[l]!=null?arguments[l]:{};l%2?s(Object(o),!0).forEach(function(g){a(i,g,o[g])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(o)):s(Object(o)).forEach(function(g){Object.defineProperty(i,g,Object.getOwnPropertyDescriptor(o,g))})}return i}t.exports=c,t.exports.__esModule=!0,t.exports.default=t.exports}(ue)),ue.exports}var Z={},ze;function lr(){return ze||(ze=1,Object.defineProperty(Z,"__esModule",{value:!0}),Z.commonLocale=void 0,Z.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),Z}var He;function or(){if(He)return W;He=1;var t=ie().default;Object.defineProperty(W,"__esModule",{value:!0}),W.default=void 0;var a=t(ir()),s=lr(),c=(0,a.default)((0,a.default)({},s.commonLocale),{},{locale:"vi_VN",today:"Hôm nay",now:"Bây giờ",backToToday:"Trở về hôm nay",ok:"OK",clear:"Xóa",week:"Tuần",month:"Tháng",year:"Năm",timeSelect:"Chọn thời gian",dateSelect:"Chọn ngày",weekSelect:"Chọn tuần",monthSelect:"Chọn tháng",yearSelect:"Chọn năm",decadeSelect:"Chọn thập kỷ",dateFormat:"D/M/YYYY",dateTimeFormat:"D/M/YYYY HH:mm:ss",previousMonth:"Tháng trước (PageUp)",nextMonth:"Tháng sau (PageDown)",previousYear:"Năm trước (Control + left)",nextYear:"Năm sau (Control + right)",previousDecade:"Thập kỷ trước",nextDecade:"Thập kỷ sau",previousCentury:"Thế kỷ trước",nextCentury:"Thế kỷ sau"});return W.default=c,W}var ee={},Ie;function nt(){if(Ie)return ee;Ie=1,Object.defineProperty(ee,"__esModule",{value:!0}),ee.default=void 0;const t={placeholder:"Chọn thời gian",rangePlaceholder:["Bắt đầu","Kết thúc"]};return ee.default=t,ee}var Ee;function ct(){if(Ee)return J;Ee=1;var t=ie().default;Object.defineProperty(J,"__esModule",{value:!0}),J.default=void 0;var a=t(or()),s=t(nt());const c={lang:Object.assign({placeholder:"Chọn thời điểm",yearPlaceholder:"Chọn năm",quarterPlaceholder:"Chọn quý",monthPlaceholder:"Chọn tháng",weekPlaceholder:"Chọn tuần",rangePlaceholder:["Ngày bắt đầu","Ngày kết thúc"],rangeYearPlaceholder:["Năm bắt đầu","Năm kết thúc"],rangeQuarterPlaceholder:["Quý bắt đầu","Quý kết thúc"],rangeMonthPlaceholder:["Tháng bắt đầu","Tháng kết thúc"],rangeWeekPlaceholder:["Tuần bắt đầu","Tuần kết thúc"]},a.default),timePickerLocale:Object.assign({},s.default)};return J.default=c,J}var De;function dr(){if(De)return U;De=1;var t=ie().default;Object.defineProperty(U,"__esModule",{value:!0}),U.default=void 0;var a=t(ct());return U.default=a.default,U}var Le;function ur(){if(Le)return G;Le=1;var t=ie().default;Object.defineProperty(G,"__esModule",{value:!0}),G.default=void 0;var a=t(sr()),s=t(dr()),c=t(ct()),i=t(nt());const l="${label} không phải kiểu ${type} hợp lệ",o={locale:"vi",Pagination:a.default,DatePicker:c.default,TimePicker:i.default,Calendar:s.default,global:{placeholder:"Vui lòng chọn",close:"Đóng"},Table:{filterTitle:"Bộ lọc",filterConfirm:"Đồng ý",filterReset:"Bỏ lọc",filterEmptyText:"Không có bộ lọc",filterCheckAll:"Chọn tất cả",filterSearchPlaceholder:"Tìm kiếm bộ lọc",emptyText:"Trống",selectAll:"Chọn tất cả",selectInvert:"Chọn ngược lại",selectNone:"Bỏ chọn tất cả",selectionAll:"Chọn tất cả",sortTitle:"Sắp xếp",expand:"Mở rộng dòng",collapse:"Thu gọn dòng",triggerDesc:"Nhấp để sắp xếp giảm dần",triggerAsc:"Nhấp để sắp xếp tăng dần",cancelSort:"Nhấp để hủy sắp xếp"},Tour:{Next:"Tiếp",Previous:"Trước",Finish:"Hoàn thành"},Modal:{okText:"Đồng ý",cancelText:"Hủy",justOkText:"OK"},Popconfirm:{okText:"Đồng ý",cancelText:"Hủy"},Transfer:{titles:["",""],searchPlaceholder:"Tìm ở đây",itemUnit:"mục",itemsUnit:"mục",remove:"Gỡ bỏ",selectCurrent:"Chọn trang hiện tại",removeCurrent:"Gỡ bỏ trang hiện tại",selectAll:"Chọn tất cả",removeAll:"Gỡ bỏ tất cả",selectInvert:"Đảo ngược trang hiện tại"},Upload:{uploading:"Đang tải lên...",removeFile:"Gỡ bỏ tập tin",uploadError:"Lỗi tải lên",previewFile:"Xem trước tập tin",downloadFile:"Tải tập tin"},Empty:{description:"Trống"},Icon:{icon:"icon"},Text:{edit:"Chỉnh sửa",copy:"Sao chép",copied:"Đã sao chép",expand:"Mở rộng"},Form:{optional:"(Tùy chọn)",defaultValidateMessages:{default:"${label} không đáp ứng điều kiện quy định",required:"Hãy nhập thông tin cho trường ${label}",enum:"${label} phải có giá trị nằm trong tập [${enum}]",whitespace:"${label} không được chứa khoảng trắng",date:{format:"${label} sai định dạng ngày tháng",parse:"Không thể chuyển ${label} sang kiểu Ngày tháng",invalid:"${label} không phải giá trị Ngày tháng hợp lệ"},types:{string:l,method:l,array:l,object:l,number:l,date:l,boolean:l,integer:l,float:l,regexp:l,email:l,url:l,hex:l},string:{len:"${label} phải dài đúng ${len} ký tự",min:"Độ dài tối thiểu trường ${label} là ${min} ký tự",max:"Độ dài tối đa trường ${label} là ${max} ký tự",range:"Độ dài trường ${label} phải từ ${min} đến ${max} ký tự"},number:{len:"${label} phải bằng ${len}",min:"${label} phải lớn hơn hoặc bằng ${min}",max:"${label} phải nhỏ hơn hoặc bằng ${max}",range:"${label} phải nằm trong khoảng ${min}-${max}"},array:{len:"Mảng ${label} phải có ${len} phần tử ",min:"Mảng ${label} phải chứa tối thiểu ${min} phần tử ",max:"Mảng ${label} phải chứa tối đa ${max} phần tử ",range:"Mảng ${label} phải chứa từ ${min}-${max} phần tử"},pattern:{mismatch:"${label} không thỏa mãn mẫu kiểm tra ${pattern}"}}},Image:{preview:"Xem trước"},QRCode:{expired:"Mã QR hết hạn",refresh:"Làm mới"}};return G.default=o,G}var ge,Fe;function hr(){return Fe||(Fe=1,ge=ur()),ge}var mr=hr();const xr=ut(mr),{Title:Ae,Text:w}=ae,fr=({orderId:t,onOrderCountChange:a,activeTab:s,handleRequestPayment:c,paymentLoading:i})=>{const l=d=>{const m={0:e.jsx(Te,{}),1:e.jsx(ve,{}),2:e.jsx(rr,{spin:!0}),3:e.jsx(wt,{}),4:e.jsx(_t,{}),5:e.jsx(pt,{}),default:e.jsx(Te,{})};return m[d]||m.default},[o,g]=n.useState(null),[x,b]=n.useState([]),[N,j]=n.useState(!0),[E,q]=n.useState(null),[T,D]=n.useState(0),[C,v]=je.useMessage(),[$,k]=re.useModal();n.useEffect(()=>{s==="history"&&p()},[t,s]);const p=async()=>{var d,m,R;try{j(!0);const z=await(await fetch(`/api/orders/current?since=${t}`)).json();z.success&&(g(z.orders),b(((d=z.orders)==null?void 0:d.order_items)||[]),a&&a(((R=(m=z.orders)==null?void 0:m.order_items)==null?void 0:R.length)||0))}catch(F){console.error("Error fetching current order:",F)}finally{j(!1)}},V=async(d,m)=>{var R;if(m<1){_(d);return}try{(await(await fetch(`/api/orders/items/${d}`,{method:"PATCH",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":(R=document.querySelector('meta[name="csrf-token"]'))==null?void 0:R.content},body:JSON.stringify({quantity:m})})).json()).success?(b(Y=>Y.map(X=>X.id===d?{...X,quantity:m}:X)),C.success("Đã cập nhật số lượng")):C.error("Không thể cập nhật số lượng")}catch{C.error("Lỗi khi cập nhật số lượng")}},_=async d=>{console.log("handleRemoveItem called with itemId:",d),$.confirm({title:"Xóa món khỏi đơn hàng?",content:"Bạn có chắc muốn xóa món này khỏi đơn hàng?",onOk:async()=>{var m;try{(await(await fetch(`/api/orders/items/${d}`,{method:"DELETE",headers:{"X-CSRF-TOKEN":(m=document.querySelector('meta[name="csrf-token"]'))==null?void 0:m.content}})).json()).success?(b(z=>z.filter(Y=>Y.id!==d)),C.success("Đã xóa món khỏi đơn hàng")):C.error("Không thể xóa món")}catch{C.error("Lỗi khi xóa món")}}})},r=n.useMemo(()=>x.map(d=>`${d.id}-${d.quantity}-${d.unit_price}-${d.status}`).join("|"),[x]),u=n.useMemo(()=>x.reduce((d,m)=>d+(m.status===3?0:m.quantity*m.unit_price||0),0),[r]),y=n.useMemo(()=>x.reduce((d,m)=>d+(m.quantity*m.unit_price||0),0),[r]),L=o&&[0].includes(o.status);return N?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(Ue,{size:"large"})}):o?e.jsxs("div",{className:"order-history-container p-4 bg-gray-50 min-h-screen pb-24",children:[v,k,e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{children:[e.jsx(Ae,{level:3,className:"mb-1 text-gray-800",children:"Đơn hàng"}),e.jsx(w,{className:"text-gray-500",children:Ce(o.created_at)})]}),e.jsx(ye,{color:gt(o.status),icon:l(o.status),className:"rounded-full px-3 py-1",children:ft(o.status)})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[e.jsxs(A,{size:"small",className:"text-center",children:[e.jsx("div",{className:"font-bold text-blue-600",children:x.length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Loại món"})]}),e.jsxs(A,{size:"small",className:"text-center",children:[e.jsx("div",{className:"font-bold text-orange-600",children:x.reduce((d,m)=>d+m.quantity,0)}),e.jsx("div",{className:"text-xs text-gray-500",children:"Tổng số"})]})]}),L&&e.jsx("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-3 rounded",children:e.jsx(w,{className:"text-blue-800 text-sm",children:"💡 Bạn có thể chỉnh sửa đơn hàng này vì chưa được xác nhận"})})]}),x.length===0?e.jsx(se,{description:"Đơn hàng trống",className:"mt-12"}):e.jsx("div",{className:"space-y-3 mb-6",children:x.map(d=>{var m;return e.jsx(A,{className:"border border-gray-200",children:e.jsx("div",{className:"flex gap-3",children:e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"w-full flex justify-between",children:e.jsx(w,{strong:!0,className:"text-gray-800",children:(m=d==null?void 0:d.food)==null?void 0:m.name})}),e.jsxs("div",{className:"text-sm text-gray-600",children:[O(d==null?void 0:d.unit_price)," / món"]})]}),L&&e.jsx(S,{type:"text",size:"small",icon:e.jsx(Ze,{}),onClick:()=>_(d.id),className:"text-red-500 hover:text-red-700 w-8 h-8"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"flex items-center gap-2",children:L?e.jsxs("div",{className:"flex items-center bg-gray-50 rounded-lg p-1",children:[e.jsx(S,{type:"text",size:"small",icon:e.jsx(st,{}),onClick:()=>V(d.id,d.quantity-1),className:"w-8 h-8 rounded-md hover:bg-white border-0"}),e.jsx("div",{className:"mx-2 min-w-[40px] text-center",children:e.jsx(w,{strong:!0,className:"text-base",children:d.quantity})}),e.jsx(S,{type:"text",size:"small",icon:e.jsx(be,{}),onClick:()=>V(d.id,d.quantity+1),className:"w-8 h-8 rounded-md hover:bg-white border-0"})]}):e.jsxs(w,{className:"text-gray-600",children:["Số lượng: ",d.quantity]})}),e.jsx(w,{strong:!0,className:"text-green-600",children:O(d.unit_price*d.quantity)})]}),e.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["Đặt lúc: ",Ce(d.created_at)]})]})})},d.id)})}),e.jsxs(A,{className:"mt-4 shadow-lg",children:[e.jsx(Ae,{level:5,className:"mb-3",children:"Chi tiết thanh toán"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(w,{children:"Tạm tính:"}),e.jsx(w,{children:O(y)})]}),o.discount_amount>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx(w,{children:"Giảm giá:"}),e.jsx(w,{children:O(o.discount_amount)})]}),e.jsx(et,{className:"my-2"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(w,{strong:!0,className:"text-lg",children:"Tổng cộng:"}),e.jsx(w,{strong:!0,className:"text-lg text-green-600",children:o.payment_method?O(o.total_amount):O(u)})]})]}),o.payment_method&&e.jsx("div",{className:"mt-3 pt-3 border-t border-gray-100",children:e.jsxs(w,{className:"text-sm text-gray-600",children:["Thanh toán: ",o.payment_method]})}),(o==null?void 0:o.id)&&![0,4,5,6].includes(o.status)&&e.jsx(S,{type:"success",shape:"round",icon:e.jsx(rt,{}),size:"large",loading:i,onClick:c,className:"shadow-lg bg-green-600 hover:bg-green-700 text-base font-semibold text-white w-full mt-3",children:"Gọi thanh toán"})]})]}):e.jsx("div",{className:"order-history-container p-4 bg-gray-50 min-h-screen",children:e.jsx(se,{description:"Chưa có đơn hàng nào",className:"mt-20"})})},{Title:Dr,Text:pe,Paragraph:gr}=ae,{Search:Lr}=ne,pr=({order:t,onAddToCart:a,cartItems:s=[]})=>{const[c,i]=n.useState([]),[l,o]=n.useState([]),[g,x]=n.useState(!0),[b,N]=n.useState(""),[j,E]=n.useState("all"),[q,T]=n.useState([]);n.useEffect(()=>{D()},[]);const D=async()=>{try{x(!0);const u=await(await fetch(`/api/menu/${t==null?void 0:t.id}`)).json();u.success&&(i(u.data.menu_items||[]),o(u.data.categories||[]))}catch(r){console.error("Error fetching menu:",r)}finally{x(!1)}},C=()=>{let r=c;if(j!=="all"&&(r=r.filter(u=>u.categories.some(y=>y.id===j))),b){const u=le(b);r=r.filter(y=>le(y.name).includes(u)||le(y.description||"").includes(u))}return r},v=r=>{const u=s.find(y=>y.id===r);return u?u.quantity:0},$=r=>q.includes(r),k=r=>{T(u=>u.includes(r)?u.filter(y=>y!==r):[...u,r])},p=r=>({"Đồ uống":e.jsx(Tt,{}),"Món chính":e.jsx(oe,{}),"Tráng miệng":e.jsx($e,{}),"Khai vị":e.jsx(Oe,{})})[r]||e.jsx(oe,{}),V=[{key:"all",label:e.jsxs(we,{children:[e.jsx(oe,{}),"Tất cả"]})},...l.map(r=>({key:r.id,label:e.jsxs(we,{children:[p(r.name),r.name]})}))];if(g)return e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(Ue,{size:"large"})});const _=C();return e.jsxs("div",{className:"menu-view-container p-4 bg-gray-50 min-h-screen pb-20",children:[e.jsx("div",{className:"mb-4",children:e.jsx(ne,{placeholder:"Tìm món ăn...",allowClear:!0,size:"large",prefix:e.jsx(jt,{}),value:b,onChange:r=>N(r.target.value),className:"rounded-lg"})}),e.jsx("div",{className:"mb-4",children:e.jsx(Ge,{activeKey:j,onChange:E,size:"small",className:"category-tabs",items:V})}),_.length===0?e.jsx(se,{description:"Không tìm thấy món ăn nào",className:"mt-12"}):e.jsx(_e,{dataSource:_,renderItem:r=>e.jsx(_e.Item,{className:"mb-3 p-0",children:e.jsx(A,{className:"w-full menu-item-card hover:shadow-md transition-shadow",children:e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("div",{className:"flex gap-4",children:[r.image&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-20 h-20 rounded-lg overflow-hidden",children:e.jsx(yt,{src:r.image,alt:r.name,className:"w-full h-full object-cover",preview:!1})})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1 pr-2",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1 flex-wrap",children:[e.jsx(pe,{strong:!0,className:"text-base text-gray-800 leading-tight",children:r.name}),r.is_popular&&e.jsxs(ye,{color:"red",size:"small",children:[e.jsx(Ot,{className:"mr-1"}),"Hot"]})]}),r.description&&e.jsx(gr,{className:"text-sm text-gray-600 mb-2 leading-tight",ellipsis:{rows:2},children:r.description}),r.rating&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Oe,{className:"text-yellow-500 text-xs"}),e.jsxs(pe,{className:"text-xs text-gray-600",children:[r.rating," (",r.review_count||0,")"]})]})]}),e.jsx(S,{type:"text",size:"small",icon:e.jsx($e,{className:$(r.id)?"text-red-500":"text-gray-400"}),onClick:()=>k(r.id),className:"flex-shrink-0 w-8 h-8"})]})})]}),e.jsxs("div",{className:"flex justify-between items-center pt-2 border-t border-gray-100",children:[e.jsx("div",{children:e.jsx(pe,{strong:!0,className:"text-lg text-green-600",children:O(r.price)})}),e.jsxs("div",{className:"flex items-center gap-2",children:[v(r.id)>0&&e.jsx(te,{count:v(r.id),size:"small",className:"mr-1"}),e.jsx(S,{type:"primary",size:"small",icon:e.jsx(be,{}),onClick:()=>a(r),disabled:!r.available,className:"bg-blue-500 hover:bg-blue-600 rounded-full px-3 h-8 text-sm font-medium",children:"Thêm"})]})]}),!r.available&&e.jsx("div",{children:e.jsx(ye,{color:"red",size:"small",children:"Tạm hết"})})]})})})})]})},{Title:Be,Text:M}=ae,{TextArea:Fr}=ne,yr=({items:t=[],table:a,onUpdateQuantity:s,onRemoveItem:c,onClearCart:i,onPlaceOrder:l,total:o,isHiddenPriceCart:g})=>{const[x,b]=re.useModal(),[N,j]=je.useMessage(),[E,q]=n.useState(!1),[T]=bt.useForm(),[D,C]=n.useState(!1),v=o,$=v,k=(r,u)=>{if(u<1){p(r);return}s(r,u)},p=r=>{const u=t.find(y=>y.id===r);x.confirm({title:"Xóa món khỏi giỏ hàng?",content:`Bạn có chắc muốn xóa "${u==null?void 0:u.name}" khỏi giỏ hàng?`,icon:e.jsx(ve,{}),okText:"Xóa",okType:"danger",cancelText:"Hủy",onOk:()=>c(r)})},V=()=>{x.confirm({title:"Xóa tất cả món?",content:"Bạn có chắc muốn xóa tất cả món khỏi giỏ hàng?",icon:e.jsx(ve,{}),okText:"Xóa tất cả",okType:"danger",cancelText:"Hủy",onOk:i})},_=async r=>{try{q(!0);const u=await l({...r,subtotal:v,total:$});u.success?(N.success("Đặt món thành công!"),C(!1),T.resetFields()):N.error(u.message||"Có lỗi xảy ra khi đặt món")}catch{N.error("Có lỗi xảy ra khi đặt món")}finally{q(!1)}};return t.length===0?e.jsx("div",{className:"cart-view-container p-4 bg-gray-50 min-h-screen",children:e.jsx(se,{image:e.jsx(Qe,{className:"text-6xl text-gray-300"}),description:e.jsxs("div",{children:[e.jsx(M,{className:"text-gray-500",children:"Giỏ hàng trống"}),e.jsx("br",{}),e.jsx(M,{className:"text-sm text-gray-400",children:"Hãy thêm món ăn từ thực đơn"})]}),className:"mt-20"})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"cart-view-container p-4 bg-gray-50 min-h-screen pb-32",children:[b,j,e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs(Be,{level:4,className:"mb-0",children:["Giỏ hàng (",t.length," món)"]}),e.jsx(S,{type:"text",size:"small",icon:e.jsx(Et,{}),onClick:V,className:"text-red-500 hover:text-red-700",children:"Xóa tất cả"})]}),e.jsx("div",{className:"space-y-3 mb-6",children:t.map(r=>e.jsx(A,{className:"cart-item-card border border-gray-200 shadow-sm",children:e.jsx("div",{className:"flex gap-3",children:e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{className:"flex-1 pr-2",children:[e.jsx(M,{strong:!0,className:"text-base text-gray-800 block leading-tight",children:r.name}),!r.hide_price&&e.jsxs(M,{className:"text-sm text-gray-600",children:[!r.hide_price&&O(r.price)," / món"]})]}),e.jsx(S,{type:"text",size:"small",icon:e.jsx(Ze,{}),onClick:()=>p(r.id),className:"text-red-500 hover:text-red-700 w-8 h-8 flex-shrink-0"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center bg-gray-50 rounded-lg p-1",children:[e.jsx(S,{type:"text",size:"small",icon:e.jsx(st,{}),onClick:()=>k(r.id,r.quantity-1),className:"w-8 h-8 rounded-md hover:bg-white border-0 flex items-center justify-center"}),e.jsx("div",{className:"mx-2 min-w-[40px] text-center",children:e.jsx(M,{strong:!0,className:"text-base",children:r.quantity})}),e.jsx(S,{type:"text",size:"small",icon:e.jsx(be,{}),onClick:()=>k(r.id,r.quantity+1),className:"w-8 h-8 rounded-md hover:bg-white border-0 flex items-center justify-center"})]}),e.jsx(M,{strong:!0,className:"text-lg text-green-600",children:!r.hide_price&&O(r.price*r.quantity)})]})]})})},r.id))}),e.jsxs(A,{className:"mt-4 order-summary-card sticky bottom-20 z-10",children:[e.jsx(Be,{level:5,className:"mb-3",children:"Tổng đơn hàng"}),!g&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(M,{children:"Tạm tính:"}),e.jsx(M,{children:O(v)})]}),e.jsx(et,{className:"my-2"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(M,{strong:!0,className:"text-lg",children:"Tổng cộng:"}),e.jsx(M,{strong:!0,className:"text-lg text-green-600",children:O($)})]})]}),e.jsxs(S,{type:"primary",size:"large",block:!0,icon:e.jsx(ht,{}),onClick:_,className:"mt-4 bg-blue-500 hover:bg-blue-600 h-12 text-lg font-medium",children:["Đặt món (",t.length," món)"]})]})]})})},{Title:vr}=ae,{Content:br}=B,jr=({table:t,activeOrder:a})=>{const[s,c]=n.useState("menu"),[i,l]=n.useState([]),[o,g]=n.useState(0),[x,b]=n.useState([]),[N,j]=n.useState(!1),[E,q]=re.useModal(),[T,D]=je.useMessage(),[C,v]=n.useState(!1),[$,k]=n.useState(""),[p,V]=n.useState(window.activeOrderData||{}),_=n.useCallback(async()=>{if(p!=null&&p.id)try{const f=await(await fetch(`/api/orders/${p.id}/details`)).json();f.order&&V(f.order)}catch(h){console.error("Failed to fetch active order",h),T.error("Không thể cập nhật trạng thái đơn hàng.")}},[p.id,T]);n.useEffect(()=>{const h=setInterval(()=>{_()},3e4);return()=>clearInterval(h)},[_]);const r=()=>i.reduce((h,f)=>h+(f.quantity||0),0),u=i.some(h=>h.hide_price===!0),y=()=>i.reduce((h,f)=>h+(f.price||0)*(f.quantity||0),0),L=h=>{l(f=>f.find(P=>P.id===h.id)?f.map(P=>P.id===h.id?{...P,quantity:P.quantity+1}:P):[...f,{...h,quantity:1}])},d=h=>{l(f=>f.filter(K=>K.id!==h))},m=(h,f)=>{if(f<=0){d(h);return}l(K=>K.map(P=>P.id===h?{...P,quantity:f}:P))},R=()=>{l([])},F=async()=>{try{const f=await(await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content},body:JSON.stringify({items:i,existing_order_id:p.id})})).json();return f.success&&(R(),c("history"),b(K=>[...K,{type:"success",message:"Đặt món thành công!",timestamp:new Date}])),f}catch(h){return console.error("Error placing order:",h),{success:!1,error:"Network error"}}},z=async()=>{v(!0)},Y=async()=>{j(!0);try{const f=await(await fetch(`/api/orders/${p.id}/request-payment`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content},body:JSON.stringify({discount_code:$})})).json();f.success?(f.qr_payment_url&&window.open(f.qr_payment_url,"_blank"),T.success("Đã gửi yêu cầu thanh toán. Vui lòng đợi nhân viên."),_(),v(!1)):T.error(f.message||"Không thể gửi yêu cầu thanh toán.")}catch(h){console.error("Error requesting payment:",h),T.error("Lỗi khi gửi yêu cầu thanh toán.")}finally{j(!1)}},X=[{key:"menu",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(Qt,{className:"text-lg mb-1"}),e.jsx("span",{className:"text-xs",children:"Thực đơn"})]}),children:e.jsx(pr,{order:p,onAddToCart:L,cartItems:i})},{key:"cart",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(te,{count:r(),size:"small",offset:[10,-5],children:e.jsx(Qe,{className:"text-lg mb-1"})}),e.jsx("span",{className:"text-xs",children:"Giỏ hàng"})]}),children:e.jsx(yr,{items:i,table:t,onUpdateQuantity:m,onRemoveItem:d,onClearCart:R,onPlaceOrder:F,total:y(),isHiddenPriceCart:u})},{key:"history",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(te,{count:o,size:"small",offset:[10,-5],children:e.jsx(Yt,{className:"text-lg mb-1"})}),e.jsx("span",{className:"text-xs",children:"Lịch sử"})]}),children:e.jsx(fr,{orderId:p.id,activeTab:s,onOrderCountChange:g,paymentLoading:N,handleRequestPayment:z})}];return e.jsxs(e.Fragment,{children:[e.jsxs(B,{className:"mobile-ordering-layout min-h-screen bg-gray-50",style:{marginBottom:"58px"},children:[D,q,e.jsx("div",{className:"sticky top-0 z-50 bg-white shadow-sm border-b",children:e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center",children:e.jsx($t,{className:"text-white text-lg"})}),e.jsx("div",{children:e.jsxs("div",{className:"font-semibold text-gray-800",children:["Order ",p.order_number]})})]}),e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(te,{count:x.length,size:"small",children:e.jsx(vt,{className:"text-xl text-gray-600"})})})]})}),e.jsx(br,{className:"flex-1",children:e.jsx(Ge,{activeKey:s,onChange:c,centered:!0,size:"large",className:"[&_.ant-tabs-tab]:!px-3 [&_.ant-tabs-tab]:!py-2 [&_.ant-tabs-tab]:!min-w-[80px] [&_.ant-tabs-tab]:!flex-1 [&_.ant-tabs-tab-btn]:!w-full [&_.ant-tabs-tab-btn]:!flex [&_.ant-tabs-tab-btn]:!flex-col [&_.ant-tabs-tab-btn]:!items-center [&_.ant-tabs-tab-btn]:!justify-center [&_.ant-tabs-tab-btn]:!text-xs [&_.ant-tabs-ink-bar]:!h-1 [&_.ant-tabs-ink-bar]:!rounded [&_.ant-tabs-content-holder]:!p-0 [&_.ant-tabs-tabpane]:!p-0",tabBarStyle:{backgroundColor:"white",margin:0,padding:"0 8px",borderTop:"1px solid #f0f0f0",position:"fixed",width:"100%",height:"58px",bottom:"0",zIndex:40},items:X})})]}),e.jsxs(re,{title:"Gọi thanh toán",visible:C,onOk:Y,onCancel:()=>v(!1),okText:"Gọi thanh toán",cancelText:"Hủy",confirmLoading:N,children:[e.jsx("div",{style:{marginBottom:16},children:e.jsx(ne,{prefix:e.jsx(rt,{}),className:"w-full mb-2 border border-gray-300 rounded-md p-2",placeholder:"Nhập mã giảm giá (nếu có)",value:$,onChange:h=>k(h.target.value)})}),e.jsx("div",{children:e.jsx(vr,{level:5,strong:!0,children:"Bạn có chắc muốn gọi nhân viên để thanh toán cho đơn hàng này?"})})]})]})},Nr=window.tableData,Cr=window.activeOrderData,_r=()=>e.jsx(xt,{locale:xr,children:e.jsx(jr,{table:Nr,activeOrder:Cr})}),Ke=document.getElementById("mobile-ordering-root");Ke&&mt.createRoot(Ke).render(e.jsx(_r,{}));
