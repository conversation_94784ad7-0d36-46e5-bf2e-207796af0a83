import{r as a,j as e,c as W}from"./index-CHvake0r.js";import"./CreateOrderModal-F_3oEa81.js";import"./index-Bbq37T-v.js";import"./EditOutlined-t_UPdXXr.js";import"./ReloadOutlined-BPMc8QqU.js";import"./EyeOutlined-D5ZQtqsR.js";import"./TableOutlined-B5odrklF.js";import"./index-CC0TaQ9p.js";import"./index-Cqw7XJJF.js";import"./ActionButton-BdVp3AS3.js";import"./index-BLyO0z8k.js";import"./QrcodeOutlined-BwfiqOGi.js";import"./index-DIqWL9-0.js";import"./RocketOutlined-BtbtbzoE.js";import"./ClockCircleOutlined-B61igLBq.js";import"./CheckCircleOutlined-DMtaSJBb.js";async function E(c,i){return await fetch("/api/public/meal-tickets/use",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:c,used_count:i})})}const A=({apiBaseUrl:c="/api"})=>{var v,N,k,w,y;const[i,L]=a.useState([]),[S,g]=a.useState(!0),[d,j]=a.useState(!1),[r,m]=a.useState(""),[t,x]=a.useState({isOpen:!1,ticket:null,usedCount:""}),[f,b]=a.useState(!1);a.useEffect(()=>{u()},[]);const h=s=>s.normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/đ/g,"d").replace(/Đ/g,"D"),o=i.filter(s=>{if(!r||r.trim()==="")return!0;const n=h(r.toLowerCase().trim()),l=h(s.client_name.toLowerCase()),B=s.id.toString().toLowerCase(),T=h(s.rooms.join(" ").toLowerCase());return l.includes(n)||B.includes(n)||T.includes(n)});console.log("Search term:",r),console.log("Total tickets:",i.length),console.log("Filtered tickets:",o.length);const M=o.reduce((s,n)=>s+n.remaining_tickets,0),u=async(s=!1)=>{s?j(!0):g(!0);try{const l=await(await fetch(`${c}/public/meal-tickets/data`)).json();L(l.tickets||[])}catch(n){console.error("Error:",n)}finally{g(!1),j(!1)}},_=s=>{x({isOpen:!0,ticket:s,usedCount:""})},p=()=>{x({isOpen:!1,ticket:null,usedCount:""})},z=async()=>{if(!t.ticket||!t.usedCount)return;const s=parseInt(t.usedCount);if(isNaN(s)||s<=0){alert("Vui lòng nhập số lượng hợp lệ");return}if(s>t.ticket.remaining_tickets){alert("Số lượng sử dụng không thể lớn hơn số vé còn lại");return}b(!0);try{if((await E(t.ticket.id,s)).ok){await u(),p();const l=document.createElement("div");l.className="fixed top-6 right-6 bg-green-500 text-white px-4 py-3 rounded-xl shadow-lg z-50",l.textContent=`Đã cập nhật ${s} vé cho ${t.ticket.client_name}`,document.body.appendChild(l),setTimeout(()=>{document.body.removeChild(l)},3e3)}else throw new Error("Có lỗi xảy ra khi cập nhật")}catch(n){console.error("Error:",n),alert("Có lỗi xảy ra khi cập nhật vé")}finally{b(!1)}};return S?e.jsx("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-8 h-8 border-3 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),e.jsx("p",{className:"text-slate-600 font-medium",children:"Đang tải dữ liệu..."})]})}):e.jsxs("div",{className:"min-h-screen bg-slate-50",children:[e.jsx("div",{className:"bg-white shadow-sm sticky top-0 z-10 shadow-md",children:e.jsx("div",{className:"max-w-6xl mx-auto px-6 py-8",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"Quản lý vé ăn"}),e.jsxs("div",{className:"flex items-center gap-6 text-sm",children:[e.jsxs("p",{className:"text-slate-600",children:[e.jsx("span",{className:"font-semibold text-blue-600",children:o.length}),r?" kết quả tìm kiếm":" khách hàng đang có vé"]}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-emerald-500 rounded-full"}),e.jsxs("span",{className:"text-slate-600",children:["Chưa dùng: ",e.jsx("span",{className:"font-semibold text-emerald-600",children:M})]})]})})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx("svg",{className:"w-5 h-5 text-slate-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("input",{type:"text",placeholder:"Tìm kiếm...",value:r,onChange:s=>m(s.target.value),className:"w-64 pl-10 pr-10 py-2.5 bg-slate-50 border border-slate-200 rounded-xl focus:bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"}),r&&e.jsx("button",{onClick:()=>m(""),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:e.jsx("svg",{className:"w-4 h-4 text-slate-400 hover:text-slate-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("button",{onClick:()=>u(!0),disabled:d,className:"flex items-center gap-2 px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-colors disabled:opacity-50",children:[e.jsx("svg",{className:`w-4 h-4 ${d?"animate-spin":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Cập nhật"]})]})]})})}),e.jsx("div",{className:"max-w-6xl mx-auto px-6 py-8",children:i.length===0?e.jsxs("div",{className:"text-center py-20",children:[e.jsx("div",{className:"w-20 h-20 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:e.jsx("svg",{className:"w-10 h-10 text-slate-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})}),e.jsx("h3",{className:"text-xl font-semibold text-slate-700 mb-2",children:"Chưa có dữ liệu"}),e.jsx("p",{className:"text-slate-500 mb-6",children:"Hiện tại chưa có khách hàng nào đang có vé ăn"})]}):o.length===0?e.jsxs("div",{className:"text-center py-20",children:[e.jsx("div",{className:"w-20 h-20 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:e.jsx("svg",{className:"w-10 h-10 text-slate-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h3",{className:"text-xl font-semibold text-slate-700 mb-2",children:"Không tìm thấy kết quả"}),e.jsxs("p",{className:"text-slate-500 mb-6",children:['Không có khách hàng nào phù hợp với "',r,'"']}),e.jsx("button",{onClick:()=>m(""),className:"px-4 py-2 bg-slate-200 hover:bg-slate-300 text-slate-700 font-medium rounded-lg transition-colors",children:"Xóa bộ lọc"})]}):e.jsx("div",{className:"grid gap-4",children:o.map(s=>{const n=s.remaining_tickets<=3,l=s.remaining_tickets<=1;return e.jsx("div",{className:"bg-white rounded-2xl shadow-sm hover:shadow-md transition-all duration-200 border border-slate-100 overflow-hidden",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-sm",children:e.jsx("span",{className:"text-white font-bold text-lg",children:s.client_name.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-1",children:[e.jsx("h3",{className:"text-xl font-bold text-slate-900",children:s.client_name}),e.jsxs("button",{onClick:()=>_(s),className:"flex items-center gap-1 px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-lg transition-colors",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Sử dụng"]})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-slate-500",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"ID: ",s.id]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Check in: ",new Date(s.start_date).toLocaleDateString("vi-VN")]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}),s.day_count," đêm"]})]}),e.jsx("div",{className:"flex items-center gap-4 text-sm text-slate-500 pt-2",children:e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"})}),"Phòng: ",s.rooms.join(", ")]})})]})]}),e.jsxs("div",{className:"flex items-center gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-slate-600",children:s.total_tickets}),e.jsx("div",{className:"text-xs font-medium text-slate-400 uppercase tracking-wide",children:"Tổng"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-amber-600",children:s.used_tickets}),e.jsx("div",{className:"text-xs font-medium text-slate-400 uppercase tracking-wide",children:"Đã dùng"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-3xl font-black ${l?"text-red-600":n?"text-orange-600":"text-emerald-600"}`,children:s.remaining_tickets}),e.jsx("div",{className:"text-xs font-medium text-slate-400 uppercase tracking-wide",children:"Còn lại"})]})]})]})})},s.id)})})}),t.isOpen&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsx("div",{className:"bg-white rounded-2xl shadow-xl max-w-md w-full",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-slate-900",children:"Sử dụng vé ăn"}),e.jsx("button",{onClick:p,className:"text-slate-400 hover:text-slate-600 transition-colors",children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold",children:(v=t.ticket)==null?void 0:v.client_name.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-slate-900",children:(N=t.ticket)==null?void 0:N.client_name}),e.jsxs("p",{className:"text-sm text-slate-500",children:["Còn lại: ",(k=t.ticket)==null?void 0:k.remaining_tickets," vé"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Số lượng vé sử dụng"}),e.jsx("input",{type:"number",min:"1",max:(w=t.ticket)==null?void 0:w.remaining_tickets,value:t.usedCount,onChange:s=>x({...t,usedCount:s.target.value}),className:"w-full px-4 py-3 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all",placeholder:"Nhập số lượng..."})]})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:p,className:"flex-1 px-4 py-3 bg-slate-200 hover:bg-slate-300 text-slate-700 font-medium rounded-xl transition-colors",children:"Hủy"}),e.jsx("button",{onClick:z,disabled:!t.usedCount||f||t.usedCount>((y=t.ticket)==null?void 0:y.remaining_tickets),className:"flex-1 px-4 py-3 bg-green-500 hover:bg-green-600 text-white font-medium rounded-xl transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:f?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Đang xử lý..."]}):"Xác nhận"})]})]})})}),d&&e.jsxs("div",{className:"fixed top-6 right-6 bg-white rounded-xl shadow-lg border border-slate-200 px-4 py-3 flex items-center gap-3",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"text-sm font-medium text-slate-700",children:"Đang cập nhật dữ liệu..."})]})]})},C=document.getElementById("meal-tickets-app");C&&W.createRoot(C).render(e.jsx(A,{}));
