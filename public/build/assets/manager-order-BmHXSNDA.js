import{r,j as n,c as P}from"./index-CHvake0r.js";import{o as y,H as R,O as T,C as q,T as A,P as D}from"./CreateOrderModal-F_3oEa81.js";import{s as S}from"./index-CC0TaQ9p.js";import"./index-Bbq37T-v.js";import"./EditOutlined-t_UPdXXr.js";import"./ReloadOutlined-BPMc8QqU.js";import"./EyeOutlined-D5ZQtqsR.js";import"./TableOutlined-B5odrklF.js";import"./index-Cqw7XJJF.js";import"./ActionButton-BdVp3AS3.js";import"./index-BLyO0z8k.js";import"./QrcodeOutlined-BwfiqOGi.js";import"./index-DIqWL9-0.js";import"./RocketOutlined-BtbtbzoE.js";import"./ClockCircleOutlined-B61igLBq.js";import"./CheckCircleOutlined-DMtaSJBb.js";const I=e=>{var o;return{id:e.id,number:e.order_number,status:H(e.status),items:e.items,totalAmount:e.total_amount||0,tables:e.tables||[],createdAt:F(e.order_time),customer:e.customer,paymentStatus:e.payment_status,paymentMethod:e.payment_method,qrCodeUrl:e.qr_code_url,notes:e.notes,priority:e.priority,customerName:`${e.id}: ${((o=e.customer)==null?void 0:o.name)||"Khách hàng"}`}},N=e=>({id:e.id,name:e.name,code:e.code,capacity:e.capacity,description:e.description,location:e.location,occupied:e.status===0,qrCode:e.qr_code}),H=e=>({1:"pending",2:"preparing",3:"ready",4:"served"})[e]||"pending",F=e=>e?new Date(e).toLocaleString("vi-VN",{dateStyle:"short",timeStyle:"short"}):"",K=()=>{const[e,o]=r.useState(null),[b,c]=r.useState(!1),[w,d]=r.useState(!1),[x,p]=r.useState(!1),[C,g]=r.useState([]),[l,i]=r.useState([]),[m,O]=r.useState([]),[f,v]=S.useNotification();r.useEffect(()=>{u();const t=setInterval(a,6e4);return()=>clearInterval(t)},[]);const u=async()=>{try{const t=await y.getOrders();i(t.orders.map(I)),O(t.tables.map(N))}catch(t){console.error("Error loading data:",t)}},j=t=>{o(t);const s=l.find(E=>E.id===t);g([...s.tables]),setTimeout(()=>d(!0))},a=async()=>{await u(),S.success({message:"Thành công",description:"Đã làm mới dữ liệu!"})},_=async t=>{try{const s=await y.createOrder(t);return await u(),f.success({message:"Thành công",description:"Đã tạo đơn hàng mới thành công!",placement:"topRight"}),s}catch(s){throw console.error("Error creating order:",s),f.error({message:"Lỗi",description:s.message||"Không thể tạo đơn hàng. Vui lòng thử lại.",placement:"topRight"}),s}},h=()=>l.find(t=>t.id===e);return n.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 lg:p-6 sticky top-0",children:[v,n.jsx(R,{selectedOrder:e,onTableModal:()=>c(!0),onPaymentModal:()=>d(!0),onCreateOrder:()=>p(!0),onRefresh:a}),n.jsx(T,{orders:l,tables:m,selectedOrder:e,onSelectOrder:j,onDeselectOrder:()=>o(null),onTableModal:()=>c(!0),getSelectedOrder:h}),n.jsx(q,{visible:x,onClose:()=>p(!1),tables:m,onCreateOrder:_,onRefresh:a}),n.jsx(A,{visible:b,onClose:()=>c(!1),tables:m,selectedTables:C,setSelectedTables:g,selectedOrder:h(),setOrders:i}),n.jsx(D,{visible:w,onClose:()=>d(!1),selectedOrder:h(),setOrders:i,onRefresh:a,onDeselectOrder:()=>o(null)})]})},M=document.getElementById("manager-ordering-root");M&&P.createRoot(M).render(n.jsx(K,{}));
