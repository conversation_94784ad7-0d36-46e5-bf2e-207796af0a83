import{r,a3 as At,a4 as Re,a5 as B,_ as mt,ak as It,a as we,ac as ve,at as ae,an as Me,bM as Tr,a0 as Je,Y as T,$ as je,a7 as Bt,y as Gt,c1 as tr,au as St,ag as Lt,a6 as ht,aj as Hr,b7 as Br,b8 as En,a9 as Lr,u as sn,ap as nr,bz as Ar,c2 as Rn,i as un,l as Fr,e as se,m as $t,p as Yt,L as Vr,c3 as Wr,b0 as jr,U as Xt,C as dn,ad as Qt,w as vt,c4 as Kr,aK as Ur,bt as Gr,Z as Xr,c5 as qr,aG as Yr,al as Qr,am as Zr,aa as Jr,o as rr,t as or,Q as fn,bb as vn,F as Ut,b1 as mn,k as xn,G as kr,H as eo,I as to,J as no,h as rn,q as ar,aN as ro,bf as oo,b4 as ao,b9 as io,av as lo,a1 as co,aZ as so,z as uo,aX as fo,aA as vo,aY as mo,a_ as go,x as po,ba as ho,a$ as So,c6 as bo,c7 as Co}from"./index-CHvake0r.js";var yo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-circle",theme:"filled"},wo=function(t,n){return r.createElement(At,Re({},t,{ref:n,icon:yo}))},Rl=r.forwardRef(wo),Eo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"info-circle",theme:"filled"},Ro=function(t,n){return r.createElement(At,Re({},t,{ref:n,icon:Eo}))},xl=r.forwardRef(Ro),xo=r.forwardRef(function(e,t){var n=e.prefixCls,o=e.style,a=e.className,i=e.duration,l=i===void 0?4.5:i,c=e.showProgress,u=e.pauseOnHover,s=u===void 0?!0:u,f=e.eventKey,g=e.content,p=e.closable,d=e.closeIcon,m=d===void 0?"x":d,v=e.props,h=e.onClick,b=e.onNoticeClose,S=e.times,R=e.hovering,C=r.useState(!1),N=B(C,2),w=N[0],E=N[1],x=r.useState(0),y=B(x,2),$=y[0],O=y[1],F=r.useState(0),L=B(F,2),V=L[0],W=L[1],_=R||w,Y=l>0&&c,J=function(){b(f)},oe=function(D){(D.key==="Enter"||D.code==="Enter"||D.keyCode===ae.ENTER)&&J()};r.useEffect(function(){if(!_&&l>0){var k=Date.now()-V,D=setTimeout(function(){J()},l*1e3-V);return function(){s&&clearTimeout(D),W(Date.now()-k)}}},[l,_,S]),r.useEffect(function(){if(!_&&Y&&(s||V===0)){var k=performance.now(),D,ie=function ce(){cancelAnimationFrame(D),D=requestAnimationFrame(function(he){var me=he+V-k,Ce=Math.min(me/(l*1e3),1);O(Ce*100),Ce<1&&ce()})};return ie(),function(){s&&cancelAnimationFrame(D)}}},[l,V,_,Y,S]);var A=r.useMemo(function(){return mt(p)==="object"&&p!==null?p:p?{closeIcon:m}:{}},[p,m]),P=It(A,!0),q=100-(!$||$<0?0:$>100?100:$),Q="".concat(n,"-notice");return r.createElement("div",Re({},v,{ref:t,className:we(Q,a,ve({},"".concat(Q,"-closable"),p)),style:o,onMouseEnter:function(D){var ie;E(!0),v==null||(ie=v.onMouseEnter)===null||ie===void 0||ie.call(v,D)},onMouseLeave:function(D){var ie;E(!1),v==null||(ie=v.onMouseLeave)===null||ie===void 0||ie.call(v,D)},onClick:h}),r.createElement("div",{className:"".concat(Q,"-content")},g),p&&r.createElement("a",Re({tabIndex:0,className:"".concat(Q,"-close"),onKeyDown:oe,"aria-label":"Close"},P,{onClick:function(D){D.preventDefault(),D.stopPropagation(),J()}}),A.closeIcon),Y&&r.createElement("progress",{className:"".concat(Q,"-progress"),max:"100",value:q},q+"%"))}),ir=Me.createContext({}),Il=function(t){var n=t.children,o=t.classNames;return Me.createElement(ir.Provider,{value:{classNames:o}},n)},In=8,$n=3,On=16,Io=function(t){var n={offset:In,threshold:$n,gap:On};if(t&&mt(t)==="object"){var o,a,i;n.offset=(o=t.offset)!==null&&o!==void 0?o:In,n.threshold=(a=t.threshold)!==null&&a!==void 0?a:$n,n.gap=(i=t.gap)!==null&&i!==void 0?i:On}return[!!t,n]},$o=["className","style","classNames","styles"],Oo=function(t){var n=t.configList,o=t.placement,a=t.prefixCls,i=t.className,l=t.style,c=t.motion,u=t.onAllNoticeRemoved,s=t.onNoticeClose,f=t.stack,g=r.useContext(ir),p=g.classNames,d=r.useRef({}),m=r.useState(null),v=B(m,2),h=v[0],b=v[1],S=r.useState([]),R=B(S,2),C=R[0],N=R[1],w=n.map(function(_){return{config:_,key:String(_.key)}}),E=Io(f),x=B(E,2),y=x[0],$=x[1],O=$.offset,F=$.threshold,L=$.gap,V=y&&(C.length>0||w.length<=F),W=typeof c=="function"?c(o):c;return r.useEffect(function(){y&&C.length>1&&N(function(_){return _.filter(function(Y){return w.some(function(J){var oe=J.key;return Y===oe})})})},[C,w,y]),r.useEffect(function(){var _;if(y&&d.current[(_=w[w.length-1])===null||_===void 0?void 0:_.key]){var Y;b(d.current[(Y=w[w.length-1])===null||Y===void 0?void 0:Y.key])}},[w,y]),Me.createElement(Tr,Re({key:o,className:we(a,"".concat(a,"-").concat(o),p==null?void 0:p.list,i,ve(ve({},"".concat(a,"-stack"),!!y),"".concat(a,"-stack-expanded"),V)),style:l,keys:w,motionAppear:!0},W,{onAllRemoved:function(){u(o)}}),function(_,Y){var J=_.config,oe=_.className,A=_.style,P=_.index,q=J,Q=q.key,k=q.times,D=String(Q),ie=J,ce=ie.className,he=ie.style,me=ie.classNames,Ce=ie.styles,H=Je(ie,$o),M=w.findIndex(function(ye){return ye.key===D}),z={};if(y){var te=w.length-1-(M>-1?M:P-1),I=o==="top"||o==="bottom"?"-50%":"0";if(te>0){var U,de,ee;z.height=V?(U=d.current[D])===null||U===void 0?void 0:U.offsetHeight:h==null?void 0:h.offsetHeight;for(var fe=0,Se=0;Se<te;Se++){var He;fe+=((He=d.current[w[w.length-1-Se].key])===null||He===void 0?void 0:He.offsetHeight)+L}var De=(V?fe:te*O)*(o.startsWith("top")?1:-1),ze=!V&&h!==null&&h!==void 0&&h.offsetWidth&&(de=d.current[D])!==null&&de!==void 0&&de.offsetWidth?((h==null?void 0:h.offsetWidth)-O*2*(te<3?te:3))/((ee=d.current[D])===null||ee===void 0?void 0:ee.offsetWidth):1;z.transform="translate3d(".concat(I,", ").concat(De,"px, 0) scaleX(").concat(ze,")")}else z.transform="translate3d(".concat(I,", 0, 0)")}return Me.createElement("div",{ref:Y,className:we("".concat(a,"-notice-wrapper"),oe,me==null?void 0:me.wrapper),style:T(T(T({},A),z),Ce==null?void 0:Ce.wrapper),onMouseEnter:function(){return N(function(Ne){return Ne.includes(D)?Ne:[].concat(je(Ne),[D])})},onMouseLeave:function(){return N(function(Ne){return Ne.filter(function(Te){return Te!==D})})}},Me.createElement(xo,Re({},H,{ref:function(Ne){M>-1?d.current[D]=Ne:delete d.current[D]},prefixCls:a,classNames:me,styles:Ce,className:we(ce,p==null?void 0:p.notice),style:he,times:k,key:Q,eventKey:Q,onNoticeClose:s,hovering:y&&C.length>0})))})},Mo=r.forwardRef(function(e,t){var n=e.prefixCls,o=n===void 0?"rc-notification":n,a=e.container,i=e.motion,l=e.maxCount,c=e.className,u=e.style,s=e.onAllRemoved,f=e.stack,g=e.renderNotifications,p=r.useState([]),d=B(p,2),m=d[0],v=d[1],h=function(y){var $,O=m.find(function(F){return F.key===y});O==null||($=O.onClose)===null||$===void 0||$.call(O),v(function(F){return F.filter(function(L){return L.key!==y})})};r.useImperativeHandle(t,function(){return{open:function(y){v(function($){var O=je($),F=O.findIndex(function(W){return W.key===y.key}),L=T({},y);if(F>=0){var V;L.times=(((V=$[F])===null||V===void 0?void 0:V.times)||0)+1,O[F]=L}else L.times=0,O.push(L);return l>0&&O.length>l&&(O=O.slice(-l)),O})},close:function(y){h(y)},destroy:function(){v([])}}});var b=r.useState({}),S=B(b,2),R=S[0],C=S[1];r.useEffect(function(){var x={};m.forEach(function(y){var $=y.placement,O=$===void 0?"topRight":$;O&&(x[O]=x[O]||[],x[O].push(y))}),Object.keys(R).forEach(function(y){x[y]=x[y]||[]}),C(x)},[m]);var N=function(y){C(function($){var O=T({},$),F=O[y]||[];return F.length||delete O[y],O})},w=r.useRef(!1);if(r.useEffect(function(){Object.keys(R).length>0?w.current=!0:w.current&&(s==null||s(),w.current=!1)},[R]),!a)return null;var E=Object.keys(R);return Bt.createPortal(r.createElement(r.Fragment,null,E.map(function(x){var y=R[x],$=r.createElement(Oo,{key:x,configList:y,placement:x,prefixCls:o,className:c==null?void 0:c(x),style:u==null?void 0:u(x),motion:i,onNoticeClose:h,onAllNoticeRemoved:N,stack:f});return g?g($,{prefixCls:o,key:x}):$})),a)}),No=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],Po=function(){return document.body},Mn=0;function Do(){for(var e={},t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return n.forEach(function(a){a&&Object.keys(a).forEach(function(i){var l=a[i];l!==void 0&&(e[i]=l)})}),e}function $l(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.getContainer,n=t===void 0?Po:t,o=e.motion,a=e.prefixCls,i=e.maxCount,l=e.className,c=e.style,u=e.onAllRemoved,s=e.stack,f=e.renderNotifications,g=Je(e,No),p=r.useState(),d=B(p,2),m=d[0],v=d[1],h=r.useRef(),b=r.createElement(Mo,{container:m,ref:h,prefixCls:a,motion:o,maxCount:i,className:l,style:c,onAllRemoved:u,stack:s,renderNotifications:f}),S=r.useState([]),R=B(S,2),C=R[0],N=R[1],w=Gt(function(x){var y=Do(g,x);(y.key===null||y.key===void 0)&&(y.key="rc-notification-".concat(Mn),Mn+=1),N(function($){return[].concat(je($),[{type:"open",config:y}])})}),E=r.useMemo(function(){return{open:w,close:function(y){N(function($){return[].concat(je($),[{type:"close",key:y}])})},destroy:function(){N(function(y){return[].concat(je(y),[{type:"destroy"}])})}}},[]);return r.useEffect(function(){v(n())}),r.useEffect(function(){if(h.current&&C.length){C.forEach(function($){switch($.type){case"open":h.current.open($.config);break;case"close":h.current.close($.key);break;case"destroy":h.current.destroy();break}});var x,y;N(function($){return(x!==$||!y)&&(x=$,y=$.filter(function(O){return!C.includes(O)})),y})}},[C]),[E,b]}const _o=new St("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),zo=new St("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),To=(e,t=!1)=>{const{antCls:n}=e,o=`${n}-fade`,a=t?"&":"";return[tr(o,_o,zo,e.motionDurationMid,t),{[`
        ${a}${o}-enter,
        ${a}${o}-appear
      `]:{opacity:0,animationTimingFunction:"linear"},[`${a}${o}-leave`]:{animationTimingFunction:"linear"}}]},Ho=new St("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Bo=new St("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),Lo=new St("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Ao=new St("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Fo=new St("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Vo=new St("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Wo=new St("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),jo=new St("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}}),Ko={"move-up":{inKeyframes:Wo,outKeyframes:jo},"move-down":{inKeyframes:Ho,outKeyframes:Bo},"move-left":{inKeyframes:Lo,outKeyframes:Ao},"move-right":{inKeyframes:Fo,outKeyframes:Vo}},Nn=(e,t)=>{const{antCls:n}=e,o=`${n}-${t}`,{inKeyframes:a,outKeyframes:i}=Ko[t];return[tr(o,a,i,e.motionDurationMid),{[`
        ${o}-enter,
        ${o}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${o}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]};var lr=r.createContext(null),Pn=[];function Uo(e,t){var n=r.useState(function(){if(!Lt())return null;var m=document.createElement("div");return m}),o=B(n,1),a=o[0],i=r.useRef(!1),l=r.useContext(lr),c=r.useState(Pn),u=B(c,2),s=u[0],f=u[1],g=l||(i.current?void 0:function(m){f(function(v){var h=[m].concat(je(v));return h})});function p(){a.parentElement||document.body.appendChild(a),i.current=!0}function d(){var m;(m=a.parentElement)===null||m===void 0||m.removeChild(a),i.current=!1}return ht(function(){return e?l?l(p):p():d(),d},[e]),ht(function(){s.length&&(s.forEach(function(m){return m()}),f(Pn))},[s]),[a,g]}function Go(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}var Xo="rc-util-locker-".concat(Date.now()),Dn=0;function qo(e){var t=!!e,n=r.useState(function(){return Dn+=1,"".concat(Xo,"_").concat(Dn)}),o=B(n,1),a=o[0];ht(function(){if(t){var i=Hr(document.body).width,l=Go();Br(`
html body {
  overflow-y: hidden;
  `.concat(l?"width: calc(100% - ".concat(i,"px);"):"",`
}`),a)}else En(a);return function(){En(a)}},[t,a])}var Yo=!1;function Qo(e){return Yo}var _n=function(t){return t===!1?!1:!Lt()||!t?null:typeof t=="string"?document.querySelector(t):typeof t=="function"?t():t},Zo=r.forwardRef(function(e,t){var n=e.open,o=e.autoLock,a=e.getContainer;e.debug;var i=e.autoDestroy,l=i===void 0?!0:i,c=e.children,u=r.useState(n),s=B(u,2),f=s[0],g=s[1],p=f||n;r.useEffect(function(){(l||n)&&g(n)},[n,l]);var d=r.useState(function(){return _n(a)}),m=B(d,2),v=m[0],h=m[1];r.useEffect(function(){var O=_n(a);h(O??null)});var b=Uo(p&&!v),S=B(b,2),R=S[0],C=S[1],N=v??R;qo(o&&n&&Lt()&&(N===R||N===document.body));var w=null;if(c&&Lr(c)&&t){var E=c;w=E.ref}var x=sn(w,t);if(!p||!Lt()||v===void 0)return null;var y=N===!1||Qo(),$=c;return t&&($=r.cloneElement(c,{ref:x})),r.createElement(lr.Provider,{value:C},y?$:Bt.createPortal($,N))}),cr=r.createContext({});function zn(e,t,n){var o=t;return!o&&n&&(o="".concat(e,"-").concat(n)),o}function Tn(e,t){var n=e["page".concat(t?"Y":"X","Offset")],o="scroll".concat(t?"Top":"Left");if(typeof n!="number"){var a=e.document;n=a.documentElement[o],typeof n!="number"&&(n=a.body[o])}return n}function Jo(e){var t=e.getBoundingClientRect(),n={left:t.left,top:t.top},o=e.ownerDocument,a=o.defaultView||o.parentWindow;return n.left+=Tn(a),n.top+=Tn(a,!0),n}const ko=r.memo(function(e){var t=e.children;return t},function(e,t){var n=t.shouldUpdate;return!n});var ea={width:0,height:0,overflow:"hidden",outline:"none"},ta={outline:"none"},na=Me.forwardRef(function(e,t){var n=e.prefixCls,o=e.className,a=e.style,i=e.title,l=e.ariaId,c=e.footer,u=e.closable,s=e.closeIcon,f=e.onClose,g=e.children,p=e.bodyStyle,d=e.bodyProps,m=e.modalRender,v=e.onMouseDown,h=e.onMouseUp,b=e.holderRef,S=e.visible,R=e.forceRender,C=e.width,N=e.height,w=e.classNames,E=e.styles,x=Me.useContext(cr),y=x.panel,$=sn(b,y),O=r.useRef(),F=r.useRef();Me.useImperativeHandle(t,function(){return{focus:function(){var q;(q=O.current)===null||q===void 0||q.focus({preventScroll:!0})},changeActive:function(q){var Q=document,k=Q.activeElement;q&&k===F.current?O.current.focus({preventScroll:!0}):!q&&k===O.current&&F.current.focus({preventScroll:!0})}}});var L={};C!==void 0&&(L.width=C),N!==void 0&&(L.height=N);var V=c?Me.createElement("div",{className:we("".concat(n,"-footer"),w==null?void 0:w.footer),style:T({},E==null?void 0:E.footer)},c):null,W=i?Me.createElement("div",{className:we("".concat(n,"-header"),w==null?void 0:w.header),style:T({},E==null?void 0:E.header)},Me.createElement("div",{className:"".concat(n,"-title"),id:l},i)):null,_=r.useMemo(function(){return mt(u)==="object"&&u!==null?u:u?{closeIcon:s??Me.createElement("span",{className:"".concat(n,"-close-x")})}:{}},[u,s,n]),Y=It(_,!0),J=mt(u)==="object"&&u.disabled,oe=u?Me.createElement("button",Re({type:"button",onClick:f,"aria-label":"Close"},Y,{className:"".concat(n,"-close"),disabled:J}),_.closeIcon):null,A=Me.createElement("div",{className:we("".concat(n,"-content"),w==null?void 0:w.content),style:E==null?void 0:E.content},oe,W,Me.createElement("div",Re({className:we("".concat(n,"-body"),w==null?void 0:w.body),style:T(T({},p),E==null?void 0:E.body)},d),g),V);return Me.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":i?l:null,"aria-modal":"true",ref:$,style:T(T({},a),L),className:we(n,o),onMouseDown:v,onMouseUp:h},Me.createElement("div",{ref:O,tabIndex:0,style:ta},Me.createElement(ko,{shouldUpdate:S||R},m?m(A):A)),Me.createElement("div",{tabIndex:0,ref:F,style:ea}))}),sr=r.forwardRef(function(e,t){var n=e.prefixCls,o=e.title,a=e.style,i=e.className,l=e.visible,c=e.forceRender,u=e.destroyOnClose,s=e.motionName,f=e.ariaId,g=e.onVisibleChanged,p=e.mousePosition,d=r.useRef(),m=r.useState(),v=B(m,2),h=v[0],b=v[1],S={};h&&(S.transformOrigin=h);function R(){var C=Jo(d.current);b(p&&(p.x||p.y)?"".concat(p.x-C.left,"px ").concat(p.y-C.top,"px"):"")}return r.createElement(nr,{visible:l,onVisibleChanged:g,onAppearPrepare:R,onEnterPrepare:R,forceRender:c,motionName:s,removeOnLeave:u,ref:d},function(C,N){var w=C.className,E=C.style;return r.createElement(na,Re({},e,{ref:t,title:o,ariaId:f,prefixCls:n,holderRef:N,style:T(T(T({},E),a),S),className:we(i,w)}))})});sr.displayName="Content";var ra=function(t){var n=t.prefixCls,o=t.style,a=t.visible,i=t.maskProps,l=t.motionName,c=t.className;return r.createElement(nr,{key:"mask",visible:a,motionName:l,leavedClassName:"".concat(n,"-mask-hidden")},function(u,s){var f=u.className,g=u.style;return r.createElement("div",Re({ref:s,style:T(T({},g),o),className:we("".concat(n,"-mask"),f,c)},i))})},oa=function(t){var n=t.prefixCls,o=n===void 0?"rc-dialog":n,a=t.zIndex,i=t.visible,l=i===void 0?!1:i,c=t.keyboard,u=c===void 0?!0:c,s=t.focusTriggerAfterClose,f=s===void 0?!0:s,g=t.wrapStyle,p=t.wrapClassName,d=t.wrapProps,m=t.onClose,v=t.afterOpenChange,h=t.afterClose,b=t.transitionName,S=t.animation,R=t.closable,C=R===void 0?!0:R,N=t.mask,w=N===void 0?!0:N,E=t.maskTransitionName,x=t.maskAnimation,y=t.maskClosable,$=y===void 0?!0:y,O=t.maskStyle,F=t.maskProps,L=t.rootClassName,V=t.classNames,W=t.styles,_=r.useRef(),Y=r.useRef(),J=r.useRef(),oe=r.useState(l),A=B(oe,2),P=A[0],q=A[1],Q=Ar();function k(){Rn(Y.current,document.activeElement)||(_.current=document.activeElement)}function D(){if(!Rn(Y.current,document.activeElement)){var I;(I=J.current)===null||I===void 0||I.focus()}}function ie(I){if(I)D();else{if(q(!1),w&&_.current&&f){try{_.current.focus({preventScroll:!0})}catch{}_.current=null}P&&(h==null||h())}v==null||v(I)}function ce(I){m==null||m(I)}var he=r.useRef(!1),me=r.useRef(),Ce=function(){clearTimeout(me.current),he.current=!0},H=function(){me.current=setTimeout(function(){he.current=!1})},M=null;$&&(M=function(U){he.current?he.current=!1:Y.current===U.target&&ce(U)});function z(I){if(u&&I.keyCode===ae.ESC){I.stopPropagation(),ce(I);return}l&&I.keyCode===ae.TAB&&J.current.changeActive(!I.shiftKey)}r.useEffect(function(){l&&(q(!0),k())},[l]),r.useEffect(function(){return function(){clearTimeout(me.current)}},[]);var te=T(T(T({zIndex:a},g),W==null?void 0:W.wrapper),{},{display:P?null:"none"});return r.createElement("div",Re({className:we("".concat(o,"-root"),L)},It(t,{data:!0})),r.createElement(ra,{prefixCls:o,visible:w&&l,motionName:zn(o,E,x),style:T(T({zIndex:a},O),W==null?void 0:W.mask),maskProps:F,className:V==null?void 0:V.mask}),r.createElement("div",Re({tabIndex:-1,onKeyDown:z,className:we("".concat(o,"-wrap"),p,V==null?void 0:V.wrapper),ref:Y,onClick:M,style:te},d),r.createElement(sr,Re({},t,{onMouseDown:Ce,onMouseUp:H,ref:J,closable:C,ariaId:Q,prefixCls:o,visible:l&&P,onClose:ce,onVisibleChanged:ie,motionName:zn(o,b,S)}))))},aa=function(t){var n=t.visible,o=t.getContainer,a=t.forceRender,i=t.destroyOnClose,l=i===void 0?!1:i,c=t.afterClose,u=t.panelRef,s=r.useState(n),f=B(s,2),g=f[0],p=f[1],d=r.useMemo(function(){return{panel:u}},[u]);return r.useEffect(function(){n&&p(!0)},[n]),!a&&l&&!g?null:r.createElement(cr.Provider,{value:d},r.createElement(Zo,{open:n||a||g,autoDestroy:!1,getContainer:o,autoLock:n||g},r.createElement(oa,Re({},t,{destroyOnClose:l,afterClose:function(){c==null||c(),p(!1)}}))))};aa.displayName="Dialog";function Hn(e){return{position:e,inset:0}}const ia=e=>{const{componentCls:t,antCls:n}=e;return[{[`${t}-root`]:{[`${t}${n}-zoom-enter, ${t}${n}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${t}${n}-zoom-leave ${t}-content`]:{pointerEvents:"none"},[`${t}-mask`]:Object.assign(Object.assign({},Hn("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${t}-hidden`]:{display:"none"}}),[`${t}-wrap`]:Object.assign(Object.assign({},Hn("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${t}-root`]:To(e)}]},la=e=>{const{componentCls:t}=e;return[{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl"},[`${t}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[t]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[t]:{maxWidth:"calc(100vw - 16px)",margin:`${se(e.marginXS)} auto`},[`${t}-centered`]:{[t]:{flex:1}}}}},{[t]:Object.assign(Object.assign({},Yt(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${se(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${t}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${t}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${t}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:se(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:disabled":{pointerEvents:"none"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},Vr(e)),[`${t}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${se(e.borderRadiusLG)} ${se(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${t}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${t}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${se(e.margin)} auto`}},[`${t}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${t}-open`]:{overflow:"hidden"}})},{[`${t}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${t}-content,
          ${t}-body,
          ${t}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${t}-confirm-body`]:{marginBottom:"auto"}}}]},ca=e=>{const{componentCls:t}=e;return{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl",[`${t}-confirm-body`]:{direction:"rtl"}}}}},sa=e=>{const{componentCls:t}=e,n=Wr(e);delete n.xs;const o=Object.keys(n).map(a=>({[`@media (min-width: ${se(n[a])})`]:{width:`var(--${t.replace(".","")}-${a}-width)`}}));return{[`${t}-root`]:{[t]:[{width:`var(--${t.replace(".","")}-xs-width)`}].concat(je(o))}}},ua=e=>{const t=e.padding,n=e.fontSizeHeading5,o=e.lineHeightHeading5;return $t(e,{modalHeaderHeight:e.calc(e.calc(o).mul(n).equal()).add(e.calc(t).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},da=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${se(e.paddingMD)} ${se(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${se(e.padding)} ${se(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${se(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${se(e.paddingXS)} ${se(e.padding)}`:0,footerBorderTop:e.wireframe?`${se(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${se(e.borderRadiusLG)} ${se(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${se(e.padding*2)} ${se(e.padding*2)} ${se(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM}),Ol=un("Modal",e=>{const t=ua(e);return[la(t),ca(t),ia(t),Fr(t,"zoom"),sa(t)]},da,{unitless:{titleLineHeight:!0}}),Ml=Me.createContext({});function fa(e){return t=>r.createElement(jr,{theme:{token:{motion:!1,zIndexPopupBase:0}}},r.createElement(e,Object.assign({},t)))}const va=(e,t,n,o,a)=>fa(l=>{const{prefixCls:c,style:u}=l,s=r.useRef(null),[f,g]=r.useState(0),[p,d]=r.useState(0),[m,v]=Xt(!1,{value:l.open}),{getPrefixCls:h}=r.useContext(dn),b=h(o||"select",c);r.useEffect(()=>{if(v(!0),typeof ResizeObserver<"u"){const C=new ResizeObserver(w=>{const E=w[0].target;g(E.offsetHeight+8),d(E.offsetWidth)}),N=setInterval(()=>{var w;const E=a?`.${a(b)}`:`.${b}-dropdown`,x=(w=s.current)===null||w===void 0?void 0:w.querySelector(E);x&&(clearInterval(N),C.observe(x))},10);return()=>{clearInterval(N),C.disconnect()}}},[]);let S=Object.assign(Object.assign({},l),{style:Object.assign(Object.assign({},u),{margin:0}),open:m,visible:m,getPopupContainer:()=>s.current});n&&(S=n(S)),t&&Object.assign(S,{[t]:{overflow:{adjustX:!1,adjustY:!1}}});const R={paddingBottom:f,position:"relative",minWidth:p};return r.createElement("div",{ref:s,style:R},r.createElement(e,Object.assign({},S)))});var Zt=function(t){var n=t.className,o=t.customizeIcon,a=t.customizeIconProps,i=t.children,l=t.onMouseDown,c=t.onClick,u=typeof o=="function"?o(a):o;return r.createElement("span",{className:n,onMouseDown:function(f){f.preventDefault(),l==null||l(f)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:c,"aria-hidden":!0},u!==void 0?u:r.createElement("span",{className:we(n.split(/\s+/).map(function(s){return"".concat(s,"-icon")}))},i))},ma=function(t,n,o,a,i){var l=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!1,c=arguments.length>6?arguments[6]:void 0,u=arguments.length>7?arguments[7]:void 0,s=Me.useMemo(function(){if(mt(a)==="object")return a.clearIcon;if(i)return i},[a,i]),f=Me.useMemo(function(){return!!(!l&&a&&(o.length||c)&&!(u==="combobox"&&c===""))},[a,l,o.length,c,u]);return{allowClear:f,clearIcon:Me.createElement(Zt,{className:"".concat(t,"-clear"),onMouseDown:n,customizeIcon:s},"×")}},ur=r.createContext(null);function ga(){return r.useContext(ur)}function pa(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:10,t=r.useState(!1),n=B(t,2),o=n[0],a=n[1],i=r.useRef(null),l=function(){window.clearTimeout(i.current)};r.useEffect(function(){return l},[]);var c=function(s,f){l(),i.current=window.setTimeout(function(){a(s),f&&f()},e)};return[o,c,l]}function dr(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:250,t=r.useRef(null),n=r.useRef(null);r.useEffect(function(){return function(){window.clearTimeout(n.current)}},[]);function o(a){(a||t.current===null)&&(t.current=a),window.clearTimeout(n.current),n.current=window.setTimeout(function(){t.current=null},e)}return[function(){return t.current},o]}function ha(e,t,n,o){var a=r.useRef(null);a.current={open:t,triggerOpen:n,customizedTrigger:o},r.useEffect(function(){function i(l){var c;if(!((c=a.current)!==null&&c!==void 0&&c.customizedTrigger)){var u=l.target;u.shadowRoot&&l.composed&&(u=l.composedPath()[0]||u),a.current.open&&e().filter(function(s){return s}).every(function(s){return!s.contains(u)&&s!==u})&&a.current.triggerOpen(!1)}}return window.addEventListener("mousedown",i),function(){return window.removeEventListener("mousedown",i)}},[])}function Sa(e){return e&&![ae.ESC,ae.SHIFT,ae.BACKSPACE,ae.TAB,ae.WIN_KEY,ae.ALT,ae.META,ae.WIN_KEY_RIGHT,ae.CTRL,ae.SEMICOLON,ae.EQUALS,ae.CAPS_LOCK,ae.CONTEXT_MENU,ae.F1,ae.F2,ae.F3,ae.F4,ae.F5,ae.F6,ae.F7,ae.F8,ae.F9,ae.F10,ae.F11,ae.F12].includes(e)}var ba=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],Nt=void 0;function Ca(e,t){var n=e.prefixCls,o=e.invalidate,a=e.item,i=e.renderItem,l=e.responsive,c=e.responsiveDisabled,u=e.registerSize,s=e.itemKey,f=e.className,g=e.style,p=e.children,d=e.display,m=e.order,v=e.component,h=v===void 0?"div":v,b=Je(e,ba),S=l&&!d;function R(x){u(s,x)}r.useEffect(function(){return function(){R(null)}},[]);var C=i&&a!==Nt?i(a,{index:m}):p,N;o||(N={opacity:S?0:1,height:S?0:Nt,overflowY:S?"hidden":Nt,order:l?m:Nt,pointerEvents:S?"none":Nt,position:S?"absolute":Nt});var w={};S&&(w["aria-hidden"]=!0);var E=r.createElement(h,Re({className:we(!o&&n,f),style:T(T({},N),g)},w,b,{ref:t}),C);return l&&(E=r.createElement(Qt,{onResize:function(y){var $=y.offsetWidth;R($)},disabled:c},E)),E}var Ht=r.forwardRef(Ca);Ht.displayName="Item";function ya(e){if(typeof MessageChannel>"u")vt(e);else{var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(void 0)}}function wa(){var e=r.useRef(null),t=function(o){e.current||(e.current=[],ya(function(){Bt.unstable_batchedUpdates(function(){e.current.forEach(function(a){a()}),e.current=null})})),e.current.push(o)};return t}function Tt(e,t){var n=r.useState(t),o=B(n,2),a=o[0],i=o[1],l=Gt(function(c){e(function(){i(c)})});return[a,l]}var qt=Me.createContext(null),Ea=["component"],Ra=["className"],xa=["className"],Ia=function(t,n){var o=r.useContext(qt);if(!o){var a=t.component,i=a===void 0?"div":a,l=Je(t,Ea);return r.createElement(i,Re({},l,{ref:n}))}var c=o.className,u=Je(o,Ra),s=t.className,f=Je(t,xa);return r.createElement(qt.Provider,{value:null},r.createElement(Ht,Re({ref:n,className:we(c,s)},u,f)))},fr=r.forwardRef(Ia);fr.displayName="RawItem";var $a=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],vr="responsive",mr="invalidate";function Oa(e){return"+ ".concat(e.length," ...")}function Ma(e,t){var n=e.prefixCls,o=n===void 0?"rc-overflow":n,a=e.data,i=a===void 0?[]:a,l=e.renderItem,c=e.renderRawItem,u=e.itemKey,s=e.itemWidth,f=s===void 0?10:s,g=e.ssr,p=e.style,d=e.className,m=e.maxCount,v=e.renderRest,h=e.renderRawRest,b=e.suffix,S=e.component,R=S===void 0?"div":S,C=e.itemComponent,N=e.onVisibleChange,w=Je(e,$a),E=g==="full",x=wa(),y=Tt(x,null),$=B(y,2),O=$[0],F=$[1],L=O||0,V=Tt(x,new Map),W=B(V,2),_=W[0],Y=W[1],J=Tt(x,0),oe=B(J,2),A=oe[0],P=oe[1],q=Tt(x,0),Q=B(q,2),k=Q[0],D=Q[1],ie=Tt(x,0),ce=B(ie,2),he=ce[0],me=ce[1],Ce=r.useState(null),H=B(Ce,2),M=H[0],z=H[1],te=r.useState(null),I=B(te,2),U=I[0],de=I[1],ee=r.useMemo(function(){return U===null&&E?Number.MAX_SAFE_INTEGER:U||0},[U,O]),fe=r.useState(!1),Se=B(fe,2),He=Se[0],De=Se[1],ze="".concat(o,"-item"),ye=Math.max(A,k),Ne=m===vr,Te=i.length&&Ne,$e=m===mr,Be=Te||typeof m=="number"&&i.length>m,Ee=r.useMemo(function(){var ne=i;return Te?O===null&&E?ne=i:ne=i.slice(0,Math.min(i.length,L/f)):typeof m=="number"&&(ne=i.slice(0,m)),ne},[i,f,O,m,Te]),Oe=r.useMemo(function(){return Te?i.slice(ee+1):i.slice(Ee.length)},[i,Ee,Te,ee]),Le=r.useCallback(function(ne,ge){var le;return typeof u=="function"?u(ne):(le=u&&(ne==null?void 0:ne[u]))!==null&&le!==void 0?le:ge},[u]),ot=r.useCallback(l||function(ne){return ne},[l]);function Ae(ne,ge,le){U===ne&&(ge===void 0||ge===M)||(de(ne),le||(De(ne<i.length-1),N==null||N(ne)),ge!==void 0&&z(ge))}function qe(ne,ge){F(ge.clientWidth)}function _e(ne,ge){Y(function(le){var Ve=new Map(le);return ge===null?Ve.delete(ne):Ve.set(ne,ge),Ve})}function bt(ne,ge){D(ge),P(k)}function Ge(ne,ge){me(ge)}function nt(ne){return _.get(Le(Ee[ne],ne))}ht(function(){if(L&&typeof ye=="number"&&Ee){var ne=he,ge=Ee.length,le=ge-1;if(!ge){Ae(0,null);return}for(var Ve=0;Ve<ge;Ve+=1){var Xe=nt(Ve);if(E&&(Xe=Xe||0),Xe===void 0){Ae(Ve-1,void 0,!0);break}if(ne+=Xe,le===0&&ne<=L||Ve===le-1&&ne+nt(le)<=L){Ae(le,null);break}else if(ne+ye>L){Ae(Ve-1,ne-Xe-he+k);break}}b&&nt(0)+he>L&&z(null)}},[L,_,k,he,Le,Ee]);var Fe=He&&!!Oe.length,gt={};M!==null&&Te&&(gt={position:"absolute",left:M,top:0});var Ye={prefixCls:ze,responsive:Te,component:C,invalidate:$e},at=c?function(ne,ge){var le=Le(ne,ge);return r.createElement(qt.Provider,{key:le,value:T(T({},Ye),{},{order:ge,item:ne,itemKey:le,registerSize:_e,display:ge<=ee})},c(ne,ge))}:function(ne,ge){var le=Le(ne,ge);return r.createElement(Ht,Re({},Ye,{order:ge,key:le,item:ne,renderItem:ot,itemKey:le,registerSize:_e,display:ge<=ee}))},it={order:Fe?ee:Number.MAX_SAFE_INTEGER,className:"".concat(ze,"-rest"),registerSize:bt,display:Fe},lt=v||Oa,rt=h?r.createElement(qt.Provider,{value:T(T({},Ye),it)},h(Oe)):r.createElement(Ht,Re({},Ye,it),typeof lt=="function"?lt(Oe):lt),Qe=r.createElement(R,Re({className:we(!$e&&o,d),style:p,ref:t},w),Ee.map(at),Be?rt:null,b&&r.createElement(Ht,Re({},Ye,{responsive:Ne,responsiveDisabled:!Te,order:ee,className:"".concat(ze,"-suffix"),registerSize:Ge,display:!0,style:gt}),b));return Ne?r.createElement(Qt,{onResize:qe,disabled:!Te},Qe):Qe}var Ft=r.forwardRef(Ma);Ft.displayName="Overflow";Ft.Item=fr;Ft.RESPONSIVE=vr;Ft.INVALIDATE=mr;function Na(e,t,n){var o=T(T({},e),t);return Object.keys(t).forEach(function(a){var i=t[a];typeof i=="function"&&(o[a]=function(){for(var l,c=arguments.length,u=new Array(c),s=0;s<c;s++)u[s]=arguments[s];return i.apply(void 0,u),(l=e[a])===null||l===void 0?void 0:l.call.apply(l,[e].concat(u))})}),o}var Pa=["prefixCls","id","inputElement","autoFocus","autoComplete","editable","activeDescendantId","value","open","attrs"],Da=function(t,n){var o=t.prefixCls,a=t.id,i=t.inputElement,l=t.autoFocus,c=t.autoComplete,u=t.editable,s=t.activeDescendantId,f=t.value,g=t.open,p=t.attrs,d=Je(t,Pa),m=i||r.createElement("input",null),v=m,h=v.ref,b=v.props;return Kr(!("maxLength"in m.props)),m=r.cloneElement(m,T(T(T({type:"search"},Na(d,b)),{},{id:a,ref:Ur(n,h),autoComplete:c||"off",autoFocus:l,className:we("".concat(o,"-selection-search-input"),b==null?void 0:b.className),role:"combobox","aria-expanded":g||!1,"aria-haspopup":"listbox","aria-owns":"".concat(a,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(a,"_list"),"aria-activedescendant":g?s:void 0},p),{},{value:u?f:"",readOnly:!u,unselectable:u?null:"on",style:T(T({},b.style),{},{opacity:u?null:0})})),m},gr=r.forwardRef(Da);function pr(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}var _a=typeof window<"u"&&window.document&&window.document.documentElement,za=_a;function Ta(e){return e!=null}function Ha(e){return!e&&e!==0}function Bn(e){return["string","number"].includes(mt(e))}function hr(e){var t=void 0;return e&&(Bn(e.title)?t=e.title.toString():Bn(e.label)&&(t=e.label.toString())),t}function Ba(e,t){za?r.useLayoutEffect(e,t):r.useEffect(e,t)}function La(e){var t;return(t=e.key)!==null&&t!==void 0?t:e.value}var Ln=function(t){t.preventDefault(),t.stopPropagation()},Aa=function(t){var n=t.id,o=t.prefixCls,a=t.values,i=t.open,l=t.searchValue,c=t.autoClearSearchValue,u=t.inputRef,s=t.placeholder,f=t.disabled,g=t.mode,p=t.showSearch,d=t.autoFocus,m=t.autoComplete,v=t.activeDescendantId,h=t.tabIndex,b=t.removeIcon,S=t.maxTagCount,R=t.maxTagTextLength,C=t.maxTagPlaceholder,N=C===void 0?function(te){return"+ ".concat(te.length," ...")}:C,w=t.tagRender,E=t.onToggleOpen,x=t.onRemove,y=t.onInputChange,$=t.onInputPaste,O=t.onInputKeyDown,F=t.onInputMouseDown,L=t.onInputCompositionStart,V=t.onInputCompositionEnd,W=t.onInputBlur,_=r.useRef(null),Y=r.useState(0),J=B(Y,2),oe=J[0],A=J[1],P=r.useState(!1),q=B(P,2),Q=q[0],k=q[1],D="".concat(o,"-selection"),ie=i||g==="multiple"&&c===!1||g==="tags"?l:"",ce=g==="tags"||g==="multiple"&&c===!1||p&&(i||Q);Ba(function(){A(_.current.scrollWidth)},[ie]);var he=function(I,U,de,ee,fe){return r.createElement("span",{title:hr(I),className:we("".concat(D,"-item"),ve({},"".concat(D,"-item-disabled"),de))},r.createElement("span",{className:"".concat(D,"-item-content")},U),ee&&r.createElement(Zt,{className:"".concat(D,"-item-remove"),onMouseDown:Ln,onClick:fe,customizeIcon:b},"×"))},me=function(I,U,de,ee,fe,Se){var He=function(ze){Ln(ze),E(!i)};return r.createElement("span",{onMouseDown:He},w({label:U,value:I,disabled:de,closable:ee,onClose:fe,isMaxTag:!!Se}))},Ce=function(I){var U=I.disabled,de=I.label,ee=I.value,fe=!f&&!U,Se=de;if(typeof R=="number"&&(typeof de=="string"||typeof de=="number")){var He=String(Se);He.length>R&&(Se="".concat(He.slice(0,R),"..."))}var De=function(ye){ye&&ye.stopPropagation(),x(I)};return typeof w=="function"?me(ee,Se,U,fe,De):he(I,Se,U,fe,De)},H=function(I){if(!a.length)return null;var U=typeof N=="function"?N(I):N;return typeof w=="function"?me(void 0,U,!1,!1,void 0,!0):he({title:U},U,!1)},M=r.createElement("div",{className:"".concat(D,"-search"),style:{width:oe},onFocus:function(){k(!0)},onBlur:function(){k(!1)}},r.createElement(gr,{ref:u,open:i,prefixCls:o,id:n,inputElement:null,disabled:f,autoFocus:d,autoComplete:m,editable:ce,activeDescendantId:v,value:ie,onKeyDown:O,onMouseDown:F,onChange:y,onPaste:$,onCompositionStart:L,onCompositionEnd:V,onBlur:W,tabIndex:h,attrs:It(t,!0)}),r.createElement("span",{ref:_,className:"".concat(D,"-search-mirror"),"aria-hidden":!0},ie," ")),z=r.createElement(Ft,{prefixCls:"".concat(D,"-overflow"),data:a,renderItem:Ce,renderRest:H,suffix:M,itemKey:La,maxCount:S});return r.createElement("span",{className:"".concat(D,"-wrap")},z,!a.length&&!ie&&r.createElement("span",{className:"".concat(D,"-placeholder")},s))},Fa=function(t){var n=t.inputElement,o=t.prefixCls,a=t.id,i=t.inputRef,l=t.disabled,c=t.autoFocus,u=t.autoComplete,s=t.activeDescendantId,f=t.mode,g=t.open,p=t.values,d=t.placeholder,m=t.tabIndex,v=t.showSearch,h=t.searchValue,b=t.activeValue,S=t.maxLength,R=t.onInputKeyDown,C=t.onInputMouseDown,N=t.onInputChange,w=t.onInputPaste,E=t.onInputCompositionStart,x=t.onInputCompositionEnd,y=t.onInputBlur,$=t.title,O=r.useState(!1),F=B(O,2),L=F[0],V=F[1],W=f==="combobox",_=W||v,Y=p[0],J=h||"";W&&b&&!L&&(J=b),r.useEffect(function(){W&&V(!1)},[W,b]);var oe=f!=="combobox"&&!g&&!v?!1:!!J,A=$===void 0?hr(Y):$,P=r.useMemo(function(){return Y?null:r.createElement("span",{className:"".concat(o,"-selection-placeholder"),style:oe?{visibility:"hidden"}:void 0},d)},[Y,oe,d,o]);return r.createElement("span",{className:"".concat(o,"-selection-wrap")},r.createElement("span",{className:"".concat(o,"-selection-search")},r.createElement(gr,{ref:i,prefixCls:o,id:a,open:g,inputElement:n,disabled:l,autoFocus:c,autoComplete:u,editable:_,activeDescendantId:s,value:J,onKeyDown:R,onMouseDown:C,onChange:function(Q){V(!0),N(Q)},onPaste:w,onCompositionStart:E,onCompositionEnd:x,onBlur:y,tabIndex:m,attrs:It(t,!0),maxLength:W?S:void 0})),!W&&Y?r.createElement("span",{className:"".concat(o,"-selection-item"),title:A,style:oe?{visibility:"hidden"}:void 0},Y.label):null,P)},Va=function(t,n){var o=r.useRef(null),a=r.useRef(!1),i=t.prefixCls,l=t.open,c=t.mode,u=t.showSearch,s=t.tokenWithEnter,f=t.disabled,g=t.prefix,p=t.autoClearSearchValue,d=t.onSearch,m=t.onSearchSubmit,v=t.onToggleOpen,h=t.onInputKeyDown,b=t.onInputBlur,S=t.domRef;r.useImperativeHandle(n,function(){return{focus:function(A){o.current.focus(A)},blur:function(){o.current.blur()}}});var R=dr(0),C=B(R,2),N=C[0],w=C[1],E=function(A){var P=A.which,q=o.current instanceof HTMLTextAreaElement;!q&&l&&(P===ae.UP||P===ae.DOWN)&&A.preventDefault(),h&&h(A),P===ae.ENTER&&c==="tags"&&!a.current&&!l&&(m==null||m(A.target.value)),!(q&&!l&&~[ae.UP,ae.DOWN,ae.LEFT,ae.RIGHT].indexOf(P))&&Sa(P)&&v(!0)},x=function(){w(!0)},y=r.useRef(null),$=function(A){d(A,!0,a.current)!==!1&&v(!0)},O=function(){a.current=!0},F=function(A){a.current=!1,c!=="combobox"&&$(A.target.value)},L=function(A){var P=A.target.value;if(s&&y.current&&/[\r\n]/.test(y.current)){var q=y.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");P=P.replace(q,y.current)}y.current=null,$(P)},V=function(A){var P=A.clipboardData,q=P==null?void 0:P.getData("text");y.current=q||""},W=function(A){var P=A.target;if(P!==o.current){var q=document.body.style.msTouchAction!==void 0;q?setTimeout(function(){o.current.focus()}):o.current.focus()}},_=function(A){var P=N();A.target!==o.current&&!P&&!(c==="combobox"&&f)&&A.preventDefault(),(c!=="combobox"&&(!u||!P)||!l)&&(l&&p!==!1&&d("",!0,!1),v())},Y={inputRef:o,onInputKeyDown:E,onInputMouseDown:x,onInputChange:L,onInputPaste:V,onInputCompositionStart:O,onInputCompositionEnd:F,onInputBlur:b},J=c==="multiple"||c==="tags"?r.createElement(Aa,Re({},t,Y)):r.createElement(Fa,Re({},t,Y));return r.createElement("div",{ref:S,className:"".concat(i,"-selector"),onClick:W,onMouseDown:_},g&&r.createElement("div",{className:"".concat(i,"-prefix")},g),J)},Wa=r.forwardRef(Va),ja=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],Ka=function(t){var n=t===!0?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"}}},Ua=function(t,n){var o=t.prefixCls;t.disabled;var a=t.visible,i=t.children,l=t.popupElement,c=t.animation,u=t.transitionName,s=t.dropdownStyle,f=t.dropdownClassName,g=t.direction,p=g===void 0?"ltr":g,d=t.placement,m=t.builtinPlacements,v=t.dropdownMatchSelectWidth,h=t.dropdownRender,b=t.dropdownAlign,S=t.getPopupContainer,R=t.empty,C=t.getTriggerDOMNode,N=t.onPopupVisibleChange,w=t.onPopupMouseEnter,E=Je(t,ja),x="".concat(o,"-dropdown"),y=l;h&&(y=h(l));var $=r.useMemo(function(){return m||Ka(v)},[m,v]),O=c?"".concat(x,"-").concat(c):u,F=typeof v=="number",L=r.useMemo(function(){return F?null:v===!1?"minWidth":"width"},[v,F]),V=s;F&&(V=T(T({},V),{},{width:v}));var W=r.useRef(null);return r.useImperativeHandle(n,function(){return{getPopupElement:function(){var Y;return(Y=W.current)===null||Y===void 0?void 0:Y.popupElement}}}),r.createElement(Gr,Re({},E,{showAction:N?["click"]:[],hideAction:N?["click"]:[],popupPlacement:d||(p==="rtl"?"bottomRight":"bottomLeft"),builtinPlacements:$,prefixCls:x,popupTransitionName:O,popup:r.createElement("div",{onMouseEnter:w},y),ref:W,stretch:L,popupAlign:b,popupVisible:a,getPopupContainer:S,popupClassName:we(f,ve({},"".concat(x,"-empty"),R)),popupStyle:V,getTriggerDOMNode:C,onPopupVisibleChange:N}),i)},Ga=r.forwardRef(Ua);function An(e,t){var n=e.key,o;return"value"in e&&(o=e.value),n??(o!==void 0?o:"rc-index-key-".concat(t))}function on(e){return typeof e<"u"&&!Number.isNaN(e)}function Sr(e,t){var n=e||{},o=n.label,a=n.value,i=n.options,l=n.groupLabel,c=o||(t?"children":"label");return{label:c,value:a||"value",options:i||"options",groupLabel:l||c}}function Xa(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.fieldNames,o=t.childrenAsData,a=[],i=Sr(n,!1),l=i.label,c=i.value,u=i.options,s=i.groupLabel;function f(g,p){Array.isArray(g)&&g.forEach(function(d){if(p||!(u in d)){var m=d[c];a.push({key:An(d,a.length),groupOption:p,data:d,label:d[l],value:m})}else{var v=d[s];v===void 0&&o&&(v=d.label),a.push({key:An(d,a.length),group:!0,data:d,label:v}),f(d[u],!0)}})}return f(e,!1),a}function an(e){var t=T({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return Xr(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var qa=function(t,n,o){if(!n||!n.length)return null;var a=!1,i=function c(u,s){var f=qr(s),g=f[0],p=f.slice(1);if(!g)return[u];var d=u.split(g);return a=a||d.length>1,d.reduce(function(m,v){return[].concat(je(m),je(c(v,p)))},[]).filter(Boolean)},l=i(t,n);return a?typeof o<"u"?l.slice(0,o):l:null},gn=r.createContext(null);function Ya(e){var t=e.visible,n=e.values;if(!t)return null;var o=50;return r.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,o).map(function(a){var i=a.label,l=a.value;return["number","string"].includes(mt(i))?i:l}).join(", ")),n.length>o?", ...":null)}var Qa=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],Za=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],ln=function(t){return t==="tags"||t==="multiple"},Ja=r.forwardRef(function(e,t){var n,o=e.id,a=e.prefixCls,i=e.className,l=e.showSearch,c=e.tagRender,u=e.direction,s=e.omitDomProps,f=e.displayValues,g=e.onDisplayValuesChange,p=e.emptyOptions,d=e.notFoundContent,m=d===void 0?"Not Found":d,v=e.onClear,h=e.mode,b=e.disabled,S=e.loading,R=e.getInputElement,C=e.getRawInputElement,N=e.open,w=e.defaultOpen,E=e.onDropdownVisibleChange,x=e.activeValue,y=e.onActiveValueChange,$=e.activeDescendantId,O=e.searchValue,F=e.autoClearSearchValue,L=e.onSearch,V=e.onSearchSplit,W=e.tokenSeparators,_=e.allowClear,Y=e.prefix,J=e.suffixIcon,oe=e.clearIcon,A=e.OptionList,P=e.animation,q=e.transitionName,Q=e.dropdownStyle,k=e.dropdownClassName,D=e.dropdownMatchSelectWidth,ie=e.dropdownRender,ce=e.dropdownAlign,he=e.placement,me=e.builtinPlacements,Ce=e.getPopupContainer,H=e.showAction,M=H===void 0?[]:H,z=e.onFocus,te=e.onBlur,I=e.onKeyUp,U=e.onKeyDown,de=e.onMouseDown,ee=Je(e,Qa),fe=ln(h),Se=(l!==void 0?l:fe)||h==="combobox",He=T({},ee);Za.forEach(function(xe){delete He[xe]}),s==null||s.forEach(function(xe){delete He[xe]});var De=r.useState(!1),ze=B(De,2),ye=ze[0],Ne=ze[1];r.useEffect(function(){Ne(Yr())},[]);var Te=r.useRef(null),$e=r.useRef(null),Be=r.useRef(null),Ee=r.useRef(null),Oe=r.useRef(null),Le=r.useRef(!1),ot=pa(),Ae=B(ot,3),qe=Ae[0],_e=Ae[1],bt=Ae[2];r.useImperativeHandle(t,function(){var xe,ue;return{focus:(xe=Ee.current)===null||xe===void 0?void 0:xe.focus,blur:(ue=Ee.current)===null||ue===void 0?void 0:ue.blur,scrollTo:function(tt){var Ue;return(Ue=Oe.current)===null||Ue===void 0?void 0:Ue.scrollTo(tt)},nativeElement:Te.current||$e.current}});var Ge=r.useMemo(function(){var xe;if(h!=="combobox")return O;var ue=(xe=f[0])===null||xe===void 0?void 0:xe.value;return typeof ue=="string"||typeof ue=="number"?String(ue):""},[O,h,f]),nt=h==="combobox"&&typeof R=="function"&&R()||null,Fe=typeof C=="function"&&C(),gt=sn($e,Fe==null||(n=Fe.props)===null||n===void 0?void 0:n.ref),Ye=r.useState(!1),at=B(Ye,2),it=at[0],lt=at[1];ht(function(){lt(!0)},[]);var rt=Xt(!1,{defaultValue:w,value:N}),Qe=B(rt,2),ne=Qe[0],ge=Qe[1],le=it?ne:!1,Ve=!m&&p;(b||Ve&&le&&h==="combobox")&&(le=!1);var Xe=Ve?!1:le,j=r.useCallback(function(xe){var ue=xe!==void 0?xe:!le;b||(ge(ue),le!==ue&&(E==null||E(ue)))},[b,le,ge,E]),re=r.useMemo(function(){return(W||[]).some(function(xe){return[`
`,`\r
`].includes(xe)})},[W]),Z=r.useContext(gn)||{},G=Z.maxCount,be=Z.rawValues,Pe=function(ue,et,tt){if(!(fe&&on(G)&&(be==null?void 0:be.size)>=G)){var Ue=!0,Ze=ue;y==null||y(null);var yt=qa(ue,W,on(G)?G-be.size:void 0),pt=tt?null:yt;return h!=="combobox"&&pt&&(Ze="",V==null||V(pt),j(!1),Ue=!1),L&&Ge!==Ze&&L(Ze,{source:et?"typing":"effect"}),Ue}},ct=function(ue){!ue||!ue.trim()||L(ue,{source:"submit"})};r.useEffect(function(){!le&&!fe&&h!=="combobox"&&Pe("",!1,!1)},[le]),r.useEffect(function(){ne&&b&&ge(!1),b&&!Le.current&&_e(!1)},[b]);var ke=dr(),ut=B(ke,2),We=ut[0],dt=ut[1],wt=r.useRef(!1),Pt=function(ue){var et=We(),tt=ue.key,Ue=tt==="Enter";if(Ue&&(h!=="combobox"&&ue.preventDefault(),le||j(!0)),dt(!!Ge),tt==="Backspace"&&!et&&fe&&!Ge&&f.length){for(var Ze=je(f),yt=null,pt=Ze.length-1;pt>=0;pt-=1){var Et=Ze[pt];if(!Et.disabled){Ze.splice(pt,1),yt=Et;break}}yt&&g(Ze,{type:"remove",values:[yt]})}for(var Mt=arguments.length,Rt=new Array(Mt>1?Mt-1:0),jt=1;jt<Mt;jt++)Rt[jt-1]=arguments[jt];if(le&&(!Ue||!wt.current)){var Kt;Ue&&(wt.current=!0),(Kt=Oe.current)===null||Kt===void 0||Kt.onKeyDown.apply(Kt,[ue].concat(Rt))}U==null||U.apply(void 0,[ue].concat(Rt))},Wt=function(ue){for(var et=arguments.length,tt=new Array(et>1?et-1:0),Ue=1;Ue<et;Ue++)tt[Ue-1]=arguments[Ue];if(le){var Ze;(Ze=Oe.current)===null||Ze===void 0||Ze.onKeyUp.apply(Ze,[ue].concat(tt))}ue.key==="Enter"&&(wt.current=!1),I==null||I.apply(void 0,[ue].concat(tt))},ft=function(ue){var et=f.filter(function(tt){return tt!==ue});g(et,{type:"remove",values:[ue]})},st=function(){wt.current=!1},X=r.useRef(!1),K=function(){_e(!0),b||(z&&!X.current&&z.apply(void 0,arguments),M.includes("focus")&&j(!0)),X.current=!0},pe=function(){Le.current=!0,_e(!1,function(){X.current=!1,Le.current=!1,j(!1)}),!b&&(Ge&&(h==="tags"?L(Ge,{source:"submit"}):h==="multiple"&&L("",{source:"blur"})),te&&te.apply(void 0,arguments))},Ie=[];r.useEffect(function(){return function(){Ie.forEach(function(xe){return clearTimeout(xe)}),Ie.splice(0,Ie.length)}},[]);var Ke=function(ue){var et,tt=ue.target,Ue=(et=Be.current)===null||et===void 0?void 0:et.getPopupElement();if(Ue&&Ue.contains(tt)){var Ze=setTimeout(function(){var Mt=Ie.indexOf(Ze);if(Mt!==-1&&Ie.splice(Mt,1),bt(),!ye&&!Ue.contains(document.activeElement)){var Rt;(Rt=Ee.current)===null||Rt===void 0||Rt.focus()}});Ie.push(Ze)}for(var yt=arguments.length,pt=new Array(yt>1?yt-1:0),Et=1;Et<yt;Et++)pt[Et-1]=arguments[Et];de==null||de.apply(void 0,[ue].concat(pt))},Ot=r.useState({}),Ct=B(Ot,2),Dt=Ct[1];function Jt(){Dt({})}var _t;Fe&&(_t=function(ue){j(ue)}),ha(function(){var xe;return[Te.current,(xe=Be.current)===null||xe===void 0?void 0:xe.getPopupElement()]},Xe,j,!!Fe);var zt=r.useMemo(function(){return T(T({},e),{},{notFoundContent:m,open:le,triggerOpen:Xe,id:o,showSearch:Se,multiple:fe,toggleOpen:j})},[e,m,Xe,le,o,Se,fe,j]),bn=!!J||S,Cn;bn&&(Cn=r.createElement(Zt,{className:we("".concat(a,"-arrow"),ve({},"".concat(a,"-arrow-loading"),S)),customizeIcon:J,customizeIconProps:{loading:S,searchValue:Ge,open:le,focused:qe,showSearch:Se}}));var Nr=function(){var ue;v==null||v(),(ue=Ee.current)===null||ue===void 0||ue.focus(),g([],{type:"clear",values:f}),Pe("",!1,!1)},yn=ma(a,Nr,f,_,oe,b,Ge,h),Pr=yn.allowClear,Dr=yn.clearIcon,_r=r.createElement(A,{ref:Oe}),zr=we(a,i,ve(ve(ve(ve(ve(ve(ve(ve(ve(ve({},"".concat(a,"-focused"),qe),"".concat(a,"-multiple"),fe),"".concat(a,"-single"),!fe),"".concat(a,"-allow-clear"),_),"".concat(a,"-show-arrow"),bn),"".concat(a,"-disabled"),b),"".concat(a,"-loading"),S),"".concat(a,"-open"),le),"".concat(a,"-customize-input"),nt),"".concat(a,"-show-search"),Se)),wn=r.createElement(Ga,{ref:Be,disabled:b,prefixCls:a,visible:Xe,popupElement:_r,animation:P,transitionName:q,dropdownStyle:Q,dropdownClassName:k,direction:u,dropdownMatchSelectWidth:D,dropdownRender:ie,dropdownAlign:ce,placement:he,builtinPlacements:me,getPopupContainer:Ce,empty:p,getTriggerDOMNode:function(ue){return $e.current||ue},onPopupVisibleChange:_t,onPopupMouseEnter:Jt},Fe?r.cloneElement(Fe,{ref:gt}):r.createElement(Wa,Re({},e,{domRef:$e,prefixCls:a,inputElement:nt,ref:Ee,id:o,prefix:Y,showSearch:Se,autoClearSearchValue:F,mode:h,activeDescendantId:$,tagRender:c,values:f,open:le,onToggleOpen:j,activeValue:x,searchValue:Ge,onSearch:Pe,onSearchSubmit:ct,onRemove:ft,tokenWithEnter:re,onInputBlur:st}))),kt;return Fe?kt=wn:kt=r.createElement("div",Re({className:zr},He,{ref:Te,onMouseDown:Ke,onKeyDown:Pt,onKeyUp:Wt,onFocus:K,onBlur:pe}),r.createElement(Ya,{visible:qe&&!le,values:f}),wn,Cn,Pr&&Dr),r.createElement(ur.Provider,{value:zt},kt)}),pn=function(){return null};pn.isSelectOptGroup=!0;var hn=function(){return null};hn.isSelectOption=!0;var br=r.forwardRef(function(e,t){var n=e.height,o=e.offsetY,a=e.offsetX,i=e.children,l=e.prefixCls,c=e.onInnerResize,u=e.innerProps,s=e.rtl,f=e.extra,g={},p={display:"flex",flexDirection:"column"};return o!==void 0&&(g={height:n,position:"relative",overflow:"hidden"},p=T(T({},p),{},ve(ve(ve(ve(ve({transform:"translateY(".concat(o,"px)")},s?"marginRight":"marginLeft",-a),"position","absolute"),"left",0),"right",0),"top",0))),r.createElement("div",{style:g},r.createElement(Qt,{onResize:function(m){var v=m.offsetHeight;v&&c&&c()}},r.createElement("div",Re({style:p,className:we(ve({},"".concat(l,"-holder-inner"),l)),ref:t},u),i,f)))});br.displayName="Filler";function ka(e){var t=e.children,n=e.setRef,o=r.useCallback(function(a){n(a)},[]);return r.cloneElement(t,{ref:o})}function ei(e,t,n,o,a,i,l,c){var u=c.getKey;return e.slice(t,n+1).map(function(s,f){var g=t+f,p=l(s,g,{style:{width:o},offsetX:a}),d=u(s);return r.createElement(ka,{key:d,setRef:function(v){return i(s,v)}},p)})}function ti(e,t,n){var o=e.length,a=t.length,i,l;if(o===0&&a===0)return null;o<a?(i=e,l=t):(i=t,l=e);var c={__EMPTY_ITEM__:!0};function u(m){return m!==void 0?n(m):c}for(var s=null,f=Math.abs(o-a)!==1,g=0;g<l.length;g+=1){var p=u(i[g]),d=u(l[g]);if(p!==d){s=g,f=f||p!==u(l[g+1]);break}}return s===null?null:{index:s,multiple:f}}function ni(e,t,n){var o=r.useState(e),a=B(o,2),i=a[0],l=a[1],c=r.useState(null),u=B(c,2),s=u[0],f=u[1];return r.useEffect(function(){var g=ti(i||[],e||[],t);(g==null?void 0:g.index)!==void 0&&f(e[g.index]),l(e)},[e]),[s]}var Fn=(typeof navigator>"u"?"undefined":mt(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const Cr=function(e,t,n,o){var a=r.useRef(!1),i=r.useRef(null);function l(){clearTimeout(i.current),a.current=!0,i.current=setTimeout(function(){a.current=!1},50)}var c=r.useRef({top:e,bottom:t,left:n,right:o});return c.current.top=e,c.current.bottom=t,c.current.left=n,c.current.right=o,function(u,s){var f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,g=u?s<0&&c.current.left||s>0&&c.current.right:s<0&&c.current.top||s>0&&c.current.bottom;return f&&g?(clearTimeout(i.current),a.current=!1):(!g||a.current)&&l(),!a.current&&g}};function ri(e,t,n,o,a,i,l){var c=r.useRef(0),u=r.useRef(null),s=r.useRef(null),f=r.useRef(!1),g=Cr(t,n,o,a);function p(S,R){if(vt.cancel(u.current),!g(!1,R)){var C=S;if(!C._virtualHandled)C._virtualHandled=!0;else return;c.current+=R,s.current=R,Fn||C.preventDefault(),u.current=vt(function(){var N=f.current?10:1;l(c.current*N,!1),c.current=0})}}function d(S,R){l(R,!0),Fn||S.preventDefault()}var m=r.useRef(null),v=r.useRef(null);function h(S){if(e){vt.cancel(v.current),v.current=vt(function(){m.current=null},2);var R=S.deltaX,C=S.deltaY,N=S.shiftKey,w=R,E=C;(m.current==="sx"||!m.current&&N&&C&&!R)&&(w=C,E=0,m.current="sx");var x=Math.abs(w),y=Math.abs(E);m.current===null&&(m.current=i&&x>y?"x":"y"),m.current==="y"?p(S,E):d(S,w)}}function b(S){e&&(f.current=S.detail===s.current)}return[h,b]}function oi(e,t,n,o){var a=r.useMemo(function(){return[new Map,[]]},[e,n.id,o]),i=B(a,2),l=i[0],c=i[1],u=function(f){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:f,p=l.get(f),d=l.get(g);if(p===void 0||d===void 0)for(var m=e.length,v=c.length;v<m;v+=1){var h,b=e[v],S=t(b);l.set(S,v);var R=(h=n.get(S))!==null&&h!==void 0?h:o;if(c[v]=(c[v-1]||0)+R,S===f&&(p=v),S===g&&(d=v),p!==void 0&&d!==void 0)break}return{top:c[p-1]||0,bottom:c[d]}};return u}var ai=function(){function e(){Zr(this,e),ve(this,"maps",void 0),ve(this,"id",0),ve(this,"diffRecords",new Map),this.maps=Object.create(null)}return Qr(e,[{key:"set",value:function(n,o){this.diffRecords.set(n,this.maps[n]),this.maps[n]=o,this.id+=1}},{key:"get",value:function(n){return this.maps[n]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function Vn(e){var t=parseFloat(e);return isNaN(t)?0:t}function ii(e,t,n){var o=r.useState(0),a=B(o,2),i=a[0],l=a[1],c=r.useRef(new Map),u=r.useRef(new ai),s=r.useRef(0);function f(){s.current+=1}function g(){var d=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;f();var m=function(){var b=!1;c.current.forEach(function(S,R){if(S&&S.offsetParent){var C=S.offsetHeight,N=getComputedStyle(S),w=N.marginTop,E=N.marginBottom,x=Vn(w),y=Vn(E),$=C+x+y;u.current.get(R)!==$&&(u.current.set(R,$),b=!0)}}),b&&l(function(S){return S+1})};if(d)m();else{s.current+=1;var v=s.current;Promise.resolve().then(function(){v===s.current&&m()})}}function p(d,m){var v=e(d);c.current.get(v),m?(c.current.set(v,m),g()):c.current.delete(v)}return r.useEffect(function(){return f},[]),[p,g,u.current,i]}var Wn=14/15;function li(e,t,n){var o=r.useRef(!1),a=r.useRef(0),i=r.useRef(0),l=r.useRef(null),c=r.useRef(null),u,s=function(d){if(o.current){var m=Math.ceil(d.touches[0].pageX),v=Math.ceil(d.touches[0].pageY),h=a.current-m,b=i.current-v,S=Math.abs(h)>Math.abs(b);S?a.current=m:i.current=v;var R=n(S,S?h:b,!1,d);R&&d.preventDefault(),clearInterval(c.current),R&&(c.current=setInterval(function(){S?h*=Wn:b*=Wn;var C=Math.floor(S?h:b);(!n(S,C,!0)||Math.abs(C)<=.1)&&clearInterval(c.current)},16))}},f=function(){o.current=!1,u()},g=function(d){u(),d.touches.length===1&&!o.current&&(o.current=!0,a.current=Math.ceil(d.touches[0].pageX),i.current=Math.ceil(d.touches[0].pageY),l.current=d.target,l.current.addEventListener("touchmove",s,{passive:!1}),l.current.addEventListener("touchend",f,{passive:!0}))};u=function(){l.current&&(l.current.removeEventListener("touchmove",s),l.current.removeEventListener("touchend",f))},ht(function(){return e&&t.current.addEventListener("touchstart",g,{passive:!0}),function(){var p;(p=t.current)===null||p===void 0||p.removeEventListener("touchstart",g),u(),clearInterval(c.current)}},[e])}function jn(e){return Math.floor(Math.pow(e,.5))}function cn(e,t){var n="touches"in e?e.touches[0]:e;return n[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}function ci(e,t,n){r.useEffect(function(){var o=t.current;if(e&&o){var a=!1,i,l,c=function(){vt.cancel(i)},u=function p(){c(),i=vt(function(){n(l),p()})},s=function(d){if(!(d.target.draggable||d.button!==0)){var m=d;m._virtualHandled||(m._virtualHandled=!0,a=!0)}},f=function(){a=!1,c()},g=function(d){if(a){var m=cn(d,!1),v=o.getBoundingClientRect(),h=v.top,b=v.bottom;if(m<=h){var S=h-m;l=-jn(S),u()}else if(m>=b){var R=m-b;l=jn(R),u()}else c()}};return o.addEventListener("mousedown",s),o.ownerDocument.addEventListener("mouseup",f),o.ownerDocument.addEventListener("mousemove",g),function(){o.removeEventListener("mousedown",s),o.ownerDocument.removeEventListener("mouseup",f),o.ownerDocument.removeEventListener("mousemove",g),c()}}},[e])}var si=10;function ui(e,t,n,o,a,i,l,c){var u=r.useRef(),s=r.useState(null),f=B(s,2),g=f[0],p=f[1];return ht(function(){if(g&&g.times<si){if(!e.current){p(function(oe){return T({},oe)});return}i();var d=g.targetAlign,m=g.originAlign,v=g.index,h=g.offset,b=e.current.clientHeight,S=!1,R=d,C=null;if(b){for(var N=d||m,w=0,E=0,x=0,y=Math.min(t.length-1,v),$=0;$<=y;$+=1){var O=a(t[$]);E=w;var F=n.get(O);x=E+(F===void 0?o:F),w=x}for(var L=N==="top"?h:b-h,V=y;V>=0;V-=1){var W=a(t[V]),_=n.get(W);if(_===void 0){S=!0;break}if(L-=_,L<=0)break}switch(N){case"top":C=E-h;break;case"bottom":C=x-b+h;break;default:{var Y=e.current.scrollTop,J=Y+b;E<Y?R="top":x>J&&(R="bottom")}}C!==null&&l(C),C!==g.lastTop&&(S=!0)}S&&p(T(T({},g),{},{times:g.times+1,targetAlign:R,lastTop:C}))}},[g,e.current]),function(d){if(d==null){c();return}if(vt.cancel(u.current),typeof d=="number")l(d);else if(d&&mt(d)==="object"){var m,v=d.align;"index"in d?m=d.index:m=t.findIndex(function(S){return a(S)===d.key});var h=d.offset,b=h===void 0?0:h;p({times:0,index:m,offset:b,originAlign:v})}}}var Kn=r.forwardRef(function(e,t){var n=e.prefixCls,o=e.rtl,a=e.scrollOffset,i=e.scrollRange,l=e.onStartMove,c=e.onStopMove,u=e.onScroll,s=e.horizontal,f=e.spinSize,g=e.containerSize,p=e.style,d=e.thumbStyle,m=e.showScrollBar,v=r.useState(!1),h=B(v,2),b=h[0],S=h[1],R=r.useState(null),C=B(R,2),N=C[0],w=C[1],E=r.useState(null),x=B(E,2),y=x[0],$=x[1],O=!o,F=r.useRef(),L=r.useRef(),V=r.useState(m),W=B(V,2),_=W[0],Y=W[1],J=r.useRef(),oe=function(){m===!0||m===!1||(clearTimeout(J.current),Y(!0),J.current=setTimeout(function(){Y(!1)},3e3))},A=i-g||0,P=g-f||0,q=r.useMemo(function(){if(a===0||A===0)return 0;var H=a/A;return H*P},[a,A,P]),Q=function(M){M.stopPropagation(),M.preventDefault()},k=r.useRef({top:q,dragging:b,pageY:N,startTop:y});k.current={top:q,dragging:b,pageY:N,startTop:y};var D=function(M){S(!0),w(cn(M,s)),$(k.current.top),l(),M.stopPropagation(),M.preventDefault()};r.useEffect(function(){var H=function(I){I.preventDefault()},M=F.current,z=L.current;return M.addEventListener("touchstart",H,{passive:!1}),z.addEventListener("touchstart",D,{passive:!1}),function(){M.removeEventListener("touchstart",H),z.removeEventListener("touchstart",D)}},[]);var ie=r.useRef();ie.current=A;var ce=r.useRef();ce.current=P,r.useEffect(function(){if(b){var H,M=function(I){var U=k.current,de=U.dragging,ee=U.pageY,fe=U.startTop;vt.cancel(H);var Se=F.current.getBoundingClientRect(),He=g/(s?Se.width:Se.height);if(de){var De=(cn(I,s)-ee)*He,ze=fe;!O&&s?ze-=De:ze+=De;var ye=ie.current,Ne=ce.current,Te=Ne?ze/Ne:0,$e=Math.ceil(Te*ye);$e=Math.max($e,0),$e=Math.min($e,ye),H=vt(function(){u($e,s)})}},z=function(){S(!1),c()};return window.addEventListener("mousemove",M,{passive:!0}),window.addEventListener("touchmove",M,{passive:!0}),window.addEventListener("mouseup",z,{passive:!0}),window.addEventListener("touchend",z,{passive:!0}),function(){window.removeEventListener("mousemove",M),window.removeEventListener("touchmove",M),window.removeEventListener("mouseup",z),window.removeEventListener("touchend",z),vt.cancel(H)}}},[b]),r.useEffect(function(){return oe(),function(){clearTimeout(J.current)}},[a]),r.useImperativeHandle(t,function(){return{delayHidden:oe}});var he="".concat(n,"-scrollbar"),me={position:"absolute",visibility:_?null:"hidden"},Ce={position:"absolute",borderRadius:99,background:"var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",cursor:"pointer",userSelect:"none"};return s?(Object.assign(me,{height:8,left:0,right:0,bottom:0}),Object.assign(Ce,ve({height:"100%",width:f},O?"left":"right",q))):(Object.assign(me,ve({width:8,top:0,bottom:0},O?"right":"left",0)),Object.assign(Ce,{width:"100%",height:f,top:q})),r.createElement("div",{ref:F,className:we(he,ve(ve(ve({},"".concat(he,"-horizontal"),s),"".concat(he,"-vertical"),!s),"".concat(he,"-visible"),_)),style:T(T({},me),p),onMouseDown:Q,onMouseMove:oe},r.createElement("div",{ref:L,className:we("".concat(he,"-thumb"),ve({},"".concat(he,"-thumb-moving"),b)),style:T(T({},Ce),d),onMouseDown:D}))}),di=20;function Un(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=e/t*e;return isNaN(n)&&(n=0),n=Math.max(n,di),Math.floor(n)}var fi=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],vi=[],mi={overflowY:"auto",overflowAnchor:"none"};function gi(e,t){var n=e.prefixCls,o=n===void 0?"rc-virtual-list":n,a=e.className,i=e.height,l=e.itemHeight,c=e.fullHeight,u=c===void 0?!0:c,s=e.style,f=e.data,g=e.children,p=e.itemKey,d=e.virtual,m=e.direction,v=e.scrollWidth,h=e.component,b=h===void 0?"div":h,S=e.onScroll,R=e.onVirtualScroll,C=e.onVisibleChange,N=e.innerProps,w=e.extraRender,E=e.styles,x=e.showScrollBar,y=x===void 0?"optional":x,$=Je(e,fi),O=r.useCallback(function(X){return typeof p=="function"?p(X):X==null?void 0:X[p]},[p]),F=ii(O),L=B(F,4),V=L[0],W=L[1],_=L[2],Y=L[3],J=!!(d!==!1&&i&&l),oe=r.useMemo(function(){return Object.values(_.maps).reduce(function(X,K){return X+K},0)},[_.id,_.maps]),A=J&&f&&(Math.max(l*f.length,oe)>i||!!v),P=m==="rtl",q=we(o,ve({},"".concat(o,"-rtl"),P),a),Q=f||vi,k=r.useRef(),D=r.useRef(),ie=r.useRef(),ce=r.useState(0),he=B(ce,2),me=he[0],Ce=he[1],H=r.useState(0),M=B(H,2),z=M[0],te=M[1],I=r.useState(!1),U=B(I,2),de=U[0],ee=U[1],fe=function(){ee(!0)},Se=function(){ee(!1)},He={getKey:O};function De(X){Ce(function(K){var pe;typeof X=="function"?pe=X(K):pe=X;var Ie=lt(pe);return k.current.scrollTop=Ie,Ie})}var ze=r.useRef({start:0,end:Q.length}),ye=r.useRef(),Ne=ni(Q,O),Te=B(Ne,1),$e=Te[0];ye.current=$e;var Be=r.useMemo(function(){if(!J)return{scrollHeight:void 0,start:0,end:Q.length-1,offset:void 0};if(!A){var X;return{scrollHeight:((X=D.current)===null||X===void 0?void 0:X.offsetHeight)||0,start:0,end:Q.length-1,offset:void 0}}for(var K=0,pe,Ie,Ke,Ot=Q.length,Ct=0;Ct<Ot;Ct+=1){var Dt=Q[Ct],Jt=O(Dt),_t=_.get(Jt),zt=K+(_t===void 0?l:_t);zt>=me&&pe===void 0&&(pe=Ct,Ie=K),zt>me+i&&Ke===void 0&&(Ke=Ct),K=zt}return pe===void 0&&(pe=0,Ie=0,Ke=Math.ceil(i/l)),Ke===void 0&&(Ke=Q.length-1),Ke=Math.min(Ke+1,Q.length-1),{scrollHeight:K,start:pe,end:Ke,offset:Ie}},[A,J,me,Q,Y,i]),Ee=Be.scrollHeight,Oe=Be.start,Le=Be.end,ot=Be.offset;ze.current.start=Oe,ze.current.end=Le,r.useLayoutEffect(function(){var X=_.getRecord();if(X.size===1){var K=Array.from(X.keys())[0],pe=X.get(K),Ie=Q[Oe];if(Ie&&pe===void 0){var Ke=O(Ie);if(Ke===K){var Ot=_.get(K),Ct=Ot-l;De(function(Dt){return Dt+Ct})}}}_.resetRecord()},[Ee]);var Ae=r.useState({width:0,height:i}),qe=B(Ae,2),_e=qe[0],bt=qe[1],Ge=function(K){bt({width:K.offsetWidth,height:K.offsetHeight})},nt=r.useRef(),Fe=r.useRef(),gt=r.useMemo(function(){return Un(_e.width,v)},[_e.width,v]),Ye=r.useMemo(function(){return Un(_e.height,Ee)},[_e.height,Ee]),at=Ee-i,it=r.useRef(at);it.current=at;function lt(X){var K=X;return Number.isNaN(it.current)||(K=Math.min(K,it.current)),K=Math.max(K,0),K}var rt=me<=0,Qe=me>=at,ne=z<=0,ge=z>=v,le=Cr(rt,Qe,ne,ge),Ve=function(){return{x:P?-z:z,y:me}},Xe=r.useRef(Ve()),j=Gt(function(X){if(R){var K=T(T({},Ve()),X);(Xe.current.x!==K.x||Xe.current.y!==K.y)&&(R(K),Xe.current=K)}});function re(X,K){var pe=X;K?(Bt.flushSync(function(){te(pe)}),j()):De(pe)}function Z(X){var K=X.currentTarget.scrollTop;K!==me&&De(K),S==null||S(X),j()}var G=function(K){var pe=K,Ie=v?v-_e.width:0;return pe=Math.max(pe,0),pe=Math.min(pe,Ie),pe},be=Gt(function(X,K){K?(Bt.flushSync(function(){te(function(pe){var Ie=pe+(P?-X:X);return G(Ie)})}),j()):De(function(pe){var Ie=pe+X;return Ie})}),Pe=ri(J,rt,Qe,ne,ge,!!v,be),ct=B(Pe,2),ke=ct[0],ut=ct[1];li(J,k,function(X,K,pe,Ie){var Ke=Ie;return le(X,K,pe)?!1:!Ke||!Ke._virtualHandled?(Ke&&(Ke._virtualHandled=!0),ke({preventDefault:function(){},deltaX:X?K:0,deltaY:X?0:K}),!0):!1}),ci(A,k,function(X){De(function(K){return K+X})}),ht(function(){function X(pe){var Ie=rt&&pe.detail<0,Ke=Qe&&pe.detail>0;J&&!Ie&&!Ke&&pe.preventDefault()}var K=k.current;return K.addEventListener("wheel",ke,{passive:!1}),K.addEventListener("DOMMouseScroll",ut,{passive:!0}),K.addEventListener("MozMousePixelScroll",X,{passive:!1}),function(){K.removeEventListener("wheel",ke),K.removeEventListener("DOMMouseScroll",ut),K.removeEventListener("MozMousePixelScroll",X)}},[J,rt,Qe]),ht(function(){if(v){var X=G(z);te(X),j({x:X})}},[_e.width,v]);var We=function(){var K,pe;(K=nt.current)===null||K===void 0||K.delayHidden(),(pe=Fe.current)===null||pe===void 0||pe.delayHidden()},dt=ui(k,Q,_,l,O,function(){return W(!0)},De,We);r.useImperativeHandle(t,function(){return{nativeElement:ie.current,getScrollInfo:Ve,scrollTo:function(K){function pe(Ie){return Ie&&mt(Ie)==="object"&&("left"in Ie||"top"in Ie)}pe(K)?(K.left!==void 0&&te(G(K.left)),dt(K.top)):dt(K)}}}),ht(function(){if(C){var X=Q.slice(Oe,Le+1);C(X,Q)}},[Oe,Le,Q]);var wt=oi(Q,O,_,l),Pt=w==null?void 0:w({start:Oe,end:Le,virtual:A,offsetX:z,offsetY:ot,rtl:P,getSize:wt}),Wt=ei(Q,Oe,Le,v,z,V,g,He),ft=null;i&&(ft=T(ve({},u?"height":"maxHeight",i),mi),J&&(ft.overflowY="hidden",v&&(ft.overflowX="hidden"),de&&(ft.pointerEvents="none")));var st={};return P&&(st.dir="rtl"),r.createElement("div",Re({ref:ie,style:T(T({},s),{},{position:"relative"}),className:q},st,$),r.createElement(Qt,{onResize:Ge},r.createElement(b,{className:"".concat(o,"-holder"),style:ft,ref:k,onScroll:Z,onMouseEnter:We},r.createElement(br,{prefixCls:o,height:Ee,offsetX:z,offsetY:ot,scrollWidth:v,onInnerResize:W,ref:D,innerProps:N,rtl:P,extra:Pt},Wt))),A&&Ee>i&&r.createElement(Kn,{ref:nt,prefixCls:o,scrollOffset:me,scrollRange:Ee,rtl:P,onScroll:re,onStartMove:fe,onStopMove:Se,spinSize:Ye,containerSize:_e.height,style:E==null?void 0:E.verticalScrollBar,thumbStyle:E==null?void 0:E.verticalScrollBarThumb,showScrollBar:y}),A&&v>_e.width&&r.createElement(Kn,{ref:Fe,prefixCls:o,scrollOffset:z,scrollRange:v,rtl:P,onScroll:re,onStartMove:fe,onStopMove:Se,spinSize:gt,containerSize:_e.width,horizontal:!0,style:E==null?void 0:E.horizontalScrollBar,thumbStyle:E==null?void 0:E.horizontalScrollBarThumb,showScrollBar:y}))}var yr=r.forwardRef(gi);yr.displayName="List";function pi(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}var hi=["disabled","title","children","style","className"];function Gn(e){return typeof e=="string"||typeof e=="number"}var Si=function(t,n){var o=ga(),a=o.prefixCls,i=o.id,l=o.open,c=o.multiple,u=o.mode,s=o.searchValue,f=o.toggleOpen,g=o.notFoundContent,p=o.onPopupScroll,d=r.useContext(gn),m=d.maxCount,v=d.flattenOptions,h=d.onActiveValue,b=d.defaultActiveFirstOption,S=d.onSelect,R=d.menuItemSelectedIcon,C=d.rawValues,N=d.fieldNames,w=d.virtual,E=d.direction,x=d.listHeight,y=d.listItemHeight,$=d.optionRender,O="".concat(a,"-item"),F=Jr(function(){return v},[l,v],function(H,M){return M[0]&&H[1]!==M[1]}),L=r.useRef(null),V=r.useMemo(function(){return c&&on(m)&&(C==null?void 0:C.size)>=m},[c,m,C==null?void 0:C.size]),W=function(M){M.preventDefault()},_=function(M){var z;(z=L.current)===null||z===void 0||z.scrollTo(typeof M=="number"?{index:M}:M)},Y=r.useCallback(function(H){return u==="combobox"?!1:C.has(H)},[u,je(C).toString(),C.size]),J=function(M){for(var z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,te=F.length,I=0;I<te;I+=1){var U=(M+I*z+te)%te,de=F[U]||{},ee=de.group,fe=de.data;if(!ee&&!(fe!=null&&fe.disabled)&&(Y(fe.value)||!V))return U}return-1},oe=r.useState(function(){return J(0)}),A=B(oe,2),P=A[0],q=A[1],Q=function(M){var z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;q(M);var te={source:z?"keyboard":"mouse"},I=F[M];if(!I){h(null,-1,te);return}h(I.value,M,te)};r.useEffect(function(){Q(b!==!1?J(0):-1)},[F.length,s]);var k=r.useCallback(function(H){return u==="combobox"?String(H).toLowerCase()===s.toLowerCase():C.has(H)},[u,s,je(C).toString(),C.size]);r.useEffect(function(){var H=setTimeout(function(){if(!c&&l&&C.size===1){var z=Array.from(C)[0],te=F.findIndex(function(I){var U=I.data;return s?String(U.value).startsWith(s):U.value===z});te!==-1&&(Q(te),_(te))}});if(l){var M;(M=L.current)===null||M===void 0||M.scrollTo(void 0)}return function(){return clearTimeout(H)}},[l,s]);var D=function(M){M!==void 0&&S(M,{selected:!C.has(M)}),c||f(!1)};if(r.useImperativeHandle(n,function(){return{onKeyDown:function(M){var z=M.which,te=M.ctrlKey;switch(z){case ae.N:case ae.P:case ae.UP:case ae.DOWN:{var I=0;if(z===ae.UP?I=-1:z===ae.DOWN?I=1:pi()&&te&&(z===ae.N?I=1:z===ae.P&&(I=-1)),I!==0){var U=J(P+I,I);_(U),Q(U,!0)}break}case ae.TAB:case ae.ENTER:{var de,ee=F[P];ee&&!(ee!=null&&(de=ee.data)!==null&&de!==void 0&&de.disabled)&&!V?D(ee.value):D(void 0),l&&M.preventDefault();break}case ae.ESC:f(!1),l&&M.stopPropagation()}},onKeyUp:function(){},scrollTo:function(M){_(M)}}}),F.length===0)return r.createElement("div",{role:"listbox",id:"".concat(i,"_list"),className:"".concat(O,"-empty"),onMouseDown:W},g);var ie=Object.keys(N).map(function(H){return N[H]}),ce=function(M){return M.label};function he(H,M){var z=H.group;return{role:z?"presentation":"option",id:"".concat(i,"_list_").concat(M)}}var me=function(M){var z=F[M];if(!z)return null;var te=z.data||{},I=te.value,U=z.group,de=It(te,!0),ee=ce(z);return z?r.createElement("div",Re({"aria-label":typeof ee=="string"&&!U?ee:null},de,{key:M},he(z,M),{"aria-selected":k(I)}),I):null},Ce={role:"listbox",id:"".concat(i,"_list")};return r.createElement(r.Fragment,null,w&&r.createElement("div",Re({},Ce,{style:{height:0,width:0,overflow:"hidden"}}),me(P-1),me(P),me(P+1)),r.createElement(yr,{itemKey:"key",ref:L,data:F,height:x,itemHeight:y,fullHeight:!1,onMouseDown:W,onScroll:p,virtual:w,direction:E,innerProps:w?null:Ce},function(H,M){var z=H.group,te=H.groupOption,I=H.data,U=H.label,de=H.value,ee=I.key;if(z){var fe,Se=(fe=I.title)!==null&&fe!==void 0?fe:Gn(U)?U.toString():void 0;return r.createElement("div",{className:we(O,"".concat(O,"-group"),I.className),title:Se},U!==void 0?U:ee)}var He=I.disabled,De=I.title;I.children;var ze=I.style,ye=I.className,Ne=Je(I,hi),Te=rr(Ne,ie),$e=Y(de),Be=He||!$e&&V,Ee="".concat(O,"-option"),Oe=we(O,Ee,ye,ve(ve(ve(ve({},"".concat(Ee,"-grouped"),te),"".concat(Ee,"-active"),P===M&&!Be),"".concat(Ee,"-disabled"),Be),"".concat(Ee,"-selected"),$e)),Le=ce(H),ot=!R||typeof R=="function"||$e,Ae=typeof Le=="number"?Le:Le||de,qe=Gn(Ae)?Ae.toString():void 0;return De!==void 0&&(qe=De),r.createElement("div",Re({},It(Te),w?{}:he(H,M),{"aria-selected":k(de),className:Oe,title:qe,onMouseMove:function(){P===M||Be||Q(M)},onClick:function(){Be||D(de)},style:ze}),r.createElement("div",{className:"".concat(Ee,"-content")},typeof $=="function"?$(H,{index:M}):Ae),r.isValidElement(R)||$e,ot&&r.createElement(Zt,{className:"".concat(O,"-option-state"),customizeIcon:R,customizeIconProps:{value:de,disabled:Be,isSelected:$e}},$e?"✓":null))}))},bi=r.forwardRef(Si);const Ci=function(e,t){var n=r.useRef({values:new Map,options:new Map}),o=r.useMemo(function(){var i=n.current,l=i.values,c=i.options,u=e.map(function(g){if(g.label===void 0){var p;return T(T({},g),{},{label:(p=l.get(g.value))===null||p===void 0?void 0:p.label})}return g}),s=new Map,f=new Map;return u.forEach(function(g){s.set(g.value,g),f.set(g.value,t.get(g.value)||c.get(g.value))}),n.current.values=s,n.current.options=f,u},[e,t]),a=r.useCallback(function(i){return t.get(i)||n.current.options.get(i)},[t]);return[o,a]};function en(e,t){return pr(e).join("").toUpperCase().includes(t)}const yi=function(e,t,n,o,a){return r.useMemo(function(){if(!n||o===!1)return e;var i=t.options,l=t.label,c=t.value,u=[],s=typeof o=="function",f=n.toUpperCase(),g=s?o:function(d,m){return a?en(m[a],f):m[i]?en(m[l!=="children"?l:"label"],f):en(m[c],f)},p=s?function(d){return an(d)}:function(d){return d};return e.forEach(function(d){if(d[i]){var m=g(n,p(d));if(m)u.push(d);else{var v=d[i].filter(function(h){return g(n,p(h))});v.length&&u.push(T(T({},d),{},ve({},i,v)))}return}g(n,p(d))&&u.push(d)}),u},[e,o,a,n,t])};var Xn=0,wi=Lt();function Ei(){var e;return wi?(e=Xn,Xn+=1):e="TEST_OR_SSR",e}function Ri(e){var t=r.useState(),n=B(t,2),o=n[0],a=n[1];return r.useEffect(function(){a("rc_select_".concat(Ei()))},[]),e||o}var xi=["children","value"],Ii=["children"];function $i(e){var t=e,n=t.key,o=t.props,a=o.children,i=o.value,l=Je(o,xi);return T({key:n,value:i!==void 0?i:n,children:a},l)}function wr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return or(e).map(function(n,o){if(!r.isValidElement(n)||!n.type)return null;var a=n,i=a.type.isSelectOptGroup,l=a.key,c=a.props,u=c.children,s=Je(c,Ii);return t||!i?$i(n):T(T({key:"__RC_SELECT_GRP__".concat(l===null?o:l,"__"),label:l},s),{},{options:wr(u)})}).filter(function(n){return n})}var Oi=function(t,n,o,a,i){return r.useMemo(function(){var l=t,c=!t;c&&(l=wr(n));var u=new Map,s=new Map,f=function(d,m,v){v&&typeof v=="string"&&d.set(m[v],m)},g=function p(d){for(var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,v=0;v<d.length;v+=1){var h=d[v];!h[o.options]||m?(u.set(h[o.value],h),f(s,h,o.label),f(s,h,a),f(s,h,i)):p(h[o.options],!0)}};return g(l),{options:l,valueOptions:u,labelOptions:s}},[t,n,o,a,i])};function qn(e){var t=r.useRef();t.current=e;var n=r.useCallback(function(){return t.current.apply(t,arguments)},[]);return n}var Mi=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],Ni=["inputValue"];function Pi(e){return!e||mt(e)!=="object"}var Di=r.forwardRef(function(e,t){var n=e.id,o=e.mode,a=e.prefixCls,i=a===void 0?"rc-select":a,l=e.backfill,c=e.fieldNames,u=e.inputValue,s=e.searchValue,f=e.onSearch,g=e.autoClearSearchValue,p=g===void 0?!0:g,d=e.onSelect,m=e.onDeselect,v=e.dropdownMatchSelectWidth,h=v===void 0?!0:v,b=e.filterOption,S=e.filterSort,R=e.optionFilterProp,C=e.optionLabelProp,N=e.options,w=e.optionRender,E=e.children,x=e.defaultActiveFirstOption,y=e.menuItemSelectedIcon,$=e.virtual,O=e.direction,F=e.listHeight,L=F===void 0?200:F,V=e.listItemHeight,W=V===void 0?20:V,_=e.labelRender,Y=e.value,J=e.defaultValue,oe=e.labelInValue,A=e.onChange,P=e.maxCount,q=Je(e,Mi),Q=Ri(n),k=ln(o),D=!!(!N&&E),ie=r.useMemo(function(){return b===void 0&&o==="combobox"?!1:b},[b,o]),ce=r.useMemo(function(){return Sr(c,D)},[JSON.stringify(c),D]),he=Xt("",{value:s!==void 0?s:u,postState:function(re){return re||""}}),me=B(he,2),Ce=me[0],H=me[1],M=Oi(N,E,ce,R,C),z=M.valueOptions,te=M.labelOptions,I=M.options,U=r.useCallback(function(j){var re=pr(j);return re.map(function(Z){var G,be,Pe,ct,ke;if(Pi(Z))G=Z;else{var ut;Pe=Z.key,be=Z.label,G=(ut=Z.value)!==null&&ut!==void 0?ut:Pe}var We=z.get(G);if(We){var dt;be===void 0&&(be=We==null?void 0:We[C||ce.label]),Pe===void 0&&(Pe=(dt=We==null?void 0:We.key)!==null&&dt!==void 0?dt:G),ct=We==null?void 0:We.disabled,ke=We==null?void 0:We.title}return{label:be,value:G,key:Pe,disabled:ct,title:ke}})},[ce,C,z]),de=Xt(J,{value:Y}),ee=B(de,2),fe=ee[0],Se=ee[1],He=r.useMemo(function(){var j,re=k&&fe===null?[]:fe,Z=U(re);return o==="combobox"&&Ha((j=Z[0])===null||j===void 0?void 0:j.value)?[]:Z},[fe,U,o,k]),De=Ci(He,z),ze=B(De,2),ye=ze[0],Ne=ze[1],Te=r.useMemo(function(){if(!o&&ye.length===1){var j=ye[0];if(j.value===null&&(j.label===null||j.label===void 0))return[]}return ye.map(function(re){var Z;return T(T({},re),{},{label:(Z=typeof _=="function"?_(re):re.label)!==null&&Z!==void 0?Z:re.value})})},[o,ye,_]),$e=r.useMemo(function(){return new Set(ye.map(function(j){return j.value}))},[ye]);r.useEffect(function(){if(o==="combobox"){var j,re=(j=ye[0])===null||j===void 0?void 0:j.value;H(Ta(re)?String(re):"")}},[ye]);var Be=qn(function(j,re){var Z=re??j;return ve(ve({},ce.value,j),ce.label,Z)}),Ee=r.useMemo(function(){if(o!=="tags")return I;var j=je(I),re=function(G){return z.has(G)};return je(ye).sort(function(Z,G){return Z.value<G.value?-1:1}).forEach(function(Z){var G=Z.value;re(G)||j.push(Be(G,Z.label))}),j},[Be,I,z,ye,o]),Oe=yi(Ee,ce,Ce,ie,R),Le=r.useMemo(function(){return o!=="tags"||!Ce||Oe.some(function(j){return j[R||"value"]===Ce})||Oe.some(function(j){return j[ce.value]===Ce})?Oe:[Be(Ce)].concat(je(Oe))},[Be,R,o,Oe,Ce,ce]),ot=function j(re){var Z=je(re).sort(function(G,be){return S(G,be,{searchValue:Ce})});return Z.map(function(G){return Array.isArray(G.options)?T(T({},G),{},{options:G.options.length>0?j(G.options):G.options}):G})},Ae=r.useMemo(function(){return S?ot(Le):Le},[Le,S,Ce]),qe=r.useMemo(function(){return Xa(Ae,{fieldNames:ce,childrenAsData:D})},[Ae,ce,D]),_e=function(re){var Z=U(re);if(Se(Z),A&&(Z.length!==ye.length||Z.some(function(Pe,ct){var ke;return((ke=ye[ct])===null||ke===void 0?void 0:ke.value)!==(Pe==null?void 0:Pe.value)}))){var G=oe?Z:Z.map(function(Pe){return Pe.value}),be=Z.map(function(Pe){return an(Ne(Pe.value))});A(k?G:G[0],k?be:be[0])}},bt=r.useState(null),Ge=B(bt,2),nt=Ge[0],Fe=Ge[1],gt=r.useState(0),Ye=B(gt,2),at=Ye[0],it=Ye[1],lt=x!==void 0?x:o!=="combobox",rt=r.useCallback(function(j,re){var Z=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},G=Z.source,be=G===void 0?"keyboard":G;it(re),l&&o==="combobox"&&j!==null&&be==="keyboard"&&Fe(String(j))},[l,o]),Qe=function(re,Z,G){var be=function(){var ft,st=Ne(re);return[oe?{label:st==null?void 0:st[ce.label],value:re,key:(ft=st==null?void 0:st.key)!==null&&ft!==void 0?ft:re}:re,an(st)]};if(Z&&d){var Pe=be(),ct=B(Pe,2),ke=ct[0],ut=ct[1];d(ke,ut)}else if(!Z&&m&&G!=="clear"){var We=be(),dt=B(We,2),wt=dt[0],Pt=dt[1];m(wt,Pt)}},ne=qn(function(j,re){var Z,G=k?re.selected:!0;G?Z=k?[].concat(je(ye),[j]):[j]:Z=ye.filter(function(be){return be.value!==j}),_e(Z),Qe(j,G),o==="combobox"?Fe(""):(!ln||p)&&(H(""),Fe(""))}),ge=function(re,Z){_e(re);var G=Z.type,be=Z.values;(G==="remove"||G==="clear")&&be.forEach(function(Pe){Qe(Pe.value,!1,G)})},le=function(re,Z){if(H(re),Fe(null),Z.source==="submit"){var G=(re||"").trim();if(G){var be=Array.from(new Set([].concat(je($e),[G])));_e(be),Qe(G,!0),H("")}return}Z.source!=="blur"&&(o==="combobox"&&_e(re),f==null||f(re))},Ve=function(re){var Z=re;o!=="tags"&&(Z=re.map(function(be){var Pe=te.get(be);return Pe==null?void 0:Pe.value}).filter(function(be){return be!==void 0}));var G=Array.from(new Set([].concat(je($e),je(Z))));_e(G),G.forEach(function(be){Qe(be,!0)})},Xe=r.useMemo(function(){var j=$!==!1&&h!==!1;return T(T({},M),{},{flattenOptions:qe,onActiveValue:rt,defaultActiveFirstOption:lt,onSelect:ne,menuItemSelectedIcon:y,rawValues:$e,fieldNames:ce,virtual:j,direction:O,listHeight:L,listItemHeight:W,childrenAsData:D,maxCount:P,optionRender:w})},[P,M,qe,rt,lt,ne,y,$e,ce,$,h,O,L,W,D,w]);return r.createElement(gn.Provider,{value:Xe},r.createElement(Ja,Re({},q,{id:Q,prefixCls:i,ref:t,omitDomProps:Ni,mode:o,displayValues:Te,onDisplayValuesChange:ge,direction:O,searchValue:Ce,onSearch:le,autoClearSearchValue:p,onSearchSplit:Ve,dropdownMatchSelectWidth:h,OptionList:bi,emptyOptions:!qe.length,activeValue:nt,activeDescendantId:"".concat(Q,"_list_").concat(at)})))}),Sn=Di;Sn.Option=hn;Sn.OptGroup=pn;const _i=()=>{const[,e]=fn(),[t]=vn("Empty"),o=new Ut(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return r.createElement("svg",{style:o,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},r.createElement("title",null,(t==null?void 0:t.description)||"Empty"),r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("g",{transform:"translate(24 31.67)"},r.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),r.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),r.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),r.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),r.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),r.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),r.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},r.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),r.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},zi=()=>{const[,e]=fn(),[t]=vn("Empty"),{colorFill:n,colorFillTertiary:o,colorFillQuaternary:a,colorBgContainer:i}=e,{borderColor:l,shadowColor:c,contentColor:u}=r.useMemo(()=>({borderColor:new Ut(n).onBackground(i).toHexString(),shadowColor:new Ut(o).onBackground(i).toHexString(),contentColor:new Ut(a).onBackground(i).toHexString()}),[n,o,a,i]);return r.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},r.createElement("title",null,(t==null?void 0:t.description)||"Empty"),r.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},r.createElement("ellipse",{fill:c,cx:"32",cy:"33",rx:"32",ry:"7"}),r.createElement("g",{fillRule:"nonzero",stroke:l},r.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),r.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:u}))))},Ti=e=>{const{componentCls:t,margin:n,marginXS:o,marginXL:a,fontSize:i,lineHeight:l}=e;return{[t]:{marginInline:o,fontSize:i,lineHeight:l,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:o,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:n},"&-normal":{marginBlock:a,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:o,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}},Hi=un("Empty",e=>{const{componentCls:t,controlHeightLG:n,calc:o}=e,a=$t(e,{emptyImgCls:`${t}-img`,emptyImgHeight:o(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:o(n).mul(.875).equal()});return[Ti(a)]});var Bi=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const Er=r.createElement(_i,null),Rr=r.createElement(zi,null),xt=e=>{const{className:t,rootClassName:n,prefixCls:o,image:a=Er,description:i,children:l,imageStyle:c,style:u,classNames:s,styles:f}=e,g=Bi(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:p,direction:d,className:m,style:v,classNames:h,styles:b}=mn("empty"),S=p("empty",o),[R,C,N]=Hi(S),[w]=vn("Empty"),E=typeof i<"u"?i:w==null?void 0:w.description,x=typeof E=="string"?E:"empty";let y=null;return typeof a=="string"?y=r.createElement("img",{alt:x,src:a}):y=a,R(r.createElement("div",Object.assign({className:we(C,N,S,m,{[`${S}-normal`]:a===Rr,[`${S}-rtl`]:d==="rtl"},t,n,h.root,s==null?void 0:s.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},b.root),v),f==null?void 0:f.root),u)},g),r.createElement("div",{className:we(`${S}-image`,h.image,s==null?void 0:s.image),style:Object.assign(Object.assign(Object.assign({},c),b.image),f==null?void 0:f.image)},y),E&&r.createElement("div",{className:we(`${S}-description`,h.description,s==null?void 0:s.description),style:Object.assign(Object.assign({},b.description),f==null?void 0:f.description)},E),l&&r.createElement("div",{className:we(`${S}-footer`,h.footer,s==null?void 0:s.footer),style:Object.assign(Object.assign({},b.footer),f==null?void 0:f.footer)},l)))};xt.PRESENTED_IMAGE_DEFAULT=Er;xt.PRESENTED_IMAGE_SIMPLE=Rr;const Li=e=>{const{componentName:t}=e,{getPrefixCls:n}=r.useContext(dn),o=n("empty");switch(t){case"Table":case"List":return Me.createElement(xt,{image:xt.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return Me.createElement(xt,{image:xt.PRESENTED_IMAGE_SIMPLE,className:`${o}-small`});case"Table.filter":return null;default:return Me.createElement(xt,null)}},Ai=e=>{const n={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:e==="scroll"?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},n),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},n),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},n),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},n),{points:["br","tr"],offset:[0,-4]})}};function Fi(e,t){return e||Ai(t)}const Yn=e=>{const{optionHeight:t,optionFontSize:n,optionLineHeight:o,optionPadding:a}=e;return{position:"relative",display:"block",minHeight:t,padding:a,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:o,boxSizing:"border-box"}},Vi=e=>{const{antCls:t,componentCls:n}=e,o=`${n}-item`,a=`&${t}-slide-up-enter${t}-slide-up-enter-active`,i=`&${t}-slide-up-appear${t}-slide-up-appear-active`,l=`&${t}-slide-up-leave${t}-slide-up-leave-active`,c=`${n}-dropdown-placement-`,u=`${o}-option-selected`;return[{[`${n}-dropdown`]:Object.assign(Object.assign({},Yt(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
          ${a}${c}bottomLeft,
          ${i}${c}bottomLeft
        `]:{animationName:no},[`
          ${a}${c}topLeft,
          ${i}${c}topLeft,
          ${a}${c}topRight,
          ${i}${c}topRight
        `]:{animationName:to},[`${l}${c}bottomLeft`]:{animationName:eo},[`
          ${l}${c}topLeft,
          ${l}${c}topRight
        `]:{animationName:kr},"&-hidden":{display:"none"},[o]:Object.assign(Object.assign({},Yn(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},rn),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${o}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${o}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${o}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${o}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},Yn(e)),{color:e.colorTextDisabled})}),[`${u}:has(+ ${u})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${u}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},xn(e,"slide-up"),xn(e,"slide-down"),Nn(e,"move-up"),Nn(e,"move-down")]},Wi=e=>{const{multipleSelectItemHeight:t,paddingXXS:n,lineWidth:o,INTERNAL_FIXED_ITEM_MARGIN:a}=e,i=e.max(e.calc(n).sub(o).equal(),0),l=e.max(e.calc(i).sub(a).equal(),0);return{basePadding:i,containerPadding:l,itemHeight:se(t),itemLineHeight:se(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}},ji=e=>{const{multipleSelectItemHeight:t,selectHeight:n,lineWidth:o}=e;return e.calc(n).sub(t).div(2).sub(o).equal()},Ki=e=>{const{componentCls:t,iconCls:n,borderRadiusSM:o,motionDurationSlow:a,paddingXS:i,multipleItemColorDisabled:l,multipleItemBorderColorDisabled:c,colorIcon:u,colorIconHover:s,INTERNAL_FIXED_ITEM_MARGIN:f}=e;return{[`${t}-selection-overflow`]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"calc(100% - 4px)",display:"inline-flex"},[`${t}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:f,borderRadius:o,cursor:"default",transition:`font-size ${a}, line-height ${a}, height ${a}`,marginInlineEnd:e.calc(f).mul(2).equal(),paddingInlineStart:i,paddingInlineEnd:e.calc(i).div(2).equal(),[`${t}-disabled&`]:{color:l,borderColor:c,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(i).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},ar()),{display:"inline-flex",alignItems:"center",color:u,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${n}`]:{verticalAlign:"-0.2em"},"&:hover":{color:s}})}}}},Ui=(e,t)=>{const{componentCls:n,INTERNAL_FIXED_ITEM_MARGIN:o}=e,a=`${n}-selection-overflow`,i=e.multipleSelectItemHeight,l=ji(e),c=t?`${n}-${t}`:"",u=Wi(e);return{[`${n}-multiple${c}`]:Object.assign(Object.assign({},Ki(e)),{[`${n}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:u.basePadding,paddingBlock:u.containerPadding,borderRadius:e.borderRadius,[`${n}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${se(o)} 0`,lineHeight:se(i),visibility:"hidden",content:'"\\a0"'}},[`${n}-selection-item`]:{height:u.itemHeight,lineHeight:se(u.itemLineHeight)},[`${n}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:se(i),marginBlock:o}},[`${n}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(u.basePadding).equal()},[`${a}-item + ${a}-item,
        ${n}-prefix + ${n}-selection-wrap
      `]:{[`${n}-selection-search`]:{marginInlineStart:0},[`${n}-selection-placeholder`]:{insetInlineStart:0}},[`${a}-item-suffix`]:{minHeight:u.itemHeight,marginBlock:o},[`${n}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(l).equal(),"\n          &-input,\n          &-mirror\n        ":{height:i,fontFamily:e.fontFamily,lineHeight:se(i),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${n}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(u.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function tn(e,t){const{componentCls:n}=e,o=t?`${n}-${t}`:"",a={[`${n}-multiple${o}`]:{fontSize:e.fontSize,[`${n}-selector`]:{[`${n}-show-search&`]:{cursor:"text"}},[`
        &${n}-show-arrow ${n}-selector,
        &${n}-allow-clear ${n}-selector
      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[Ui(e,t),a]}const Gi=e=>{const{componentCls:t}=e,n=$t(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),o=$t(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[tn(e),tn(n,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${t}-selection-search`]:{marginInlineStart:2}}},tn(o,"lg")]};function nn(e,t){const{componentCls:n,inputPaddingHorizontalBase:o,borderRadius:a}=e,i=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),l=t?`${n}-${t}`:"";return{[`${n}-single${l}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${n}-selector`]:Object.assign(Object.assign({},Yt(e,!0)),{display:"flex",borderRadius:a,flex:"1 1 auto",[`${n}-selection-wrap:after`]:{lineHeight:se(i)},[`${n}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`
          ${n}-selection-item,
          ${n}-selection-placeholder
        `]:{display:"block",padding:0,lineHeight:se(i),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${n}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${n}-selection-item:empty:after`,`${n}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${n}-show-arrow ${n}-selection-item,
        &${n}-show-arrow ${n}-selection-search,
        &${n}-show-arrow ${n}-selection-placeholder
      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${n}-open ${n}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${se(o)}`,[`${n}-selection-search-input`]:{height:i,fontSize:e.fontSize},"&:after":{lineHeight:se(i)}}},[`&${n}-customize-input`]:{[`${n}-selector`]:{"&:after":{display:"none"},[`${n}-selection-search`]:{position:"static",width:"100%"},[`${n}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${se(o)}`,"&:after":{display:"none"}}}}}}}function Xi(e){const{componentCls:t}=e,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[nn(e),nn($t(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selector`]:{padding:`0 ${se(n)}`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()},[`
            &${t}-show-arrow ${t}-selection-item,
            &${t}-show-arrow ${t}-selection-placeholder
          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},nn($t(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const qi=e=>{const{fontSize:t,lineHeight:n,lineWidth:o,controlHeight:a,controlHeightSM:i,controlHeightLG:l,paddingXXS:c,controlPaddingHorizontal:u,zIndexPopupBase:s,colorText:f,fontWeightStrong:g,controlItemBgActive:p,controlItemBgHover:d,colorBgContainer:m,colorFillSecondary:v,colorBgContainerDisabled:h,colorTextDisabled:b,colorPrimaryHover:S,colorPrimary:R,controlOutline:C}=e,N=c*2,w=o*2,E=Math.min(a-N,a-w),x=Math.min(i-N,i-w),y=Math.min(l-N,l-w);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(c/2),zIndexPopup:s+50,optionSelectedColor:f,optionSelectedFontWeight:g,optionSelectedBg:p,optionActiveBg:d,optionPadding:`${(a-t*n)/2}px ${u}px`,optionFontSize:t,optionLineHeight:n,optionHeight:a,selectorBg:m,clearBg:m,singleItemHeightLG:l,multipleItemBg:v,multipleItemBorderColor:"transparent",multipleItemHeight:E,multipleItemHeightSM:x,multipleItemHeightLG:y,multipleSelectorBgDisabled:h,multipleItemColorDisabled:b,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(e.fontSize*1.25),hoverBorderColor:S,activeBorderColor:R,activeOutlineColor:C,selectAffixPadding:c}},xr=(e,t)=>{const{componentCls:n,antCls:o,controlOutlineWidth:a}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{border:`${se(e.lineWidth)} ${e.lineType} ${t.borderColor}`,background:e.selectorBg},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,boxShadow:`0 0 0 ${se(a)} ${t.activeOutlineColor}`,outline:0},[`${n}-prefix`]:{color:t.color}}}},Qn=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},xr(e,t))}),Yi=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},xr(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Qn(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Qn(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${se(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),Ir=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{background:t.bg,border:`${se(e.lineWidth)} ${e.lineType} transparent`,color:t.color},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{background:t.hoverBg},[`${n}-focused& ${n}-selector`]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},Zn=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Ir(e,t))}),Qi=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},Ir(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),Zn(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),Zn(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${se(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),Zi=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${se(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${se(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),$r=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{borderWidth:`0 0 ${se(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,outline:0},[`${n}-prefix`]:{color:t.color}}}},Jn=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},$r(e,t))}),Ji=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},$r(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Jn(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Jn(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${se(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),ki=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},Yi(e)),Qi(e)),Zi(e)),Ji(e))}),el=e=>{const{componentCls:t}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${t}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},tl=e=>{const{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},nl=e=>{const{antCls:t,componentCls:n,inputPaddingHorizontalBase:o,iconCls:a}=e,i={[`${n}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}};return{[n]:Object.assign(Object.assign({},Yt(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${n}-customize-input) ${n}-selector`]:Object.assign(Object.assign({},el(e)),tl(e)),[`${n}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},rn),{[`> ${t}-typography`]:{display:"inline"}}),[`${n}-selection-placeholder`]:Object.assign(Object.assign({},rn),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${n}-arrow`]:Object.assign(Object.assign({},ar()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[a]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${n}-suffix)`]:{pointerEvents:"auto"}},[`${n}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${n}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${n}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${n}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorIcon}},"@media(hover:none)":i,"&:hover":i}),[`${n}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${n}-has-feedback`]:{[`${n}-clear`]:{insetInlineEnd:e.calc(o).add(e.fontSize).add(e.paddingXS).equal()}}}}}},rl=e=>{const{componentCls:t}=e;return[{[t]:{[`&${t}-in-form-item`]:{width:"100%"}}},nl(e),Xi(e),Gi(e),Vi(e),{[`${t}-rtl`]:{direction:"rtl"}},ro(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]},ol=un("Select",(e,{rootPrefixCls:t})=>{const n=$t(e,{rootPrefixCls:t,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[rl(n),ki(n)]},qi,{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}});var al={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},il=function(t,n){return r.createElement(At,Re({},t,{ref:n,icon:al}))},ll=r.forwardRef(il),cl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"},sl=function(t,n){return r.createElement(At,Re({},t,{ref:n,icon:cl}))},ul=r.forwardRef(sl);function dl({suffixIcon:e,clearIcon:t,menuItemSelectedIcon:n,removeIcon:o,loading:a,multiple:i,hasFeedback:l,prefixCls:c,showSuffixIcon:u,feedbackIcon:s,showArrow:f,componentName:g}){const p=t??r.createElement(oo,null),d=b=>e===null&&!l&&!f?null:r.createElement(r.Fragment,null,u!==!1&&b,l&&s);let m=null;if(e!==void 0)m=d(e);else if(a)m=d(r.createElement(lo,{spin:!0}));else{const b=`${c}-suffix`;m=({open:S,showSearch:R})=>d(S&&R?r.createElement(ul,{className:b}):r.createElement(ll,{className:b}))}let v=null;n!==void 0?v=n:i?v=r.createElement(ao,null):v=null;let h=null;return o!==void 0?h=o:h=r.createElement(io,null),{clearIcon:p,suffixIcon:m,itemIcon:v,removeIcon:h}}function fl(e,t){return t!==void 0?t:e!==null}var vl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const Or="SECRET_COMBOBOX_MODE_DO_NOT_USE",ml=(e,t)=>{var n,o,a,i,l;const{prefixCls:c,bordered:u,className:s,rootClassName:f,getPopupContainer:g,popupClassName:p,dropdownClassName:d,listHeight:m=256,placement:v,listItemHeight:h,size:b,disabled:S,notFoundContent:R,status:C,builtinPlacements:N,dropdownMatchSelectWidth:w,popupMatchSelectWidth:E,direction:x,style:y,allowClear:$,variant:O,dropdownStyle:F,transitionName:L,tagRender:V,maxCount:W,prefix:_,dropdownRender:Y,popupRender:J,onDropdownVisibleChange:oe,onOpenChange:A,styles:P,classNames:q}=e,Q=vl(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","styles","classNames"]),{getPopupContainer:k,getPrefixCls:D,renderEmpty:ie,direction:ce,virtual:he,popupMatchSelectWidth:me,popupOverflow:Ce}=r.useContext(dn),{showSearch:H,style:M,styles:z,className:te,classNames:I}=mn("select"),[,U]=fn(),de=h??(U==null?void 0:U.controlHeight),ee=D("select",c),fe=D(),Se=x??ce,{compactSize:He,compactItemClassnames:De}=co(ee,Se),[ze,ye]=so("select",O,u),Ne=uo(ee),[Te,$e,Be]=ol(ee,Ne),Ee=r.useMemo(()=>{const{mode:G}=e;if(G!=="combobox")return G===Or?"combobox":G},[e.mode]),Oe=Ee==="multiple"||Ee==="tags",Le=fl(e.suffixIcon,e.showArrow),ot=(n=E??w)!==null&&n!==void 0?n:me,Ae=((o=P==null?void 0:P.popup)===null||o===void 0?void 0:o.root)||((a=z.popup)===null||a===void 0?void 0:a.root)||F,qe=J||Y,_e=A||oe,{status:bt,hasFeedback:Ge,isFormItemInput:nt,feedbackIcon:Fe}=r.useContext(fo),gt=So(bt,C);let Ye;R!==void 0?Ye=R:Ee==="combobox"?Ye=null:Ye=(ie==null?void 0:ie("Select"))||r.createElement(Li,{componentName:"Select"});const{suffixIcon:at,itemIcon:it,removeIcon:lt,clearIcon:rt}=dl(Object.assign(Object.assign({},Q),{multiple:Oe,hasFeedback:Ge,feedbackIcon:Fe,showSuffixIcon:Le,prefixCls:ee,componentName:"Select"})),Qe=$===!0?{clearIcon:rt}:$,ne=rr(Q,["suffixIcon","itemIcon"]),ge=we(((i=q==null?void 0:q.popup)===null||i===void 0?void 0:i.root)||((l=I==null?void 0:I.popup)===null||l===void 0?void 0:l.root)||p||d,{[`${ee}-dropdown-${Se}`]:Se==="rtl"},f,I.root,q==null?void 0:q.root,Be,Ne,$e),le=vo(G=>{var be;return(be=b??He)!==null&&be!==void 0?be:G}),Ve=r.useContext(mo),Xe=S??Ve,j=we({[`${ee}-lg`]:le==="large",[`${ee}-sm`]:le==="small",[`${ee}-rtl`]:Se==="rtl",[`${ee}-${ze}`]:ye,[`${ee}-in-form-item`]:nt},go(ee,gt,Ge),De,te,s,I.root,q==null?void 0:q.root,f,Be,Ne,$e),re=r.useMemo(()=>v!==void 0?v:Se==="rtl"?"bottomRight":"bottomLeft",[v,Se]),[Z]=po("SelectLike",Ae==null?void 0:Ae.zIndex);return Te(r.createElement(Sn,Object.assign({ref:t,virtual:he,showSearch:H},ne,{style:Object.assign(Object.assign(Object.assign(Object.assign({},z.root),P==null?void 0:P.root),M),y),dropdownMatchSelectWidth:ot,transitionName:ho(fe,"slide-up",L),builtinPlacements:Fi(N,Ce),listHeight:m,listItemHeight:de,mode:Ee,prefixCls:ee,placement:re,direction:Se,prefix:_,suffixIcon:at,menuItemSelectedIcon:it,removeIcon:lt,allowClear:Qe,notFoundContent:Ye,className:j,getPopupContainer:g||k,dropdownClassName:ge,disabled:Xe,dropdownStyle:Object.assign(Object.assign({},Ae),{zIndex:Z}),maxCount:Oe?W:void 0,tagRender:Oe?V:void 0,dropdownRender:qe,onDropdownVisibleChange:_e})))},Vt=r.forwardRef(ml),gl=va(Vt,"dropdownAlign");Vt.SECRET_COMBOBOX_MODE_DO_NOT_USE=Or;Vt.Option=hn;Vt.OptGroup=pn;Vt._InternalPanelDoNotUseOrYouWillBeFired=gl;function kn(e){return["small","middle","large"].includes(e)}function er(e){return e?typeof e=="number"&&!Number.isNaN(e):!1}const Mr=Me.createContext({latestIndex:0}),pl=Mr.Provider,hl=({className:e,index:t,children:n,split:o,style:a})=>{const{latestIndex:i}=r.useContext(Mr);return n==null?null:r.createElement(r.Fragment,null,r.createElement("div",{className:e,style:a},n),t<i&&o&&r.createElement("span",{className:`${e}-split`},o))};var Sl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const bl=r.forwardRef((e,t)=>{var n;const{getPrefixCls:o,direction:a,size:i,className:l,style:c,classNames:u,styles:s}=mn("space"),{size:f=i??"small",align:g,className:p,rootClassName:d,children:m,direction:v="horizontal",prefixCls:h,split:b,style:S,wrap:R=!1,classNames:C,styles:N}=e,w=Sl(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[E,x]=Array.isArray(f)?f:[f,f],y=kn(x),$=kn(E),O=er(x),F=er(E),L=or(m,{keepEmpty:!0}),V=g===void 0&&v==="horizontal"?"center":g,W=o("space",h),[_,Y,J]=bo(W),oe=we(W,l,Y,`${W}-${v}`,{[`${W}-rtl`]:a==="rtl",[`${W}-align-${V}`]:V,[`${W}-gap-row-${x}`]:y,[`${W}-gap-col-${E}`]:$},p,d,J),A=we(`${W}-item`,(n=C==null?void 0:C.item)!==null&&n!==void 0?n:u.item);let P=0;const q=L.map((D,ie)=>{var ce;D!=null&&(P=ie);const he=(D==null?void 0:D.key)||`${A}-${ie}`;return r.createElement(hl,{className:A,key:he,index:ie,split:b,style:(ce=N==null?void 0:N.item)!==null&&ce!==void 0?ce:s.item},D)}),Q=r.useMemo(()=>({latestIndex:P}),[P]);if(L.length===0)return null;const k={};return R&&(k.flexWrap="wrap"),!$&&F&&(k.columnGap=E),!y&&O&&(k.rowGap=x),_(r.createElement("div",Object.assign({ref:t,className:oe,style:Object.assign(Object.assign(Object.assign({},k),c),S)},w),r.createElement(pl,{value:Q},q)))}),Cl=bl;Cl.Compact=Co;var yl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"},wl=function(t,n){return r.createElement(At,Re({},t,{ref:n,icon:yl}))},Nl=r.forwardRef(wl);export{Ml as A,Li as D,xt as E,xo as N,na as P,ll as R,Cl as S,ul as a,Vt as b,aa as c,ia as d,To as e,Nl as f,va as g,Rl as h,Nn as i,xl as j,Il as k,Ki as l,Wi as m,dl as n,Ol as o,ua as p,da as q,$l as u,fa as w};
