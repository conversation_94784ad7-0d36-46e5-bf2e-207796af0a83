import{r,a3 as _e,a4 as Me,c8 as rt,ax as Le,e as st,i as lt,a as Y,c0 as it,b as at,at as Se,b2 as ct,y as ut,b1 as dt,aK as Ne,T as me,b4 as pt,av as ft,t as Pe,a6 as U,$ as De,C as mt,bb as gt,o as Ae,U as Ce,ad as bt}from"./index-CHvake0r.js";import{i as Oe,R as yt}from"./EditOutlined-t_UPdXXr.js";const vt=(e,n=!1)=>n&&e==null?[]:Array.isArray(e)?e:[e];var Et={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"},ht=function(n,o){return r.createElement(_e,Me({},n,{ref:o,icon:Et}))},xt=r.forwardRef(ht),St={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"},Ct=function(n,o){return r.createElement(_e,Me({},n,{ref:o,icon:St}))},Ot=r.forwardRef(Ct);const wt=(e,n,o,t)=>{const{titleMarginBottom:s,fontWeightStrong:i}=t;return{marginBottom:s,color:o,fontWeight:i,fontSize:e,lineHeight:n}},Rt=e=>{const n=[1,2,3,4,5],o={};return n.forEach(t=>{o[`
      h${t}&,
      div&-h${t},
      div&-h${t} > textarea,
      h${t}
    `]=wt(e[`fontSizeHeading${t}`],e[`lineHeightHeading${t}`],e.colorTextHeading,e)}),o},Tt=e=>{const{componentCls:n}=e;return{"a&, a":Object.assign(Object.assign({},Le(e)),{userSelect:"text",[`&[disabled], &${n}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},jt=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:rt[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),It=e=>{const{componentCls:n,paddingSM:o}=e,t=o;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(t).mul(-1).equal(),marginBottom:`calc(1em - ${st(t)})`},[`${n}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},$t=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),_t=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),Mt=e=>{const{componentCls:n,titleMarginTop:o}=e;return{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${n}-secondary`]:{color:e.colorTextDescription},[`&${n}-success`]:{color:e.colorSuccessText},[`&${n}-warning`]:{color:e.colorWarningText},[`&${n}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${n}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},Rt(e)),{[`
      & + h1${n},
      & + h2${n},
      & + h3${n},
      & + h4${n},
      & + h5${n}
      `]:{marginTop:o},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:o}}}),jt(e)),Tt(e)),{[`
        ${n}-expand,
        ${n}-collapse,
        ${n}-edit,
        ${n}-copy
      `]:Object.assign(Object.assign({},Le(e)),{marginInlineStart:e.marginXXS})}),It(e)),$t(e)),_t()),{"&-rtl":{direction:"rtl"}})}},Lt=()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}),ke=lt("Typography",e=>[Mt(e)],Lt),Nt=e=>{const{prefixCls:n,"aria-label":o,className:t,style:s,direction:i,maxLength:c,autoSize:g=!0,value:u,onSave:d,onCancel:p,onEnd:m,component:l,enterIcon:h=r.createElement(Ot,null)}=e,S=r.useRef(null),y=r.useRef(!1),O=r.useRef(null),[b,_]=r.useState(u);r.useEffect(()=>{_(u)},[u]),r.useEffect(()=>{var v;if(!((v=S.current)===null||v===void 0)&&v.resizableTextArea){const{textArea:C}=S.current.resizableTextArea;C.focus();const{length:x}=C.value;C.setSelectionRange(x,x)}},[]);const k=({target:v})=>{_(v.value.replace(/[\n\r]/g,""))},w=()=>{y.current=!0},R=()=>{y.current=!1},I=({keyCode:v})=>{y.current||(O.current=v)},E=()=>{d(b.trim())},H=({keyCode:v,ctrlKey:C,altKey:x,metaKey:D,shiftKey:j})=>{O.current!==v||y.current||C||x||D||j||(v===Se.ENTER?(E(),m==null||m()):v===Se.ESC&&p())},V=()=>{E()},[W,N,F]=ke(n),P=Y(n,`${n}-edit-content`,{[`${n}-rtl`]:i==="rtl",[`${n}-${l}`]:!!l},t,N,F);return W(r.createElement("div",{className:P,style:s},r.createElement(it,{ref:S,maxLength:c,value:b,onChange:k,onKeyDown:I,onKeyUp:H,onCompositionStart:w,onCompositionEnd:R,onBlur:V,"aria-label":o,rows:1,autoSize:g}),h!==null?at(h,{className:`${n}-edit-content-confirm`}):null))};var le,we;function Pt(){return we||(we=1,le=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var n=document.activeElement,o=[],t=0;t<e.rangeCount;t++)o.push(e.getRangeAt(t));switch(n.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":n.blur();break;default:n=null;break}return e.removeAllRanges(),function(){e.type==="Caret"&&e.removeAllRanges(),e.rangeCount||o.forEach(function(s){e.addRange(s)}),n&&n.focus()}}),le}var ie,Re;function Dt(){if(Re)return ie;Re=1;var e=Pt(),n={"text/plain":"Text","text/html":"Url",default:"Text"},o="Copy to clipboard: #{key}, Enter";function t(i){var c=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return i.replace(/#{\s*key\s*}/g,c)}function s(i,c){var g,u,d,p,m,l,h=!1;c||(c={}),g=c.debug||!1;try{d=e(),p=document.createRange(),m=document.getSelection(),l=document.createElement("span"),l.textContent=i,l.ariaHidden="true",l.style.all="unset",l.style.position="fixed",l.style.top=0,l.style.clip="rect(0, 0, 0, 0)",l.style.whiteSpace="pre",l.style.webkitUserSelect="text",l.style.MozUserSelect="text",l.style.msUserSelect="text",l.style.userSelect="text",l.addEventListener("copy",function(y){if(y.stopPropagation(),c.format)if(y.preventDefault(),typeof y.clipboardData>"u"){g&&console.warn("unable to use e.clipboardData"),g&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var O=n[c.format]||n.default;window.clipboardData.setData(O,i)}else y.clipboardData.clearData(),y.clipboardData.setData(c.format,i);c.onCopy&&(y.preventDefault(),c.onCopy(y.clipboardData))}),document.body.appendChild(l),p.selectNodeContents(l),m.addRange(p);var S=document.execCommand("copy");if(!S)throw new Error("copy command was unsuccessful");h=!0}catch(y){g&&console.error("unable to copy using execCommand: ",y),g&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(c.format||"text",i),c.onCopy&&c.onCopy(window.clipboardData),h=!0}catch(O){g&&console.error("unable to copy using clipboardData: ",O),g&&console.error("falling back to prompt"),u=t("message"in c?c.message:o),window.prompt(u,i)}}finally{m&&(typeof m.removeRange=="function"?m.removeRange(p):m.removeAllRanges()),l&&document.body.removeChild(l),d()}return h}return ie=s,ie}var At=Dt();const kt=ct(At);var Ht=function(e,n,o,t){function s(i){return i instanceof o?i:new o(function(c){c(i)})}return new(o||(o=Promise))(function(i,c){function g(p){try{d(t.next(p))}catch(m){c(m)}}function u(p){try{d(t.throw(p))}catch(m){c(m)}}function d(p){p.done?i(p.value):s(p.value).then(g,u)}d((t=t.apply(e,n||[])).next())})};const zt=({copyConfig:e,children:n})=>{const[o,t]=r.useState(!1),[s,i]=r.useState(!1),c=r.useRef(null),g=()=>{c.current&&clearTimeout(c.current)},u={};e.format&&(u.format=e.format),r.useEffect(()=>g,[]);const d=ut(p=>Ht(void 0,void 0,void 0,function*(){var m;p==null||p.preventDefault(),p==null||p.stopPropagation(),i(!0);try{const l=typeof e.text=="function"?yield e.text():e.text;kt(l||vt(n,!0).join("")||"",u),i(!1),t(!0),g(),c.current=setTimeout(()=>{t(!1)},3e3),(m=e.onCopy)===null||m===void 0||m.call(e,p)}catch(l){throw i(!1),l}}));return{copied:o,copyLoading:s,onClick:d}};function ae(e,n){return r.useMemo(()=>{const o=!!e;return[o,Object.assign(Object.assign({},n),o&&typeof e=="object"?e:null)]},[e])}const Bt=e=>{const n=r.useRef(void 0);return r.useEffect(()=>{n.current=e}),n.current},Wt=(e,n,o)=>r.useMemo(()=>e===!0?{title:n??o}:r.isValidElement(e)?{title:e}:typeof e=="object"?Object.assign({title:n??o},e):{title:e},[e,n,o]);var Ut=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(o[t[s]]=e[t[s]]);return o};const He=r.forwardRef((e,n)=>{const{prefixCls:o,component:t="article",className:s,rootClassName:i,setContentRef:c,children:g,direction:u,style:d}=e,p=Ut(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:m,direction:l,className:h,style:S}=dt("typography"),y=u??l,O=c?Ne(n,c):n,b=m("typography",o),[_,k,w]=ke(b),R=Y(b,h,{[`${b}-rtl`]:y==="rtl"},s,i,k,w),I=Object.assign(Object.assign({},S),d);return _(r.createElement(t,Object.assign({className:R,style:I,ref:O},p),g))});function Te(e){return e===!1?[!1,!1]:Array.isArray(e)?e:[e]}function ce(e,n,o){return e===!0||e===void 0?n:e||o&&n}function Vt(e){const n=document.createElement("em");e.appendChild(n);const o=e.getBoundingClientRect(),t=n.getBoundingClientRect();return e.removeChild(n),o.left>t.left||t.right>o.right||o.top>t.top||t.bottom>o.bottom}const ge=e=>["string","number"].includes(typeof e),Ft=({prefixCls:e,copied:n,locale:o,iconOnly:t,tooltips:s,icon:i,tabIndex:c,onCopy:g,loading:u})=>{const d=Te(s),p=Te(i),{copied:m,copy:l}=o??{},h=n?m:l,S=ce(d[n?1:0],h),y=typeof S=="string"?S:h;return r.createElement(me,{title:S},r.createElement("button",{type:"button",className:Y(`${e}-copy`,{[`${e}-copy-success`]:n,[`${e}-copy-icon-only`]:t}),onClick:g,"aria-label":y,tabIndex:c},n?ce(p[1],r.createElement(pt,null),!0):ce(p[0],u?r.createElement(ft,null):r.createElement(xt,null),!0)))},G=r.forwardRef(({style:e,children:n},o)=>{const t=r.useRef(null);return r.useImperativeHandle(o,()=>({isExceed:()=>{const s=t.current;return s.scrollHeight>s.clientHeight},getHeight:()=>t.current.clientHeight})),r.createElement("span",{"aria-hidden":!0,ref:t,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},e)},n)}),qt=e=>e.reduce((n,o)=>n+(ge(o)?String(o).length:1),0);function je(e,n){let o=0;const t=[];for(let s=0;s<e.length;s+=1){if(o===n)return t;const i=e[s],g=ge(i)?String(i).length:1,u=o+g;if(u>n){const d=n-o;return t.push(String(i).slice(0,d)),t}t.push(i),o=u}return e}const ue=0,de=1,pe=2,fe=3,Ie=4,Q={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function Kt(e){const{enableMeasure:n,width:o,text:t,children:s,rows:i,expanded:c,miscDeps:g,onEllipsis:u}=e,d=r.useMemo(()=>Pe(t),[t]),p=r.useMemo(()=>qt(d),[t]),m=r.useMemo(()=>s(d,!1),[t]),[l,h]=r.useState(null),S=r.useRef(null),y=r.useRef(null),O=r.useRef(null),b=r.useRef(null),_=r.useRef(null),[k,w]=r.useState(!1),[R,I]=r.useState(ue),[E,H]=r.useState(0),[V,W]=r.useState(null);U(()=>{I(n&&o&&p?de:ue)},[o,t,i,n,d]),U(()=>{var v,C,x,D;if(R===de){I(pe);const j=y.current&&getComputedStyle(y.current).whiteSpace;W(j)}else if(R===pe){const j=!!(!((v=O.current)===null||v===void 0)&&v.isExceed());I(j?fe:Ie),h(j?[0,p]:null),w(j);const z=((C=O.current)===null||C===void 0?void 0:C.getHeight())||0,te=i===1?0:((x=b.current)===null||x===void 0?void 0:x.getHeight())||0,K=((D=_.current)===null||D===void 0?void 0:D.getHeight())||0,ne=Math.max(z,te+K);H(ne+1),u(j)}},[R]);const N=l?Math.ceil((l[0]+l[1])/2):0;U(()=>{var v;const[C,x]=l||[0,0];if(C!==x){const j=(((v=S.current)===null||v===void 0?void 0:v.getHeight())||0)>E;let z=N;x-C===1&&(z=j?C:x),h(j?[C,z]:[z,x])}},[l,N]);const F=r.useMemo(()=>{if(!n)return s(d,!1);if(R!==fe||!l||l[0]!==l[1]){const v=s(d,!1);return[Ie,ue].includes(R)?v:r.createElement("span",{style:Object.assign(Object.assign({},Q),{WebkitLineClamp:i})},v)}return s(c?d:je(d,l[0]),k)},[c,R,l,d].concat(De(g))),P={width:o,margin:0,padding:0,whiteSpace:V==="nowrap"?"normal":"inherit"};return r.createElement(r.Fragment,null,F,R===pe&&r.createElement(r.Fragment,null,r.createElement(G,{style:Object.assign(Object.assign(Object.assign({},P),Q),{WebkitLineClamp:i}),ref:O},m),r.createElement(G,{style:Object.assign(Object.assign(Object.assign({},P),Q),{WebkitLineClamp:i-1}),ref:b},m),r.createElement(G,{style:Object.assign(Object.assign(Object.assign({},P),Q),{WebkitLineClamp:1}),ref:_},s([],!0))),R===fe&&l&&l[0]!==l[1]&&r.createElement(G,{style:Object.assign(Object.assign({},P),{top:400}),ref:S},s(je(d,N),!0)),R===de&&r.createElement("span",{style:{whiteSpace:"inherit"},ref:y}))}const Xt=({enableEllipsis:e,isEllipsis:n,children:o,tooltipProps:t})=>!(t!=null&&t.title)||!e?o:r.createElement(me,Object.assign({open:n?void 0:!1},t),o);var Jt=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(o[t[s]]=e[t[s]]);return o};function Gt({mark:e,code:n,underline:o,delete:t,strong:s,keyboard:i,italic:c},g){let u=g;function d(p,m){m&&(u=r.createElement(p,{},u))}return d("strong",s),d("u",o),d("del",t),d("code",n),d("mark",e),d("kbd",i),d("i",c),u}const Qt="...",$e=["delete","mark","code","underline","strong","keyboard","italic"],Z=r.forwardRef((e,n)=>{var o;const{prefixCls:t,className:s,style:i,type:c,disabled:g,children:u,ellipsis:d,editable:p,copyable:m,component:l,title:h}=e,S=Jt(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:y,direction:O}=r.useContext(mt),[b]=gt("Text"),_=r.useRef(null),k=r.useRef(null),w=y("typography",t),R=Ae(S,$e),[I,E]=ae(p),[H,V]=Ce(!1,{value:E.editing}),{triggerType:W=["icon"]}=E,N=a=>{var f;a&&((f=E.onStart)===null||f===void 0||f.call(E)),V(a)},F=Bt(H);U(()=>{var a;!H&&F&&((a=k.current)===null||a===void 0||a.focus())},[H]);const P=a=>{a==null||a.preventDefault(),N(!0)},v=a=>{var f;(f=E.onChange)===null||f===void 0||f.call(E,a),N(!1)},C=()=>{var a;(a=E.onCancel)===null||a===void 0||a.call(E),N(!1)},[x,D]=ae(m),{copied:j,copyLoading:z,onClick:te}=zt({copyConfig:D,children:u}),[K,ne]=r.useState(!1),[be,ze]=r.useState(!1),[ye,Be]=r.useState(!1),[ve,We]=r.useState(!1),[Ue,Ve]=r.useState(!0),[B,T]=ae(d,{expandable:!1,symbol:a=>a?b==null?void 0:b.collapse:b==null?void 0:b.expand}),[A,Fe]=Ce(T.defaultExpanded||!1,{value:T.expanded}),$=B&&(!A||T.expandable==="collapsible"),{rows:q=1}=T,X=r.useMemo(()=>$&&(T.suffix!==void 0||T.onEllipsis||T.expandable||I||x),[$,T,I,x]);U(()=>{B&&!X&&(ne(Oe("webkitLineClamp")),ze(Oe("textOverflow")))},[X,B]);const[M,qe]=r.useState($),Ee=r.useMemo(()=>X?!1:q===1?be:K,[X,be,K]);U(()=>{qe(Ee&&$)},[Ee,$]);const he=$&&(M?ve:ye),Ke=$&&q===1&&M,oe=$&&q>1&&M,Xe=(a,f)=>{var L;Fe(f.expanded),(L=T.onExpand)===null||L===void 0||L.call(T,a,f)},[xe,Je]=r.useState(0),Ge=({offsetWidth:a})=>{Je(a)},Qe=a=>{var f;Be(a),ye!==a&&((f=T.onEllipsis)===null||f===void 0||f.call(T,a))};r.useEffect(()=>{const a=_.current;if(B&&M&&a){const f=Vt(a);ve!==f&&We(f)}},[B,M,u,oe,Ue,xe]),r.useEffect(()=>{const a=_.current;if(typeof IntersectionObserver>"u"||!a||!M||!$)return;const f=new IntersectionObserver(()=>{Ve(!!a.offsetParent)});return f.observe(a),()=>{f.disconnect()}},[M,$]);const re=Wt(T.tooltip,E.text,u),J=r.useMemo(()=>{if(!(!B||M))return[E.text,u,h,re.title].find(ge)},[B,M,h,re.title,he]);if(H)return r.createElement(Nt,{value:(o=E.text)!==null&&o!==void 0?o:typeof u=="string"?u:"",onSave:v,onCancel:C,onEnd:E.onEnd,prefixCls:w,className:s,style:i,direction:O,component:l,maxLength:E.maxLength,autoSize:E.autoSize,enterIcon:E.enterIcon});const Ye=()=>{const{expandable:a,symbol:f}=T;return a?r.createElement("button",{type:"button",key:"expand",className:`${w}-${A?"collapse":"expand"}`,onClick:L=>Xe(L,{expanded:!A}),"aria-label":A?b.collapse:b==null?void 0:b.expand},typeof f=="function"?f(A):f):null},Ze=()=>{if(!I)return;const{icon:a,tooltip:f,tabIndex:L}=E,se=Pe(f)[0]||(b==null?void 0:b.edit),ot=typeof se=="string"?se:"";return W.includes("icon")?r.createElement(me,{key:"edit",title:f===!1?"":se},r.createElement("button",{type:"button",ref:k,className:`${w}-edit`,onClick:P,"aria-label":ot,tabIndex:L},a||r.createElement(yt,{role:"button"}))):null},et=()=>x?r.createElement(Ft,Object.assign({key:"copy"},D,{prefixCls:w,copied:j,locale:b,onCopy:te,loading:z,iconOnly:u==null})):null,tt=a=>[a&&Ye(),Ze(),et()],nt=a=>[a&&!A&&r.createElement("span",{"aria-hidden":!0,key:"ellipsis"},Qt),T.suffix,tt(a)];return r.createElement(bt,{onResize:Ge,disabled:!$},a=>r.createElement(Xt,{tooltipProps:re,enableEllipsis:$,isEllipsis:he},r.createElement(He,Object.assign({className:Y({[`${w}-${c}`]:c,[`${w}-disabled`]:g,[`${w}-ellipsis`]:B,[`${w}-ellipsis-single-line`]:Ke,[`${w}-ellipsis-multiple-line`]:oe},s),prefixCls:t,style:Object.assign(Object.assign({},i),{WebkitLineClamp:oe?q:void 0}),component:l,ref:Ne(a,_,n),direction:O,onClick:W.includes("text")?P:void 0,"aria-label":J==null?void 0:J.toString(),title:h},R),r.createElement(Kt,{enableMeasure:$&&!M,text:u,rows:q,width:xe,onEllipsis:Qe,expanded:A,miscDeps:[j,A,z,I,x,b].concat(De($e.map(f=>e[f])))},(f,L)=>Gt(e,r.createElement(r.Fragment,null,f.length>0&&L&&!A&&J?r.createElement("span",{key:"show-content","aria-hidden":!0},f):f,nt(L)))))))});var Yt=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(o[t[s]]=e[t[s]]);return o};const Zt=r.forwardRef((e,n)=>{var{ellipsis:o,rel:t}=e,s=Yt(e,["ellipsis","rel"]);const i=Object.assign(Object.assign({},s),{rel:t===void 0&&s.target==="_blank"?"noopener noreferrer":t});return delete i.navigate,r.createElement(Z,Object.assign({},i,{ref:n,ellipsis:!!o,component:"a"}))}),en=r.forwardRef((e,n)=>r.createElement(Z,Object.assign({ref:n},e,{component:"div"})));var tn=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(o[t[s]]=e[t[s]]);return o};const nn=(e,n)=>{var{ellipsis:o}=e,t=tn(e,["ellipsis"]);const s=r.useMemo(()=>o&&typeof o=="object"?Ae(o,["expandable","rows"]):o,[o]);return r.createElement(Z,Object.assign({ref:n},t,{ellipsis:s,component:"span"}))},on=r.forwardRef(nn);var rn=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(o[t[s]]=e[t[s]]);return o};const sn=[1,2,3,4,5],ln=r.forwardRef((e,n)=>{const{level:o=1}=e,t=rn(e,["level"]),s=sn.includes(o)?`h${o}`:"h1";return r.createElement(Z,Object.assign({ref:n},t,{component:s}))}),ee=He;ee.Text=on;ee.Link=Zt;ee.Title=ln;ee.Paragraph=en;export{ee as T};
