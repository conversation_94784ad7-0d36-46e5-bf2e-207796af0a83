import{i as se,m as xe,l as ge,K as ue,p as fe,bo as ye,O as he,N as be,r as l,C as Z,a as z,bp as ve,b1 as ne,U as oe,T as je,ba as Ne,b as Ce,at as Oe,be as ae,bb as we,az as Pe,a2 as K,bq as Se,o as $e,j as t,aE as Ee,aF as ee,aB as F,aD as H,b4 as ke,c as Te}from"./index-CHvake0r.js";import{l as _e}from"./kitchent-0R1q2rXv.js";import{T as Be}from"./index-Bbq37T-v.js";import{R as Y}from"./ClockCircleOutlined-B61igLBq.js";import{R as G,B as q}from"./FireOutlined-B7wZholJ.js";import{R as Ie}from"./ReloadOutlined-BPMc8QqU.js";import{A as ze}from"./ActionButton-BdVp3AS3.js";import{R as U}from"./TableOutlined-B5odrklF.js";import"./EditOutlined-t_UPdXXr.js";const M=e=>e?typeof e=="function"?e():e:null,Re=e=>{const{componentCls:n,popoverColor:o,titleMinWidth:s,fontWeightStrong:a,innerPadding:m,boxShadowSecondary:x,colorTextHeading:p,borderRadiusLG:u,zIndexPopup:b,titleMarginBottom:y,colorBgElevated:v,popoverBg:P,titleBorderBottom:S,innerContentPadding:O,titlePadding:g}=e;return[{[n]:Object.assign(Object.assign({},fe(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:b,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":v,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${n}-content`]:{position:"relative"},[`${n}-inner`]:{backgroundColor:P,backgroundClip:"padding-box",borderRadius:u,boxShadow:x,padding:m},[`${n}-title`]:{minWidth:s,marginBottom:y,color:p,fontWeight:a,borderBottom:S,padding:g},[`${n}-inner-content`]:{color:o,padding:O}})},ue(e,"var(--antd-arrow-background-color)"),{[`${n}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${n}-content`]:{display:"inline-block"}}}]},We=e=>{const{componentCls:n}=e;return{[n]:ye.map(o=>{const s=e[`${o}6`];return{[`&${n}-${o}`]:{"--antd-arrow-background-color":s,[`${n}-inner`]:{backgroundColor:s},[`${n}-arrow`]:{background:"transparent"}}}})}},Me=e=>{const{lineWidth:n,controlHeight:o,fontHeight:s,padding:a,wireframe:m,zIndexPopupBase:x,borderRadiusLG:p,marginXS:u,lineType:b,colorSplit:y,paddingSM:v}=e,P=o-s,S=P/2,O=P/2-n,g=a;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:x+30},he(e)),be({contentRadius:p,limitVerticalRadius:!0})),{innerPadding:m?0:12,titleMarginBottom:m?0:u,titlePadding:m?`${S}px ${g}px ${O}px`:0,titleBorderBottom:m?`${n}px ${b} ${y}`:"none",innerContentPadding:m?`${v}px ${g}px`:0})},re=se("Popover",e=>{const{colorBgElevated:n,colorText:o}=e,s=xe(e,{popoverBg:n,popoverColor:o});return[Re(s),We(s),ge(s,"zoom-big")]},Me,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var Le=function(e,n){var o={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&n.indexOf(s)<0&&(o[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)n.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(o[s[a]]=e[s[a]]);return o};const le=({title:e,content:n,prefixCls:o})=>!e&&!n?null:l.createElement(l.Fragment,null,e&&l.createElement("div",{className:`${o}-title`},e),n&&l.createElement("div",{className:`${o}-inner-content`},n)),De=e=>{const{hashId:n,prefixCls:o,className:s,style:a,placement:m="top",title:x,content:p,children:u}=e,b=M(x),y=M(p),v=z(n,o,`${o}-pure`,`${o}-placement-${m}`,s);return l.createElement("div",{className:v,style:a},l.createElement("div",{className:`${o}-arrow`}),l.createElement(ve,Object.assign({},e,{className:n,prefixCls:o}),u||l.createElement(le,{prefixCls:o,title:b,content:y})))},ie=e=>{const{prefixCls:n,className:o}=e,s=Le(e,["prefixCls","className"]),{getPrefixCls:a}=l.useContext(Z),m=a("popover",n),[x,p,u]=re(m);return x(l.createElement(De,Object.assign({},s,{prefixCls:m,hashId:p,className:z(o,u)})))};var Ve=function(e,n){var o={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&n.indexOf(s)<0&&(o[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)n.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(o[s[a]]=e[s[a]]);return o};const Ae=l.forwardRef((e,n)=>{var o,s;const{prefixCls:a,title:m,content:x,overlayClassName:p,placement:u="top",trigger:b="hover",children:y,mouseEnterDelay:v=.1,mouseLeaveDelay:P=.1,onOpenChange:S,overlayStyle:O={},styles:g,classNames:C}=e,E=Ve(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:$,className:T,style:R,classNames:B,styles:W}=ne("popover"),_=$("popover",a),[I,L,r]=re(_),i=$(),d=z(p,L,r,T,B.root,C==null?void 0:C.root),c=z(B.body,C==null?void 0:C.body),[j,f]=oe(!1,{value:(o=e.open)!==null&&o!==void 0?o:e.visible,defaultValue:(s=e.defaultOpen)!==null&&s!==void 0?s:e.defaultVisible}),w=(k,A)=>{f(k,!0),S==null||S(k,A)},h=k=>{k.keyCode===Oe.ESC&&w(!1,k)},N=k=>{w(k)},D=M(m),V=M(x);return I(l.createElement(je,Object.assign({placement:u,trigger:b,mouseEnterDelay:v,mouseLeaveDelay:P},E,{prefixCls:_,classNames:{root:d,body:c},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},W.root),R),O),g==null?void 0:g.root),body:Object.assign(Object.assign({},W.body),g==null?void 0:g.body)},ref:n,open:j,onOpenChange:N,overlay:D||V?l.createElement(le,{prefixCls:_,title:D,content:V}):null,transitionName:Ne(i,"zoom-big",E.transitionName),"data-popover-inject":!0}),Ce(y,{onKeyDown:k=>{var A,X;l.isValidElement(y)&&((X=y==null?void 0:(A=y.props).onKeyDown)===null||X===void 0||X.call(A,k)),h(k)}})))}),ce=Ae;ce._InternalPanelDoNotUseOrYouWillBeFired=ie;const Fe=e=>{const{componentCls:n,iconCls:o,antCls:s,zIndexPopup:a,colorText:m,colorWarning:x,marginXXS:p,marginXS:u,fontSize:b,fontWeightStrong:y,colorTextHeading:v}=e;return{[n]:{zIndex:a,[`&${s}-popover`]:{fontSize:b},[`${n}-message`]:{marginBottom:u,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${n}-message-icon ${o}`]:{color:x,fontSize:b,lineHeight:1,marginInlineEnd:u},[`${n}-title`]:{fontWeight:y,color:v,"&:only-child":{fontWeight:"normal"}},[`${n}-description`]:{marginTop:p,color:m}},[`${n}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:u}}}}},He=e=>{const{zIndexPopupBase:n}=e;return{zIndexPopup:n+60}},de=se("Popconfirm",e=>Fe(e),He,{resetStyle:!1});var qe=function(e,n){var o={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&n.indexOf(s)<0&&(o[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)n.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(o[s[a]]=e[s[a]]);return o};const me=e=>{const{prefixCls:n,okButtonProps:o,cancelButtonProps:s,title:a,description:m,cancelText:x,okText:p,okType:u="primary",icon:b=l.createElement(ae,null),showCancel:y=!0,close:v,onConfirm:P,onCancel:S,onPopupClick:O}=e,{getPrefixCls:g}=l.useContext(Z),[C]=we("Popconfirm",Pe.Popconfirm),E=M(a),$=M(m);return l.createElement("div",{className:`${n}-inner-content`,onClick:O},l.createElement("div",{className:`${n}-message`},b&&l.createElement("span",{className:`${n}-message-icon`},b),l.createElement("div",{className:`${n}-message-text`},E&&l.createElement("div",{className:`${n}-title`},E),$&&l.createElement("div",{className:`${n}-description`},$))),l.createElement("div",{className:`${n}-buttons`},y&&l.createElement(K,Object.assign({onClick:S,size:"small"},s),x||(C==null?void 0:C.cancelText)),l.createElement(ze,{buttonProps:Object.assign(Object.assign({size:"small"},Se(u)),o),actionFn:P,close:v,prefixCls:g("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},p||(C==null?void 0:C.okText))))},Ke=e=>{const{prefixCls:n,placement:o,className:s,style:a}=e,m=qe(e,["prefixCls","placement","className","style"]),{getPrefixCls:x}=l.useContext(Z),p=x("popconfirm",n),[u]=de(p);return u(l.createElement(ie,{placement:o,className:z(p,s),style:a,content:l.createElement(me,Object.assign({prefixCls:p},m))}))};var Xe=function(e,n){var o={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&n.indexOf(s)<0&&(o[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)n.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(o[s[a]]=e[s[a]]);return o};const Ye=l.forwardRef((e,n)=>{var o,s;const{prefixCls:a,placement:m="top",trigger:x="click",okType:p="primary",icon:u=l.createElement(ae,null),children:b,overlayClassName:y,onOpenChange:v,onVisibleChange:P,overlayStyle:S,styles:O,classNames:g}=e,C=Xe(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:E,className:$,style:T,classNames:R,styles:B}=ne("popconfirm"),[W,_]=oe(!1,{value:(o=e.open)!==null&&o!==void 0?o:e.visible,defaultValue:(s=e.defaultOpen)!==null&&s!==void 0?s:e.defaultVisible}),I=(h,N)=>{_(h,!0),P==null||P(h),v==null||v(h,N)},L=h=>{I(!1,h)},r=h=>{var N;return(N=e.onConfirm)===null||N===void 0?void 0:N.call(void 0,h)},i=h=>{var N;I(!1,h),(N=e.onCancel)===null||N===void 0||N.call(void 0,h)},d=(h,N)=>{const{disabled:D=!1}=e;D||I(h,N)},c=E("popconfirm",a),j=z(c,$,y,R.root,g==null?void 0:g.root),f=z(R.body,g==null?void 0:g.body),[w]=de(c);return w(l.createElement(ce,Object.assign({},$e(C,["title"]),{trigger:x,placement:m,onOpenChange:d,open:W,ref:n,classNames:{root:j,body:f},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},B.root),T),S),O==null?void 0:O.root),body:Object.assign(Object.assign({},B.body),O==null?void 0:O.body)},content:l.createElement(me,Object.assign({okType:p,icon:u},e,{prefixCls:c,close:L,onConfirm:r,onCancel:i})),"data-popover-inject":!0}),b))}),pe=Ye;pe._InternalPanelDoNotUseOrYouWillBeFired=Ke;const{Title:J,Text:Q}=Be,Ge=()=>{const[e,n]=l.useState({}),[o,s]=l.useState({}),[a,m]=l.useState(!0),[x,p]=l.useState(null),[u,b]=l.useState({}),[y,v]=l.useState({}),[P,S]=l.useState({}),[O,g]=l.useState(new Date),C=(r,i)=>{const d=`${r}-${i}`;b(c=>({...c,[d]:!0}))},E=(r,i)=>{const d=`${r}-${i}`;b(c=>({...c,[d]:!1}))},$=r=>{try{const[i,d]=r.split(" "),[c,j,f]=i.split("/"),[w,h]=d.split(":"),N=new Date(f,j-1,c,w,h),V=Math.floor((new Date-N)/6e4);return Math.max(0,V)}catch{return 0}},T=async()=>{try{p(null),m(!0);const r=await fetch("/api/reception-foods",{method:"GET",headers:{Accept:"application/json"},credentials:"same-origin"});if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);const i=await r.json();if(i.success)n(i.processedFoods||{}),s(i.waitingFood||{}),g(new Date),R(i.waitingFoodByName||[]);else throw new Error("API returned error")}catch(r){console.error("Error fetching orders:",r),p(r.message)}finally{m(!1)}},R=r=>{const i=r.map(d=>`${d.name} ${d.quantity} suất`).join(", ");i&&_e("Các Món Chưa trả: "+i)},B=async(r,i,d,c,j)=>{try{d===4&&v(w=>({...w,[c]:!0}));const f=await fetch("/api/reception-foods/update-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:r,dish_name:i,status:d})});if(!f.ok)throw new Error(`HTTP error! status: ${f.status}`);await T(),d===4&&E(j,i)}catch(f){console.error("Error updating item status:",f),p(`Lỗi cập nhật: ${f.message}`)}finally{v(f=>({...f,[c]:!1})),S(f=>({...f,[c]:!1}))}},W=async(r,i,d)=>{const c=`${d}-${i}`;await B(r,i,4,c,d)};l.useEffect(()=>{T();const r=setInterval(()=>{T()},6e4);return()=>clearInterval(r)},[]);const _=r=>r<15?"green":r<30?"orange":"red",I=()=>Object.values(e).reduce((r,i)=>r+i.total_quantity,0),L=()=>Object.values(o).reduce((r,i)=>r+i.foods.reduce((d,c)=>d+c.total_quantity,0),0);return a&&Object.keys(e).length===0&&Object.keys(o).length===0?t.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx(Y,{className:"text-6xl text-gray-400 mb-4"}),t.jsx("div",{className:"text-xl text-gray-500",children:"Đang tải dữ liệu..."})]})}):x&&Object.keys(e).length===0&&Object.keys(o).length===0?t.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-red-500 text-2xl mb-4",children:"⚠️ Lỗi kết nối"}),t.jsx("div",{className:"text-gray-500 mb-4 text-lg",children:x}),t.jsx(K,{type:"primary",size:"large",onClick:T,children:"Thử lại"})]})}):t.jsx("div",{className:"min-h-screen bg-gray-50",style:{padding:"12px"},children:t.jsxs("div",{className:"max-w-full mx-auto",children:[t.jsxs("div",{className:"mb-4 bg-white rounded-lg shadow-sm p-4",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(G,{className:"text-red-500 text-2xl mr-3"}),t.jsxs("div",{children:[t.jsx(J,{level:3,className:"mb-0",children:"Quản Lý Món Ăn"}),t.jsxs(Q,{type:"secondary",className:"text-sm",children:[t.jsx(Y,{className:"mr-1"}),O.toLocaleTimeString()]})]})]}),t.jsx(K,{type:"primary",icon:t.jsx(Ie,{}),loading:a,onClick:T,size:"large",children:"Cập nhật"})]}),x&&t.jsxs("div",{className:"mt-2 text-red-500 text-sm",children:["⚠️ ",x]})]}),t.jsxs(Ee,{gutter:[16,16],children:[t.jsx(ee,{xs:24,lg:12,children:t.jsx(F,{title:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(G,{className:"text-orange-500 text-xl mr-2"}),t.jsx("span",{className:"text-lg font-semibold",children:"Món Ăn Đã Sẵn Sàng"})]}),t.jsx(q,{count:I(),style:{backgroundColor:"#52c41a"}})]}),className:"shadow-sm",bodyStyle:{padding:"16px",height:"calc(100vh - 200px)",overflowY:"auto"},children:Object.keys(e).length===0?t.jsxs("div",{className:"text-center text-gray-500 py-8",children:[t.jsx(G,{className:"text-4xl mb-2 text-gray-300"}),t.jsx("div",{className:"text-lg",children:"Chưa có món nào hoàn thành"})]}):t.jsx("div",{className:"space-y-4",children:Object.entries(e).map(([r,i])=>{var d;return t.jsxs(F,{size:"small",className:"border-l-4 border-l-green-500 shadow-sm hover:shadow-md transition-shadow",bodyStyle:{padding:"12px"},children:[t.jsx("div",{className:"mb-3",children:t.jsx(J,{level:5,className:"mb-1 text-green-600 text-base",children:r})}),t.jsx("div",{className:"space-y-3",children:(d=i.orders)==null?void 0:d.sort((c,j)=>$(j.ordered_at)-$(c.ordered_at)).map((c,j)=>{var h;const f=`${c.order_code}-${r}`,w=$(c.ordered_at);return t.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[t.jsxs("div",{className:"flex justify-between items-start mb-2",children:[t.jsx("div",{className:"flex flex-wrap gap-1",children:((h=c.tables)==null?void 0:h.length)>0&&c.tables.map(N=>t.jsx(H,{color:"blue",className:"text-xs",children:N??"N/A"},N))}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Q,{type:"secondary",className:"text-xs font-bold",children:c.name}),t.jsx(q,{count:c.quantity,style:{backgroundColor:"#1890ff"}})]})]}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("div",{className:"flex gap-1",children:t.jsxs(H,{color:_(w),className:"font-medium m-0",children:[w," phút"]})}),t.jsx(pe,{title:"Xác nhận trả khách",description:`Đánh dấu ${r} đã trả khách?`,open:u[f]||!1,onConfirm:()=>W(c.items,r,c.order_code),okButtonProps:{loading:y[f]||!1},onCancel:()=>E(c.order_code,r),okText:"Đã trả",cancelText:"Hủy",children:t.jsx(K,{type:"primary",size:"small",icon:t.jsx(ke,{}),onClick:()=>C(c.order_code,r),className:"bg-green-500 hover:bg-green-600 border-green-500",children:"Đã Trả"})})]})]},j)})})]},r)})})})}),t.jsx(ee,{xs:24,lg:12,children:t.jsx(F,{title:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(U,{className:"text-blue-500 text-xl mr-2"}),t.jsx("span",{className:"text-lg font-semibold",children:"Các Món Chưa trả"})]}),t.jsx(q,{count:L(),style:{backgroundColor:"#1890ff"}})]}),className:"shadow-sm",bodyStyle:{padding:"16px",height:"calc(100vh - 200px)",overflowY:"auto"},children:Object.keys(o).length===0?t.jsxs("div",{className:"text-center text-gray-500 py-8",children:[t.jsx(U,{className:"text-4xl mb-2 text-gray-300"}),t.jsx("div",{className:"text-lg",children:"Tất cả đơn hàng đã hoàn thành"})]}):t.jsx("div",{className:"space-y-4",children:Object.entries(o).map(([r,i])=>{var d,c;return t.jsxs(F,{size:"small",className:"border-l-4 border-l-blue-500 shadow-sm hover:shadow-md transition-shadow",bodyStyle:{padding:"12px"},children:[t.jsxs("div",{className:"mb-3",children:[t.jsx(J,{level:5,className:"mb-1 text-blue-600 text-base",children:i==null?void 0:i.name}),t.jsx("div",{className:"flex flex-wrap gap-1",children:((d=i.tables)==null?void 0:d.length)>0&&i.tables.map(j=>t.jsxs(H,{color:"blue",size:"small",children:[t.jsx(U,{className:"mr-1"}),j??"N/A"]},j))})]}),t.jsx("div",{className:"space-y-2",children:(c=i.foods)==null?void 0:c.map((j,f)=>{const w=$(j.ordered_at);return t.jsx("div",{className:"bg-gray-50 rounded p-2",children:t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("div",{className:"flex items-center space-x-2 flex-1",children:[t.jsx(Q,{strong:!0,className:"text-sm",children:j.name}),t.jsx(q,{count:j.total_quantity,size:"small"})]}),t.jsxs(H,{color:_(w),className:"font-medium ml-2",children:[w," phút"]})]})},f)})})]},r)})})})})]}),t.jsxs("div",{className:"mt-6 text-center text-gray-500 text-sm bg-white rounded-lg p-3 shadow-sm",children:[t.jsx(Y,{className:"mr-1"}),"Tự động cập nhật mỗi 30 giây"]})]})})},te=document.getElementById("employee-management-order");te&&Te.createRoot(te).render(t.jsx(Ge,{}));
