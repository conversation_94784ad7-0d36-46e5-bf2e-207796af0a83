import{t as Mt,r as c,C as ot,o as Vt,b1 as Et,a as it,$ as zt,a3 as V,a4 as E,b2 as dt,j as e,aD as $e,aB as U,a2 as O,aC as Te,b3 as ut,b4 as It,c as Ht,b0 as Dt}from"./index-CHvake0r.js";import{f as ke,e as At,h as Lt,i as P,b as Ft,d as we,L as Re,I as Ut,r as xe,R as mt,c as Bt}from"./formatter-Blxj4gZL.js";import{T as me}from"./index-Bbq37T-v.js";import{s as Pe}from"./index-CIWcONm8.js";import{M as ie,a as he,F as Kt}from"./index-Cqw7XJJF.js";import{S as ht}from"./index-D_dKmeqG.js";import{E as de,S as qe,a as Yt,b as ft}from"./EyeOutlined-D5ZQtqsR.js";import{a as xt,u as gt,L as Gt,S as Xt,R as pt}from"./DeleteOutlined-C56jyccF.js";import{D as vt}from"./index-DIqWL9-0.js";import{R as Me}from"./ClockCircleOutlined-B61igLBq.js";import{R as Qt}from"./CheckCircleOutlined-DMtaSJBb.js";import{R as Wt,a as Jt,b as Zt}from"./RocketOutlined-BtbtbzoE.js";import{R as er,B as oe}from"./FireOutlined-B7wZholJ.js";import"./EditOutlined-t_UPdXXr.js";import"./ActionButton-BdVp3AS3.js";function tr(t,a,r){return typeof r=="boolean"?r:t.length?!0:Mt(a).some(i=>i.type===xt)}var yt=function(t,a){var r={};for(var l in t)Object.prototype.hasOwnProperty.call(t,l)&&a.indexOf(l)<0&&(r[l]=t[l]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,l=Object.getOwnPropertySymbols(t);i<l.length;i++)a.indexOf(l[i])<0&&Object.prototype.propertyIsEnumerable.call(t,l[i])&&(r[l[i]]=t[l[i]]);return r};function fe({suffixCls:t,tagName:a,displayName:r}){return l=>c.forwardRef((s,o)=>c.createElement(l,Object.assign({ref:o,suffixCls:t,tagName:a},s)))}const Oe=c.forwardRef((t,a)=>{const{prefixCls:r,suffixCls:l,className:i,tagName:s}=t,o=yt(t,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:g}=c.useContext(ot),f=g("layout",r),[v,b,j]=gt(f),k=l?`${f}-${l}`:f;return v(c.createElement(s,Object.assign({className:it(r||k,i,b,j),ref:a},o)))}),rr=c.forwardRef((t,a)=>{const{direction:r}=c.useContext(ot),[l,i]=c.useState([]),{prefixCls:s,className:o,rootClassName:g,children:f,hasSider:v,tagName:b,style:j}=t,k=yt(t,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),H=Vt(k,["suffixCls"]),{getPrefixCls:D,className:$,style:_}=Et("layout"),N=D("layout",s),S=tr(l,f,v),[w,R,p]=gt(N),T=it(N,{[`${N}-has-sider`]:S,[`${N}-rtl`]:r==="rtl"},$,o,g,R,p),n=c.useMemo(()=>({siderHook:{addSider:u=>{i(y=>[].concat(zt(y),[u]))},removeSider:u=>{i(y=>y.filter(F=>F!==u))}}}),[]);return w(c.createElement(Gt.Provider,{value:n},c.createElement(b,Object.assign({ref:a,className:T,style:Object.assign(Object.assign({},_),j)},H),f)))}),ar=fe({tagName:"div",displayName:"Layout"})(rr),nr=fe({suffixCls:"header",tagName:"header",displayName:"Header"})(Oe),sr=fe({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(Oe),lr=fe({suffixCls:"content",tagName:"main",displayName:"Content"})(Oe),B=ar;B.Header=nr;B.Footer=sr;B.Content=lr;B.Sider=xt;B._InternalSiderContext=Xt;var cr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"}}]},name:"appstore",theme:"outlined"},or=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:cr}))},ge=c.forwardRef(or),ir={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"},dr=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:ir}))},ur=c.forwardRef(dr),mr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"},hr=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:mr}))},bt=c.forwardRef(hr),fr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"},xr=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:fr}))},gr=c.forwardRef(xr),pr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9zM512 814.8S156 586.7 156 385.5C156 283.6 240.3 201 344.3 201c73.1 0 136.5 40.8 167.7 100.4C543.2 241.8 606.6 201 679.7 201c104 0 188.3 82.6 188.3 184.5 0 201.2-356 429.3-356 429.3z"}}]},name:"heart",theme:"outlined"},vr=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:pr}))},Ve=c.forwardRef(vr),yr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M536.1 273H488c-4.4 0-8 3.6-8 8v275.3c0 2.6 1.2 5 3.3 6.5l165.3 120.7c3.6 2.6 8.6 1.9 11.2-1.7l28.6-39c2.7-3.7 1.9-8.7-1.7-11.2L544.1 528.5V281c0-4.4-3.6-8-8-8zm219.8 75.2l156.8 38.3c5 1.2 9.9-2.6 9.9-7.7l.8-161.5c0-6.7-7.7-10.5-12.9-6.3L752.9 334.1a8 8 0 003 14.1zm167.7 301.1l-56.7-19.5a8 8 0 00-10.1 4.8c-1.9 5.1-3.9 10.1-6 15.1-17.8 42.1-43.3 80-75.9 112.5a353 353 0 01-112.5 75.9 352.18 352.18 0 01-137.7 27.8c-47.8 0-94.1-9.3-137.7-27.8a353 353 0 01-112.5-75.9c-32.5-32.5-58-70.4-75.9-112.5A353.44 353.44 0 01171 512c0-47.8 9.3-94.2 27.8-137.8 17.8-42.1 43.3-80 75.9-112.5a353 353 0 01112.5-75.9C430.6 167.3 477 158 524.8 158s94.1 9.3 137.7 27.8A353 353 0 01775 261.7c10.2 10.3 19.8 21 28.6 32.3l59.8-46.8C784.7 146.6 662.2 81.9 524.6 82 285 82.1 92.6 276.7 95 516.4 97.4 751.9 288.9 942 524.8 942c185.5 0 343.5-117.6 403.7-282.3 1.5-4.2-.7-8.9-4.9-10.4z"}}]},name:"history",theme:"outlined"},br=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:yr}))},jr=c.forwardRef(br),Nr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},_r=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:Nr}))},Cr=c.forwardRef(_r),Sr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"},$r=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:Sr}))},jt=c.forwardRef($r),wr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},Tr=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:wr}))},Ee=c.forwardRef(Tr),Pr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"},Or=function(a,r){return c.createElement(V,E({},a,{ref:r,icon:Pr}))},kr=c.forwardRef(Or),X={},pe={exports:{}},ze;function L(){return ze||(ze=1,function(t){function a(r){return r&&r.__esModule?r:{default:r}}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports}(pe)),pe.exports}var Q={},Ie;function Rr(){if(Ie)return Q;Ie=1,Object.defineProperty(Q,"__esModule",{value:!0}),Q.default=void 0;var t={items_per_page:"/ trang",jump_to:"Đến",jump_to_confirm:"xác nhận",page:"Trang",prev_page:"Trang Trước",next_page:"Trang Kế",prev_5:"Về 5 Trang Trước",next_5:"Đến 5 Trang Kế",prev_3:"Về 3 Trang Trước",next_3:"Đến 3 Trang Kế",page_size:"kích thước trang"};return Q.default=t,Q}var W={},J={},Z={},ve={exports:{}},ye={exports:{}},be={exports:{}},je={exports:{}},He;function Nt(){return He||(He=1,function(t){function a(r){"@babel/helpers - typeof";return t.exports=a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(l){return typeof l}:function(l){return l&&typeof Symbol=="function"&&l.constructor===Symbol&&l!==Symbol.prototype?"symbol":typeof l},t.exports.__esModule=!0,t.exports.default=t.exports,a(r)}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports}(je)),je.exports}var Ne={exports:{}},De;function qr(){return De||(De=1,function(t){var a=Nt().default;function r(l,i){if(a(l)!="object"||!l)return l;var s=l[Symbol.toPrimitive];if(s!==void 0){var o=s.call(l,i||"default");if(a(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(l)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}(Ne)),Ne.exports}var Ae;function Mr(){return Ae||(Ae=1,function(t){var a=Nt().default,r=qr();function l(i){var s=r(i,"string");return a(s)=="symbol"?s:s+""}t.exports=l,t.exports.__esModule=!0,t.exports.default=t.exports}(be)),be.exports}var Le;function Vr(){return Le||(Le=1,function(t){var a=Mr();function r(l,i,s){return(i=a(i))in l?Object.defineProperty(l,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):l[i]=s,l}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}(ye)),ye.exports}var Fe;function _t(){return Fe||(Fe=1,function(t){var a=Vr();function r(i,s){var o=Object.keys(i);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(i);s&&(g=g.filter(function(f){return Object.getOwnPropertyDescriptor(i,f).enumerable})),o.push.apply(o,g)}return o}function l(i){for(var s=1;s<arguments.length;s++){var o=arguments[s]!=null?arguments[s]:{};s%2?r(Object(o),!0).forEach(function(g){a(i,g,o[g])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(o)):r(Object(o)).forEach(function(g){Object.defineProperty(i,g,Object.getOwnPropertyDescriptor(o,g))})}return i}t.exports=l,t.exports.__esModule=!0,t.exports.default=t.exports}(ve)),ve.exports}var ee={},Ue;function Ct(){return Ue||(Ue=1,Object.defineProperty(ee,"__esModule",{value:!0}),ee.commonLocale=void 0,ee.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),ee}var Be;function Er(){if(Be)return Z;Be=1;var t=L().default;Object.defineProperty(Z,"__esModule",{value:!0}),Z.default=void 0;var a=t(_t()),r=Ct(),l=(0,a.default)((0,a.default)({},r.commonLocale),{},{locale:"vi_VN",today:"Hôm nay",now:"Bây giờ",backToToday:"Trở về hôm nay",ok:"OK",clear:"Xóa",week:"Tuần",month:"Tháng",year:"Năm",timeSelect:"Chọn thời gian",dateSelect:"Chọn ngày",weekSelect:"Chọn tuần",monthSelect:"Chọn tháng",yearSelect:"Chọn năm",decadeSelect:"Chọn thập kỷ",dateFormat:"D/M/YYYY",dateTimeFormat:"D/M/YYYY HH:mm:ss",previousMonth:"Tháng trước (PageUp)",nextMonth:"Tháng sau (PageDown)",previousYear:"Năm trước (Control + left)",nextYear:"Năm sau (Control + right)",previousDecade:"Thập kỷ trước",nextDecade:"Thập kỷ sau",previousCentury:"Thế kỷ trước",nextCentury:"Thế kỷ sau"});return Z.default=l,Z}var te={},Ke;function St(){if(Ke)return te;Ke=1,Object.defineProperty(te,"__esModule",{value:!0}),te.default=void 0;const t={placeholder:"Chọn thời gian",rangePlaceholder:["Bắt đầu","Kết thúc"]};return te.default=t,te}var Ye;function $t(){if(Ye)return J;Ye=1;var t=L().default;Object.defineProperty(J,"__esModule",{value:!0}),J.default=void 0;var a=t(Er()),r=t(St());const l={lang:Object.assign({placeholder:"Chọn thời điểm",yearPlaceholder:"Chọn năm",quarterPlaceholder:"Chọn quý",monthPlaceholder:"Chọn tháng",weekPlaceholder:"Chọn tuần",rangePlaceholder:["Ngày bắt đầu","Ngày kết thúc"],rangeYearPlaceholder:["Năm bắt đầu","Năm kết thúc"],rangeQuarterPlaceholder:["Quý bắt đầu","Quý kết thúc"],rangeMonthPlaceholder:["Tháng bắt đầu","Tháng kết thúc"],rangeWeekPlaceholder:["Tuần bắt đầu","Tuần kết thúc"]},a.default),timePickerLocale:Object.assign({},r.default)};return J.default=l,J}var Ge;function zr(){if(Ge)return W;Ge=1;var t=L().default;Object.defineProperty(W,"__esModule",{value:!0}),W.default=void 0;var a=t($t());return W.default=a.default,W}var Xe;function Ir(){if(Xe)return X;Xe=1;var t=L().default;Object.defineProperty(X,"__esModule",{value:!0}),X.default=void 0;var a=t(Rr()),r=t(zr()),l=t($t()),i=t(St());const s="${label} không phải kiểu ${type} hợp lệ",o={locale:"vi",Pagination:a.default,DatePicker:l.default,TimePicker:i.default,Calendar:r.default,global:{placeholder:"Vui lòng chọn",close:"Đóng"},Table:{filterTitle:"Bộ lọc",filterConfirm:"Đồng ý",filterReset:"Bỏ lọc",filterEmptyText:"Không có bộ lọc",filterCheckAll:"Chọn tất cả",filterSearchPlaceholder:"Tìm kiếm bộ lọc",emptyText:"Trống",selectAll:"Chọn tất cả",selectInvert:"Chọn ngược lại",selectNone:"Bỏ chọn tất cả",selectionAll:"Chọn tất cả",sortTitle:"Sắp xếp",expand:"Mở rộng dòng",collapse:"Thu gọn dòng",triggerDesc:"Nhấp để sắp xếp giảm dần",triggerAsc:"Nhấp để sắp xếp tăng dần",cancelSort:"Nhấp để hủy sắp xếp"},Tour:{Next:"Tiếp",Previous:"Trước",Finish:"Hoàn thành"},Modal:{okText:"Đồng ý",cancelText:"Hủy",justOkText:"OK"},Popconfirm:{okText:"Đồng ý",cancelText:"Hủy"},Transfer:{titles:["",""],searchPlaceholder:"Tìm ở đây",itemUnit:"mục",itemsUnit:"mục",remove:"Gỡ bỏ",selectCurrent:"Chọn trang hiện tại",removeCurrent:"Gỡ bỏ trang hiện tại",selectAll:"Chọn tất cả",removeAll:"Gỡ bỏ tất cả",selectInvert:"Đảo ngược trang hiện tại"},Upload:{uploading:"Đang tải lên...",removeFile:"Gỡ bỏ tập tin",uploadError:"Lỗi tải lên",previewFile:"Xem trước tập tin",downloadFile:"Tải tập tin"},Empty:{description:"Trống"},Icon:{icon:"icon"},Text:{edit:"Chỉnh sửa",copy:"Sao chép",copied:"Đã sao chép",expand:"Mở rộng"},Form:{optional:"(Tùy chọn)",defaultValidateMessages:{default:"${label} không đáp ứng điều kiện quy định",required:"Hãy nhập thông tin cho trường ${label}",enum:"${label} phải có giá trị nằm trong tập [${enum}]",whitespace:"${label} không được chứa khoảng trắng",date:{format:"${label} sai định dạng ngày tháng",parse:"Không thể chuyển ${label} sang kiểu Ngày tháng",invalid:"${label} không phải giá trị Ngày tháng hợp lệ"},types:{string:s,method:s,array:s,object:s,number:s,date:s,boolean:s,integer:s,float:s,regexp:s,email:s,url:s,hex:s},string:{len:"${label} phải dài đúng ${len} ký tự",min:"Độ dài tối thiểu trường ${label} là ${min} ký tự",max:"Độ dài tối đa trường ${label} là ${max} ký tự",range:"Độ dài trường ${label} phải từ ${min} đến ${max} ký tự"},number:{len:"${label} phải bằng ${len}",min:"${label} phải lớn hơn hoặc bằng ${min}",max:"${label} phải nhỏ hơn hoặc bằng ${max}",range:"${label} phải nằm trong khoảng ${min}-${max}"},array:{len:"Mảng ${label} phải có ${len} phần tử ",min:"Mảng ${label} phải chứa tối thiểu ${min} phần tử ",max:"Mảng ${label} phải chứa tối đa ${max} phần tử ",range:"Mảng ${label} phải chứa từ ${min}-${max} phần tử"},pattern:{mismatch:"${label} không thỏa mãn mẫu kiểm tra ${pattern}"}}},Image:{preview:"Xem trước"},QRCode:{expired:"Mã QR hết hạn",refresh:"Làm mới"}};return X.default=o,X}var _e,Qe;function Hr(){return Qe||(Qe=1,_e=Ir()),_e}var Dr=Hr();const Ar=dt(Dr);var re={},ae={},We;function Lr(){if(We)return ae;We=1,Object.defineProperty(ae,"__esModule",{value:!0}),ae.default=void 0;var t={items_per_page:"/ page",jump_to:"Go to",jump_to_confirm:"confirm",page:"Page",prev_page:"Previous Page",next_page:"Next Page",prev_5:"Previous 5 Pages",next_5:"Next 5 Pages",prev_3:"Previous 3 Pages",next_3:"Next 3 Pages",page_size:"Page Size"};return ae.default=t,ae}var ne={},se={},le={},Je;function Fr(){if(Je)return le;Je=1;var t=L().default;Object.defineProperty(le,"__esModule",{value:!0}),le.default=void 0;var a=t(_t()),r=Ct(),l=(0,a.default)((0,a.default)({},r.commonLocale),{},{locale:"en_US",today:"Today",now:"Now",backToToday:"Back to today",ok:"OK",clear:"Clear",week:"Week",month:"Month",year:"Year",timeSelect:"select time",dateSelect:"select date",weekSelect:"Choose a week",monthSelect:"Choose a month",yearSelect:"Choose a year",decadeSelect:"Choose a decade",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"Previous month (PageUp)",nextMonth:"Next month (PageDown)",previousYear:"Last year (Control + left)",nextYear:"Next year (Control + right)",previousDecade:"Last decade",nextDecade:"Next decade",previousCentury:"Last century",nextCentury:"Next century"});return le.default=l,le}var ce={},Ze;function wt(){if(Ze)return ce;Ze=1,Object.defineProperty(ce,"__esModule",{value:!0}),ce.default=void 0;const t={placeholder:"Select time",rangePlaceholder:["Start time","End time"]};return ce.default=t,ce}var et;function Tt(){if(et)return se;et=1;var t=L().default;Object.defineProperty(se,"__esModule",{value:!0}),se.default=void 0;var a=t(Fr()),r=t(wt());const l={lang:Object.assign({placeholder:"Select date",yearPlaceholder:"Select year",quarterPlaceholder:"Select quarter",monthPlaceholder:"Select month",weekPlaceholder:"Select week",rangePlaceholder:["Start date","End date"],rangeYearPlaceholder:["Start year","End year"],rangeQuarterPlaceholder:["Start quarter","End quarter"],rangeMonthPlaceholder:["Start month","End month"],rangeWeekPlaceholder:["Start week","End week"]},a.default),timePickerLocale:Object.assign({},r.default)};return se.default=l,se}var tt;function Ur(){if(tt)return ne;tt=1;var t=L().default;Object.defineProperty(ne,"__esModule",{value:!0}),ne.default=void 0;var a=t(Tt());return ne.default=a.default,ne}var rt;function Br(){if(rt)return re;rt=1;var t=L().default;Object.defineProperty(re,"__esModule",{value:!0}),re.default=void 0;var a=t(Lr()),r=t(Ur()),l=t(Tt()),i=t(wt());const s="${label} is not a valid ${type}",o={locale:"en",Pagination:a.default,DatePicker:l.default,TimePicker:i.default,Calendar:r.default,global:{placeholder:"Please select",close:"Close"},Table:{filterTitle:"Filter menu",filterConfirm:"OK",filterReset:"Reset",filterEmptyText:"No filters",filterCheckAll:"Select all items",filterSearchPlaceholder:"Search in filters",emptyText:"No data",selectAll:"Select current page",selectInvert:"Invert current page",selectNone:"Clear all data",selectionAll:"Select all data",sortTitle:"Sort",expand:"Expand row",collapse:"Collapse row",triggerDesc:"Click to sort descending",triggerAsc:"Click to sort ascending",cancelSort:"Click to cancel sorting"},Tour:{Next:"Next",Previous:"Previous",Finish:"Finish"},Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Popconfirm:{okText:"OK",cancelText:"Cancel"},Transfer:{titles:["",""],searchPlaceholder:"Search here",itemUnit:"item",itemsUnit:"items",remove:"Remove",selectCurrent:"Select current page",removeCurrent:"Remove current page",selectAll:"Select all data",deselectAll:"Deselect all data",removeAll:"Remove all data",selectInvert:"Invert current page"},Upload:{uploading:"Uploading...",removeFile:"Remove file",uploadError:"Upload error",previewFile:"Preview file",downloadFile:"Download file"},Empty:{description:"No data"},Icon:{icon:"icon"},Text:{edit:"Edit",copy:"Copy",copied:"Copied",expand:"Expand",collapse:"Collapse"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:s,method:s,array:s,object:s,number:s,date:s,boolean:s,integer:s,float:s,regexp:s,email:s,url:s,hex:s},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}},Image:{preview:"Preview"},QRCode:{expired:"QR code expired",refresh:"Refresh",scanned:"Scanned"},ColorPicker:{presetEmpty:"Empty",transparent:"Transparent",singleColor:"Single",gradientColor:"Gradient"}};return re.default=o,re}var Ce,at;function Kr(){return at||(at=1,Ce=Br()),Ce}var Yr=Kr();const Gr=dt(Yr),{Title:nt,Text:C}=me,Xr=({orderId:t,onOrderCountChange:a,activeTab:r,handleRequestPayment:l,paymentLoading:i})=>{const s=d=>{const m={0:e.jsx(Me,{}),1:e.jsx(we,{}),2:e.jsx(kr,{spin:!0}),3:e.jsx(Wt,{}),4:e.jsx(Qt,{}),5:e.jsx(Ft,{}),default:e.jsx(Me,{})};return m[d]||m.default},[o,g]=c.useState(null),[f,v]=c.useState([]),[b,j]=c.useState(!0),[k,H]=c.useState(null),[D,$]=c.useState(0),[_,N]=Pe.useMessage(),[S,w]=ie.useModal();c.useEffect(()=>{r==="history"&&R()},[t,r]);const R=async()=>{var d,m,z;try{j(!0);const I=await(await fetch(`/api/orders/current?since=${t}`)).json();I.success&&(g(I.orders),v(((d=I.orders)==null?void 0:d.order_items)||[]),a&&a(((z=(m=I.orders)==null?void 0:m.order_items)==null?void 0:z.length)||0))}catch(A){console.error("Error fetching current order:",A)}finally{j(!1)}},p=async(d,m)=>{var z;if(m<1){T(d);return}try{(await(await fetch(`/api/orders/items/${d}`,{method:"PATCH",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":(z=document.querySelector('meta[name="csrf-token"]'))==null?void 0:z.content},body:JSON.stringify({quantity:m})})).json()).success?(v(Y=>Y.map(G=>G.id===d?{...G,quantity:m}:G)),_.success("Đã cập nhật số lượng")):_.error("Không thể cập nhật số lượng")}catch{_.error("Lỗi khi cập nhật số lượng")}},T=async d=>{console.log("handleRemoveItem called with itemId:",d),S.confirm({title:"Xóa món khỏi đơn hàng?",content:"Bạn có chắc muốn xóa món này khỏi đơn hàng?",onOk:async()=>{var m;try{(await(await fetch(`/api/orders/items/${d}`,{method:"DELETE",headers:{"X-CSRF-TOKEN":(m=document.querySelector('meta[name="csrf-token"]'))==null?void 0:m.content}})).json()).success?(v(I=>I.filter(Y=>Y.id!==d)),_.success("Đã xóa món khỏi đơn hàng")):_.error("Không thể xóa món")}catch{_.error("Lỗi khi xóa món")}}})},n=c.useMemo(()=>f.map(d=>`${d.id}-${d.quantity}-${d.unit_price}-${d.status}`).join("|"),[f]),u=c.useMemo(()=>f.reduce((d,m)=>d+(m.status===3?0:m.quantity*m.unit_price||0),0),[n]),y=c.useMemo(()=>f.reduce((d,m)=>d+(m.quantity*m.unit_price||0),0),[n]),F=o&&[0].includes(o.status);return b?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(ht,{size:"large"})}):o?e.jsxs("div",{className:"order-history-container p-4 bg-gray-50 min-h-screen pb-24",children:[N,w,e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{children:[e.jsx(nt,{level:3,className:"mb-1 text-gray-800",children:"Đơn hàng"}),e.jsx(C,{className:"text-gray-500",children:ke(o.created_at)})]}),e.jsx($e,{color:Lt(o.status),icon:s(o.status),className:"rounded-full px-3 py-1",children:At(o.status)})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[e.jsxs(U,{size:"small",className:"text-center",children:[e.jsx("div",{className:"font-bold text-blue-600",children:f.length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Loại món"})]}),e.jsxs(U,{size:"small",className:"text-center",children:[e.jsx("div",{className:"font-bold text-orange-600",children:f.reduce((d,m)=>d+m.quantity,0)}),e.jsx("div",{className:"text-xs text-gray-500",children:"Tổng số"})]})]}),F&&e.jsx("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-3 rounded",children:e.jsx(C,{className:"text-blue-800 text-sm",children:"💡 Bạn có thể chỉnh sửa đơn hàng này vì chưa được xác nhận"})})]}),f.length===0?e.jsx(de,{description:"Đơn hàng trống",className:"mt-12"}):e.jsx("div",{className:"space-y-3 mb-6",children:f.map(d=>{var m;return e.jsx(U,{className:"border border-gray-200",children:e.jsx("div",{className:"flex gap-3",children:e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"w-full flex justify-between",children:e.jsx(C,{strong:!0,className:"text-gray-800",children:(m=d==null?void 0:d.food)==null?void 0:m.name})}),e.jsxs("div",{className:"text-sm text-gray-600",children:[P(d==null?void 0:d.unit_price)," / món"]})]}),F&&e.jsx(O,{type:"text",size:"small",icon:e.jsx(pt,{}),onClick:()=>T(d.id),className:"text-red-500 hover:text-red-700 w-8 h-8"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"flex items-center gap-2",children:F?e.jsxs("div",{className:"flex items-center bg-gray-50 rounded-lg p-1",children:[e.jsx(O,{type:"text",size:"small",icon:e.jsx(jt,{}),onClick:()=>p(d.id,d.quantity-1),className:"w-8 h-8 rounded-md hover:bg-white border-0"}),e.jsx("div",{className:"mx-2 min-w-[40px] text-center",children:e.jsx(C,{strong:!0,className:"text-base",children:d.quantity})}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(Te,{}),onClick:()=>p(d.id,d.quantity+1),className:"w-8 h-8 rounded-md hover:bg-white border-0"})]}):e.jsxs(C,{className:"text-gray-600",children:["Số lượng: ",d.quantity]})}),e.jsx(C,{strong:!0,className:"text-green-600",children:P(d.unit_price*d.quantity)})]}),e.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["Đặt lúc: ",ke(d.created_at)]})]})})},d.id)})}),e.jsxs(U,{className:"mt-4 shadow-lg",children:[e.jsx(nt,{level:5,className:"mb-3",children:"Chi tiết thanh toán"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(C,{children:"Tạm tính:"}),e.jsx(C,{children:P(y)})]}),o.discount_amount>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx(C,{children:"Giảm giá:"}),e.jsx(C,{children:P(o.discount_amount)})]}),e.jsx(vt,{className:"my-2"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{strong:!0,className:"text-lg",children:"Tổng cộng:"}),e.jsx(C,{strong:!0,className:"text-lg text-green-600",children:o.payment_method?P(o.total_amount):P(u)})]})]}),o.payment_method&&e.jsx("div",{className:"mt-3 pt-3 border-t border-gray-100",children:e.jsxs(C,{className:"text-sm text-gray-600",children:["Thanh toán: ",o.payment_method]})}),(o==null?void 0:o.id)&&![0,4,5,6].includes(o.status)&&e.jsx(O,{type:"success",shape:"round",icon:e.jsx(bt,{}),size:"large",loading:i,onClick:l,className:"shadow-lg bg-green-600 hover:bg-green-700 text-base font-semibold text-white w-full mt-3",children:"Gọi thanh toán"})]})]}):e.jsx("div",{className:"order-history-container p-4 bg-gray-50 min-h-screen",children:e.jsx(de,{description:"Chưa có đơn hàng nào",className:"mt-20"})})},{Title:Sa,Text:Se,Paragraph:Qr}=me,{Search:$a}=he,Wr=({order:t,onAddToCart:a,cartItems:r=[]})=>{const[l,i]=c.useState([]),[s,o]=c.useState([]),[g,f]=c.useState(!0),[v,b]=c.useState(""),[j,k]=c.useState("all"),[H,D]=c.useState([]);c.useEffect(()=>{$()},[]);const $=async()=>{try{f(!0);const u=await(await fetch(`/api/menu/${t==null?void 0:t.id}`)).json();u.success&&(i(u.data.menu_items||[]),o(u.data.categories||[]))}catch(n){console.error("Error fetching menu:",n)}finally{f(!1)}},_=()=>{let n=l;if(j!=="all"&&(n=n.filter(u=>u.categories.some(y=>y.id===j))),v){const u=xe(v);n=n.filter(y=>xe(y.name).includes(u)||xe(y.description||"").includes(u))}return n},N=n=>{const u=r.find(y=>y.id===n);return u?u.quantity:0},S=n=>H.includes(n),w=n=>{D(u=>u.includes(n)?u.filter(y=>y!==n):[...u,n])},R=n=>({"Đồ uống":e.jsx(Jt,{}),"Món chính":e.jsx(ge,{}),"Tráng miệng":e.jsx(Ve,{}),"Khai vị":e.jsx(Ee,{})})[n]||e.jsx(ge,{}),p=[{key:"all",label:e.jsxs(qe,{children:[e.jsx(ge,{}),"Tất cả"]})},...s.map(n=>({key:n.id,label:e.jsxs(qe,{children:[R(n.name),n.name]})}))];if(g)return e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(ht,{size:"large"})});const T=_();return e.jsxs("div",{className:"menu-view-container p-4 bg-gray-50 min-h-screen pb-20",children:[e.jsx("div",{className:"mb-4",children:e.jsx(he,{placeholder:"Tìm món ăn...",allowClear:!0,size:"large",prefix:e.jsx(Yt,{}),value:v,onChange:n=>b(n.target.value),className:"rounded-lg"})}),e.jsx("div",{className:"mb-4",children:e.jsx(ut,{activeKey:j,onChange:k,size:"small",className:"category-tabs",items:p})}),T.length===0?e.jsx(de,{description:"Không tìm thấy món ăn nào",className:"mt-12"}):e.jsx(Re,{dataSource:T,renderItem:n=>e.jsx(Re.Item,{className:"mb-3 p-0",children:e.jsx(U,{className:"w-full menu-item-card hover:shadow-md transition-shadow",children:e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("div",{className:"flex gap-4",children:[n.image&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-20 h-20 rounded-lg overflow-hidden",children:e.jsx(Ut,{src:n.image,alt:n.name,className:"w-full h-full object-cover",preview:!1})})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1 pr-2",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1 flex-wrap",children:[e.jsx(Se,{strong:!0,className:"text-base text-gray-800 leading-tight",children:n.name}),n.is_popular&&e.jsxs($e,{color:"red",size:"small",children:[e.jsx(er,{className:"mr-1"}),"Hot"]})]}),n.description&&e.jsx(Qr,{className:"text-sm text-gray-600 mb-2 leading-tight",ellipsis:{rows:2},children:n.description}),n.rating&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Ee,{className:"text-yellow-500 text-xs"}),e.jsxs(Se,{className:"text-xs text-gray-600",children:[n.rating," (",n.review_count||0,")"]})]})]}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(Ve,{className:S(n.id)?"text-red-500":"text-gray-400"}),onClick:()=>w(n.id),className:"flex-shrink-0 w-8 h-8"})]})})]}),e.jsxs("div",{className:"flex justify-between items-center pt-2 border-t border-gray-100",children:[e.jsx("div",{children:e.jsx(Se,{strong:!0,className:"text-lg text-green-600",children:P(n.price)})}),e.jsxs("div",{className:"flex items-center gap-2",children:[N(n.id)>0&&e.jsx(oe,{count:N(n.id),size:"small",className:"mr-1"}),e.jsx(O,{type:"primary",size:"small",icon:e.jsx(Te,{}),onClick:()=>a(n),disabled:!n.available,className:"bg-blue-500 hover:bg-blue-600 rounded-full px-3 h-8 text-sm font-medium",children:"Thêm"})]})]}),!n.available&&e.jsx("div",{children:e.jsx($e,{color:"red",size:"small",children:"Tạm hết"})})]})})})})]})},{Title:st,Text:M}=me,{TextArea:wa}=he,Jr=({items:t=[],table:a,onUpdateQuantity:r,onRemoveItem:l,onClearCart:i,onPlaceOrder:s,total:o,isHiddenPriceCart:g})=>{const[f,v]=ie.useModal(),[b,j]=Pe.useMessage(),[k,H]=c.useState(!1),[D]=Kt.useForm(),[$,_]=c.useState(!1),N=o,S=N,w=(n,u)=>{if(u<1){R(n);return}r(n,u)},R=n=>{const u=t.find(y=>y.id===n);f.confirm({title:"Xóa món khỏi giỏ hàng?",content:`Bạn có chắc muốn xóa "${u==null?void 0:u.name}" khỏi giỏ hàng?`,icon:e.jsx(we,{}),okText:"Xóa",okType:"danger",cancelText:"Hủy",onOk:()=>l(n)})},p=()=>{f.confirm({title:"Xóa tất cả món?",content:"Bạn có chắc muốn xóa tất cả món khỏi giỏ hàng?",icon:e.jsx(we,{}),okText:"Xóa tất cả",okType:"danger",cancelText:"Hủy",onOk:i})},T=async n=>{try{H(!0);const u=await s({...n,subtotal:N,total:S});u.success?(b.success("Đặt món thành công!"),_(!1),D.resetFields()):b.error(u.message||"Có lỗi xảy ra khi đặt món")}catch{b.error("Có lỗi xảy ra khi đặt món")}finally{H(!1)}};return t.length===0?e.jsx("div",{className:"cart-view-container p-4 bg-gray-50 min-h-screen",children:e.jsx(de,{image:e.jsx(mt,{className:"text-6xl text-gray-300"}),description:e.jsxs("div",{children:[e.jsx(M,{className:"text-gray-500",children:"Giỏ hàng trống"}),e.jsx("br",{}),e.jsx(M,{className:"text-sm text-gray-400",children:"Hãy thêm món ăn từ thực đơn"})]}),className:"mt-20"})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"cart-view-container p-4 bg-gray-50 min-h-screen pb-32",children:[v,j,e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs(st,{level:4,className:"mb-0",children:["Giỏ hàng (",t.length," món)"]}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(ur,{}),onClick:p,className:"text-red-500 hover:text-red-700",children:"Xóa tất cả"})]}),e.jsx("div",{className:"space-y-3 mb-6",children:t.map(n=>e.jsx(U,{className:"cart-item-card border border-gray-200 shadow-sm",children:e.jsx("div",{className:"flex gap-3",children:e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{className:"flex-1 pr-2",children:[e.jsx(M,{strong:!0,className:"text-base text-gray-800 block leading-tight",children:n.name}),!n.hide_price&&e.jsxs(M,{className:"text-sm text-gray-600",children:[!n.hide_price&&P(n.price)," / món"]})]}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(pt,{}),onClick:()=>R(n.id),className:"text-red-500 hover:text-red-700 w-8 h-8 flex-shrink-0"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center bg-gray-50 rounded-lg p-1",children:[e.jsx(O,{type:"text",size:"small",icon:e.jsx(jt,{}),onClick:()=>w(n.id,n.quantity-1),className:"w-8 h-8 rounded-md hover:bg-white border-0 flex items-center justify-center"}),e.jsx("div",{className:"mx-2 min-w-[40px] text-center",children:e.jsx(M,{strong:!0,className:"text-base",children:n.quantity})}),e.jsx(O,{type:"text",size:"small",icon:e.jsx(Te,{}),onClick:()=>w(n.id,n.quantity+1),className:"w-8 h-8 rounded-md hover:bg-white border-0 flex items-center justify-center"})]}),e.jsx(M,{strong:!0,className:"text-lg text-green-600",children:!n.hide_price&&P(n.price*n.quantity)})]})]})})},n.id))}),e.jsxs(U,{className:"mt-4 order-summary-card sticky bottom-20 z-10",children:[e.jsx(st,{level:5,className:"mb-3",children:"Tổng đơn hàng"}),!g&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(M,{children:"Tạm tính:"}),e.jsx(M,{children:P(N)})]}),e.jsx(vt,{className:"my-2"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(M,{strong:!0,className:"text-lg",children:"Tổng cộng:"}),e.jsx(M,{strong:!0,className:"text-lg text-green-600",children:P(S)})]})]}),e.jsxs(O,{type:"primary",size:"large",block:!0,icon:e.jsx(It,{}),onClick:T,className:"mt-4 bg-blue-500 hover:bg-blue-600 h-12 text-lg font-medium",children:["Đặt món (",t.length," món)"]})]})]})})},Zr={order:"Order",menu:"Menu",cart:"Cart",history:"History",orderPlacedSuccess:"Order placed successfully!",cannotUpdateOrderStatus:"Cannot update order status.",paymentRequestSent:"Payment request sent. Please wait for staff.",cannotSendPaymentRequest:"Cannot send payment request.",paymentRequestError:"Error sending payment request.",requestPayment:"Request Payment",requestPaymentTitle:"Request Payment",enterDiscountCode:"Enter discount code (if any)",confirmPaymentRequest:"Are you sure you want to call staff to pay for this order?",cancel:"Cancel",loading:"Loading...",error:"Error",success:"Success",confirm:"Confirm",language:"Language",english:"English",vietnamese:"Vietnamese",cartEmpty:"Cart is empty",addItemsFromMenu:"Add items from menu",cartItems:"Cart ({count} items)",clearAll:"Clear all",removeFromCart:"Remove from cart?",confirmRemoveItem:'Are you sure you want to remove "{name}" from cart?',remove:"Remove",removeAllItems:"Remove all items?",confirmClearCart:"Are you sure you want to remove all items from cart?",all:"All",searchPlaceholder:"Search dishes...",addToCart:"Add to cart",noOrders:"No orders yet",orderDetails:"Order Details",emptyOrder:"Empty order",paymentDetails:"Payment Details",subtotal:"Subtotal:",discount:"Discount:",total:"Total:",payment:"Payment:",pending:"Pending",confirmed:"Confirmed",preparing:"Preparing",ready:"Ready",served:"Served",completed:"Completed",cancelled:"Cancelled"},ea={order:"Order",menu:"Thực đơn",cart:"Giỏ hàng",history:"Lịch sử",orderPlacedSuccess:"Đặt món thành công!",cannotUpdateOrderStatus:"Không thể cập nhật trạng thái đơn hàng.",paymentRequestSent:"Đã gửi yêu cầu thanh toán. Vui lòng đợi nhân viên.",cannotSendPaymentRequest:"Không thể gửi yêu cầu thanh toán.",paymentRequestError:"Lỗi khi gửi yêu cầu thanh toán.",requestPayment:"Gọi thanh toán",requestPaymentTitle:"Gọi thanh toán",enterDiscountCode:"Nhập mã giảm giá (nếu có)",confirmPaymentRequest:"Bạn có chắc muốn gọi nhân viên để thanh toán cho đơn hàng này?",cancel:"Hủy",loading:"Đang tải...",error:"Lỗi",success:"Thành công",confirm:"Xác nhận",language:"Ngôn ngữ",english:"Tiếng Anh",vietnamese:"Tiếng Việt",cartEmpty:"Giỏ hàng trống",addItemsFromMenu:"Hãy thêm món ăn từ thực đơn",cartItems:"Giỏ hàng ({count} món)",clearAll:"Xóa tất cả",removeFromCart:"Xóa món khỏi giỏ hàng?",confirmRemoveItem:'Bạn có chắc muốn xóa "{name}" khỏi giỏ hàng?',remove:"Xóa",removeAllItems:"Xóa tất cả món?",confirmClearCart:"Bạn có chắc muốn xóa tất cả món khỏi giỏ hàng?",all:"Tất cả",searchPlaceholder:"Tìm kiếm món ăn...",addToCart:"Thêm vào giỏ",noOrders:"Chưa có đơn hàng nào",orderDetails:"Đơn hàng",emptyOrder:"Đơn hàng trống",paymentDetails:"Chi tiết thanh toán",subtotal:"Tạm tính:",discount:"Giảm giá:",total:"Tổng cộng:",payment:"Thanh toán:",pending:"Đang xử lý",confirmed:"Đã xác nhận",preparing:"Đang chuẩn bị",ready:"Sẵn sàng",served:"Đã phục vụ",completed:"Hoàn thành",cancelled:"Đã hủy"},ue={en:Zr,vi:ea},Pt="vi",Ot=()=>{const t=localStorage.getItem("locale");return t&&ue[t]?t:Pt},ta=t=>ue[t]?(localStorage.setItem("locale",t),!0):!1,kt=c.createContext(),ra=({children:t})=>{const[a,r]=c.useState(Ot()),s={locale:a,changeLocale:o=>{ta(o)&&(r(o),window.location.reload())},t:(o,g=o)=>{var v,b;return((v=ue[a])==null?void 0:v[o])||((b=ue[Pt])==null?void 0:b[o])||g}};return e.jsx(kt.Provider,{value:s,children:t})},Rt=()=>{const t=c.useContext(kt);if(!t)throw new Error("useTranslation must be used within a TranslationProvider");return t},{Option:lt}=ft,aa=({className:t=""})=>{const{locale:a,changeLocale:r,t:l}=Rt(),i=s=>{r(s)};return e.jsxs(ft,{value:a,onChange:i,className:`language-switcher ${t}`,style:{minWidth:120},suffixIcon:e.jsx(gr,{}),size:"small",children:[e.jsx(lt,{value:"vi",children:l("vietnamese")}),e.jsx(lt,{value:"en",children:l("english")})]})},{Title:na}=me,{Content:sa}=B,la=({table:t,activeOrder:a})=>{const{t:r}=Rt(),[l,i]=c.useState("menu"),[s,o]=c.useState([]),[g,f]=c.useState(0),[v,b]=c.useState([]),[j,k]=c.useState(!1),[H,D]=ie.useModal(),[$,_]=Pe.useMessage(),[N,S]=c.useState(!1),[w,R]=c.useState(""),[p,T]=c.useState(window.activeOrderData||{}),n=c.useCallback(async()=>{if(p!=null&&p.id)try{const x=await(await fetch(`/api/orders/${p.id}/details`)).json();x.order&&T(x.order)}catch(h){console.error("Failed to fetch active order",h),$.error(r("cannotUpdateOrderStatus"))}},[p.id,$]);c.useEffect(()=>{const h=setInterval(()=>{n()},3e4);return()=>clearInterval(h)},[n]);const u=()=>s.reduce((h,x)=>h+(x.quantity||0),0),y=s.some(h=>h.hide_price===!0),F=()=>s.reduce((h,x)=>h+(x.price||0)*(x.quantity||0),0),d=h=>{o(x=>x.find(q=>q.id===h.id)?x.map(q=>q.id===h.id?{...q,quantity:q.quantity+1}:q):[...x,{...h,quantity:1}])},m=h=>{o(x=>x.filter(K=>K.id!==h))},z=(h,x)=>{if(x<=0){m(h);return}o(K=>K.map(q=>q.id===h?{...q,quantity:x}:q))},A=()=>{o([])},I=async()=>{try{const x=await(await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content},body:JSON.stringify({items:s,existing_order_id:p.id})})).json();return x.success&&(A(),i("history"),b(K=>[...K,{type:"success",message:r("orderPlacedSuccess"),timestamp:new Date}])),x}catch(h){return console.error("Error placing order:",h),{success:!1,error:"Network error"}}},Y=async()=>{S(!0)},G=async()=>{k(!0);try{const x=await(await fetch(`/api/orders/${p.id}/request-payment`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content},body:JSON.stringify({discount_code:w})})).json();x.success?(x.qr_payment_url&&window.open(x.qr_payment_url,"_blank"),$.success(r("paymentRequestSent")),n(),S(!1)):$.error(x.message||r("cannotSendPaymentRequest"))}catch(h){console.error("Error requesting payment:",h),$.error(r("paymentRequestError"))}finally{k(!1)}},qt=[{key:"menu",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(Cr,{className:"text-lg mb-1"}),e.jsx("span",{className:"text-xs",children:r("menu")})]}),children:e.jsx(Wr,{order:p,onAddToCart:d,cartItems:s})},{key:"cart",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(oe,{count:u(),size:"small",offset:[10,-5],children:e.jsx(mt,{className:"text-lg mb-1"})}),e.jsx("span",{className:"text-xs",children:r("cart")})]}),children:e.jsx(Jr,{items:s,table:t,onUpdateQuantity:z,onRemoveItem:m,onClearCart:A,onPlaceOrder:I,total:F(),isHiddenPriceCart:y})},{key:"history",label:e.jsxs("div",{className:"flex flex-col items-center py-1",children:[e.jsx(oe,{count:g,size:"small",offset:[10,-5],children:e.jsx(jr,{className:"text-lg mb-1"})}),e.jsx("span",{className:"text-xs",children:r("history")})]}),children:e.jsx(Xr,{orderId:p.id,activeTab:l,onOrderCountChange:f,paymentLoading:j,handleRequestPayment:Y})}];return e.jsxs(e.Fragment,{children:[e.jsxs(B,{className:"mobile-ordering-layout min-h-screen bg-gray-50",style:{marginBottom:"58px"},children:[_,D,e.jsx("div",{className:"sticky top-0 z-50 bg-white shadow-sm border-b",children:e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center",children:e.jsx(Zt,{className:"text-white text-lg"})}),e.jsx("div",{children:e.jsxs("div",{className:"font-semibold text-gray-800",children:[r("order")," ",p.order_number]})})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(aa,{className:"mr-2"}),e.jsx(oe,{count:v.length,size:"small",children:e.jsx(Bt,{className:"text-xl text-gray-600"})})]})]})}),e.jsx(sa,{className:"flex-1",children:e.jsx(ut,{activeKey:l,onChange:i,centered:!0,size:"large",className:"[&_.ant-tabs-tab]:!px-3 [&_.ant-tabs-tab]:!py-2 [&_.ant-tabs-tab]:!min-w-[80px] [&_.ant-tabs-tab]:!flex-1 [&_.ant-tabs-tab-btn]:!w-full [&_.ant-tabs-tab-btn]:!flex [&_.ant-tabs-tab-btn]:!flex-col [&_.ant-tabs-tab-btn]:!items-center [&_.ant-tabs-tab-btn]:!justify-center [&_.ant-tabs-tab-btn]:!text-xs [&_.ant-tabs-ink-bar]:!h-1 [&_.ant-tabs-ink-bar]:!rounded [&_.ant-tabs-content-holder]:!p-0 [&_.ant-tabs-tabpane]:!p-0",tabBarStyle:{backgroundColor:"white",margin:0,padding:"0 8px",borderTop:"1px solid #f0f0f0",position:"fixed",width:"100%",height:"58px",bottom:"0",zIndex:40},items:qt})})]}),e.jsxs(ie,{title:r("requestPaymentTitle"),visible:N,onOk:G,onCancel:()=>S(!1),okText:r("requestPayment"),cancelText:r("cancel"),confirmLoading:j,children:[e.jsx("div",{style:{marginBottom:16},children:e.jsx(he,{prefix:e.jsx(bt,{}),className:"w-full mb-2 border border-gray-300 rounded-md p-2",placeholder:r("enterDiscountCode"),value:w,onChange:h=>R(h.target.value)})}),e.jsx("div",{children:e.jsx(na,{level:5,strong:!0,children:r("confirmPaymentRequest")})})]})]})},ca=window.tableData,oa=window.activeOrderData,ia=()=>{const a=Ot()==="en"?Gr:Ar;return e.jsx(ra,{children:e.jsx(Dt,{locale:a,children:e.jsx(la,{table:ca,activeOrder:oa})})})},ct=document.getElementById("mobile-ordering-root");ct&&Ht.createRoot(ct).render(e.jsx(ia,{}));
