import{r as t,a3 as W,a4 as j,i as D,e as $,C as oe,b5 as ne,b6 as ie,o as le,a as M}from"./index-CHvake0r.js";import{R as N,a as P}from"./index-CIWcONm8.js";var ae={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},se=function(r,o){return t.createElement(W,j({},r,{ref:o,icon:ae}))},de=t.forwardRef(se);const ce=t.createContext({siderHook:{addSider:()=>null,removeSider:()=>null}}),ge=e=>{const{antCls:r,componentCls:o,colorText:n,footerBg:i,headerHeight:s,headerPadding:d,headerColor:g,footerPadding:u,fontSize:c,bodyBg:h,headerBg:w}=e;return{[o]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:h,"&, *":{boxSizing:"border-box"},[`&${o}-has-sider`]:{flexDirection:"row",[`> ${o}, > ${o}-content`]:{width:0}},[`${o}-header, &${o}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${o}-header`]:{height:s,padding:d,color:g,lineHeight:$(s),background:w,[`${r}-menu`]:{lineHeight:"inherit"}},[`${o}-footer`]:{padding:u,color:n,fontSize:c,background:i},[`${o}-content`]:{flex:"auto",color:n,minHeight:0}}},_=e=>{const{colorBgLayout:r,controlHeight:o,controlHeightLG:n,colorText:i,controlHeightSM:s,marginXXS:d,colorTextLightSolid:g,colorBgContainer:u}=e,c=n*1.25;return{colorBgHeader:"#001529",colorBgBody:r,colorBgTrigger:"#002140",bodyBg:r,headerBg:"#001529",headerHeight:o*2,headerPadding:`0 ${c}px`,headerColor:i,footerPadding:`${s}px ${c}px`,footerBg:r,siderBg:"#001529",triggerHeight:n+d*2,triggerBg:"#002140",triggerColor:g,zeroTriggerWidth:n,zeroTriggerHeight:n,lightSiderBg:u,lightTriggerBg:u,lightTriggerColor:i}},A=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],Se=D("Layout",e=>[ge(e)],_,{deprecatedTokens:A}),ue=e=>{const{componentCls:r,siderBg:o,motionDurationMid:n,motionDurationSlow:i,antCls:s,triggerHeight:d,triggerColor:g,triggerBg:u,headerHeight:c,zeroTriggerWidth:h,zeroTriggerHeight:w,borderRadiusLG:f,lightSiderBg:C,lightTriggerColor:p,lightTriggerBg:x,bodyBg:y}=e;return{[r]:{position:"relative",minWidth:0,background:o,transition:`all ${n}, background 0s`,"&-has-trigger":{paddingBottom:d},"&-right":{order:1},[`${r}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${s}-menu${s}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${r}-children`]:{overflow:"hidden"},[`${r}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:d,color:g,lineHeight:$(d),textAlign:"center",background:u,cursor:"pointer",transition:`all ${n}`},[`${r}-zero-width-trigger`]:{position:"absolute",top:c,insetInlineEnd:e.calc(h).mul(-1).equal(),zIndex:1,width:h,height:w,color:g,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:o,borderRadius:`0 ${$(f)} ${$(f)} 0`,cursor:"pointer",transition:`background ${i} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${i}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(h).mul(-1).equal(),borderRadius:`${$(f)} 0 0 ${$(f)}`}},"&-light":{background:C,[`${r}-trigger`]:{color:p,background:x},[`${r}-zero-width-trigger`]:{color:p,background:x,border:`1px solid ${y}`,borderInlineStart:0}}}}},he=D(["Layout","Sider"],e=>[ue(e)],_,{deprecatedTokens:A});var fe=function(e,r){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)r.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(o[n[i]]=e[n[i]]);return o};const L={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},pe=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),me=t.createContext({}),xe=(()=>{let e=0;return(r="")=>(e+=1,`${r}${e}`)})(),Be=t.forwardRef((e,r)=>{const{prefixCls:o,className:n,trigger:i,children:s,defaultCollapsed:d=!1,theme:g="dark",style:u={},collapsible:c=!1,reverseArrow:h=!1,width:w=200,collapsedWidth:f=80,zeroWidthTriggerStyle:C,breakpoint:p,onCollapse:x,onBreakpoint:y}=e,F=fe(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:T}=t.useContext(ce),[m,k]=t.useState("collapsed"in e?e.collapsed:d),[E,V]=t.useState(!1);t.useEffect(()=>{"collapsed"in e&&k(e.collapsed)},[e.collapsed]);const O=(l,v)=>{"collapsed"in e||k(l),x==null||x(l,v)},{getPrefixCls:X,direction:G}=t.useContext(oe),a=X("layout-sider",o),[Q,q,K]=he(a),S=t.useRef(null);S.current=l=>{V(l.matches),y==null||y(l.matches),m!==l.matches&&O(l.matches,"responsive")},t.useEffect(()=>{function l(re){var H;return(H=S.current)===null||H===void 0?void 0:H.call(S,re)}let v;return typeof(window==null?void 0:window.matchMedia)<"u"&&p&&p in L&&(v=window.matchMedia(`screen and (max-width: ${L[p]})`),ne(v,l),l(v)),()=>{ie(v,l)}},[p]),t.useEffect(()=>{const l=xe("ant-sider-");return T.addSider(l),()=>T.removeSider(l)},[]);const I=()=>{O(!m,"clickTrigger")},J=le(F,["collapsed"]),B=m?f:w,b=pe(B)?`${B}px`:String(B),z=parseFloat(String(f||0))===0?t.createElement("span",{onClick:I,className:M(`${a}-zero-width-trigger`,`${a}-zero-width-trigger-${h?"right":"left"}`),style:C},i||t.createElement(de,null)):null,R=G==="rtl"==!h,U={expanded:R?t.createElement(P,null):t.createElement(N,null),collapsed:R?t.createElement(N,null):t.createElement(P,null)}[m?"collapsed":"expanded"],Y=i!==null?z||t.createElement("div",{className:`${a}-trigger`,onClick:I,style:{width:b}},i||U):null,Z=Object.assign(Object.assign({},u),{flex:`0 0 ${b}`,maxWidth:b,minWidth:b,width:b}),ee=M(a,`${a}-${g}`,{[`${a}-collapsed`]:!!m,[`${a}-has-trigger`]:c&&i!==null&&!z,[`${a}-below`]:!!E,[`${a}-zero-width`]:parseFloat(b)===0},n,q,K),te=t.useMemo(()=>({siderCollapsed:m}),[m]);return Q(t.createElement(me.Provider,{value:te},t.createElement("aside",Object.assign({className:ee},J,{style:Z,ref:r}),t.createElement("div",{className:`${a}-children`},s),c||E&&z?Y:null)))});var be={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"},ve=function(r,o){return t.createElement(W,j({},r,{ref:o,icon:be}))},ze=t.forwardRef(ve);export{ce as L,ze as R,me as S,Be as a,Se as u};
