import{w as Ve,r as a,C as Ht,a as te,D as Ua,t as dr,M as Ga,o as en,b as Jt,T as qn,s as Za,u as Ja,g as Qa,d as el,e as Y,f as jo,h as Ao,i as In,m as un,k as xn,l as Fo,n as Yn,p as pn,F as Vt,q as tl,v as nl,x as Wo,S as rl,y as ht,z as ur,E as ol,R as Vo,A as Xo,B as al,G as ll,H as il,I as sl,J as cl,K as dl,L as zr,N as ul,O as fl,P as Nn,Q as fr,U as qo,V as vl,W as ml,X as pl,_ as ut,Y as j,Z as Bt,$ as Me,a0 as bt,a1 as gl,a2 as Tt,a3 as It,a4 as Ee,a5 as oe,a6 as ot,a7 as fn,a8 as vn,a9 as _r,aa as Yo,ab as vr,ac as P,ad as qt,ae as hl,af as jr,ag as bl,ah as mr,ai as Ar,aj as Fr,ak as Cn,al as pr,am as gr,an as De,ao as yl,ap as Sl,aq as xl,ar as Cl,as as ke,at as Kt,au as wl,av as $l,aw as Uo,ax as El,ay as Rl,az as Il,aA as Nl,j as Ke,aB as kl,aC as Ol,aD as Tl,c as Ml}from"./index-CHvake0r.js";import"./bootstrap-DNxiirP_.js";/* empty css            */import{i as Wr,g as Pl,S as Un,R as Dl,a as Kl,E as Vr,D as Bl,b as Go}from"./EyeOutlined-D5ZQtqsR.js";import{g as Zo,I as Ll,F as Wt,M as Hl,a as Hn}from"./index-Cqw7XJJF.js";import{R as zl,a as _l,b as Xr,P as jl,s as sn}from"./index-CIWcONm8.js";import{S as Jo}from"./index-D_dKmeqG.js";import{i as Al,R as Fl}from"./EditOutlined-t_UPdXXr.js";import{C as wn,R as Qo,g as Wl}from"./index-BLyO0z8k.js";import{S as ea,R as Vl}from"./DeleteOutlined-C56jyccF.js";import"./ActionButton-BdVp3AS3.js";function Gn(e){return e!=null&&e===e.window}const Xl=e=>{var t,r;if(typeof window>"u")return 0;let n=0;return Gn(e)?n=e.pageYOffset:e instanceof Document?n=e.documentElement.scrollTop:(e instanceof HTMLElement||e)&&(n=e.scrollTop),e&&!Gn(e)&&typeof n!="number"&&(n=(r=((t=e.ownerDocument)!==null&&t!==void 0?t:e).documentElement)===null||r===void 0?void 0:r.scrollTop),n};function ql(e,t,r,n){const o=r-t;return e/=n/2,e<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t}function Yl(e,t={}){const{getContainer:r=()=>window,callback:n,duration:o=450}=t,l=r(),s=Xl(l),i=Date.now(),d=()=>{const p=Date.now()-i,u=ql(p>o?o:p,s,e,o);Gn(l)?l.scrollTo(window.pageXOffset,u):l instanceof Document||l.constructor.name==="HTMLDocument"?l.documentElement.scrollTop=u:l.scrollTop=u,p<o?Ve(d):typeof n=="function"&&n()};Ve(d)}const Ul=e=>typeof e!="object"&&typeof e!="function"||e===null,$n=a.createContext({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var Gl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const ta=e=>{const{prefixCls:t,className:r,dashed:n}=e,o=Gl(e,["prefixCls","className","dashed"]),{getPrefixCls:l}=a.useContext(Ht),s=l("menu",t),i=te({[`${s}-item-divider-dashed`]:!!n},r);return a.createElement(Ua,Object.assign({className:i},o))},na=e=>{var t;const{className:r,children:n,icon:o,title:l,danger:s,extra:i}=e,{prefixCls:d,firstLevel:c,direction:p,disableMenuItemTitleTooltip:u,inlineCollapsed:v}=a.useContext($n),f=S=>{const C=n==null?void 0:n[0],w=a.createElement("span",{className:te(`${d}-title-content`,{[`${d}-title-content-with-extra`]:!!i||i===0})},n);return(!o||a.isValidElement(n)&&n.type==="span")&&n&&S&&c&&typeof C=="string"?a.createElement("div",{className:`${d}-inline-collapsed-noicon`},C.charAt(0)):w},{siderCollapsed:m}=a.useContext(ea);let g=l;typeof l>"u"?g=c?n:"":l===!1&&(g="");const h={title:g};!m&&!v&&(h.title=null,h.open=!1);const b=dr(n).length;let y=a.createElement(Ga,Object.assign({},en(e,["title","icon","danger"]),{className:te({[`${d}-item-danger`]:s,[`${d}-item-only-child`]:(o?b+1:b)===1},r),title:typeof l=="string"?l:void 0}),Jt(o,{className:te(a.isValidElement(o)?(t=o.props)===null||t===void 0?void 0:t.className:"",`${d}-item-icon`)}),f(v));return u||(y=a.createElement(qn,Object.assign({},h,{placement:p==="rtl"?"left":"right",classNames:{root:`${d}-inline-collapsed-tooltip`}}),y)),y};var Zl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const En=a.createContext(null),ra=a.forwardRef((e,t)=>{const{children:r}=e,n=Zl(e,["children"]),o=a.useContext(En),l=a.useMemo(()=>Object.assign(Object.assign({},o),n),[o,n.prefixCls,n.mode,n.selectable,n.rootClassName]),s=Za(r),i=Ja(t,s?Qa(r):null);return a.createElement(En.Provider,{value:l},a.createElement(el,{space:!0},s?a.cloneElement(r,{ref:i}):r))}),Jl=e=>{const{componentCls:t,motionDurationSlow:r,horizontalLineHeight:n,colorSplit:o,lineWidth:l,lineType:s,itemPaddingInline:i}=e;return{[`${t}-horizontal`]:{lineHeight:n,border:0,borderBottom:`${Y(l)} ${s} ${o}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:i},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${r}`,`background ${r}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}},Ql=({componentCls:e,menuArrowOffset:t,calc:r})=>({[`${e}-rtl`]:{direction:"rtl"},[`${e}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${e}-rtl${e}-vertical,
    ${e}-submenu-rtl ${e}-vertical`]:{[`${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${Y(r(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${Y(t)})`}}}}),qr=e=>Object.assign({},jo(e)),Yr=(e,t)=>{const{componentCls:r,itemColor:n,itemSelectedColor:o,subMenuItemSelectedColor:l,groupTitleColor:s,itemBg:i,subMenuItemBg:d,itemSelectedBg:c,activeBarHeight:p,activeBarWidth:u,activeBarBorderWidth:v,motionDurationSlow:f,motionEaseInOut:m,motionEaseOut:g,itemPaddingInline:h,motionDurationMid:b,itemHoverColor:y,lineType:S,colorSplit:C,itemDisabledColor:w,dangerItemColor:R,dangerItemHoverColor:$,dangerItemSelectedColor:k,dangerItemActiveBg:x,dangerItemSelectedBg:T,popupBg:O,itemHoverBg:D,itemActiveBg:M,menuSubMenuBg:E,horizontalItemSelectedColor:I,horizontalItemSelectedBg:N,horizontalItemBorderRadius:K,horizontalItemHoverBg:z}=e;return{[`${r}-${t}, ${r}-${t} > ${r}`]:{color:n,background:i,[`&${r}-root:focus-visible`]:Object.assign({},qr(e)),[`${r}-item`]:{"&-group-title, &-extra":{color:s}},[`${r}-submenu-selected > ${r}-submenu-title`]:{color:l},[`${r}-item, ${r}-submenu-title`]:{color:n,[`&:not(${r}-item-disabled):focus-visible`]:Object.assign({},qr(e))},[`${r}-item-disabled, ${r}-submenu-disabled`]:{color:`${w} !important`},[`${r}-item:not(${r}-item-selected):not(${r}-submenu-selected)`]:{[`&:hover, > ${r}-submenu-title:hover`]:{color:y}},[`&:not(${r}-horizontal)`]:{[`${r}-item:not(${r}-item-selected)`]:{"&:hover":{backgroundColor:D},"&:active":{backgroundColor:M}},[`${r}-submenu-title`]:{"&:hover":{backgroundColor:D},"&:active":{backgroundColor:M}}},[`${r}-item-danger`]:{color:R,[`&${r}-item:hover`]:{[`&:not(${r}-item-selected):not(${r}-submenu-selected)`]:{color:$}},[`&${r}-item:active`]:{background:x}},[`${r}-item a`]:{"&, &:hover":{color:"inherit"}},[`${r}-item-selected`]:{color:o,[`&${r}-item-danger`]:{color:k},"a, a:hover":{color:"inherit"}},[`& ${r}-item-selected`]:{backgroundColor:c,[`&${r}-item-danger`]:{backgroundColor:T}},[`&${r}-submenu > ${r}`]:{backgroundColor:E},[`&${r}-popup > ${r}`]:{backgroundColor:O},[`&${r}-submenu-popup > ${r}`]:{backgroundColor:O},[`&${r}-horizontal`]:Object.assign(Object.assign({},t==="dark"?{borderBottom:0}:{}),{[`> ${r}-item, > ${r}-submenu`]:{top:v,marginTop:e.calc(v).mul(-1).equal(),marginBottom:0,borderRadius:K,"&::after":{position:"absolute",insetInline:h,bottom:0,borderBottom:`${Y(p)} solid transparent`,transition:`border-color ${f} ${m}`,content:'""'},"&:hover, &-active, &-open":{background:z,"&::after":{borderBottomWidth:p,borderBottomColor:I}},"&-selected":{color:I,backgroundColor:N,"&:hover":{backgroundColor:N},"&::after":{borderBottomWidth:p,borderBottomColor:I}}}}),[`&${r}-root`]:{[`&${r}-inline, &${r}-vertical`]:{borderInlineEnd:`${Y(v)} ${S} ${C}`}},[`&${r}-inline`]:{[`${r}-sub${r}-inline`]:{background:d},[`${r}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${Y(u)} solid ${o}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${b} ${g}`,`opacity ${b} ${g}`].join(","),content:'""'},[`&${r}-item-danger`]:{"&::after":{borderInlineEndColor:k}}},[`${r}-selected, ${r}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${b} ${m}`,`opacity ${b} ${m}`].join(",")}}}}}},Ur=e=>{const{componentCls:t,itemHeight:r,itemMarginInline:n,padding:o,menuArrowSize:l,marginXS:s,itemMarginBlock:i,itemWidth:d,itemPaddingInline:c}=e,p=e.calc(l).add(o).add(s).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:r,lineHeight:Y(r),paddingInline:c,overflow:"hidden",textOverflow:"ellipsis",marginInline:n,marginBlock:i,width:d},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:r,lineHeight:Y(r)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:p}}},ei=e=>{const{componentCls:t,iconCls:r,itemHeight:n,colorTextLightSolid:o,dropdownWidth:l,controlHeightLG:s,motionEaseOut:i,paddingXL:d,itemMarginInline:c,fontSizeLG:p,motionDurationFast:u,motionDurationSlow:v,paddingXS:f,boxShadowSecondary:m,collapsedWidth:g,collapsedIconSize:h}=e,b={height:n,lineHeight:Y(n),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},Ur(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},Ur(e)),{boxShadow:m})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:l,maxHeight:`calc(100vh - ${Y(e.calc(s).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${v}`,`background ${v}`,`padding ${u} ${i}`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:b,[`& ${t}-item-group-title`]:{paddingInlineStart:d}},[`${t}-item`]:b}},{[`${t}-inline-collapsed`]:{width:g,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:p,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${Y(e.calc(h).div(2).equal())} - ${Y(c)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${r}`]:{margin:0,fontSize:h,lineHeight:Y(n),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${r}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${r}`]:{display:"none"},"a, a:hover":{color:o}},[`${t}-item-group-title`]:Object.assign(Object.assign({},Ao),{paddingInline:f})}}]},Gr=e=>{const{componentCls:t,motionDurationSlow:r,motionDurationMid:n,motionEaseInOut:o,motionEaseOut:l,iconCls:s,iconSize:i,iconMarginInlineEnd:d}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${r}`,`background ${r}`,`padding calc(${r} + 0.1s) ${o}`].join(","),[`${t}-item-icon, ${s}`]:{minWidth:i,fontSize:i,transition:[`font-size ${n} ${l}`,`margin ${r} ${o}`,`color ${r}`].join(","),"+ span":{marginInlineStart:d,opacity:1,transition:[`opacity ${r} ${o}`,`margin ${r}`,`color ${r}`].join(",")}},[`${t}-item-icon`]:Object.assign({},tl()),[`&${t}-item-only-child`]:{[`> ${s}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Zr=e=>{const{componentCls:t,motionDurationSlow:r,motionEaseInOut:n,borderRadius:o,menuArrowSize:l,menuArrowOffset:s}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:l,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${r} ${n}, opacity ${r}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(l).mul(.6).equal(),height:e.calc(l).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:o,transition:[`background ${r} ${n}`,`transform ${r} ${n}`,`top ${r} ${n}`,`color ${r} ${n}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${Y(e.calc(s).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${Y(s)})`}}}}},ti=e=>{const{antCls:t,componentCls:r,fontSize:n,motionDurationSlow:o,motionDurationMid:l,motionEaseInOut:s,paddingXS:i,padding:d,colorSplit:c,lineWidth:p,zIndexPopup:u,borderRadiusLG:v,subMenuItemBorderRadius:f,menuArrowSize:m,menuArrowOffset:g,lineType:h,groupTitleLineHeight:b,groupTitleFontSize:y}=e;return[{"":{[r]:Object.assign(Object.assign({},Yn()),{"&-hidden":{display:"none"}})},[`${r}-submenu-hidden`]:{display:"none"}},{[r]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},pn(e)),Yn()),{marginBottom:0,paddingInlineStart:0,fontSize:n,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${o} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${r}-item`]:{flex:"none"}},[`${r}-item, ${r}-submenu, ${r}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${r}-item-group-title`]:{padding:`${Y(i)} ${Y(d)}`,fontSize:y,lineHeight:b,transition:`all ${o}`},[`&-horizontal ${r}-submenu`]:{transition:[`border-color ${o} ${s}`,`background ${o} ${s}`].join(",")},[`${r}-submenu, ${r}-submenu-inline`]:{transition:[`border-color ${o} ${s}`,`background ${o} ${s}`,`padding ${l} ${s}`].join(",")},[`${r}-submenu ${r}-sub`]:{cursor:"initial",transition:[`background ${o} ${s}`,`padding ${o} ${s}`].join(",")},[`${r}-title-content`]:{transition:`color ${o}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${r}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${r}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${r}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:c,borderStyle:h,borderWidth:0,borderTopWidth:p,marginBlock:p,padding:0,"&-dashed":{borderStyle:"dashed"}}}),Gr(e)),{[`${r}-item-group`]:{[`${r}-item-group-list`]:{margin:0,padding:0,[`${r}-item, ${r}-submenu-title`]:{paddingInline:`${Y(e.calc(n).mul(2).equal())} ${Y(d)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:u,borderRadius:v,boxShadow:"none",transformOrigin:"0 0",[`&${r}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${r}`]:Object.assign(Object.assign(Object.assign({borderRadius:v},Gr(e)),Zr(e)),{[`${r}-item, ${r}-submenu > ${r}-submenu-title`]:{borderRadius:f},[`${r}-submenu-title::after`]:{transition:`transform ${o} ${s}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),Zr(e)),{[`&-inline-collapsed ${r}-submenu-arrow,
        &-inline ${r}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${Y(g)})`},"&::after":{transform:`rotate(45deg) translateX(${Y(e.calc(g).mul(-1).equal())})`}},[`${r}-submenu-open${r}-submenu-inline > ${r}-submenu-title > ${r}-submenu-arrow`]:{transform:`translateY(${Y(e.calc(m).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${Y(e.calc(g).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${Y(g)})`}}})},{[`${t}-layout-header`]:{[r]:{lineHeight:"inherit"}}}]},ni=e=>{var t,r,n;const{colorPrimary:o,colorError:l,colorTextDisabled:s,colorErrorBg:i,colorText:d,colorTextDescription:c,colorBgContainer:p,colorFillAlter:u,colorFillContent:v,lineWidth:f,lineWidthBold:m,controlItemBgActive:g,colorBgTextHover:h,controlHeightLG:b,lineHeight:y,colorBgElevated:S,marginXXS:C,padding:w,fontSize:R,controlHeightSM:$,fontSizeLG:k,colorTextLightSolid:x,colorErrorHover:T}=e,O=(t=e.activeBarWidth)!==null&&t!==void 0?t:0,D=(r=e.activeBarBorderWidth)!==null&&r!==void 0?r:f,M=(n=e.itemMarginInline)!==null&&n!==void 0?n:e.marginXXS,E=new Vt(x).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:d,itemColor:d,colorItemTextHover:d,itemHoverColor:d,colorItemTextHoverHorizontal:o,horizontalItemHoverColor:o,colorGroupTitle:c,groupTitleColor:c,colorItemTextSelected:o,itemSelectedColor:o,subMenuItemSelectedColor:o,colorItemTextSelectedHorizontal:o,horizontalItemSelectedColor:o,colorItemBg:p,itemBg:p,colorItemBgHover:h,itemHoverBg:h,colorItemBgActive:v,itemActiveBg:g,colorSubItemBg:u,subMenuItemBg:u,colorItemBgSelected:g,itemSelectedBg:g,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:O,colorActiveBarHeight:m,activeBarHeight:m,colorActiveBarBorderSize:f,activeBarBorderWidth:D,colorItemTextDisabled:s,itemDisabledColor:s,colorDangerItemText:l,dangerItemColor:l,colorDangerItemTextHover:l,dangerItemHoverColor:l,colorDangerItemTextSelected:l,dangerItemSelectedColor:l,colorDangerItemBgActive:i,dangerItemActiveBg:i,colorDangerItemBgSelected:i,dangerItemSelectedBg:i,itemMarginInline:M,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:b,groupTitleLineHeight:y,collapsedWidth:b*2,popupBg:S,itemMarginBlock:C,itemPaddingInline:w,horizontalLineHeight:`${b*1.15}px`,iconSize:R,iconMarginInlineEnd:$-R,collapsedIconSize:k,groupTitleFontSize:R,darkItemDisabledColor:new Vt(x).setA(.25).toRgbString(),darkItemColor:E,darkDangerItemColor:l,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:x,darkItemSelectedBg:o,darkDangerItemSelectedBg:l,darkItemHoverBg:"transparent",darkGroupTitleColor:E,darkItemHoverColor:x,darkDangerItemHoverColor:T,darkDangerItemSelectedColor:x,darkDangerItemActiveBg:l,itemWidth:O?`calc(100% + ${D}px)`:`calc(100% - ${M*2}px)`}},ri=(e,t=e,r=!0)=>In("Menu",o=>{const{colorBgElevated:l,controlHeightLG:s,fontSize:i,darkItemColor:d,darkDangerItemColor:c,darkItemBg:p,darkSubMenuItemBg:u,darkItemSelectedColor:v,darkItemSelectedBg:f,darkDangerItemSelectedBg:m,darkItemHoverBg:g,darkGroupTitleColor:h,darkItemHoverColor:b,darkItemDisabledColor:y,darkDangerItemHoverColor:S,darkDangerItemSelectedColor:C,darkDangerItemActiveBg:w,popupBg:R,darkPopupBg:$}=o,k=o.calc(i).div(7).mul(5).equal(),x=un(o,{menuArrowSize:k,menuHorizontalHeight:o.calc(s).mul(1.15).equal(),menuArrowOffset:o.calc(k).mul(.25).equal(),menuSubMenuBg:l,calc:o.calc,popupBg:R}),T=un(x,{itemColor:d,itemHoverColor:b,groupTitleColor:h,itemSelectedColor:v,subMenuItemSelectedColor:v,itemBg:p,popupBg:$,subMenuItemBg:u,itemActiveBg:"transparent",itemSelectedBg:f,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:g,itemDisabledColor:y,dangerItemColor:c,dangerItemHoverColor:S,dangerItemSelectedColor:C,dangerItemActiveBg:w,dangerItemSelectedBg:m,menuSubMenuBg:u,horizontalItemSelectedColor:v,horizontalItemSelectedBg:f});return[ti(x),Jl(x),ei(x),Yr(x,"light"),Yr(T,"dark"),Ql(x),Zo(x),xn(x,"slide-up"),xn(x,"slide-down"),Fo(x,"zoom-big")]},ni,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:r,unitless:{groupTitleLineHeight:!0}})(e,t),oa=e=>{var t;const{popupClassName:r,icon:n,title:o,theme:l}=e,s=a.useContext($n),{prefixCls:i,inlineCollapsed:d,theme:c}=s,p=nl();let u;if(!n)u=d&&!p.length&&o&&typeof o=="string"?a.createElement("div",{className:`${i}-inline-collapsed-noicon`},o.charAt(0)):a.createElement("span",{className:`${i}-title-content`},o);else{const m=a.isValidElement(o)&&o.type==="span";u=a.createElement(a.Fragment,null,Jt(n,{className:te(a.isValidElement(n)?(t=n.props)===null||t===void 0?void 0:t.className:"",`${i}-item-icon`)}),m?o:a.createElement("span",{className:`${i}-title-content`},o))}const v=a.useMemo(()=>Object.assign(Object.assign({},s),{firstLevel:!1}),[s]),[f]=Wo("Menu");return a.createElement($n.Provider,{value:v},a.createElement(rl,Object.assign({},en(e,["icon"]),{title:u,popupClassName:te(i,r,`${i}-${l||c}`),popupStyle:Object.assign({zIndex:f},e.popupStyle)})))};var oi=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function zn(e){return e===null||e===!1}const ai={item:na,submenu:oa,divider:ta},li=a.forwardRef((e,t)=>{var r;const n=a.useContext(En),o=n||{},{getPrefixCls:l,getPopupContainer:s,direction:i,menu:d}=a.useContext(Ht),c=l(),{prefixCls:p,className:u,style:v,theme:f="light",expandIcon:m,_internalDisableMenuItemTitleTooltip:g,inlineCollapsed:h,siderCollapsed:b,rootClassName:y,mode:S,selectable:C,onClick:w,overflowedIndicatorPopupClassName:R}=e,$=oi(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),k=en($,["collapsedWidth"]);(r=o.validator)===null||r===void 0||r.call(o,{mode:S});const x=ht((...ne)=>{var A;w==null||w.apply(void 0,ne),(A=o.onClick)===null||A===void 0||A.call(o)}),T=o.mode||S,O=C??o.selectable,D=h??b,M={horizontal:{motionName:`${c}-slide-up`},inline:Xo(c),other:{motionName:`${c}-zoom-big`}},E=l("menu",p||o.prefixCls),I=ur(E),[N,K,z]=ri(E,I,!n),B=te(`${E}-${f}`,d==null?void 0:d.className,u),V=a.useMemo(()=>{var ne,A;if(typeof m=="function"||zn(m))return m||null;if(typeof o.expandIcon=="function"||zn(o.expandIcon))return o.expandIcon||null;if(typeof(d==null?void 0:d.expandIcon)=="function"||zn(d==null?void 0:d.expandIcon))return(d==null?void 0:d.expandIcon)||null;const se=(ne=m??(o==null?void 0:o.expandIcon))!==null&&ne!==void 0?ne:d==null?void 0:d.expandIcon;return Jt(se,{className:te(`${E}-submenu-expand-icon`,a.isValidElement(se)?(A=se.props)===null||A===void 0?void 0:A.className:void 0)})},[m,o==null?void 0:o.expandIcon,d==null?void 0:d.expandIcon,E]),Z=a.useMemo(()=>({prefixCls:E,inlineCollapsed:D||!1,direction:i,firstLevel:!0,theme:f,mode:T,disableMenuItemTitleTooltip:g}),[E,D,i,g,f]);return N(a.createElement(En.Provider,{value:null},a.createElement($n.Provider,{value:Z},a.createElement(ol,Object.assign({getPopupContainer:s,overflowedIndicator:a.createElement(Vo,null),overflowedIndicatorPopupClassName:te(E,`${E}-${f}`,R),mode:T,selectable:O,onClick:x},k,{inlineCollapsed:D,style:Object.assign(Object.assign({},d==null?void 0:d.style),v),className:B,prefixCls:E,direction:i,defaultMotions:M,expandIcon:V,ref:t,rootClassName:te(y,K,o.rootClassName,z,I),_internalComponents:ai})))))}),tn=a.forwardRef((e,t)=>{const r=a.useRef(null),n=a.useContext(ea);return a.useImperativeHandle(t,()=>({menu:r.current,focus:o=>{var l;(l=r.current)===null||l===void 0||l.focus(o)}})),a.createElement(li,Object.assign({ref:r},e,n))});tn.Item=na;tn.SubMenu=oa;tn.Divider=ta;tn.ItemGroup=al;const ii=e=>{const{componentCls:t,menuCls:r,colorError:n,colorTextLightSolid:o}=e,l=`${r}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${r} ${l}`]:{[`&${l}-danger:not(${l}-disabled)`]:{color:n,"&:hover":{color:o,backgroundColor:n}}}}}},si=e=>{const{componentCls:t,menuCls:r,zIndexPopup:n,dropdownArrowDistance:o,sizePopupArrow:l,antCls:s,iconCls:i,motionDurationMid:d,paddingBlock:c,fontSize:p,dropdownEdgeChildPadding:u,colorTextDisabled:v,fontSizeIcon:f,controlPaddingHorizontal:m,colorBgElevated:g}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:n,display:"block","&::before":{position:"absolute",insetBlock:e.calc(l).div(2).sub(o).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${s}-btn`]:{[`& > ${i}-down, & > ${s}-btn-icon > ${i}-down`]:{fontSize:f}},[`${t}-wrap`]:{position:"relative",[`${s}-btn > ${i}-down`]:{fontSize:f},[`${i}-down::before`]:{transition:`transform ${d}`}},[`${t}-wrap-open`]:{[`${i}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${s}-slide-down-enter${s}-slide-down-enter-active${t}-placement-bottomLeft,
          &${s}-slide-down-appear${s}-slide-down-appear-active${t}-placement-bottomLeft,
          &${s}-slide-down-enter${s}-slide-down-enter-active${t}-placement-bottom,
          &${s}-slide-down-appear${s}-slide-down-appear-active${t}-placement-bottom,
          &${s}-slide-down-enter${s}-slide-down-enter-active${t}-placement-bottomRight,
          &${s}-slide-down-appear${s}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:cl},[`&${s}-slide-up-enter${s}-slide-up-enter-active${t}-placement-topLeft,
          &${s}-slide-up-appear${s}-slide-up-appear-active${t}-placement-topLeft,
          &${s}-slide-up-enter${s}-slide-up-enter-active${t}-placement-top,
          &${s}-slide-up-appear${s}-slide-up-appear-active${t}-placement-top,
          &${s}-slide-up-enter${s}-slide-up-enter-active${t}-placement-topRight,
          &${s}-slide-up-appear${s}-slide-up-appear-active${t}-placement-topRight`]:{animationName:sl},[`&${s}-slide-down-leave${s}-slide-down-leave-active${t}-placement-bottomLeft,
          &${s}-slide-down-leave${s}-slide-down-leave-active${t}-placement-bottom,
          &${s}-slide-down-leave${s}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:il},[`&${s}-slide-up-leave${s}-slide-up-leave-active${t}-placement-topLeft,
          &${s}-slide-up-leave${s}-slide-up-leave-active${t}-placement-top,
          &${s}-slide-up-leave${s}-slide-up-leave-active${t}-placement-topRight`]:{animationName:ll}}},dl(e,g,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${r}`]:{position:"relative",margin:0},[`${r}-submenu-popup`]:{position:"absolute",zIndex:n,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},pn(e)),{[r]:Object.assign(Object.assign({padding:u,listStyleType:"none",backgroundColor:g,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},zr(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${r}-item-group-title`]:{padding:`${Y(c)} ${Y(m)}`,color:e.colorTextDescription,transition:`all ${d}`},[`${r}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${r}-item-icon`]:{minWidth:p,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${r}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${d}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${r}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${r}-item, ${r}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${Y(c)} ${Y(m)}`,color:e.colorText,fontWeight:"normal",fontSize:p,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${d}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},zr(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:v,cursor:"not-allowed","&:hover":{color:v,backgroundColor:g,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${Y(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:f,fontStyle:"normal"}}}),[`${r}-item-group-list`]:{margin:`0 ${Y(e.marginXS)}`,padding:0,listStyle:"none"},[`${r}-submenu-title`]:{paddingInlineEnd:e.calc(m).add(e.fontSizeSM).equal()},[`${r}-submenu-vertical`]:{position:"relative"},[`${r}-submenu${r}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:v,backgroundColor:g,cursor:"not-allowed"}},[`${r}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[xn(e,"slide-up"),xn(e,"slide-down"),Wr(e,"move-up"),Wr(e,"move-down"),Fo(e,"zoom-big")]]},ci=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},ul({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),fl(e)),di=In("Dropdown",e=>{const{marginXXS:t,sizePopupArrow:r,paddingXXS:n,componentCls:o}=e,l=un(e,{menuCls:`${o}-menu`,dropdownArrowDistance:e.calc(r).div(2).add(t).equal(),dropdownEdgeChildPadding:n});return[si(l),ii(l)]},ci,{resetStyle:!1}),kn=e=>{var t;const{menu:r,arrow:n,prefixCls:o,children:l,trigger:s,disabled:i,dropdownRender:d,popupRender:c,getPopupContainer:p,overlayClassName:u,rootClassName:v,overlayStyle:f,open:m,onOpenChange:g,visible:h,onVisibleChange:b,mouseEnterDelay:y=.15,mouseLeaveDelay:S=.1,autoAdjustOverflow:C=!0,placement:w="",overlay:R,transitionName:$,destroyOnHidden:k,destroyPopupOnHide:x}=e,{getPopupContainer:T,getPrefixCls:O,direction:D,dropdown:M}=a.useContext(Ht),E=c||d;Nn();const I=a.useMemo(()=>{const ie=O();return $!==void 0?$:w.includes("top")?`${ie}-slide-down`:`${ie}-slide-up`},[O,w,$]),N=a.useMemo(()=>w?w.includes("Center")?w.slice(0,w.indexOf("Center")):w:D==="rtl"?"bottomRight":"bottomLeft",[w,D]),K=O("dropdown",o),z=ur(K),[B,V,Z]=di(K,z),[,ne]=fr(),A=a.Children.only(Ul(l)?a.createElement("span",null,l):l),se=Jt(A,{className:te(`${K}-trigger`,{[`${K}-rtl`]:D==="rtl"},A.props.className),disabled:(t=A.props.disabled)!==null&&t!==void 0?t:i}),fe=i?[]:s,be=!!(fe!=null&&fe.includes("contextMenu")),[re,Q]=qo(!1,{value:m??h}),ae=ht(ie=>{g==null||g(ie,{source:"trigger"}),b==null||b(ie),Q(ie)}),ve=te(u,v,V,Z,z,M==null?void 0:M.className,{[`${K}-rtl`]:D==="rtl"}),X=vl({arrowPointAtCenter:typeof n=="object"&&n.pointAtCenter,autoAdjustOverflow:C,offset:ne.marginXXS,arrowWidth:n?ne.sizePopupArrow:0,borderRadius:ne.borderRadius}),F=a.useCallback(()=>{r!=null&&r.selectable&&(r!=null&&r.multiple)||(g==null||g(!1,{source:"menu"}),Q(!1))},[r==null?void 0:r.selectable,r==null?void 0:r.multiple]),H=()=>{let ie;return r!=null&&r.items?ie=a.createElement(tn,Object.assign({},r)):typeof R=="function"?ie=R():ie=R,E&&(ie=E(ie)),ie=a.Children.only(typeof ie=="string"?a.createElement("span",null,ie):ie),a.createElement(ra,{prefixCls:`${K}-menu`,rootClassName:te(Z,z),expandIcon:a.createElement("span",{className:`${K}-menu-submenu-arrow`},D==="rtl"?a.createElement(zl,{className:`${K}-menu-submenu-arrow-icon`}):a.createElement(_l,{className:`${K}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:F,validator:({mode:Ce})=>{}},ie)},[U,le]=Wo("Dropdown",f==null?void 0:f.zIndex);let de=a.createElement(ml,Object.assign({alignPoint:be},en(e,["rootClassName"]),{mouseEnterDelay:y,mouseLeaveDelay:S,visible:re,builtinPlacements:X,arrow:!!n,overlayClassName:ve,prefixCls:K,getPopupContainer:p||T,transitionName:I,trigger:fe,overlay:H,placement:N,onVisibleChange:ae,overlayStyle:Object.assign(Object.assign(Object.assign({},M==null?void 0:M.style),f),{zIndex:U}),autoDestroy:k??x}),se);return U&&(de=a.createElement(pl.Provider,{value:le},de)),B(de)},ui=Pl(kn,"align",void 0,"dropdown",e=>e),fi=e=>a.createElement(ui,Object.assign({},e),a.createElement("span",null));kn._InternalPanelDoNotUseOrYouWillBeFired=fi;function gt(e,t){return e[t]}var vi=["children"];function aa(e,t){return"".concat(e,"-").concat(t)}function mi(e){return e&&e.type&&e.type.isTreeNode}function gn(e,t){return e??t}function Qt(e){var t=e||{},r=t.title,n=t._title,o=t.key,l=t.children,s=r||"title";return{title:s,_title:n||[s],key:o||"key",children:l||"children"}}function la(e){function t(r){var n=dr(r);return n.map(function(o){if(!mi(o))return Bt(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var l=o.key,s=o.props,i=s.children,d=bt(s,vi),c=j({key:l},d),p=t(i);return p.length&&(c.children=p),c}).filter(function(o){return o})}return t(e)}function _n(e,t,r){var n=Qt(r),o=n._title,l=n.key,s=n.children,i=new Set(t===!0?[]:t),d=[];function c(p){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return p.map(function(v,f){for(var m=aa(u?u.pos:"0",f),g=gn(v[l],m),h,b=0;b<o.length;b+=1){var y=o[b];if(v[y]!==void 0){h=v[y];break}}var S=Object.assign(en(v,[].concat(Me(o),[l,s])),{title:h,key:g,parent:u,pos:m,children:null,data:v,isStart:[].concat(Me(u?u.isStart:[]),[f===0]),isEnd:[].concat(Me(u?u.isEnd:[]),[f===p.length-1])});return d.push(S),t===!0||i.has(g)?S.children=c(v[s]||[],S):S.children=[],S})}return c(e),d}function pi(e,t,r){var n={};ut(r)==="object"?n=r:n={externalGetKey:r},n=n||{};var o=n,l=o.childrenPropName,s=o.externalGetKey,i=o.fieldNames,d=Qt(i),c=d.key,p=d.children,u=l||p,v;s?typeof s=="string"?v=function(g){return g[s]}:typeof s=="function"&&(v=function(g){return s(g)}):v=function(g,h){return gn(g[c],h)};function f(m,g,h,b){var y=m?m[u]:e,S=m?aa(h.pos,g):"0",C=m?[].concat(Me(b),[m]):[];if(m){var w=v(m,S),R={node:m,index:g,pos:S,key:w,parentPos:h.node?h.pos:null,level:h.level+1,nodes:C};t(R)}y&&y.forEach(function($,k){f($,k,{node:m,pos:S,level:h?h.level+1:-1},C)})}f(null)}function hr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.initWrapper,n=t.processEntity,o=t.onProcessFinished,l=t.externalGetKey,s=t.childrenPropName,i=t.fieldNames,d=arguments.length>2?arguments[2]:void 0,c=l||d,p={},u={},v={posEntities:p,keyEntities:u};return r&&(v=r(v)||v),pi(e,function(f){var m=f.node,g=f.index,h=f.pos,b=f.key,y=f.parentPos,S=f.level,C=f.nodes,w={node:m,nodes:C,index:g,key:b,pos:h,level:S},R=gn(b,h);p[h]=w,u[R]=w,w.parent=p[y],w.parent&&(w.parent.children=w.parent.children||[],w.parent.children.push(w)),n&&n(w,v)},{externalGetKey:c,childrenPropName:s,fieldNames:i}),o&&o(v),v}function cn(e,t){var r=t.expandedKeys,n=t.selectedKeys,o=t.loadedKeys,l=t.loadingKeys,s=t.checkedKeys,i=t.halfCheckedKeys,d=t.dragOverNodeKey,c=t.dropPosition,p=t.keyEntities,u=gt(p,e),v={eventKey:e,expanded:r.indexOf(e)!==-1,selected:n.indexOf(e)!==-1,loaded:o.indexOf(e)!==-1,loading:l.indexOf(e)!==-1,checked:s.indexOf(e)!==-1,halfChecked:i.indexOf(e)!==-1,pos:String(u?u.pos:""),dragOver:d===e&&c===0,dragOverGapTop:d===e&&c===-1,dragOverGapBottom:d===e&&c===1};return v}function Qe(e){var t=e.data,r=e.expanded,n=e.selected,o=e.checked,l=e.loaded,s=e.loading,i=e.halfChecked,d=e.dragOver,c=e.dragOverGapTop,p=e.dragOverGapBottom,u=e.pos,v=e.active,f=e.eventKey,m=j(j({},t),{},{expanded:r,selected:n,checked:o,loaded:l,loading:s,halfChecked:i,dragOver:d,dragOverGapTop:c,dragOverGapBottom:p,pos:u,active:v,key:f});return"props"in m||Object.defineProperty(m,"props",{get:function(){return Bt(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),m}function ia(e,t){var r=new Set;return e.forEach(function(n){t.has(n)||r.add(n)}),r}function gi(e){var t=e||{},r=t.disabled,n=t.disableCheckbox,o=t.checkable;return!!(r||n)||o===!1}function hi(e,t,r,n){for(var o=new Set(e),l=new Set,s=0;s<=r;s+=1){var i=t.get(s)||new Set;i.forEach(function(u){var v=u.key,f=u.node,m=u.children,g=m===void 0?[]:m;o.has(v)&&!n(f)&&g.filter(function(h){return!n(h.node)}).forEach(function(h){o.add(h.key)})})}for(var d=new Set,c=r;c>=0;c-=1){var p=t.get(c)||new Set;p.forEach(function(u){var v=u.parent,f=u.node;if(!(n(f)||!u.parent||d.has(u.parent.key))){if(n(u.parent.node)){d.add(v.key);return}var m=!0,g=!1;(v.children||[]).filter(function(h){return!n(h.node)}).forEach(function(h){var b=h.key,y=o.has(b);m&&!y&&(m=!1),!g&&(y||l.has(b))&&(g=!0)}),m&&o.add(v.key),g&&l.add(v.key),d.add(v.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(ia(l,o))}}function bi(e,t,r,n,o){for(var l=new Set(e),s=new Set(t),i=0;i<=n;i+=1){var d=r.get(i)||new Set;d.forEach(function(v){var f=v.key,m=v.node,g=v.children,h=g===void 0?[]:g;!l.has(f)&&!s.has(f)&&!o(m)&&h.filter(function(b){return!o(b.node)}).forEach(function(b){l.delete(b.key)})})}s=new Set;for(var c=new Set,p=n;p>=0;p-=1){var u=r.get(p)||new Set;u.forEach(function(v){var f=v.parent,m=v.node;if(!(o(m)||!v.parent||c.has(v.parent.key))){if(o(v.parent.node)){c.add(f.key);return}var g=!0,h=!1;(f.children||[]).filter(function(b){return!o(b.node)}).forEach(function(b){var y=b.key,S=l.has(y);g&&!S&&(g=!1),!h&&(S||s.has(y))&&(h=!0)}),g||l.delete(f.key),h&&s.add(f.key),c.add(f.key)}})}return{checkedKeys:Array.from(l),halfCheckedKeys:Array.from(ia(s,l))}}function Gt(e,t,r,n){var o=[],l;n?l=n:l=gi;var s=new Set(e.filter(function(p){var u=!!gt(r,p);return u||o.push(p),u})),i=new Map,d=0;Object.keys(r).forEach(function(p){var u=r[p],v=u.level,f=i.get(v);f||(f=new Set,i.set(v,f)),f.add(u),d=Math.max(d,v)}),Bt(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(p){return"'".concat(p,"'")}).join(", ")));var c;return t===!0?c=hi(s,i,d,l):c=bi(s,t.halfCheckedKeys,i,d,l),c}var yi=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const sa=e=>{const{getPopupContainer:t,getPrefixCls:r,direction:n}=a.useContext(Ht),{prefixCls:o,type:l="default",danger:s,disabled:i,loading:d,onClick:c,htmlType:p,children:u,className:v,menu:f,arrow:m,autoFocus:g,overlay:h,trigger:b,align:y,open:S,onOpenChange:C,placement:w,getPopupContainer:R,href:$,icon:k=a.createElement(Vo,null),title:x,buttonsRender:T=X=>X,mouseEnterDelay:O,mouseLeaveDelay:D,overlayClassName:M,overlayStyle:E,destroyOnHidden:I,destroyPopupOnHide:N,dropdownRender:K,popupRender:z}=e,B=yi(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),V=r("dropdown",o),Z=`${V}-button`,A={menu:f,arrow:m,autoFocus:g,align:y,disabled:i,trigger:i?[]:b,onOpenChange:C,getPopupContainer:R||t,mouseEnterDelay:O,mouseLeaveDelay:D,overlayClassName:M,overlayStyle:E,destroyOnHidden:I,popupRender:z||K},{compactSize:se,compactItemClassnames:fe}=gl(V,n),be=te(Z,fe,v);"destroyPopupOnHide"in e&&(A.destroyPopupOnHide=N),"overlay"in e&&(A.overlay=h),"open"in e&&(A.open=S),"placement"in e?A.placement=w:A.placement=n==="rtl"?"bottomLeft":"bottomRight";const re=a.createElement(Tt,{type:l,danger:s,disabled:i,loading:d,onClick:c,htmlType:p,href:$,title:x},u),Q=a.createElement(Tt,{type:l,danger:s,icon:k}),[ae,ve]=T([re,Q]);return a.createElement(Un.Compact,Object.assign({className:be,size:se,block:!0},B),ae,a.createElement(kn,Object.assign({},A),ve))};sa.__ANT_BUTTON=!0;const br=kn;br.Button=sa;var Si={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},xi=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:Si}))},Ci=a.forwardRef(xi),wi={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"},$i=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:wi}))},Ei=a.forwardRef($i),Ri={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"},Ii=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:Ri}))},Ni=a.forwardRef(Ii),ki={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},Oi=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:ki}))},ca=a.forwardRef(Oi),Ti={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"},Mi=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:Ti}))},Pi=a.forwardRef(Mi),Di={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},Ki=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:Di}))},Bi=a.forwardRef(Ki),Li={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},Hi=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:Li}))},zi=a.forwardRef(Hi),_i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},ji=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:_i}))},Ai=a.forwardRef(ji),Fi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},Wi=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:Fi}))},Vi=a.forwardRef(Wi),Xi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},qi=function(t,r){return a.createElement(It,Ee({},t,{ref:r,icon:Xi}))},Yi=a.forwardRef(qi),Ot={},hn="rc-table-internal-hook";function yr(e){var t=a.createContext(void 0),r=function(o){var l=o.value,s=o.children,i=a.useRef(l);i.current=l;var d=a.useState(function(){return{getValue:function(){return i.current},listeners:new Set}}),c=oe(d,1),p=c[0];return ot(function(){fn.unstable_batchedUpdates(function(){p.listeners.forEach(function(u){u(l)})})},[l]),a.createElement(t.Provider,{value:p},s)};return{Context:t,Provider:r,defaultValue:e}}function st(e,t){var r=ht(typeof t=="function"?t:function(u){if(t===void 0)return u;if(!Array.isArray(t))return u[t];var v={};return t.forEach(function(f){v[f]=u[f]}),v}),n=a.useContext(e==null?void 0:e.Context),o=n||{},l=o.listeners,s=o.getValue,i=a.useRef();i.current=r(n?s():e==null?void 0:e.defaultValue);var d=a.useState({}),c=oe(d,2),p=c[1];return ot(function(){if(!n)return;function u(v){var f=r(v);vn(i.current,f,!0)||p({})}return l.add(u),function(){l.delete(u)}},[n]),i.current}function Ui(){var e=a.createContext(null);function t(){return a.useContext(e)}function r(o,l){var s=_r(o),i=function(c,p){var u=s?{ref:p}:{},v=a.useRef(0),f=a.useRef(c),m=t();return m!==null?a.createElement(o,Ee({},c,u)):((!l||l(f.current,c))&&(v.current+=1),f.current=c,a.createElement(e.Provider,{value:v.current},a.createElement(o,Ee({},c,u))))};return s?a.forwardRef(i):i}function n(o,l){var s=_r(o),i=function(c,p){var u=s?{ref:p}:{};return t(),a.createElement(o,Ee({},c,u))};return s?a.memo(a.forwardRef(i),l):a.memo(i,l)}return{makeImmutable:r,responseImmutable:n,useImmutableMark:t}}var Sr=Ui(),da=Sr.makeImmutable,nn=Sr.responseImmutable,Gi=Sr.useImmutableMark,vt=yr(),ua=a.createContext({renderWithProps:!1}),Zi="RC_TABLE_KEY";function Ji(e){return e==null?[]:Array.isArray(e)?e:[e]}function On(e){var t=[],r={};return e.forEach(function(n){for(var o=n||{},l=o.key,s=o.dataIndex,i=l||Ji(s).join("-")||Zi;r[i];)i="".concat(i,"_next");r[i]=!0,t.push(i)}),t}function Zn(e){return e!=null}function Qi(e){return typeof e=="number"&&!Number.isNaN(e)}function es(e){return e&&ut(e)==="object"&&!Array.isArray(e)&&!a.isValidElement(e)}function ts(e,t,r,n,o,l){var s=a.useContext(ua),i=Gi(),d=Yo(function(){if(Zn(n))return[n];var c=t==null||t===""?[]:Array.isArray(t)?t:[t],p=vr(e,c),u=p,v=void 0;if(o){var f=o(p,e,r);es(f)?(u=f.children,v=f.props,s.renderWithProps=!0):u=f}return[u,v]},[i,e,n,t,o,r],function(c,p){if(l){var u=oe(c,2),v=u[1],f=oe(p,2),m=f[1];return l(m,v)}return s.renderWithProps?!0:!vn(c,p,!0)});return d}function ns(e,t,r,n){var o=e+t-1;return e<=n&&o>=r}function rs(e,t){return st(vt,function(r){var n=ns(e,t||1,r.hoverStartRow,r.hoverEndRow);return[n,r.onHover]})}var os=function(t){var r=t.ellipsis,n=t.rowType,o=t.children,l,s=r===!0?{showTitle:!0}:r;return s&&(s.showTitle||n==="header")&&(typeof o=="string"||typeof o=="number"?l=o.toString():a.isValidElement(o)&&typeof o.props.children=="string"&&(l=o.props.children)),l};function as(e){var t,r,n,o,l,s,i,d,c=e.component,p=e.children,u=e.ellipsis,v=e.scope,f=e.prefixCls,m=e.className,g=e.align,h=e.record,b=e.render,y=e.dataIndex,S=e.renderIndex,C=e.shouldCellUpdate,w=e.index,R=e.rowType,$=e.colSpan,k=e.rowSpan,x=e.fixLeft,T=e.fixRight,O=e.firstFixLeft,D=e.lastFixLeft,M=e.firstFixRight,E=e.lastFixRight,I=e.appendNode,N=e.additionalProps,K=N===void 0?{}:N,z=e.isSticky,B="".concat(f,"-cell"),V=st(vt,["supportSticky","allColumnsFixedLeft","rowHoverable"]),Z=V.supportSticky,ne=V.allColumnsFixedLeft,A=V.rowHoverable,se=ts(h,y,S,p,b,C),fe=oe(se,2),be=fe[0],re=fe[1],Q={},ae=typeof x=="number"&&Z,ve=typeof T=="number"&&Z;ae&&(Q.position="sticky",Q.left=x),ve&&(Q.position="sticky",Q.right=T);var X=(t=(r=(n=re==null?void 0:re.colSpan)!==null&&n!==void 0?n:K.colSpan)!==null&&r!==void 0?r:$)!==null&&t!==void 0?t:1,F=(o=(l=(s=re==null?void 0:re.rowSpan)!==null&&s!==void 0?s:K.rowSpan)!==null&&l!==void 0?l:k)!==null&&o!==void 0?o:1,H=rs(w,F),U=oe(H,2),le=U[0],de=U[1],ie=ht(function(xe){var he;h&&de(w,w+F-1),K==null||(he=K.onMouseEnter)===null||he===void 0||he.call(K,xe)}),Ce=ht(function(xe){var he;h&&de(-1,-1),K==null||(he=K.onMouseLeave)===null||he===void 0||he.call(K,xe)});if(X===0||F===0)return null;var je=(i=K.title)!==null&&i!==void 0?i:os({rowType:R,ellipsis:u,children:be}),we=te(B,m,(d={},P(P(P(P(P(P(P(P(P(P(d,"".concat(B,"-fix-left"),ae&&Z),"".concat(B,"-fix-left-first"),O&&Z),"".concat(B,"-fix-left-last"),D&&Z),"".concat(B,"-fix-left-all"),D&&ne&&Z),"".concat(B,"-fix-right"),ve&&Z),"".concat(B,"-fix-right-first"),M&&Z),"".concat(B,"-fix-right-last"),E&&Z),"".concat(B,"-ellipsis"),u),"".concat(B,"-with-append"),I),"".concat(B,"-fix-sticky"),(ae||ve)&&z&&Z),P(d,"".concat(B,"-row-hover"),!re&&le)),K.className,re==null?void 0:re.className),W={};g&&(W.textAlign=g);var q=j(j(j(j({},re==null?void 0:re.style),Q),W),K.style),ue=be;return ut(ue)==="object"&&!Array.isArray(ue)&&!a.isValidElement(ue)&&(ue=null),u&&(D||M)&&(ue=a.createElement("span",{className:"".concat(B,"-content")},ue)),a.createElement(c,Ee({},re,K,{className:we,style:q,title:je,scope:v,onMouseEnter:A?ie:void 0,onMouseLeave:A?Ce:void 0,colSpan:X!==1?X:null,rowSpan:F!==1?F:null}),I,ue)}const rn=a.memo(as);function xr(e,t,r,n,o){var l=r[e]||{},s=r[t]||{},i,d;l.fixed==="left"?i=n.left[o==="rtl"?t:e]:s.fixed==="right"&&(d=n.right[o==="rtl"?e:t]);var c=!1,p=!1,u=!1,v=!1,f=r[t+1],m=r[e-1],g=f&&!f.fixed||m&&!m.fixed||r.every(function(C){return C.fixed==="left"});if(o==="rtl"){if(i!==void 0){var h=m&&m.fixed==="left";v=!h&&g}else if(d!==void 0){var b=f&&f.fixed==="right";u=!b&&g}}else if(i!==void 0){var y=f&&f.fixed==="left";c=!y&&g}else if(d!==void 0){var S=m&&m.fixed==="right";p=!S&&g}return{fixLeft:i,fixRight:d,lastFixLeft:c,firstFixRight:p,lastFixRight:u,firstFixLeft:v,isSticky:n.isSticky}}var fa=a.createContext({});function ls(e){var t=e.className,r=e.index,n=e.children,o=e.colSpan,l=o===void 0?1:o,s=e.rowSpan,i=e.align,d=st(vt,["prefixCls","direction"]),c=d.prefixCls,p=d.direction,u=a.useContext(fa),v=u.scrollColumnIndex,f=u.stickyOffsets,m=u.flattenColumns,g=r+l-1,h=g+1===v?l+1:l,b=xr(r,r+h-1,m,f,p);return a.createElement(rn,Ee({className:t,index:r,component:"td",prefixCls:c,record:null,dataIndex:null,align:i,colSpan:h,rowSpan:s,render:function(){return n}},b))}var is=["children"];function ss(e){var t=e.children,r=bt(e,is);return a.createElement("tr",r,t)}function Tn(e){var t=e.children;return t}Tn.Row=ss;Tn.Cell=ls;function cs(e){var t=e.children,r=e.stickyOffsets,n=e.flattenColumns,o=st(vt,"prefixCls"),l=n.length-1,s=n[l],i=a.useMemo(function(){return{stickyOffsets:r,flattenColumns:n,scrollColumnIndex:s!=null&&s.scrollbar?l:null}},[s,n,l,r]);return a.createElement(fa.Provider,{value:i},a.createElement("tfoot",{className:"".concat(o,"-summary")},t))}const yn=nn(cs);var va=Tn;function ds(e){return null}function us(e){return null}function ma(e,t,r,n,o,l,s){var i=l(t,s);e.push({record:t,indent:r,index:s,rowKey:i});var d=o==null?void 0:o.has(i);if(t&&Array.isArray(t[n])&&d)for(var c=0;c<t[n].length;c+=1)ma(e,t[n][c],r+1,n,o,l,c)}function pa(e,t,r,n){var o=a.useMemo(function(){if(r!=null&&r.size){for(var l=[],s=0;s<(e==null?void 0:e.length);s+=1){var i=e[s];ma(l,i,0,t,r,n,s)}return l}return e==null?void 0:e.map(function(d,c){return{record:d,indent:0,index:c,rowKey:n(d,c)}})},[e,t,r,n]);return o}function ga(e,t,r,n){var o=st(vt,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),l=o.flattenColumns,s=o.expandableType,i=o.expandedKeys,d=o.childrenColumnName,c=o.onTriggerExpand,p=o.rowExpandable,u=o.onRow,v=o.expandRowByClick,f=o.rowClassName,m=s==="nest",g=s==="row"&&(!p||p(e)),h=g||m,b=i&&i.has(t),y=d&&e&&e[d],S=ht(c),C=u==null?void 0:u(e,r),w=C==null?void 0:C.onClick,R=function(T){v&&h&&c(e,T);for(var O=arguments.length,D=new Array(O>1?O-1:0),M=1;M<O;M++)D[M-1]=arguments[M];w==null||w.apply(void 0,[T].concat(D))},$;typeof f=="string"?$=f:typeof f=="function"&&($=f(e,r,n));var k=On(l);return j(j({},o),{},{columnsKey:k,nestExpandable:m,expanded:b,hasNestChildren:y,record:e,onTriggerExpand:S,rowSupportExpand:g,expandable:h,rowProps:j(j({},C),{},{className:te($,C==null?void 0:C.className),onClick:R})})}function ha(e){var t=e.prefixCls,r=e.children,n=e.component,o=e.cellComponent,l=e.className,s=e.expanded,i=e.colSpan,d=e.isEmpty,c=e.stickyOffset,p=c===void 0?0:c,u=st(vt,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),v=u.scrollbarSize,f=u.fixHeader,m=u.fixColumn,g=u.componentWidth,h=u.horizonScroll,b=r;return(d?h&&g:m)&&(b=a.createElement("div",{style:{width:g-p-(f&&!d?v:0),position:"sticky",left:p,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},b)),a.createElement(n,{className:l,style:{display:s?null:"none"}},a.createElement(rn,{component:o,prefixCls:t,colSpan:i},b))}function fs(e){var t=e.prefixCls,r=e.record,n=e.onExpand,o=e.expanded,l=e.expandable,s="".concat(t,"-row-expand-icon");if(!l)return a.createElement("span",{className:te(s,"".concat(t,"-row-spaced"))});var i=function(c){n(r,c),c.stopPropagation()};return a.createElement("span",{className:te(s,P(P({},"".concat(t,"-row-expanded"),o),"".concat(t,"-row-collapsed"),!o)),onClick:i})}function vs(e,t,r){var n=[];function o(l){(l||[]).forEach(function(s,i){n.push(t(s,i)),o(s[r])})}return o(e),n}function ba(e,t,r,n){return typeof e=="string"?e:typeof e=="function"?e(t,r,n):""}function ya(e,t,r,n,o){var l,s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:[],i=arguments.length>6&&arguments[6]!==void 0?arguments[6]:0,d=e.record,c=e.prefixCls,p=e.columnsKey,u=e.fixedInfoList,v=e.expandIconColumnIndex,f=e.nestExpandable,m=e.indentSize,g=e.expandIcon,h=e.expanded,b=e.hasNestChildren,y=e.onTriggerExpand,S=e.expandable,C=e.expandedKeys,w=p[r],R=u[r],$;r===(v||0)&&f&&($=a.createElement(a.Fragment,null,a.createElement("span",{style:{paddingLeft:"".concat(m*n,"px")},className:"".concat(c,"-row-indent indent-level-").concat(n)}),g({prefixCls:c,expanded:h,expandable:b,record:d,onExpand:y})));var k=((l=t.onCell)===null||l===void 0?void 0:l.call(t,d,o))||{};if(i){var x=k.rowSpan,T=x===void 0?1:x;if(S&&T&&r<i){for(var O=T,D=o;D<o+T;D+=1){var M=s[D];C.has(M)&&(O+=1)}k.rowSpan=O}}return{key:w,fixedInfo:R,appendCellNode:$,additionalCellProps:k}}function ms(e){var t=e.className,r=e.style,n=e.record,o=e.index,l=e.renderIndex,s=e.rowKey,i=e.rowKeys,d=e.indent,c=d===void 0?0:d,p=e.rowComponent,u=e.cellComponent,v=e.scopeCellComponent,f=e.expandedRowInfo,m=ga(n,s,o,c),g=m.prefixCls,h=m.flattenColumns,b=m.expandedRowClassName,y=m.expandedRowRender,S=m.rowProps,C=m.expanded,w=m.rowSupportExpand,R=a.useRef(!1);R.current||(R.current=C);var $=ba(b,n,o,c),k=a.createElement(p,Ee({},S,{"data-row-key":s,className:te(t,"".concat(g,"-row"),"".concat(g,"-row-level-").concat(c),S==null?void 0:S.className,P({},$,c>=1)),style:j(j({},r),S==null?void 0:S.style)}),h.map(function(O,D){var M=O.render,E=O.dataIndex,I=O.className,N=ya(m,O,D,c,o,i,f==null?void 0:f.offset),K=N.key,z=N.fixedInfo,B=N.appendCellNode,V=N.additionalCellProps;return a.createElement(rn,Ee({className:I,ellipsis:O.ellipsis,align:O.align,scope:O.rowScope,component:O.rowScope?v:u,prefixCls:g,key:K,record:n,index:o,renderIndex:l,dataIndex:E,render:M,shouldCellUpdate:O.shouldCellUpdate},z,{appendNode:B,additionalProps:V}))})),x;if(w&&(R.current||C)){var T=y(n,o,c+1,C);x=a.createElement(ha,{expanded:C,className:te("".concat(g,"-expanded-row"),"".concat(g,"-expanded-row-level-").concat(c+1),$),prefixCls:g,component:p,cellComponent:u,colSpan:f?f.colSpan:h.length,stickyOffset:f==null?void 0:f.sticky,isEmpty:!1},T)}return a.createElement(a.Fragment,null,k,x)}const ps=nn(ms);function gs(e){var t=e.columnKey,r=e.onColumnResize,n=a.useRef();return ot(function(){n.current&&r(t,n.current.offsetWidth)},[]),a.createElement(qt,{data:t},a.createElement("td",{ref:n,style:{padding:0,border:0,height:0}},a.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function hs(e){var t=e.prefixCls,r=e.columnsKey,n=e.onColumnResize,o=a.useRef(null);return a.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0},ref:o},a.createElement(qt.Collection,{onBatchResize:function(s){hl(o.current)&&s.forEach(function(i){var d=i.data,c=i.size;n(d,c.offsetWidth)})}},r.map(function(l){return a.createElement(gs,{key:l,columnKey:l,onColumnResize:n})})))}function bs(e){var t=e.data,r=e.measureColumnWidth,n=st(vt,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode","expandedRowOffset","fixedInfoList","colWidths"]),o=n.prefixCls,l=n.getComponent,s=n.onColumnResize,i=n.flattenColumns,d=n.getRowKey,c=n.expandedKeys,p=n.childrenColumnName,u=n.emptyNode,v=n.expandedRowOffset,f=v===void 0?0:v,m=n.colWidths,g=pa(t,p,c,d),h=a.useMemo(function(){return g.map(function(x){return x.rowKey})},[g]),b=a.useRef({renderWithProps:!1}),y=a.useMemo(function(){for(var x=i.length-f,T=0,O=0;O<f;O+=1)T+=m[O]||0;return{offset:f,colSpan:x,sticky:T}},[i.length,f,m]),S=l(["body","wrapper"],"tbody"),C=l(["body","row"],"tr"),w=l(["body","cell"],"td"),R=l(["body","cell"],"th"),$;t.length?$=g.map(function(x,T){var O=x.record,D=x.indent,M=x.index,E=x.rowKey;return a.createElement(ps,{key:E,rowKey:E,rowKeys:h,record:O,index:T,renderIndex:M,rowComponent:C,cellComponent:w,scopeCellComponent:R,indent:D,expandedRowInfo:y})}):$=a.createElement(ha,{expanded:!0,className:"".concat(o,"-placeholder"),prefixCls:o,component:C,cellComponent:w,colSpan:i.length,isEmpty:!0},u);var k=On(i);return a.createElement(ua.Provider,{value:b.current},a.createElement(S,{className:"".concat(o,"-tbody")},r&&a.createElement(hs,{prefixCls:o,columnsKey:k,onColumnResize:s}),$))}const ys=nn(bs);var Ss=["expandable"],dn="RC_TABLE_INTERNAL_COL_DEFINE";function xs(e){var t=e.expandable,r=bt(e,Ss),n;return"expandable"in e?n=j(j({},r),t):n=r,n.showExpandColumn===!1&&(n.expandIconColumnIndex=-1),n}var Cs=["columnType"];function Sa(e){for(var t=e.colWidths,r=e.columns,n=e.columCount,o=st(vt,["tableLayout"]),l=o.tableLayout,s=[],i=n||r.length,d=!1,c=i-1;c>=0;c-=1){var p=t[c],u=r&&r[c],v=void 0,f=void 0;if(u&&(v=u[dn],l==="auto"&&(f=u.minWidth)),p||f||v||d){var m=v||{};m.columnType;var g=bt(m,Cs);s.unshift(a.createElement("col",Ee({key:c,style:{width:p,minWidth:f}},g))),d=!0}}return a.createElement("colgroup",null,s)}var ws=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"];function $s(e,t){return a.useMemo(function(){for(var r=[],n=0;n<t;n+=1){var o=e[n];if(o!==void 0)r[n]=o;else return null}return r},[e.join("_"),t])}var Es=a.forwardRef(function(e,t){var r=e.className,n=e.noData,o=e.columns,l=e.flattenColumns,s=e.colWidths,i=e.columCount,d=e.stickyOffsets,c=e.direction,p=e.fixHeader,u=e.stickyTopOffset,v=e.stickyBottomOffset,f=e.stickyClassName,m=e.onScroll,g=e.maxContentScroll,h=e.children,b=bt(e,ws),y=st(vt,["prefixCls","scrollbarSize","isSticky","getComponent"]),S=y.prefixCls,C=y.scrollbarSize,w=y.isSticky,R=y.getComponent,$=R(["header","table"],"table"),k=w&&!p?0:C,x=a.useRef(null),T=a.useCallback(function(z){jr(t,z),jr(x,z)},[]);a.useEffect(function(){function z(V){var Z=V,ne=Z.currentTarget,A=Z.deltaX;A&&(m({currentTarget:ne,scrollLeft:ne.scrollLeft+A}),V.preventDefault())}var B=x.current;return B==null||B.addEventListener("wheel",z,{passive:!1}),function(){B==null||B.removeEventListener("wheel",z)}},[]);var O=a.useMemo(function(){return l.every(function(z){return z.width})},[l]),D=l[l.length-1],M={fixed:D?D.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(S,"-cell-scrollbar")}}},E=a.useMemo(function(){return k?[].concat(Me(o),[M]):o},[k,o]),I=a.useMemo(function(){return k?[].concat(Me(l),[M]):l},[k,l]),N=a.useMemo(function(){var z=d.right,B=d.left;return j(j({},d),{},{left:c==="rtl"?[].concat(Me(B.map(function(V){return V+k})),[0]):B,right:c==="rtl"?z:[].concat(Me(z.map(function(V){return V+k})),[0]),isSticky:w})},[k,d,w]),K=$s(s,i);return a.createElement("div",{style:j({overflow:"hidden"},w?{top:u,bottom:v}:{}),ref:T,className:te(r,P({},f,!!f))},a.createElement($,{style:{tableLayout:"fixed",visibility:n||K?null:"hidden"}},(!n||!g||O)&&a.createElement(Sa,{colWidths:K?[].concat(Me(K),[k]):[],columCount:i+1,columns:I}),h(j(j({},b),{},{stickyOffsets:N,columns:E,flattenColumns:I}))))});const Jr=a.memo(Es);var Rs=function(t){var r=t.cells,n=t.stickyOffsets,o=t.flattenColumns,l=t.rowComponent,s=t.cellComponent,i=t.onHeaderRow,d=t.index,c=st(vt,["prefixCls","direction"]),p=c.prefixCls,u=c.direction,v;i&&(v=i(r.map(function(m){return m.column}),d));var f=On(r.map(function(m){return m.column}));return a.createElement(l,v,r.map(function(m,g){var h=m.column,b=xr(m.colStart,m.colEnd,o,n,u),y;return h&&h.onHeaderCell&&(y=m.column.onHeaderCell(h)),a.createElement(rn,Ee({},m,{scope:h.title?m.colSpan>1?"colgroup":"col":null,ellipsis:h.ellipsis,align:h.align,component:s,prefixCls:p,key:f[g]},b,{additionalProps:y,rowType:"header"}))}))};function Is(e){var t=[];function r(s,i){var d=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;t[d]=t[d]||[];var c=i,p=s.filter(Boolean).map(function(u){var v={key:u.key,className:u.className||"",children:u.title,column:u,colStart:c},f=1,m=u.children;return m&&m.length>0&&(f=r(m,c,d+1).reduce(function(g,h){return g+h},0),v.hasSubColumns=!0),"colSpan"in u&&(f=u.colSpan),"rowSpan"in u&&(v.rowSpan=u.rowSpan),v.colSpan=f,v.colEnd=v.colStart+f-1,t[d].push(v),c+=f,f});return p}r(e,0);for(var n=t.length,o=function(i){t[i].forEach(function(d){!("rowSpan"in d)&&!d.hasSubColumns&&(d.rowSpan=n-i)})},l=0;l<n;l+=1)o(l);return t}var Ns=function(t){var r=t.stickyOffsets,n=t.columns,o=t.flattenColumns,l=t.onHeaderRow,s=st(vt,["prefixCls","getComponent"]),i=s.prefixCls,d=s.getComponent,c=a.useMemo(function(){return Is(n)},[n]),p=d(["header","wrapper"],"thead"),u=d(["header","row"],"tr"),v=d(["header","cell"],"th");return a.createElement(p,{className:"".concat(i,"-thead")},c.map(function(f,m){var g=a.createElement(Rs,{key:m,flattenColumns:o,cells:f,stickyOffsets:r,rowComponent:u,cellComponent:v,onHeaderRow:l,index:m});return g}))};const Qr=nn(Ns);function eo(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return typeof t=="number"?t:t.endsWith("%")?e*parseFloat(t)/100:null}function ks(e,t,r){return a.useMemo(function(){if(t&&t>0){var n=0,o=0;e.forEach(function(v){var f=eo(t,v.width);f?n+=f:o+=1});var l=Math.max(t,r),s=Math.max(l-n,o),i=o,d=s/o,c=0,p=e.map(function(v){var f=j({},v),m=eo(t,f.width);if(m)f.width=m;else{var g=Math.floor(d);f.width=i===1?s:g,s-=g,i-=1}return c+=f.width,f});if(c<l){var u=l/c;s=l,p.forEach(function(v,f){var m=Math.floor(v.width*u);v.width=f===p.length-1?s:m,s-=m})}return[p,Math.max(c,l)]}return[e,t]},[e,t,r])}var Os=["children"],Ts=["fixed"];function Cr(e){return dr(e).filter(function(t){return a.isValidElement(t)}).map(function(t){var r=t.key,n=t.props,o=n.children,l=bt(n,Os),s=j({key:r},l);return o&&(s.children=Cr(o)),s})}function xa(e){return e.filter(function(t){return t&&ut(t)==="object"&&!t.hidden}).map(function(t){var r=t.children;return r&&r.length>0?j(j({},t),{},{children:xa(r)}):t})}function Jn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key";return e.filter(function(r){return r&&ut(r)==="object"}).reduce(function(r,n,o){var l=n.fixed,s=l===!0?"left":l,i="".concat(t,"-").concat(o),d=n.children;return d&&d.length>0?[].concat(Me(r),Me(Jn(d,i).map(function(c){return j({fixed:s},c)}))):[].concat(Me(r),[j(j({key:i},n),{},{fixed:s})])},[])}function Ms(e){return e.map(function(t){var r=t.fixed,n=bt(t,Ts),o=r;return r==="left"?o="right":r==="right"&&(o="left"),j({fixed:o},n)})}function Ps(e,t){var r=e.prefixCls,n=e.columns,o=e.children,l=e.expandable,s=e.expandedKeys,i=e.columnTitle,d=e.getRowKey,c=e.onTriggerExpand,p=e.expandIcon,u=e.rowExpandable,v=e.expandIconColumnIndex,f=e.expandedRowOffset,m=f===void 0?0:f,g=e.direction,h=e.expandRowByClick,b=e.columnWidth,y=e.fixed,S=e.scrollWidth,C=e.clientWidth,w=a.useMemo(function(){var E=n||Cr(o)||[];return xa(E.slice())},[n,o]),R=a.useMemo(function(){if(l){var E=w.slice();if(!E.includes(Ot)){var I=v||0;I>=0&&(I||y==="left"||!y)&&E.splice(I,0,Ot),y==="right"&&E.splice(w.length,0,Ot)}var N=E.indexOf(Ot);E=E.filter(function(V,Z){return V!==Ot||Z===N});var K=w[N],z;y?z=y:z=K?K.fixed:null;var B=P(P(P(P(P(P({},dn,{className:"".concat(r,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),"title",i),"fixed",z),"className","".concat(r,"-row-expand-icon-cell")),"width",b),"render",function(Z,ne,A){var se=d(ne,A),fe=s.has(se),be=u?u(ne):!0,re=p({prefixCls:r,expanded:fe,expandable:be,record:ne,onExpand:c});return h?a.createElement("span",{onClick:function(ae){return ae.stopPropagation()}},re):re});return E.map(function(V,Z){var ne=V===Ot?B:V;return Z<m?j(j({},ne),{},{fixed:ne.fixed||"left"}):ne})}return w.filter(function(V){return V!==Ot})},[l,w,d,s,p,g,m]),$=a.useMemo(function(){var E=R;return t&&(E=t(E)),E.length||(E=[{render:function(){return null}}]),E},[t,R,g]),k=a.useMemo(function(){return g==="rtl"?Ms(Jn($)):Jn($)},[$,g,S]),x=a.useMemo(function(){for(var E=-1,I=k.length-1;I>=0;I-=1){var N=k[I].fixed;if(N==="left"||N===!0){E=I;break}}if(E>=0)for(var K=0;K<=E;K+=1){var z=k[K].fixed;if(z!=="left"&&z!==!0)return!0}var B=k.findIndex(function(ne){var A=ne.fixed;return A==="right"});if(B>=0)for(var V=B;V<k.length;V+=1){var Z=k[V].fixed;if(Z!=="right")return!0}return!1},[k]),T=ks(k,S,C),O=oe(T,2),D=O[0],M=O[1];return[$,D,M,x]}function Ds(e,t,r){var n=xs(e),o=n.expandIcon,l=n.expandedRowKeys,s=n.defaultExpandedRowKeys,i=n.defaultExpandAllRows,d=n.expandedRowRender,c=n.onExpand,p=n.onExpandedRowsChange,u=n.childrenColumnName,v=o||fs,f=u||"children",m=a.useMemo(function(){return d?"row":e.expandable&&e.internalHooks===hn&&e.expandable.__PARENT_RENDER_ICON__||t.some(function(w){return w&&ut(w)==="object"&&w[f]})?"nest":!1},[!!d,t]),g=a.useState(function(){return s||(i?vs(t,r,f):[])}),h=oe(g,2),b=h[0],y=h[1],S=a.useMemo(function(){return new Set(l||b||[])},[l,b]),C=a.useCallback(function(w){var R=r(w,t.indexOf(w)),$,k=S.has(R);k?(S.delete(R),$=Me(S)):$=[].concat(Me(S),[R]),y($),c&&c(!k,w),p&&p($)},[r,S,t,c,p]);return[n,m,S,v,f,C]}function Ks(e,t,r){var n=e.map(function(o,l){return xr(l,l,e,t,r)});return Yo(function(){return n},[n],function(o,l){return!vn(o,l)})}function Bs(e){var t=a.useRef(e),r=a.useState({}),n=oe(r,2),o=n[1],l=a.useRef(null),s=a.useRef([]);function i(d){s.current.push(d);var c=Promise.resolve();l.current=c,c.then(function(){if(l.current===c){var p=s.current,u=t.current;s.current=[],p.forEach(function(v){t.current=v(t.current)}),l.current=null,u!==t.current&&o({})}})}return a.useEffect(function(){return function(){l.current=null}},[]),[t.current,i]}function Ls(e){var t=a.useRef(null),r=a.useRef();function n(){window.clearTimeout(r.current)}function o(s){t.current=s,n(),r.current=window.setTimeout(function(){t.current=null,r.current=void 0},100)}function l(){return t.current}return a.useEffect(function(){return n},[]),[o,l]}function Hs(){var e=a.useState(-1),t=oe(e,2),r=t[0],n=t[1],o=a.useState(-1),l=oe(o,2),s=l[0],i=l[1],d=a.useCallback(function(c,p){n(c),i(p)},[]);return[r,s,d]}var to=bl()?window:null;function zs(e,t){var r=ut(e)==="object"?e:{},n=r.offsetHeader,o=n===void 0?0:n,l=r.offsetSummary,s=l===void 0?0:l,i=r.offsetScroll,d=i===void 0?0:i,c=r.getContainer,p=c===void 0?function(){return to}:c,u=p()||to,v=!!e;return a.useMemo(function(){return{isSticky:v,stickyClassName:v?"".concat(t,"-sticky-holder"):"",offsetHeader:o,offsetSummary:s,offsetScroll:d,container:u}},[v,d,o,s,t,u])}function _s(e,t,r){var n=a.useMemo(function(){var o=t.length,l=function(c,p,u){for(var v=[],f=0,m=c;m!==p;m+=u)v.push(f),t[m].fixed&&(f+=e[m]||0);return v},s=l(0,o,1),i=l(o-1,-1,-1).reverse();return r==="rtl"?{left:i,right:s}:{left:s,right:i}},[e,t,r]);return n}function no(e){var t=e.className,r=e.children;return a.createElement("div",{className:t},r)}function ro(e){var t=mr(e),r=t.getBoundingClientRect(),n=document.documentElement;return{left:r.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:r.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}var js=function(t,r){var n,o,l=t.scrollBodyRef,s=t.onScroll,i=t.offsetScroll,d=t.container,c=t.direction,p=st(vt,"prefixCls"),u=((n=l.current)===null||n===void 0?void 0:n.scrollWidth)||0,v=((o=l.current)===null||o===void 0?void 0:o.clientWidth)||0,f=u&&v*(v/u),m=a.useRef(),g=Bs({scrollLeft:0,isHiddenScrollBar:!0}),h=oe(g,2),b=h[0],y=h[1],S=a.useRef({delta:0,x:0}),C=a.useState(!1),w=oe(C,2),R=w[0],$=w[1],k=a.useRef(null);a.useEffect(function(){return function(){Ve.cancel(k.current)}},[]);var x=function(){$(!1)},T=function(I){I.persist(),S.current.delta=I.pageX-b.scrollLeft,S.current.x=0,$(!0),I.preventDefault()},O=function(I){var N,K=I||((N=window)===null||N===void 0?void 0:N.event),z=K.buttons;if(!R||z===0){R&&$(!1);return}var B=S.current.x+I.pageX-S.current.x-S.current.delta,V=c==="rtl";B=Math.max(V?f-v:0,Math.min(V?0:v-f,B));var Z=!V||Math.abs(B)+Math.abs(f)<v;Z&&(s({scrollLeft:B/v*(u+2)}),S.current.x=I.pageX)},D=function(){Ve.cancel(k.current),k.current=Ve(function(){if(l.current){var I=ro(l.current).top,N=I+l.current.offsetHeight,K=d===window?document.documentElement.scrollTop+window.innerHeight:ro(d).top+d.clientHeight;N-Ar()<=K||I>=K-i?y(function(z){return j(j({},z),{},{isHiddenScrollBar:!0})}):y(function(z){return j(j({},z),{},{isHiddenScrollBar:!1})})}})},M=function(I){y(function(N){return j(j({},N),{},{scrollLeft:I/u*v||0})})};return a.useImperativeHandle(r,function(){return{setScrollLeft:M,checkScrollBarVisible:D}}),a.useEffect(function(){var E=Xr(document.body,"mouseup",x,!1),I=Xr(document.body,"mousemove",O,!1);return D(),function(){E.remove(),I.remove()}},[f,R]),a.useEffect(function(){if(l.current){for(var E=[],I=mr(l.current);I;)E.push(I),I=I.parentElement;return E.forEach(function(N){return N.addEventListener("scroll",D,!1)}),window.addEventListener("resize",D,!1),window.addEventListener("scroll",D,!1),d.addEventListener("scroll",D,!1),function(){E.forEach(function(N){return N.removeEventListener("scroll",D)}),window.removeEventListener("resize",D),window.removeEventListener("scroll",D),d.removeEventListener("scroll",D)}}},[d]),a.useEffect(function(){b.isHiddenScrollBar||y(function(E){var I=l.current;return I?j(j({},E),{},{scrollLeft:I.scrollLeft/I.scrollWidth*I.clientWidth}):E})},[b.isHiddenScrollBar]),u<=v||!f||b.isHiddenScrollBar?null:a.createElement("div",{style:{height:Ar(),width:v,bottom:i},className:"".concat(p,"-sticky-scroll")},a.createElement("div",{onMouseDown:T,ref:m,className:te("".concat(p,"-sticky-scroll-bar"),P({},"".concat(p,"-sticky-scroll-bar-active"),R)),style:{width:"".concat(f,"px"),transform:"translate3d(".concat(b.scrollLeft,"px, 0, 0)")}}))};const As=a.forwardRef(js);var Ca="rc-table",Fs=[],Ws={};function Vs(){return"No Data"}function Xs(e,t){var r=j({rowKey:"key",prefixCls:Ca,emptyText:Vs},e),n=r.prefixCls,o=r.className,l=r.rowClassName,s=r.style,i=r.data,d=r.rowKey,c=r.scroll,p=r.tableLayout,u=r.direction,v=r.title,f=r.footer,m=r.summary,g=r.caption,h=r.id,b=r.showHeader,y=r.components,S=r.emptyText,C=r.onRow,w=r.onHeaderRow,R=r.onScroll,$=r.internalHooks,k=r.transformColumns,x=r.internalRefs,T=r.tailor,O=r.getContainerWidth,D=r.sticky,M=r.rowHoverable,E=M===void 0?!0:M,I=i||Fs,N=!!I.length,K=$===hn,z=a.useCallback(function($e,Te){return vr(y,$e)||Te},[y]),B=a.useMemo(function(){return typeof d=="function"?d:function($e){var Te=$e&&$e[d];return Te}},[d]),V=z(["body"]),Z=Hs(),ne=oe(Z,3),A=ne[0],se=ne[1],fe=ne[2],be=Ds(r,I,B),re=oe(be,6),Q=re[0],ae=re[1],ve=re[2],X=re[3],F=re[4],H=re[5],U=c==null?void 0:c.x,le=a.useState(0),de=oe(le,2),ie=de[0],Ce=de[1],je=Ps(j(j(j({},r),Q),{},{expandable:!!Q.expandedRowRender,columnTitle:Q.columnTitle,expandedKeys:ve,getRowKey:B,onTriggerExpand:H,expandIcon:X,expandIconColumnIndex:Q.expandIconColumnIndex,direction:u,scrollWidth:K&&T&&typeof U=="number"?U:null,clientWidth:ie}),K?k:null),we=oe(je,4),W=we[0],q=we[1],ue=we[2],xe=we[3],he=ue??U,Oe=a.useMemo(function(){return{columns:W,flattenColumns:q}},[W,q]),Re=a.useRef(),ze=a.useRef(),me=a.useRef(),pe=a.useRef();a.useImperativeHandle(t,function(){return{nativeElement:Re.current,scrollTo:function(Te){var lt;if(me.current instanceof HTMLElement){var St=Te.index,it=Te.top,Ut=Te.key;if(Qi(it)){var At;(At=me.current)===null||At===void 0||At.scrollTo({top:it})}else{var Ft,ln=Ut??B(I[St]);(Ft=me.current.querySelector('[data-row-key="'.concat(ln,'"]')))===null||Ft===void 0||Ft.scrollIntoView()}}else(lt=me.current)!==null&&lt!==void 0&&lt.scrollTo&&me.current.scrollTo(Te)}}});var ee=a.useRef(),G=a.useState(!1),Pe=oe(G,2),Le=Pe[0],ce=Pe[1],Ae=a.useState(!1),Ie=oe(Ae,2),_e=Ie[0],Ge=Ie[1],et=a.useState(new Map),yt=oe(et,2),Be=yt[0],mt=yt[1],wt=On(q),Fe=wt.map(function($e){return Be.get($e)}),Xe=a.useMemo(function(){return Fe},[Fe.join("_")]),tt=_s(Xe,q,u),Ue=c&&Zn(c.y),Ze=c&&Zn(he)||!!Q.fixed,qe=Ze&&q.some(function($e){var Te=$e.fixed;return Te}),ft=a.useRef(),nt=zs(D,n),ct=nt.isSticky,zt=nt.offsetHeader,xt=nt.offsetSummary,Pt=nt.offsetScroll,_t=nt.stickyClassName,Se=nt.container,ye=a.useMemo(function(){return m==null?void 0:m(I)},[m,I]),He=(Ue||ct)&&a.isValidElement(ye)&&ye.type===Tn&&ye.props.fixed,We,Ye,at;Ue&&(Ye={overflowY:N?"scroll":"auto",maxHeight:c.y}),Ze&&(We={overflowX:"auto"},Ue||(Ye={overflowY:"hidden"}),at={width:he===!0?"auto":he,minWidth:"100%"});var dt=a.useCallback(function($e,Te){mt(function(lt){if(lt.get($e)!==Te){var St=new Map(lt);return St.set($e,Te),St}return lt})},[]),Je=Ls(),pt=oe(Je,2),jt=pt[0],_=pt[1];function L($e,Te){Te&&(typeof Te=="function"?Te($e):Te.scrollLeft!==$e&&(Te.scrollLeft=$e,Te.scrollLeft!==$e&&setTimeout(function(){Te.scrollLeft=$e},0)))}var J=ht(function($e){var Te=$e.currentTarget,lt=$e.scrollLeft,St=u==="rtl",it=typeof lt=="number"?lt:Te.scrollLeft,Ut=Te||Ws;if(!_()||_()===Ut){var At;jt(Ut),L(it,ze.current),L(it,me.current),L(it,ee.current),L(it,(At=ft.current)===null||At===void 0?void 0:At.setScrollLeft)}var Ft=Te||ze.current;if(Ft){var ln=K&&T&&typeof he=="number"?he:Ft.scrollWidth,Ln=Ft.clientWidth;if(ln===Ln){ce(!1),Ge(!1);return}St?(ce(-it<ln-Ln),Ge(-it>0)):(ce(it>0),Ge(it<ln-Ln))}}),ge=ht(function($e){J($e),R==null||R($e)}),Ne=function(){if(Ze&&me.current){var Te;J({currentTarget:mr(me.current),scrollLeft:(Te=me.current)===null||Te===void 0?void 0:Te.scrollLeft})}else ce(!1),Ge(!1)},Ct=function(Te){var lt,St=Te.width;(lt=ft.current)===null||lt===void 0||lt.checkScrollBarVisible();var it=Re.current?Re.current.offsetWidth:St;K&&O&&Re.current&&(it=O(Re.current,it)||it),it!==ie&&(Ne(),Ce(it))},rt=a.useRef(!1);a.useEffect(function(){rt.current&&Ne()},[Ze,i,W.length]),a.useEffect(function(){rt.current=!0},[]);var Nt=a.useState(0),Yt=oe(Nt,2),$t=Yt[0],Et=Yt[1],Fa=a.useState(!0),kr=oe(Fa,2),Or=kr[0],Wa=kr[1];ot(function(){(!T||!K)&&(me.current instanceof Element?Et(Fr(me.current).width):Et(Fr(pe.current).width)),Wa(Al("position","sticky"))},[]),a.useEffect(function(){K&&x&&(x.body.current=me.current)});var Va=a.useCallback(function($e){return a.createElement(a.Fragment,null,a.createElement(Qr,$e),He==="top"&&a.createElement(yn,$e,ye))},[He,ye]),Xa=a.useCallback(function($e){return a.createElement(yn,$e,ye)},[ye]),Tr=z(["table"],"table"),bn=a.useMemo(function(){return p||(qe?he==="max-content"?"auto":"fixed":Ue||ct||q.some(function($e){var Te=$e.ellipsis;return Te})?"fixed":"auto")},[Ue,qe,q,p,ct]),Pn,Dn={colWidths:Xe,columCount:q.length,stickyOffsets:tt,onHeaderRow:w,fixHeader:Ue,scroll:c},Mr=a.useMemo(function(){return N?null:typeof S=="function"?S():S},[N,S]),Pr=a.createElement(ys,{data:I,measureColumnWidth:Ue||Ze||ct}),Dr=a.createElement(Sa,{colWidths:q.map(function($e){var Te=$e.width;return Te}),columns:q}),Kr=g!=null?a.createElement("caption",{className:"".concat(n,"-caption")},g):void 0,qa=Cn(r,{data:!0}),Br=Cn(r,{aria:!0});if(Ue||ct){var Kn;typeof V=="function"?(Kn=V(I,{scrollbarSize:$t,ref:me,onScroll:J}),Dn.colWidths=q.map(function($e,Te){var lt=$e.width,St=Te===q.length-1?lt-$t:lt;return typeof St=="number"&&!Number.isNaN(St)?St:0})):Kn=a.createElement("div",{style:j(j({},We),Ye),onScroll:ge,ref:me,className:te("".concat(n,"-body"))},a.createElement(Tr,Ee({style:j(j({},at),{},{tableLayout:bn})},Br),Kr,Dr,Pr,!He&&ye&&a.createElement(yn,{stickyOffsets:tt,flattenColumns:q},ye)));var Lr=j(j(j({noData:!I.length,maxContentScroll:Ze&&he==="max-content"},Dn),Oe),{},{direction:u,stickyClassName:_t,onScroll:J});Pn=a.createElement(a.Fragment,null,b!==!1&&a.createElement(Jr,Ee({},Lr,{stickyTopOffset:zt,className:"".concat(n,"-header"),ref:ze}),Va),Kn,He&&He!=="top"&&a.createElement(Jr,Ee({},Lr,{stickyBottomOffset:xt,className:"".concat(n,"-summary"),ref:ee}),Xa),ct&&me.current&&me.current instanceof Element&&a.createElement(As,{ref:ft,offsetScroll:Pt,scrollBodyRef:me,onScroll:J,container:Se,direction:u}))}else Pn=a.createElement("div",{style:j(j({},We),Ye),className:te("".concat(n,"-content")),onScroll:J,ref:me},a.createElement(Tr,Ee({style:j(j({},at),{},{tableLayout:bn})},Br),Kr,Dr,b!==!1&&a.createElement(Qr,Ee({},Dn,Oe)),Pr,ye&&a.createElement(yn,{stickyOffsets:tt,flattenColumns:q},ye)));var Bn=a.createElement("div",Ee({className:te(n,o,P(P(P(P(P(P(P(P(P(P({},"".concat(n,"-rtl"),u==="rtl"),"".concat(n,"-ping-left"),Le),"".concat(n,"-ping-right"),_e),"".concat(n,"-layout-fixed"),p==="fixed"),"".concat(n,"-fixed-header"),Ue),"".concat(n,"-fixed-column"),qe),"".concat(n,"-fixed-column-gapped"),qe&&xe),"".concat(n,"-scroll-horizontal"),Ze),"".concat(n,"-has-fix-left"),q[0]&&q[0].fixed),"".concat(n,"-has-fix-right"),q[q.length-1]&&q[q.length-1].fixed==="right")),style:s,id:h,ref:Re},qa),v&&a.createElement(no,{className:"".concat(n,"-title")},v(I)),a.createElement("div",{ref:pe,className:"".concat(n,"-container")},Pn),f&&a.createElement(no,{className:"".concat(n,"-footer")},f(I)));Ze&&(Bn=a.createElement(qt,{onResize:Ct},Bn));var Hr=Ks(q,tt,u),Ya=a.useMemo(function(){return{scrollX:he,prefixCls:n,getComponent:z,scrollbarSize:$t,direction:u,fixedInfoList:Hr,isSticky:ct,supportSticky:Or,componentWidth:ie,fixHeader:Ue,fixColumn:qe,horizonScroll:Ze,tableLayout:bn,rowClassName:l,expandedRowClassName:Q.expandedRowClassName,expandIcon:X,expandableType:ae,expandRowByClick:Q.expandRowByClick,expandedRowRender:Q.expandedRowRender,expandedRowOffset:Q.expandedRowOffset,onTriggerExpand:H,expandIconColumnIndex:Q.expandIconColumnIndex,indentSize:Q.indentSize,allColumnsFixedLeft:q.every(function($e){return $e.fixed==="left"}),emptyNode:Mr,columns:W,flattenColumns:q,onColumnResize:dt,colWidths:Xe,hoverStartRow:A,hoverEndRow:se,onHover:fe,rowExpandable:Q.rowExpandable,onRow:C,getRowKey:B,expandedKeys:ve,childrenColumnName:F,rowHoverable:E}},[he,n,z,$t,u,Hr,ct,Or,ie,Ue,qe,Ze,bn,l,Q.expandedRowClassName,X,ae,Q.expandRowByClick,Q.expandedRowRender,Q.expandedRowOffset,H,Q.expandIconColumnIndex,Q.indentSize,Mr,W,q,dt,Xe,A,se,fe,Q.rowExpandable,C,B,ve,F,E]);return a.createElement(vt.Provider,{value:Ya},Bn)}var qs=a.forwardRef(Xs);function wa(e){return da(qs,e)}var on=wa();on.EXPAND_COLUMN=Ot;on.INTERNAL_HOOKS=hn;on.Column=ds;on.ColumnGroup=us;on.Summary=va;var $a=a.forwardRef(function(e,t){var r=e.height,n=e.offsetY,o=e.offsetX,l=e.children,s=e.prefixCls,i=e.onInnerResize,d=e.innerProps,c=e.rtl,p=e.extra,u={},v={display:"flex",flexDirection:"column"};return n!==void 0&&(u={height:r,position:"relative",overflow:"hidden"},v=j(j({},v),{},P(P(P(P(P({transform:"translateY(".concat(n,"px)")},c?"marginRight":"marginLeft",-o),"position","absolute"),"left",0),"right",0),"top",0))),a.createElement("div",{style:u},a.createElement(qt,{onResize:function(m){var g=m.offsetHeight;g&&i&&i()}},a.createElement("div",Ee({style:v,className:te(P({},"".concat(s,"-holder-inner"),s)),ref:t},d),l,p)))});$a.displayName="Filler";function Ys(e){var t=e.children,r=e.setRef,n=a.useCallback(function(o){r(o)},[]);return a.cloneElement(t,{ref:n})}function Us(e,t,r,n,o,l,s,i){var d=i.getKey;return e.slice(t,r+1).map(function(c,p){var u=t+p,v=s(c,u,{style:{width:n},offsetX:o}),f=d(c);return a.createElement(Ys,{key:f,setRef:function(g){return l(c,g)}},v)})}function Gs(e,t,r){var n=e.length,o=t.length,l,s;if(n===0&&o===0)return null;n<o?(l=e,s=t):(l=t,s=e);var i={__EMPTY_ITEM__:!0};function d(m){return m!==void 0?r(m):i}for(var c=null,p=Math.abs(n-o)!==1,u=0;u<s.length;u+=1){var v=d(l[u]),f=d(s[u]);if(v!==f){c=u,p=p||v!==d(s[u+1]);break}}return c===null?null:{index:c,multiple:p}}function Zs(e,t,r){var n=a.useState(e),o=oe(n,2),l=o[0],s=o[1],i=a.useState(null),d=oe(i,2),c=d[0],p=d[1];return a.useEffect(function(){var u=Gs(l||[],e||[],t);(u==null?void 0:u.index)!==void 0&&p(e[u.index]),s(e)},[e]),[c]}var oo=(typeof navigator>"u"?"undefined":ut(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const Ea=function(e,t,r,n){var o=a.useRef(!1),l=a.useRef(null);function s(){clearTimeout(l.current),o.current=!0,l.current=setTimeout(function(){o.current=!1},50)}var i=a.useRef({top:e,bottom:t,left:r,right:n});return i.current.top=e,i.current.bottom=t,i.current.left=r,i.current.right=n,function(d,c){var p=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=d?c<0&&i.current.left||c>0&&i.current.right:c<0&&i.current.top||c>0&&i.current.bottom;return p&&u?(clearTimeout(l.current),o.current=!1):(!u||o.current)&&s(),!o.current&&u}};function Js(e,t,r,n,o,l,s){var i=a.useRef(0),d=a.useRef(null),c=a.useRef(null),p=a.useRef(!1),u=Ea(t,r,n,o);function v(y,S){if(Ve.cancel(d.current),!u(!1,S)){var C=y;if(!C._virtualHandled)C._virtualHandled=!0;else return;i.current+=S,c.current=S,oo||C.preventDefault(),d.current=Ve(function(){var w=p.current?10:1;s(i.current*w,!1),i.current=0})}}function f(y,S){s(S,!0),oo||y.preventDefault()}var m=a.useRef(null),g=a.useRef(null);function h(y){if(e){Ve.cancel(g.current),g.current=Ve(function(){m.current=null},2);var S=y.deltaX,C=y.deltaY,w=y.shiftKey,R=S,$=C;(m.current==="sx"||!m.current&&w&&C&&!S)&&(R=C,$=0,m.current="sx");var k=Math.abs(R),x=Math.abs($);m.current===null&&(m.current=l&&k>x?"x":"y"),m.current==="y"?v(y,$):f(y,R)}}function b(y){e&&(p.current=y.detail===c.current)}return[h,b]}function Qs(e,t,r,n){var o=a.useMemo(function(){return[new Map,[]]},[e,r.id,n]),l=oe(o,2),s=l[0],i=l[1],d=function(p){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:p,v=s.get(p),f=s.get(u);if(v===void 0||f===void 0)for(var m=e.length,g=i.length;g<m;g+=1){var h,b=e[g],y=t(b);s.set(y,g);var S=(h=r.get(y))!==null&&h!==void 0?h:n;if(i[g]=(i[g-1]||0)+S,y===p&&(v=g),y===u&&(f=g),v!==void 0&&f!==void 0)break}return{top:i[v-1]||0,bottom:i[f]}};return d}var ec=function(){function e(){gr(this,e),P(this,"maps",void 0),P(this,"id",0),P(this,"diffRecords",new Map),this.maps=Object.create(null)}return pr(e,[{key:"set",value:function(r,n){this.diffRecords.set(r,this.maps[r]),this.maps[r]=n,this.id+=1}},{key:"get",value:function(r){return this.maps[r]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function ao(e){var t=parseFloat(e);return isNaN(t)?0:t}function tc(e,t,r){var n=a.useState(0),o=oe(n,2),l=o[0],s=o[1],i=a.useRef(new Map),d=a.useRef(new ec),c=a.useRef(0);function p(){c.current+=1}function u(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;p();var m=function(){var b=!1;i.current.forEach(function(y,S){if(y&&y.offsetParent){var C=y.offsetHeight,w=getComputedStyle(y),R=w.marginTop,$=w.marginBottom,k=ao(R),x=ao($),T=C+k+x;d.current.get(S)!==T&&(d.current.set(S,T),b=!0)}}),b&&s(function(y){return y+1})};if(f)m();else{c.current+=1;var g=c.current;Promise.resolve().then(function(){g===c.current&&m()})}}function v(f,m){var g=e(f);i.current.get(g),m?(i.current.set(g,m),u()):i.current.delete(g)}return a.useEffect(function(){return p},[]),[v,u,d.current,l]}var lo=14/15;function nc(e,t,r){var n=a.useRef(!1),o=a.useRef(0),l=a.useRef(0),s=a.useRef(null),i=a.useRef(null),d,c=function(f){if(n.current){var m=Math.ceil(f.touches[0].pageX),g=Math.ceil(f.touches[0].pageY),h=o.current-m,b=l.current-g,y=Math.abs(h)>Math.abs(b);y?o.current=m:l.current=g;var S=r(y,y?h:b,!1,f);S&&f.preventDefault(),clearInterval(i.current),S&&(i.current=setInterval(function(){y?h*=lo:b*=lo;var C=Math.floor(y?h:b);(!r(y,C,!0)||Math.abs(C)<=.1)&&clearInterval(i.current)},16))}},p=function(){n.current=!1,d()},u=function(f){d(),f.touches.length===1&&!n.current&&(n.current=!0,o.current=Math.ceil(f.touches[0].pageX),l.current=Math.ceil(f.touches[0].pageY),s.current=f.target,s.current.addEventListener("touchmove",c,{passive:!1}),s.current.addEventListener("touchend",p,{passive:!0}))};d=function(){s.current&&(s.current.removeEventListener("touchmove",c),s.current.removeEventListener("touchend",p))},ot(function(){return e&&t.current.addEventListener("touchstart",u,{passive:!0}),function(){var v;(v=t.current)===null||v===void 0||v.removeEventListener("touchstart",u),d(),clearInterval(i.current)}},[e])}function io(e){return Math.floor(Math.pow(e,.5))}function Qn(e,t){var r="touches"in e?e.touches[0]:e;return r[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}function rc(e,t,r){a.useEffect(function(){var n=t.current;if(e&&n){var o=!1,l,s,i=function(){Ve.cancel(l)},d=function v(){i(),l=Ve(function(){r(s),v()})},c=function(f){if(!(f.target.draggable||f.button!==0)){var m=f;m._virtualHandled||(m._virtualHandled=!0,o=!0)}},p=function(){o=!1,i()},u=function(f){if(o){var m=Qn(f,!1),g=n.getBoundingClientRect(),h=g.top,b=g.bottom;if(m<=h){var y=h-m;s=-io(y),d()}else if(m>=b){var S=m-b;s=io(S),d()}else i()}};return n.addEventListener("mousedown",c),n.ownerDocument.addEventListener("mouseup",p),n.ownerDocument.addEventListener("mousemove",u),function(){n.removeEventListener("mousedown",c),n.ownerDocument.removeEventListener("mouseup",p),n.ownerDocument.removeEventListener("mousemove",u),i()}}},[e])}var oc=10;function ac(e,t,r,n,o,l,s,i){var d=a.useRef(),c=a.useState(null),p=oe(c,2),u=p[0],v=p[1];return ot(function(){if(u&&u.times<oc){if(!e.current){v(function(B){return j({},B)});return}l();var f=u.targetAlign,m=u.originAlign,g=u.index,h=u.offset,b=e.current.clientHeight,y=!1,S=f,C=null;if(b){for(var w=f||m,R=0,$=0,k=0,x=Math.min(t.length-1,g),T=0;T<=x;T+=1){var O=o(t[T]);$=R;var D=r.get(O);k=$+(D===void 0?n:D),R=k}for(var M=w==="top"?h:b-h,E=x;E>=0;E-=1){var I=o(t[E]),N=r.get(I);if(N===void 0){y=!0;break}if(M-=N,M<=0)break}switch(w){case"top":C=$-h;break;case"bottom":C=k-b+h;break;default:{var K=e.current.scrollTop,z=K+b;$<K?S="top":k>z&&(S="bottom")}}C!==null&&s(C),C!==u.lastTop&&(y=!0)}y&&v(j(j({},u),{},{times:u.times+1,targetAlign:S,lastTop:C}))}},[u,e.current]),function(f){if(f==null){i();return}if(Ve.cancel(d.current),typeof f=="number")s(f);else if(f&&ut(f)==="object"){var m,g=f.align;"index"in f?m=f.index:m=t.findIndex(function(y){return o(y)===f.key});var h=f.offset,b=h===void 0?0:h;v({times:0,index:m,offset:b,originAlign:g})}}}var so=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.rtl,o=e.scrollOffset,l=e.scrollRange,s=e.onStartMove,i=e.onStopMove,d=e.onScroll,c=e.horizontal,p=e.spinSize,u=e.containerSize,v=e.style,f=e.thumbStyle,m=e.showScrollBar,g=a.useState(!1),h=oe(g,2),b=h[0],y=h[1],S=a.useState(null),C=oe(S,2),w=C[0],R=C[1],$=a.useState(null),k=oe($,2),x=k[0],T=k[1],O=!n,D=a.useRef(),M=a.useRef(),E=a.useState(m),I=oe(E,2),N=I[0],K=I[1],z=a.useRef(),B=function(){m===!0||m===!1||(clearTimeout(z.current),K(!0),z.current=setTimeout(function(){K(!1)},3e3))},V=l-u||0,Z=u-p||0,ne=a.useMemo(function(){if(o===0||V===0)return 0;var X=o/V;return X*Z},[o,V,Z]),A=function(F){F.stopPropagation(),F.preventDefault()},se=a.useRef({top:ne,dragging:b,pageY:w,startTop:x});se.current={top:ne,dragging:b,pageY:w,startTop:x};var fe=function(F){y(!0),R(Qn(F,c)),T(se.current.top),s(),F.stopPropagation(),F.preventDefault()};a.useEffect(function(){var X=function(le){le.preventDefault()},F=D.current,H=M.current;return F.addEventListener("touchstart",X,{passive:!1}),H.addEventListener("touchstart",fe,{passive:!1}),function(){F.removeEventListener("touchstart",X),H.removeEventListener("touchstart",fe)}},[]);var be=a.useRef();be.current=V;var re=a.useRef();re.current=Z,a.useEffect(function(){if(b){var X,F=function(le){var de=se.current,ie=de.dragging,Ce=de.pageY,je=de.startTop;Ve.cancel(X);var we=D.current.getBoundingClientRect(),W=u/(c?we.width:we.height);if(ie){var q=(Qn(le,c)-Ce)*W,ue=je;!O&&c?ue-=q:ue+=q;var xe=be.current,he=re.current,Oe=he?ue/he:0,Re=Math.ceil(Oe*xe);Re=Math.max(Re,0),Re=Math.min(Re,xe),X=Ve(function(){d(Re,c)})}},H=function(){y(!1),i()};return window.addEventListener("mousemove",F,{passive:!0}),window.addEventListener("touchmove",F,{passive:!0}),window.addEventListener("mouseup",H,{passive:!0}),window.addEventListener("touchend",H,{passive:!0}),function(){window.removeEventListener("mousemove",F),window.removeEventListener("touchmove",F),window.removeEventListener("mouseup",H),window.removeEventListener("touchend",H),Ve.cancel(X)}}},[b]),a.useEffect(function(){return B(),function(){clearTimeout(z.current)}},[o]),a.useImperativeHandle(t,function(){return{delayHidden:B}});var Q="".concat(r,"-scrollbar"),ae={position:"absolute",visibility:N?null:"hidden"},ve={position:"absolute",borderRadius:99,background:"var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",cursor:"pointer",userSelect:"none"};return c?(Object.assign(ae,{height:8,left:0,right:0,bottom:0}),Object.assign(ve,P({height:"100%",width:p},O?"left":"right",ne))):(Object.assign(ae,P({width:8,top:0,bottom:0},O?"right":"left",0)),Object.assign(ve,{width:"100%",height:p,top:ne})),a.createElement("div",{ref:D,className:te(Q,P(P(P({},"".concat(Q,"-horizontal"),c),"".concat(Q,"-vertical"),!c),"".concat(Q,"-visible"),N)),style:j(j({},ae),v),onMouseDown:A,onMouseMove:B},a.createElement("div",{ref:M,className:te("".concat(Q,"-thumb"),P({},"".concat(Q,"-thumb-moving"),b)),style:j(j({},ve),f),onMouseDown:fe}))}),lc=20;function co(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=e/t*e;return isNaN(r)&&(r=0),r=Math.max(r,lc),Math.floor(r)}var ic=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],sc=[],cc={overflowY:"auto",overflowAnchor:"none"};function dc(e,t){var r=e.prefixCls,n=r===void 0?"rc-virtual-list":r,o=e.className,l=e.height,s=e.itemHeight,i=e.fullHeight,d=i===void 0?!0:i,c=e.style,p=e.data,u=e.children,v=e.itemKey,f=e.virtual,m=e.direction,g=e.scrollWidth,h=e.component,b=h===void 0?"div":h,y=e.onScroll,S=e.onVirtualScroll,C=e.onVisibleChange,w=e.innerProps,R=e.extraRender,$=e.styles,k=e.showScrollBar,x=k===void 0?"optional":k,T=bt(e,ic),O=a.useCallback(function(_){return typeof v=="function"?v(_):_==null?void 0:_[v]},[v]),D=tc(O),M=oe(D,4),E=M[0],I=M[1],N=M[2],K=M[3],z=!!(f!==!1&&l&&s),B=a.useMemo(function(){return Object.values(N.maps).reduce(function(_,L){return _+L},0)},[N.id,N.maps]),V=z&&p&&(Math.max(s*p.length,B)>l||!!g),Z=m==="rtl",ne=te(n,P({},"".concat(n,"-rtl"),Z),o),A=p||sc,se=a.useRef(),fe=a.useRef(),be=a.useRef(),re=a.useState(0),Q=oe(re,2),ae=Q[0],ve=Q[1],X=a.useState(0),F=oe(X,2),H=F[0],U=F[1],le=a.useState(!1),de=oe(le,2),ie=de[0],Ce=de[1],je=function(){Ce(!0)},we=function(){Ce(!1)},W={getKey:O};function q(_){ve(function(L){var J;typeof _=="function"?J=_(L):J=_;var ge=wt(J);return se.current.scrollTop=ge,ge})}var ue=a.useRef({start:0,end:A.length}),xe=a.useRef(),he=Zs(A,O),Oe=oe(he,1),Re=Oe[0];xe.current=Re;var ze=a.useMemo(function(){if(!z)return{scrollHeight:void 0,start:0,end:A.length-1,offset:void 0};if(!V){var _;return{scrollHeight:((_=fe.current)===null||_===void 0?void 0:_.offsetHeight)||0,start:0,end:A.length-1,offset:void 0}}for(var L=0,J,ge,Ne,Ct=A.length,rt=0;rt<Ct;rt+=1){var Nt=A[rt],Yt=O(Nt),$t=N.get(Yt),Et=L+($t===void 0?s:$t);Et>=ae&&J===void 0&&(J=rt,ge=L),Et>ae+l&&Ne===void 0&&(Ne=rt),L=Et}return J===void 0&&(J=0,ge=0,Ne=Math.ceil(l/s)),Ne===void 0&&(Ne=A.length-1),Ne=Math.min(Ne+1,A.length-1),{scrollHeight:L,start:J,end:Ne,offset:ge}},[V,z,ae,A,K,l]),me=ze.scrollHeight,pe=ze.start,ee=ze.end,G=ze.offset;ue.current.start=pe,ue.current.end=ee,a.useLayoutEffect(function(){var _=N.getRecord();if(_.size===1){var L=Array.from(_.keys())[0],J=_.get(L),ge=A[pe];if(ge&&J===void 0){var Ne=O(ge);if(Ne===L){var Ct=N.get(L),rt=Ct-s;q(function(Nt){return Nt+rt})}}}N.resetRecord()},[me]);var Pe=a.useState({width:0,height:l}),Le=oe(Pe,2),ce=Le[0],Ae=Le[1],Ie=function(L){Ae({width:L.offsetWidth,height:L.offsetHeight})},_e=a.useRef(),Ge=a.useRef(),et=a.useMemo(function(){return co(ce.width,g)},[ce.width,g]),yt=a.useMemo(function(){return co(ce.height,me)},[ce.height,me]),Be=me-l,mt=a.useRef(Be);mt.current=Be;function wt(_){var L=_;return Number.isNaN(mt.current)||(L=Math.min(L,mt.current)),L=Math.max(L,0),L}var Fe=ae<=0,Xe=ae>=Be,tt=H<=0,Ue=H>=g,Ze=Ea(Fe,Xe,tt,Ue),qe=function(){return{x:Z?-H:H,y:ae}},ft=a.useRef(qe()),nt=ht(function(_){if(S){var L=j(j({},qe()),_);(ft.current.x!==L.x||ft.current.y!==L.y)&&(S(L),ft.current=L)}});function ct(_,L){var J=_;L?(fn.flushSync(function(){U(J)}),nt()):q(J)}function zt(_){var L=_.currentTarget.scrollTop;L!==ae&&q(L),y==null||y(_),nt()}var xt=function(L){var J=L,ge=g?g-ce.width:0;return J=Math.max(J,0),J=Math.min(J,ge),J},Pt=ht(function(_,L){L?(fn.flushSync(function(){U(function(J){var ge=J+(Z?-_:_);return xt(ge)})}),nt()):q(function(J){var ge=J+_;return ge})}),_t=Js(z,Fe,Xe,tt,Ue,!!g,Pt),Se=oe(_t,2),ye=Se[0],He=Se[1];nc(z,se,function(_,L,J,ge){var Ne=ge;return Ze(_,L,J)?!1:!Ne||!Ne._virtualHandled?(Ne&&(Ne._virtualHandled=!0),ye({preventDefault:function(){},deltaX:_?L:0,deltaY:_?0:L}),!0):!1}),rc(V,se,function(_){q(function(L){return L+_})}),ot(function(){function _(J){var ge=Fe&&J.detail<0,Ne=Xe&&J.detail>0;z&&!ge&&!Ne&&J.preventDefault()}var L=se.current;return L.addEventListener("wheel",ye,{passive:!1}),L.addEventListener("DOMMouseScroll",He,{passive:!0}),L.addEventListener("MozMousePixelScroll",_,{passive:!1}),function(){L.removeEventListener("wheel",ye),L.removeEventListener("DOMMouseScroll",He),L.removeEventListener("MozMousePixelScroll",_)}},[z,Fe,Xe]),ot(function(){if(g){var _=xt(H);U(_),nt({x:_})}},[ce.width,g]);var We=function(){var L,J;(L=_e.current)===null||L===void 0||L.delayHidden(),(J=Ge.current)===null||J===void 0||J.delayHidden()},Ye=ac(se,A,N,s,O,function(){return I(!0)},q,We);a.useImperativeHandle(t,function(){return{nativeElement:be.current,getScrollInfo:qe,scrollTo:function(L){function J(ge){return ge&&ut(ge)==="object"&&("left"in ge||"top"in ge)}J(L)?(L.left!==void 0&&U(xt(L.left)),Ye(L.top)):Ye(L)}}}),ot(function(){if(C){var _=A.slice(pe,ee+1);C(_,A)}},[pe,ee,A]);var at=Qs(A,O,N,s),dt=R==null?void 0:R({start:pe,end:ee,virtual:V,offsetX:H,offsetY:G,rtl:Z,getSize:at}),Je=Us(A,pe,ee,g,H,E,u,W),pt=null;l&&(pt=j(P({},d?"height":"maxHeight",l),cc),z&&(pt.overflowY="hidden",g&&(pt.overflowX="hidden"),ie&&(pt.pointerEvents="none")));var jt={};return Z&&(jt.dir="rtl"),a.createElement("div",Ee({ref:be,style:j(j({},c),{},{position:"relative"}),className:ne},jt,T),a.createElement(qt,{onResize:Ie},a.createElement(b,{className:"".concat(n,"-holder"),style:pt,ref:se,onScroll:zt,onMouseEnter:We},a.createElement($a,{prefixCls:n,height:me,offsetX:H,offsetY:G,scrollWidth:g,onInnerResize:I,ref:fe,innerProps:w,rtl:Z,extra:dt},Je))),V&&me>l&&a.createElement(so,{ref:_e,prefixCls:n,scrollOffset:ae,scrollRange:me,rtl:Z,onScroll:ct,onStartMove:je,onStopMove:we,spinSize:yt,containerSize:ce.height,style:$==null?void 0:$.verticalScrollBar,thumbStyle:$==null?void 0:$.verticalScrollBarThumb,showScrollBar:x}),V&&g>ce.width&&a.createElement(so,{ref:Ge,prefixCls:n,scrollOffset:H,scrollRange:g,rtl:Z,onScroll:ct,onStartMove:je,onStopMove:we,spinSize:et,containerSize:ce.width,horizontal:!0,style:$==null?void 0:$.horizontalScrollBar,thumbStyle:$==null?void 0:$.horizontalScrollBarThumb,showScrollBar:x}))}var Ra=a.forwardRef(dc);Ra.displayName="List";var wr=yr(null),Ia=yr(null);function uc(e,t,r){var n=t||1;return r[e+n]-(r[e]||0)}function fc(e){var t=e.rowInfo,r=e.column,n=e.colIndex,o=e.indent,l=e.index,s=e.component,i=e.renderIndex,d=e.record,c=e.style,p=e.className,u=e.inverse,v=e.getHeight,f=r.render,m=r.dataIndex,g=r.className,h=r.width,b=st(Ia,["columnsOffset"]),y=b.columnsOffset,S=ya(t,r,n,o,l),C=S.key,w=S.fixedInfo,R=S.appendCellNode,$=S.additionalCellProps,k=$.style,x=$.colSpan,T=x===void 0?1:x,O=$.rowSpan,D=O===void 0?1:O,M=n-1,E=uc(M,T,y),I=T>1?h-E:0,N=j(j(j({},k),c),{},{flex:"0 0 ".concat(E,"px"),width:"".concat(E,"px"),marginRight:I,pointerEvents:"auto"}),K=a.useMemo(function(){return u?D<=1:T===0||D===0||D>1},[D,T,u]);K?N.visibility="hidden":u&&(N.height=v==null?void 0:v(D));var z=K?function(){return null}:f,B={};return(D===0||T===0)&&(B.rowSpan=1,B.colSpan=1),a.createElement(rn,Ee({className:te(g,p),ellipsis:r.ellipsis,align:r.align,scope:r.rowScope,component:s,prefixCls:t.prefixCls,key:C,record:d,index:l,renderIndex:i,dataIndex:m,render:z,shouldCellUpdate:r.shouldCellUpdate},w,{appendNode:R,additionalProps:j(j({},$),{},{style:N},B)}))}var vc=["data","index","className","rowKey","style","extra","getHeight"],mc=a.forwardRef(function(e,t){var r=e.data,n=e.index,o=e.className,l=e.rowKey,s=e.style,i=e.extra,d=e.getHeight,c=bt(e,vc),p=r.record,u=r.indent,v=r.index,f=st(vt,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),m=f.scrollX,g=f.flattenColumns,h=f.prefixCls,b=f.fixColumn,y=f.componentWidth,S=st(wr,["getComponent"]),C=S.getComponent,w=ga(p,l,n,u),R=C(["body","row"],"div"),$=C(["body","cell"],"div"),k=w.rowSupportExpand,x=w.expanded,T=w.rowProps,O=w.expandedRowRender,D=w.expandedRowClassName,M;if(k&&x){var E=O(p,n,u+1,x),I=ba(D,p,n,u),N={};b&&(N={style:P({},"--virtual-width","".concat(y,"px"))});var K="".concat(h,"-expanded-row-cell");M=a.createElement(R,{className:te("".concat(h,"-expanded-row"),"".concat(h,"-expanded-row-level-").concat(u+1),I)},a.createElement(rn,{component:$,prefixCls:h,className:te(K,P({},"".concat(K,"-fixed"),b)),additionalProps:N},E))}var z=j(j({},s),{},{width:m});i&&(z.position="absolute",z.pointerEvents="none");var B=a.createElement(R,Ee({},T,c,{"data-row-key":l,ref:k?null:t,className:te(o,"".concat(h,"-row"),T==null?void 0:T.className,P({},"".concat(h,"-row-extra"),i)),style:j(j({},z),T==null?void 0:T.style)}),g.map(function(V,Z){return a.createElement(fc,{key:Z,component:$,rowInfo:w,column:V,colIndex:Z,indent:u,index:n,renderIndex:v,record:p,inverse:i,getHeight:d})}));return k?a.createElement("div",{ref:t},B,M):B}),uo=nn(mc),pc=a.forwardRef(function(e,t){var r=e.data,n=e.onScroll,o=st(vt,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","scrollX","direction"]),l=o.flattenColumns,s=o.onColumnResize,i=o.getRowKey,d=o.expandedKeys,c=o.prefixCls,p=o.childrenColumnName,u=o.scrollX,v=o.direction,f=st(wr),m=f.sticky,g=f.scrollY,h=f.listItemHeight,b=f.getComponent,y=f.onScroll,S=a.useRef(),C=pa(r,p,d,i),w=a.useMemo(function(){var M=0;return l.map(function(E){var I=E.width,N=E.key;return M+=I,[N,I,M]})},[l]),R=a.useMemo(function(){return w.map(function(M){return M[2]})},[w]);a.useEffect(function(){w.forEach(function(M){var E=oe(M,2),I=E[0],N=E[1];s(I,N)})},[w]),a.useImperativeHandle(t,function(){var M,E={scrollTo:function(N){var K;(K=S.current)===null||K===void 0||K.scrollTo(N)},nativeElement:(M=S.current)===null||M===void 0?void 0:M.nativeElement};return Object.defineProperty(E,"scrollLeft",{get:function(){var N;return((N=S.current)===null||N===void 0?void 0:N.getScrollInfo().x)||0},set:function(N){var K;(K=S.current)===null||K===void 0||K.scrollTo({left:N})}}),E});var $=function(E,I){var N,K=(N=C[I])===null||N===void 0?void 0:N.record,z=E.onCell;if(z){var B,V=z(K,I);return(B=V==null?void 0:V.rowSpan)!==null&&B!==void 0?B:1}return 1},k=function(E){var I=E.start,N=E.end,K=E.getSize,z=E.offsetY;if(N<0)return null;for(var B=l.filter(function(X){return $(X,I)===0}),V=I,Z=function(F){if(B=B.filter(function(H){return $(H,F)===0}),!B.length)return V=F,1},ne=I;ne>=0&&!Z(ne);ne-=1);for(var A=l.filter(function(X){return $(X,N)!==1}),se=N,fe=function(F){if(A=A.filter(function(H){return $(H,F)!==1}),!A.length)return se=Math.max(F-1,N),1},be=N;be<C.length&&!fe(be);be+=1);for(var re=[],Q=function(F){var H=C[F];if(!H)return 1;l.some(function(U){return $(U,F)>1})&&re.push(F)},ae=V;ae<=se;ae+=1)Q(ae);var ve=re.map(function(X){var F=C[X],H=i(F.record,X),U=function(ie){var Ce=X+ie-1,je=i(C[Ce].record,Ce),we=K(H,je);return we.bottom-we.top},le=K(H);return a.createElement(uo,{key:X,data:F,rowKey:H,index:X,style:{top:-z+le.top},extra:!0,getHeight:U})});return ve},x=a.useMemo(function(){return{columnsOffset:R}},[R]),T="".concat(c,"-tbody"),O=b(["body","wrapper"]),D={};return m&&(D.position="sticky",D.bottom=0,ut(m)==="object"&&m.offsetScroll&&(D.bottom=m.offsetScroll)),a.createElement(Ia.Provider,{value:x},a.createElement(Ra,{fullHeight:!1,ref:S,prefixCls:"".concat(T,"-virtual"),styles:{horizontalScrollBar:D},className:T,height:g,itemHeight:h||24,data:C,itemKey:function(E){return i(E.record)},component:O,scrollWidth:u,direction:v,onVirtualScroll:function(E){var I,N=E.x;n({currentTarget:(I=S.current)===null||I===void 0?void 0:I.nativeElement,scrollLeft:N})},onScroll:y,extraRender:k},function(M,E,I){var N=i(M.record,E);return a.createElement(uo,{data:M,rowKey:N,index:E,style:I.style})}))}),gc=nn(pc),hc=function(t,r){var n=r.ref,o=r.onScroll;return a.createElement(gc,{ref:n,data:t,onScroll:o})};function bc(e,t){var r=e.data,n=e.columns,o=e.scroll,l=e.sticky,s=e.prefixCls,i=s===void 0?Ca:s,d=e.className,c=e.listItemHeight,p=e.components,u=e.onScroll,v=o||{},f=v.x,m=v.y;typeof f!="number"&&(f=1),typeof m!="number"&&(m=500);var g=ht(function(y,S){return vr(p,y)||S}),h=ht(u),b=a.useMemo(function(){return{sticky:l,scrollY:m,listItemHeight:c,getComponent:g,onScroll:h}},[l,m,c,g,h]);return a.createElement(wr.Provider,{value:b},a.createElement(on,Ee({},e,{className:te(d,"".concat(i,"-virtual")),scroll:j(j({},o),{},{x:f}),components:j(j({},p),{},{body:r!=null&&r.length?hc:void 0}),columns:n,internalHooks:hn,tailor:!0,ref:t})))}var yc=a.forwardRef(bc);function Na(e){return da(yc,e)}Na();const Sc=e=>null,xc=e=>null;var $r=a.createContext(null),Cc=a.createContext({}),wc=function(t){for(var r=t.prefixCls,n=t.level,o=t.isStart,l=t.isEnd,s="".concat(r,"-indent-unit"),i=[],d=0;d<n;d+=1)i.push(a.createElement("span",{key:d,className:te(s,P(P({},"".concat(s,"-start"),o[d]),"".concat(s,"-end"),l[d]))}));return a.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},i)};const $c=a.memo(wc);var Ec=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],fo="open",vo="close",Rc="---",mn=function(t){var r,n,o,l=t.eventKey,s=t.className,i=t.style,d=t.dragOver,c=t.dragOverGapTop,p=t.dragOverGapBottom,u=t.isLeaf,v=t.isStart,f=t.isEnd,m=t.expanded,g=t.selected,h=t.checked,b=t.halfChecked,y=t.loading,S=t.domRef,C=t.active,w=t.data,R=t.onMouseMove,$=t.selectable,k=bt(t,Ec),x=De.useContext($r),T=De.useContext(Cc),O=De.useRef(null),D=De.useState(!1),M=oe(D,2),E=M[0],I=M[1],N=!!(x.disabled||t.disabled||(r=T.nodeDisabled)!==null&&r!==void 0&&r.call(T,w)),K=De.useMemo(function(){return!x.checkable||t.checkable===!1?!1:x.checkable},[x.checkable,t.checkable]),z=function(G){N||x.onNodeSelect(G,Qe(t))},B=function(G){N||!K||t.disableCheckbox||x.onNodeCheck(G,Qe(t),!h)},V=De.useMemo(function(){return typeof $=="boolean"?$:x.selectable},[$,x.selectable]),Z=function(G){x.onNodeClick(G,Qe(t)),V?z(G):B(G)},ne=function(G){x.onNodeDoubleClick(G,Qe(t))},A=function(G){x.onNodeMouseEnter(G,Qe(t))},se=function(G){x.onNodeMouseLeave(G,Qe(t))},fe=function(G){x.onNodeContextMenu(G,Qe(t))},be=De.useMemo(function(){return!!(x.draggable&&(!x.draggable.nodeDraggable||x.draggable.nodeDraggable(w)))},[x.draggable,w]),re=function(G){G.stopPropagation(),I(!0),x.onNodeDragStart(G,t);try{G.dataTransfer.setData("text/plain","")}catch{}},Q=function(G){G.preventDefault(),G.stopPropagation(),x.onNodeDragEnter(G,t)},ae=function(G){G.preventDefault(),G.stopPropagation(),x.onNodeDragOver(G,t)},ve=function(G){G.stopPropagation(),x.onNodeDragLeave(G,t)},X=function(G){G.stopPropagation(),I(!1),x.onNodeDragEnd(G,t)},F=function(G){G.preventDefault(),G.stopPropagation(),I(!1),x.onNodeDrop(G,t)},H=function(G){y||x.onNodeExpand(G,Qe(t))},U=De.useMemo(function(){var ee=gt(x.keyEntities,l)||{},G=ee.children;return!!(G||[]).length},[x.keyEntities,l]),le=De.useMemo(function(){return u===!1?!1:u||!x.loadData&&!U||x.loadData&&t.loaded&&!U},[u,x.loadData,U,t.loaded]);De.useEffect(function(){y||typeof x.loadData=="function"&&m&&!le&&!t.loaded&&x.onNodeLoad(Qe(t))},[y,x.loadData,x.onNodeLoad,m,le,t]);var de=De.useMemo(function(){var ee;return(ee=x.draggable)!==null&&ee!==void 0&&ee.icon?De.createElement("span",{className:"".concat(x.prefixCls,"-draggable-icon")},x.draggable.icon):null},[x.draggable]),ie=function(G){var Pe=t.switcherIcon||x.switcherIcon;return typeof Pe=="function"?Pe(j(j({},t),{},{isLeaf:G})):Pe},Ce=function(){if(le){var G=ie(!0);return G!==!1?De.createElement("span",{className:te("".concat(x.prefixCls,"-switcher"),"".concat(x.prefixCls,"-switcher-noop"))},G):null}var Pe=ie(!1);return Pe!==!1?De.createElement("span",{onClick:H,className:te("".concat(x.prefixCls,"-switcher"),"".concat(x.prefixCls,"-switcher_").concat(m?fo:vo))},Pe):null},je=De.useMemo(function(){if(!K)return null;var ee=typeof K!="boolean"?K:null;return De.createElement("span",{className:te("".concat(x.prefixCls,"-checkbox"),P(P(P({},"".concat(x.prefixCls,"-checkbox-checked"),h),"".concat(x.prefixCls,"-checkbox-indeterminate"),!h&&b),"".concat(x.prefixCls,"-checkbox-disabled"),N||t.disableCheckbox)),onClick:B,role:"checkbox","aria-checked":b?"mixed":h,"aria-disabled":N||t.disableCheckbox,"aria-label":"Select ".concat(typeof t.title=="string"?t.title:"tree node")},ee)},[K,h,b,N,t.disableCheckbox,t.title]),we=De.useMemo(function(){return le?null:m?fo:vo},[le,m]),W=De.useMemo(function(){return De.createElement("span",{className:te("".concat(x.prefixCls,"-iconEle"),"".concat(x.prefixCls,"-icon__").concat(we||"docu"),P({},"".concat(x.prefixCls,"-icon_loading"),y))})},[x.prefixCls,we,y]),q=De.useMemo(function(){var ee=!!x.draggable,G=!t.disabled&&ee&&x.dragOverNodeKey===l;return G?x.dropIndicatorRender({dropPosition:x.dropPosition,dropLevelOffset:x.dropLevelOffset,indent:x.indent,prefixCls:x.prefixCls,direction:x.direction}):null},[x.dropPosition,x.dropLevelOffset,x.indent,x.prefixCls,x.direction,x.draggable,x.dragOverNodeKey,x.dropIndicatorRender]),ue=De.useMemo(function(){var ee=t.title,G=ee===void 0?Rc:ee,Pe="".concat(x.prefixCls,"-node-content-wrapper"),Le;if(x.showIcon){var ce=t.icon||x.icon;Le=ce?De.createElement("span",{className:te("".concat(x.prefixCls,"-iconEle"),"".concat(x.prefixCls,"-icon__customize"))},typeof ce=="function"?ce(t):ce):W}else x.loadData&&y&&(Le=W);var Ae;return typeof G=="function"?Ae=G(w):x.titleRender?Ae=x.titleRender(w):Ae=G,De.createElement("span",{ref:O,title:typeof G=="string"?G:"",className:te(Pe,"".concat(Pe,"-").concat(we||"normal"),P({},"".concat(x.prefixCls,"-node-selected"),!N&&(g||E))),onMouseEnter:A,onMouseLeave:se,onContextMenu:fe,onClick:Z,onDoubleClick:ne},Le,De.createElement("span",{className:"".concat(x.prefixCls,"-title")},Ae),q)},[x.prefixCls,x.showIcon,t,x.icon,W,x.titleRender,w,we,A,se,fe,Z,ne]),xe=Cn(k,{aria:!0,data:!0}),he=gt(x.keyEntities,l)||{},Oe=he.level,Re=f[f.length-1],ze=!N&&be,me=x.draggingNodeKey===l,pe=$!==void 0?{"aria-selected":!!$}:void 0;return De.createElement("div",Ee({ref:S,role:"treeitem","aria-expanded":u?void 0:m,className:te(s,"".concat(x.prefixCls,"-treenode"),(o={},P(P(P(P(P(P(P(P(P(P(o,"".concat(x.prefixCls,"-treenode-disabled"),N),"".concat(x.prefixCls,"-treenode-switcher-").concat(m?"open":"close"),!u),"".concat(x.prefixCls,"-treenode-checkbox-checked"),h),"".concat(x.prefixCls,"-treenode-checkbox-indeterminate"),b),"".concat(x.prefixCls,"-treenode-selected"),g),"".concat(x.prefixCls,"-treenode-loading"),y),"".concat(x.prefixCls,"-treenode-active"),C),"".concat(x.prefixCls,"-treenode-leaf-last"),Re),"".concat(x.prefixCls,"-treenode-draggable"),be),"dragging",me),P(P(P(P(P(P(P(o,"drop-target",x.dropTargetKey===l),"drop-container",x.dropContainerKey===l),"drag-over",!N&&d),"drag-over-gap-top",!N&&c),"drag-over-gap-bottom",!N&&p),"filter-node",(n=x.filterTreeNode)===null||n===void 0?void 0:n.call(x,Qe(t))),"".concat(x.prefixCls,"-treenode-leaf"),le))),style:i,draggable:ze,onDragStart:ze?re:void 0,onDragEnter:be?Q:void 0,onDragOver:be?ae:void 0,onDragLeave:be?ve:void 0,onDrop:be?F:void 0,onDragEnd:be?X:void 0,onMouseMove:R},pe,xe),De.createElement($c,{prefixCls:x.prefixCls,level:Oe,isStart:v,isEnd:f}),de,Ce(),je,ue)};mn.isTreeNode=1;function Rt(e,t){if(!e)return[];var r=e.slice(),n=r.indexOf(t);return n>=0&&r.splice(n,1),r}function kt(e,t){var r=(e||[]).slice();return r.indexOf(t)===-1&&r.push(t),r}function Er(e){return e.split("-")}function Ic(e,t){var r=[],n=gt(t,e);function o(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];l.forEach(function(s){var i=s.key,d=s.children;r.push(i),o(d)})}return o(n.children),r}function Nc(e){if(e.parent){var t=Er(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function kc(e){var t=Er(e.pos);return Number(t[t.length-1])===0}function mo(e,t,r,n,o,l,s,i,d,c){var p,u=e.clientX,v=e.clientY,f=e.target.getBoundingClientRect(),m=f.top,g=f.height,h=(c==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-u),b=(h-12)/n,y=d.filter(function(N){var K;return(K=i[N])===null||K===void 0||(K=K.children)===null||K===void 0?void 0:K.length}),S=gt(i,r.eventKey);if(v<m+g/2){var C=s.findIndex(function(N){return N.key===S.key}),w=C<=0?0:C-1,R=s[w].key;S=gt(i,R)}var $=S.key,k=S,x=S.key,T=0,O=0;if(!y.includes($))for(var D=0;D<b&&Nc(S);D+=1)S=S.parent,O+=1;var M=t.data,E=S.node,I=!0;return kc(S)&&S.level===0&&v<m+g/2&&l({dragNode:M,dropNode:E,dropPosition:-1})&&S.key===r.eventKey?T=-1:(k.children||[]).length&&y.includes(x)?l({dragNode:M,dropNode:E,dropPosition:0})?T=0:I=!1:O===0?b>-1.5?l({dragNode:M,dropNode:E,dropPosition:1})?T=1:I=!1:l({dragNode:M,dropNode:E,dropPosition:0})?T=0:l({dragNode:M,dropNode:E,dropPosition:1})?T=1:I=!1:l({dragNode:M,dropNode:E,dropPosition:1})?T=1:I=!1,{dropPosition:T,dropLevelOffset:O,dropTargetKey:S.key,dropTargetPos:S.pos,dragOverNodeKey:x,dropContainerKey:T===0?null:((p=S.parent)===null||p===void 0?void 0:p.key)||null,dropAllowed:I}}function po(e,t){if(e){var r=t.multiple;return r?e.slice():e.length?[e[0]]:e}}function jn(e){if(!e)return null;var t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(ut(e)==="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return Bt(!1,"`checkedKeys` is not an array or an object"),null;return t}function er(e,t){var r=new Set;function n(o){if(!r.has(o)){var l=gt(t,o);if(l){r.add(o);var s=l.parent,i=l.node;i.disabled||s&&n(s.key)}}}return(e||[]).forEach(function(o){n(o)}),Me(r)}function Oc(e){const[t,r]=a.useState(null);return[a.useCallback((l,s,i)=>{const d=t??l,c=Math.min(d||0,l),p=Math.max(d||0,l),u=s.slice(c,p+1).map(m=>e(m)),v=u.some(m=>!i.has(m)),f=[];return u.forEach(m=>{v?(i.has(m)||f.push(m),i.add(m)):(i.delete(m),f.push(m))}),r(v?p:null),f},[t]),l=>{r(l)}]}const Dt={},tr="SELECT_ALL",nr="SELECT_INVERT",rr="SELECT_NONE",go=[],ka=(e,t)=>{let r=[];return(t||[]).forEach(n=>{r.push(n),n&&typeof n=="object"&&e in n&&(r=[].concat(Me(r),Me(ka(e,n[e]))))}),r},Tc=(e,t)=>{const{preserveSelectedRowKeys:r,selectedRowKeys:n,defaultSelectedRowKeys:o,getCheckboxProps:l,onChange:s,onSelect:i,onSelectAll:d,onSelectInvert:c,onSelectNone:p,onSelectMultiple:u,columnWidth:v,type:f,selections:m,fixed:g,renderCell:h,hideSelectAll:b,checkStrictly:y=!0}=t||{},{prefixCls:S,data:C,pageData:w,getRecordByKey:R,getRowKey:$,expandType:k,childrenColumnName:x,locale:T,getPopupContainer:O}=e,D=Nn(),[M,E]=Oc(X=>X),[I,N]=qo(n||o||go,{value:n}),K=a.useRef(new Map),z=a.useCallback(X=>{if(r){const F=new Map;X.forEach(H=>{let U=R(H);!U&&K.current.has(H)&&(U=K.current.get(H)),F.set(H,U)}),K.current=F}},[R,r]);a.useEffect(()=>{z(I)},[I]);const B=a.useMemo(()=>ka(x,w),[x,w]),{keyEntities:V}=a.useMemo(()=>{if(y)return{keyEntities:null};let X=C;if(r){const F=new Set(B.map((U,le)=>$(U,le))),H=Array.from(K.current).reduce((U,[le,de])=>F.has(le)?U:U.concat(de),[]);X=[].concat(Me(X),Me(H))}return hr(X,{externalGetKey:$,childrenPropName:x})},[C,$,y,x,r,B]),Z=a.useMemo(()=>{const X=new Map;return B.forEach((F,H)=>{const U=$(F,H),le=(l?l(F):null)||{};X.set(U,le)}),X},[B,$,l]),ne=a.useCallback(X=>{const F=$(X);let H;return Z.has(F)?H=Z.get($(X)):H=l?l(X):void 0,!!(H!=null&&H.disabled)},[Z,$]),[A,se]=a.useMemo(()=>{if(y)return[I||[],[]];const{checkedKeys:X,halfCheckedKeys:F}=Gt(I,!0,V,ne);return[X||[],F]},[I,y,V,ne]),fe=a.useMemo(()=>{const X=f==="radio"?A.slice(0,1):A;return new Set(X)},[A,f]),be=a.useMemo(()=>f==="radio"?new Set:new Set(se),[se,f]);a.useEffect(()=>{t||N(go)},[!!t]);const re=a.useCallback((X,F)=>{let H,U;z(X),r?(H=X,U=X.map(le=>K.current.get(le))):(H=[],U=[],X.forEach(le=>{const de=R(le);de!==void 0&&(H.push(le),U.push(de))})),N(H),s==null||s(H,U,{type:F})},[N,R,s,r]),Q=a.useCallback((X,F,H,U)=>{if(i){const le=H.map(de=>R(de));i(R(X),F,le,U)}re(H,"single")},[i,R,re]),ae=a.useMemo(()=>!m||b?null:(m===!0?[tr,nr,rr]:m).map(F=>F===tr?{key:"all",text:T.selectionAll,onSelect(){re(C.map((H,U)=>$(H,U)).filter(H=>{const U=Z.get(H);return!(U!=null&&U.disabled)||fe.has(H)}),"all")}}:F===nr?{key:"invert",text:T.selectInvert,onSelect(){const H=new Set(fe);w.forEach((le,de)=>{const ie=$(le,de),Ce=Z.get(ie);Ce!=null&&Ce.disabled||(H.has(ie)?H.delete(ie):H.add(ie))});const U=Array.from(H);c&&(D.deprecated(!1,"onSelectInvert","onChange"),c(U)),re(U,"invert")}}:F===rr?{key:"none",text:T.selectNone,onSelect(){p==null||p(),re(Array.from(fe).filter(H=>{const U=Z.get(H);return U==null?void 0:U.disabled}),"none")}}:F).map(F=>Object.assign(Object.assign({},F),{onSelect:(...H)=>{var U,le;(le=F.onSelect)===null||le===void 0||(U=le).call.apply(U,[F].concat(H)),E(null)}})),[m,fe,w,$,c,re]);return[a.useCallback(X=>{var F;if(!t)return X.filter(pe=>pe!==Dt);let H=Me(X);const U=new Set(fe),le=B.map($).filter(pe=>!Z.get(pe).disabled),de=le.every(pe=>U.has(pe)),ie=le.some(pe=>U.has(pe)),Ce=()=>{const pe=[];de?le.forEach(G=>{U.delete(G),pe.push(G)}):le.forEach(G=>{U.has(G)||(U.add(G),pe.push(G))});const ee=Array.from(U);d==null||d(!de,ee.map(G=>R(G)),pe.map(G=>R(G))),re(ee,"all"),E(null)};let je,we;if(f!=="radio"){let pe;if(ae){const ce={getPopupContainer:O,items:ae.map((Ae,Ie)=>{const{key:_e,text:Ge,onSelect:et}=Ae;return{key:_e??Ie,onClick:()=>{et==null||et(le)},label:Ge}})};pe=a.createElement("div",{className:`${S}-selection-extra`},a.createElement(br,{menu:ce,getPopupContainer:O},a.createElement("span",null,a.createElement(Dl,null))))}const ee=B.map((ce,Ae)=>{const Ie=$(ce,Ae),_e=Z.get(Ie)||{};return Object.assign({checked:U.has(Ie)},_e)}).filter(({disabled:ce})=>ce),G=!!ee.length&&ee.length===B.length,Pe=G&&ee.every(({checked:ce})=>ce),Le=G&&ee.some(({checked:ce})=>ce);we=a.createElement(wn,{checked:G?Pe:!!B.length&&de,indeterminate:G?!Pe&&Le:!de&&ie,onChange:Ce,disabled:B.length===0||G,"aria-label":pe?"Custom selection":"Select all",skipGroup:!0}),je=!b&&a.createElement("div",{className:`${S}-selection`},we,pe)}let W;f==="radio"?W=(pe,ee,G)=>{const Pe=$(ee,G),Le=U.has(Pe),ce=Z.get(Pe);return{node:a.createElement(Qo,Object.assign({},ce,{checked:Le,onClick:Ae=>{var Ie;Ae.stopPropagation(),(Ie=ce==null?void 0:ce.onClick)===null||Ie===void 0||Ie.call(ce,Ae)},onChange:Ae=>{var Ie;U.has(Pe)||Q(Pe,!0,[Pe],Ae.nativeEvent),(Ie=ce==null?void 0:ce.onChange)===null||Ie===void 0||Ie.call(ce,Ae)}})),checked:Le}}:W=(pe,ee,G)=>{var Pe;const Le=$(ee,G),ce=U.has(Le),Ae=be.has(Le),Ie=Z.get(Le);let _e;return k==="nest"?_e=Ae:_e=(Pe=Ie==null?void 0:Ie.indeterminate)!==null&&Pe!==void 0?Pe:Ae,{node:a.createElement(wn,Object.assign({},Ie,{indeterminate:_e,checked:ce,skipGroup:!0,onClick:Ge=>{var et;Ge.stopPropagation(),(et=Ie==null?void 0:Ie.onClick)===null||et===void 0||et.call(Ie,Ge)},onChange:Ge=>{var et;const{nativeEvent:yt}=Ge,{shiftKey:Be}=yt,mt=le.findIndex(Fe=>Fe===Le),wt=A.some(Fe=>le.includes(Fe));if(Be&&y&&wt){const Fe=M(mt,le,U),Xe=Array.from(U);u==null||u(!ce,Xe.map(tt=>R(tt)),Fe.map(tt=>R(tt))),re(Xe,"multiple")}else{const Fe=A;if(y){const Xe=ce?Rt(Fe,Le):kt(Fe,Le);Q(Le,!ce,Xe,yt)}else{const Xe=Gt([].concat(Me(Fe),[Le]),!0,V,ne),{checkedKeys:tt,halfCheckedKeys:Ue}=Xe;let Ze=tt;if(ce){const qe=new Set(tt);qe.delete(Le),Ze=Gt(Array.from(qe),{halfCheckedKeys:Ue},V,ne).checkedKeys}Q(Le,!ce,Ze,yt)}}E(ce?null:mt),(et=Ie==null?void 0:Ie.onChange)===null||et===void 0||et.call(Ie,Ge)}})),checked:ce}};const q=(pe,ee,G)=>{const{node:Pe,checked:Le}=W(pe,ee,G);return h?h(Le,ee,G,Pe):Pe};if(!H.includes(Dt))if(H.findIndex(pe=>{var ee;return((ee=pe[dn])===null||ee===void 0?void 0:ee.columnType)==="EXPAND_COLUMN"})===0){const[pe,...ee]=H;H=[pe,Dt].concat(Me(ee))}else H=[Dt].concat(Me(H));const ue=H.indexOf(Dt);H=H.filter((pe,ee)=>pe!==Dt||ee===ue);const xe=H[ue-1],he=H[ue+1];let Oe=g;Oe===void 0&&((he==null?void 0:he.fixed)!==void 0?Oe=he.fixed:(xe==null?void 0:xe.fixed)!==void 0&&(Oe=xe.fixed)),Oe&&xe&&((F=xe[dn])===null||F===void 0?void 0:F.columnType)==="EXPAND_COLUMN"&&xe.fixed===void 0&&(xe.fixed=Oe);const Re=te(`${S}-selection-col`,{[`${S}-selection-col-with-dropdown`]:m&&f==="checkbox"}),ze=()=>t!=null&&t.columnTitle?typeof t.columnTitle=="function"?t.columnTitle(we):t.columnTitle:je,me={fixed:Oe,width:v,className:`${S}-selection-column`,title:ze(),render:q,onCell:t.onCell,align:t.align,[dn]:{className:Re}};return H.map(pe=>pe===Dt?me:pe)},[$,B,t,A,fe,be,v,ae,k,Z,u,Q,ne]),fe]};function Mc(e,t){return e._antProxy=e._antProxy||{},Object.keys(t).forEach(r=>{if(!(r in e._antProxy)){const n=e[r];e._antProxy[r]=n,e[r]=t[r]}}),e}function Pc(e,t){return a.useImperativeHandle(e,()=>{const r=t(),{nativeElement:n}=r;return typeof Proxy<"u"?new Proxy(n,{get(o,l){return r[l]?r[l]:Reflect.get(o,l)}}):Mc(n,r)})}function Dc(e){return t=>{const{prefixCls:r,onExpand:n,record:o,expanded:l,expandable:s}=t,i=`${r}-row-expand-icon`;return a.createElement("button",{type:"button",onClick:d=>{n(o,d),d.stopPropagation()},className:te(i,{[`${i}-spaced`]:!s,[`${i}-expanded`]:s&&l,[`${i}-collapsed`]:s&&!l}),"aria-label":l?e.collapse:e.expand,"aria-expanded":l})}}function Kc(e){return(r,n)=>{const o=r.querySelector(`.${e}-container`);let l=n;if(o){const s=getComputedStyle(o),i=parseInt(s.borderLeftWidth,10),d=parseInt(s.borderRightWidth,10);l=n-i-d}return l}}const Lt=(e,t)=>"key"in e&&e.key!==void 0&&e.key!==null?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t;function an(e,t){return t?`${t}-${e}`:`${e}`}const Mn=(e,t)=>typeof e=="function"?e(t):e,Bc=(e,t)=>{const r=Mn(e,t);return Object.prototype.toString.call(r)==="[object Object]"?"":r};function Lc(e){const t=a.useRef(e),r=yl();return[()=>t.current,n=>{t.current=n,r()}]}var Hc=function(t){var r=t.dropPosition,n=t.dropLevelOffset,o=t.indent,l={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:l.top=0,l.left=-n*o;break;case 1:l.bottom=0,l.left=-n*o;break;case 0:l.bottom=0,l.left=o;break}return De.createElement("div",{style:l})};function Oa(e){if(e==null)throw new TypeError("Cannot destructure "+e)}var Ta=a.forwardRef(function(e,t){var r=e.height,n=e.offsetY,o=e.offsetX,l=e.children,s=e.prefixCls,i=e.onInnerResize,d=e.innerProps,c=e.rtl,p=e.extra,u={},v={display:"flex",flexDirection:"column"};return n!==void 0&&(u={height:r,position:"relative",overflow:"hidden"},v=j(j({},v),{},P(P(P(P(P({transform:"translateY(".concat(n,"px)")},c?"marginRight":"marginLeft",-o),"position","absolute"),"left",0),"right",0),"top",0))),a.createElement("div",{style:u},a.createElement(qt,{onResize:function(m){var g=m.offsetHeight;g&&i&&i()}},a.createElement("div",Ee({style:v,className:te(P({},"".concat(s,"-holder-inner"),s)),ref:t},d),l,p)))});Ta.displayName="Filler";function zc(e){var t=e.children,r=e.setRef,n=a.useCallback(function(o){r(o)},[]);return a.cloneElement(t,{ref:n})}function _c(e,t,r,n,o,l,s,i){var d=i.getKey;return e.slice(t,r+1).map(function(c,p){var u=t+p,v=s(c,u,{style:{width:n},offsetX:o}),f=d(c);return a.createElement(zc,{key:f,setRef:function(g){return l(c,g)}},v)})}function jc(e,t,r){var n=e.length,o=t.length,l,s;if(n===0&&o===0)return null;n<o?(l=e,s=t):(l=t,s=e);var i={__EMPTY_ITEM__:!0};function d(m){return m!==void 0?r(m):i}for(var c=null,p=Math.abs(n-o)!==1,u=0;u<s.length;u+=1){var v=d(l[u]),f=d(s[u]);if(v!==f){c=u,p=p||v!==d(s[u+1]);break}}return c===null?null:{index:c,multiple:p}}function Ac(e,t,r){var n=a.useState(e),o=oe(n,2),l=o[0],s=o[1],i=a.useState(null),d=oe(i,2),c=d[0],p=d[1];return a.useEffect(function(){var u=jc(l||[],e||[],t);(u==null?void 0:u.index)!==void 0&&p(e[u.index]),s(e)},[e]),[c]}var ho=(typeof navigator>"u"?"undefined":ut(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const Ma=function(e,t,r,n){var o=a.useRef(!1),l=a.useRef(null);function s(){clearTimeout(l.current),o.current=!0,l.current=setTimeout(function(){o.current=!1},50)}var i=a.useRef({top:e,bottom:t,left:r,right:n});return i.current.top=e,i.current.bottom=t,i.current.left=r,i.current.right=n,function(d,c){var p=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=d?c<0&&i.current.left||c>0&&i.current.right:c<0&&i.current.top||c>0&&i.current.bottom;return p&&u?(clearTimeout(l.current),o.current=!1):(!u||o.current)&&s(),!o.current&&u}};function Fc(e,t,r,n,o,l,s){var i=a.useRef(0),d=a.useRef(null),c=a.useRef(null),p=a.useRef(!1),u=Ma(t,r,n,o);function v(y,S){if(Ve.cancel(d.current),!u(!1,S)){var C=y;if(!C._virtualHandled)C._virtualHandled=!0;else return;i.current+=S,c.current=S,ho||C.preventDefault(),d.current=Ve(function(){var w=p.current?10:1;s(i.current*w,!1),i.current=0})}}function f(y,S){s(S,!0),ho||y.preventDefault()}var m=a.useRef(null),g=a.useRef(null);function h(y){if(e){Ve.cancel(g.current),g.current=Ve(function(){m.current=null},2);var S=y.deltaX,C=y.deltaY,w=y.shiftKey,R=S,$=C;(m.current==="sx"||!m.current&&w&&C&&!S)&&(R=C,$=0,m.current="sx");var k=Math.abs(R),x=Math.abs($);m.current===null&&(m.current=l&&k>x?"x":"y"),m.current==="y"?v(y,$):f(y,R)}}function b(y){e&&(p.current=y.detail===c.current)}return[h,b]}function Wc(e,t,r,n){var o=a.useMemo(function(){return[new Map,[]]},[e,r.id,n]),l=oe(o,2),s=l[0],i=l[1],d=function(p){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:p,v=s.get(p),f=s.get(u);if(v===void 0||f===void 0)for(var m=e.length,g=i.length;g<m;g+=1){var h,b=e[g],y=t(b);s.set(y,g);var S=(h=r.get(y))!==null&&h!==void 0?h:n;if(i[g]=(i[g-1]||0)+S,y===p&&(v=g),y===u&&(f=g),v!==void 0&&f!==void 0)break}return{top:i[v-1]||0,bottom:i[f]}};return d}var Vc=function(){function e(){gr(this,e),P(this,"maps",void 0),P(this,"id",0),P(this,"diffRecords",new Map),this.maps=Object.create(null)}return pr(e,[{key:"set",value:function(r,n){this.diffRecords.set(r,this.maps[r]),this.maps[r]=n,this.id+=1}},{key:"get",value:function(r){return this.maps[r]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function bo(e){var t=parseFloat(e);return isNaN(t)?0:t}function Xc(e,t,r){var n=a.useState(0),o=oe(n,2),l=o[0],s=o[1],i=a.useRef(new Map),d=a.useRef(new Vc),c=a.useRef(0);function p(){c.current+=1}function u(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;p();var m=function(){var b=!1;i.current.forEach(function(y,S){if(y&&y.offsetParent){var C=y.offsetHeight,w=getComputedStyle(y),R=w.marginTop,$=w.marginBottom,k=bo(R),x=bo($),T=C+k+x;d.current.get(S)!==T&&(d.current.set(S,T),b=!0)}}),b&&s(function(y){return y+1})};if(f)m();else{c.current+=1;var g=c.current;Promise.resolve().then(function(){g===c.current&&m()})}}function v(f,m){var g=e(f);i.current.get(g),m?(i.current.set(g,m),u()):i.current.delete(g)}return a.useEffect(function(){return p},[]),[v,u,d.current,l]}var yo=14/15;function qc(e,t,r){var n=a.useRef(!1),o=a.useRef(0),l=a.useRef(0),s=a.useRef(null),i=a.useRef(null),d,c=function(f){if(n.current){var m=Math.ceil(f.touches[0].pageX),g=Math.ceil(f.touches[0].pageY),h=o.current-m,b=l.current-g,y=Math.abs(h)>Math.abs(b);y?o.current=m:l.current=g;var S=r(y,y?h:b,!1,f);S&&f.preventDefault(),clearInterval(i.current),S&&(i.current=setInterval(function(){y?h*=yo:b*=yo;var C=Math.floor(y?h:b);(!r(y,C,!0)||Math.abs(C)<=.1)&&clearInterval(i.current)},16))}},p=function(){n.current=!1,d()},u=function(f){d(),f.touches.length===1&&!n.current&&(n.current=!0,o.current=Math.ceil(f.touches[0].pageX),l.current=Math.ceil(f.touches[0].pageY),s.current=f.target,s.current.addEventListener("touchmove",c,{passive:!1}),s.current.addEventListener("touchend",p,{passive:!0}))};d=function(){s.current&&(s.current.removeEventListener("touchmove",c),s.current.removeEventListener("touchend",p))},ot(function(){return e&&t.current.addEventListener("touchstart",u,{passive:!0}),function(){var v;(v=t.current)===null||v===void 0||v.removeEventListener("touchstart",u),d(),clearInterval(i.current)}},[e])}function So(e){return Math.floor(Math.pow(e,.5))}function or(e,t){var r="touches"in e?e.touches[0]:e;return r[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}function Yc(e,t,r){a.useEffect(function(){var n=t.current;if(e&&n){var o=!1,l,s,i=function(){Ve.cancel(l)},d=function v(){i(),l=Ve(function(){r(s),v()})},c=function(f){if(!(f.target.draggable||f.button!==0)){var m=f;m._virtualHandled||(m._virtualHandled=!0,o=!0)}},p=function(){o=!1,i()},u=function(f){if(o){var m=or(f,!1),g=n.getBoundingClientRect(),h=g.top,b=g.bottom;if(m<=h){var y=h-m;s=-So(y),d()}else if(m>=b){var S=m-b;s=So(S),d()}else i()}};return n.addEventListener("mousedown",c),n.ownerDocument.addEventListener("mouseup",p),n.ownerDocument.addEventListener("mousemove",u),function(){n.removeEventListener("mousedown",c),n.ownerDocument.removeEventListener("mouseup",p),n.ownerDocument.removeEventListener("mousemove",u),i()}}},[e])}var Uc=10;function Gc(e,t,r,n,o,l,s,i){var d=a.useRef(),c=a.useState(null),p=oe(c,2),u=p[0],v=p[1];return ot(function(){if(u&&u.times<Uc){if(!e.current){v(function(B){return j({},B)});return}l();var f=u.targetAlign,m=u.originAlign,g=u.index,h=u.offset,b=e.current.clientHeight,y=!1,S=f,C=null;if(b){for(var w=f||m,R=0,$=0,k=0,x=Math.min(t.length-1,g),T=0;T<=x;T+=1){var O=o(t[T]);$=R;var D=r.get(O);k=$+(D===void 0?n:D),R=k}for(var M=w==="top"?h:b-h,E=x;E>=0;E-=1){var I=o(t[E]),N=r.get(I);if(N===void 0){y=!0;break}if(M-=N,M<=0)break}switch(w){case"top":C=$-h;break;case"bottom":C=k-b+h;break;default:{var K=e.current.scrollTop,z=K+b;$<K?S="top":k>z&&(S="bottom")}}C!==null&&s(C),C!==u.lastTop&&(y=!0)}y&&v(j(j({},u),{},{times:u.times+1,targetAlign:S,lastTop:C}))}},[u,e.current]),function(f){if(f==null){i();return}if(Ve.cancel(d.current),typeof f=="number")s(f);else if(f&&ut(f)==="object"){var m,g=f.align;"index"in f?m=f.index:m=t.findIndex(function(y){return o(y)===f.key});var h=f.offset,b=h===void 0?0:h;v({times:0,index:m,offset:b,originAlign:g})}}}var xo=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.rtl,o=e.scrollOffset,l=e.scrollRange,s=e.onStartMove,i=e.onStopMove,d=e.onScroll,c=e.horizontal,p=e.spinSize,u=e.containerSize,v=e.style,f=e.thumbStyle,m=e.showScrollBar,g=a.useState(!1),h=oe(g,2),b=h[0],y=h[1],S=a.useState(null),C=oe(S,2),w=C[0],R=C[1],$=a.useState(null),k=oe($,2),x=k[0],T=k[1],O=!n,D=a.useRef(),M=a.useRef(),E=a.useState(m),I=oe(E,2),N=I[0],K=I[1],z=a.useRef(),B=function(){m===!0||m===!1||(clearTimeout(z.current),K(!0),z.current=setTimeout(function(){K(!1)},3e3))},V=l-u||0,Z=u-p||0,ne=a.useMemo(function(){if(o===0||V===0)return 0;var X=o/V;return X*Z},[o,V,Z]),A=function(F){F.stopPropagation(),F.preventDefault()},se=a.useRef({top:ne,dragging:b,pageY:w,startTop:x});se.current={top:ne,dragging:b,pageY:w,startTop:x};var fe=function(F){y(!0),R(or(F,c)),T(se.current.top),s(),F.stopPropagation(),F.preventDefault()};a.useEffect(function(){var X=function(le){le.preventDefault()},F=D.current,H=M.current;return F.addEventListener("touchstart",X,{passive:!1}),H.addEventListener("touchstart",fe,{passive:!1}),function(){F.removeEventListener("touchstart",X),H.removeEventListener("touchstart",fe)}},[]);var be=a.useRef();be.current=V;var re=a.useRef();re.current=Z,a.useEffect(function(){if(b){var X,F=function(le){var de=se.current,ie=de.dragging,Ce=de.pageY,je=de.startTop;Ve.cancel(X);var we=D.current.getBoundingClientRect(),W=u/(c?we.width:we.height);if(ie){var q=(or(le,c)-Ce)*W,ue=je;!O&&c?ue-=q:ue+=q;var xe=be.current,he=re.current,Oe=he?ue/he:0,Re=Math.ceil(Oe*xe);Re=Math.max(Re,0),Re=Math.min(Re,xe),X=Ve(function(){d(Re,c)})}},H=function(){y(!1),i()};return window.addEventListener("mousemove",F,{passive:!0}),window.addEventListener("touchmove",F,{passive:!0}),window.addEventListener("mouseup",H,{passive:!0}),window.addEventListener("touchend",H,{passive:!0}),function(){window.removeEventListener("mousemove",F),window.removeEventListener("touchmove",F),window.removeEventListener("mouseup",H),window.removeEventListener("touchend",H),Ve.cancel(X)}}},[b]),a.useEffect(function(){return B(),function(){clearTimeout(z.current)}},[o]),a.useImperativeHandle(t,function(){return{delayHidden:B}});var Q="".concat(r,"-scrollbar"),ae={position:"absolute",visibility:N?null:"hidden"},ve={position:"absolute",borderRadius:99,background:"var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",cursor:"pointer",userSelect:"none"};return c?(Object.assign(ae,{height:8,left:0,right:0,bottom:0}),Object.assign(ve,P({height:"100%",width:p},O?"left":"right",ne))):(Object.assign(ae,P({width:8,top:0,bottom:0},O?"right":"left",0)),Object.assign(ve,{width:"100%",height:p,top:ne})),a.createElement("div",{ref:D,className:te(Q,P(P(P({},"".concat(Q,"-horizontal"),c),"".concat(Q,"-vertical"),!c),"".concat(Q,"-visible"),N)),style:j(j({},ae),v),onMouseDown:A,onMouseMove:B},a.createElement("div",{ref:M,className:te("".concat(Q,"-thumb"),P({},"".concat(Q,"-thumb-moving"),b)),style:j(j({},ve),f),onMouseDown:fe}))}),Zc=20;function Co(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=e/t*e;return isNaN(r)&&(r=0),r=Math.max(r,Zc),Math.floor(r)}var Jc=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],Qc=[],ed={overflowY:"auto",overflowAnchor:"none"};function td(e,t){var r=e.prefixCls,n=r===void 0?"rc-virtual-list":r,o=e.className,l=e.height,s=e.itemHeight,i=e.fullHeight,d=i===void 0?!0:i,c=e.style,p=e.data,u=e.children,v=e.itemKey,f=e.virtual,m=e.direction,g=e.scrollWidth,h=e.component,b=h===void 0?"div":h,y=e.onScroll,S=e.onVirtualScroll,C=e.onVisibleChange,w=e.innerProps,R=e.extraRender,$=e.styles,k=e.showScrollBar,x=k===void 0?"optional":k,T=bt(e,Jc),O=a.useCallback(function(_){return typeof v=="function"?v(_):_==null?void 0:_[v]},[v]),D=Xc(O),M=oe(D,4),E=M[0],I=M[1],N=M[2],K=M[3],z=!!(f!==!1&&l&&s),B=a.useMemo(function(){return Object.values(N.maps).reduce(function(_,L){return _+L},0)},[N.id,N.maps]),V=z&&p&&(Math.max(s*p.length,B)>l||!!g),Z=m==="rtl",ne=te(n,P({},"".concat(n,"-rtl"),Z),o),A=p||Qc,se=a.useRef(),fe=a.useRef(),be=a.useRef(),re=a.useState(0),Q=oe(re,2),ae=Q[0],ve=Q[1],X=a.useState(0),F=oe(X,2),H=F[0],U=F[1],le=a.useState(!1),de=oe(le,2),ie=de[0],Ce=de[1],je=function(){Ce(!0)},we=function(){Ce(!1)},W={getKey:O};function q(_){ve(function(L){var J;typeof _=="function"?J=_(L):J=_;var ge=wt(J);return se.current.scrollTop=ge,ge})}var ue=a.useRef({start:0,end:A.length}),xe=a.useRef(),he=Ac(A,O),Oe=oe(he,1),Re=Oe[0];xe.current=Re;var ze=a.useMemo(function(){if(!z)return{scrollHeight:void 0,start:0,end:A.length-1,offset:void 0};if(!V){var _;return{scrollHeight:((_=fe.current)===null||_===void 0?void 0:_.offsetHeight)||0,start:0,end:A.length-1,offset:void 0}}for(var L=0,J,ge,Ne,Ct=A.length,rt=0;rt<Ct;rt+=1){var Nt=A[rt],Yt=O(Nt),$t=N.get(Yt),Et=L+($t===void 0?s:$t);Et>=ae&&J===void 0&&(J=rt,ge=L),Et>ae+l&&Ne===void 0&&(Ne=rt),L=Et}return J===void 0&&(J=0,ge=0,Ne=Math.ceil(l/s)),Ne===void 0&&(Ne=A.length-1),Ne=Math.min(Ne+1,A.length-1),{scrollHeight:L,start:J,end:Ne,offset:ge}},[V,z,ae,A,K,l]),me=ze.scrollHeight,pe=ze.start,ee=ze.end,G=ze.offset;ue.current.start=pe,ue.current.end=ee,a.useLayoutEffect(function(){var _=N.getRecord();if(_.size===1){var L=Array.from(_.keys())[0],J=_.get(L),ge=A[pe];if(ge&&J===void 0){var Ne=O(ge);if(Ne===L){var Ct=N.get(L),rt=Ct-s;q(function(Nt){return Nt+rt})}}}N.resetRecord()},[me]);var Pe=a.useState({width:0,height:l}),Le=oe(Pe,2),ce=Le[0],Ae=Le[1],Ie=function(L){Ae({width:L.offsetWidth,height:L.offsetHeight})},_e=a.useRef(),Ge=a.useRef(),et=a.useMemo(function(){return Co(ce.width,g)},[ce.width,g]),yt=a.useMemo(function(){return Co(ce.height,me)},[ce.height,me]),Be=me-l,mt=a.useRef(Be);mt.current=Be;function wt(_){var L=_;return Number.isNaN(mt.current)||(L=Math.min(L,mt.current)),L=Math.max(L,0),L}var Fe=ae<=0,Xe=ae>=Be,tt=H<=0,Ue=H>=g,Ze=Ma(Fe,Xe,tt,Ue),qe=function(){return{x:Z?-H:H,y:ae}},ft=a.useRef(qe()),nt=ht(function(_){if(S){var L=j(j({},qe()),_);(ft.current.x!==L.x||ft.current.y!==L.y)&&(S(L),ft.current=L)}});function ct(_,L){var J=_;L?(fn.flushSync(function(){U(J)}),nt()):q(J)}function zt(_){var L=_.currentTarget.scrollTop;L!==ae&&q(L),y==null||y(_),nt()}var xt=function(L){var J=L,ge=g?g-ce.width:0;return J=Math.max(J,0),J=Math.min(J,ge),J},Pt=ht(function(_,L){L?(fn.flushSync(function(){U(function(J){var ge=J+(Z?-_:_);return xt(ge)})}),nt()):q(function(J){var ge=J+_;return ge})}),_t=Fc(z,Fe,Xe,tt,Ue,!!g,Pt),Se=oe(_t,2),ye=Se[0],He=Se[1];qc(z,se,function(_,L,J,ge){var Ne=ge;return Ze(_,L,J)?!1:!Ne||!Ne._virtualHandled?(Ne&&(Ne._virtualHandled=!0),ye({preventDefault:function(){},deltaX:_?L:0,deltaY:_?0:L}),!0):!1}),Yc(V,se,function(_){q(function(L){return L+_})}),ot(function(){function _(J){var ge=Fe&&J.detail<0,Ne=Xe&&J.detail>0;z&&!ge&&!Ne&&J.preventDefault()}var L=se.current;return L.addEventListener("wheel",ye,{passive:!1}),L.addEventListener("DOMMouseScroll",He,{passive:!0}),L.addEventListener("MozMousePixelScroll",_,{passive:!1}),function(){L.removeEventListener("wheel",ye),L.removeEventListener("DOMMouseScroll",He),L.removeEventListener("MozMousePixelScroll",_)}},[z,Fe,Xe]),ot(function(){if(g){var _=xt(H);U(_),nt({x:_})}},[ce.width,g]);var We=function(){var L,J;(L=_e.current)===null||L===void 0||L.delayHidden(),(J=Ge.current)===null||J===void 0||J.delayHidden()},Ye=Gc(se,A,N,s,O,function(){return I(!0)},q,We);a.useImperativeHandle(t,function(){return{nativeElement:be.current,getScrollInfo:qe,scrollTo:function(L){function J(ge){return ge&&ut(ge)==="object"&&("left"in ge||"top"in ge)}J(L)?(L.left!==void 0&&U(xt(L.left)),Ye(L.top)):Ye(L)}}}),ot(function(){if(C){var _=A.slice(pe,ee+1);C(_,A)}},[pe,ee,A]);var at=Wc(A,O,N,s),dt=R==null?void 0:R({start:pe,end:ee,virtual:V,offsetX:H,offsetY:G,rtl:Z,getSize:at}),Je=_c(A,pe,ee,g,H,E,u,W),pt=null;l&&(pt=j(P({},d?"height":"maxHeight",l),ed),z&&(pt.overflowY="hidden",g&&(pt.overflowX="hidden"),ie&&(pt.pointerEvents="none")));var jt={};return Z&&(jt.dir="rtl"),a.createElement("div",Ee({ref:be,style:j(j({},c),{},{position:"relative"}),className:ne},jt,T),a.createElement(qt,{onResize:Ie},a.createElement(b,{className:"".concat(n,"-holder"),style:pt,ref:se,onScroll:zt,onMouseEnter:We},a.createElement(Ta,{prefixCls:n,height:me,offsetX:H,offsetY:G,scrollWidth:g,onInnerResize:I,ref:fe,innerProps:w,rtl:Z,extra:dt},Je))),V&&me>l&&a.createElement(xo,{ref:_e,prefixCls:n,scrollOffset:ae,scrollRange:me,rtl:Z,onScroll:ct,onStartMove:je,onStopMove:we,spinSize:yt,containerSize:ce.height,style:$==null?void 0:$.verticalScrollBar,thumbStyle:$==null?void 0:$.verticalScrollBarThumb,showScrollBar:x}),V&&g>ce.width&&a.createElement(xo,{ref:Ge,prefixCls:n,scrollOffset:H,scrollRange:g,rtl:Z,onScroll:ct,onStartMove:je,onStopMove:we,spinSize:et,containerSize:ce.width,horizontal:!0,style:$==null?void 0:$.horizontalScrollBar,thumbStyle:$==null?void 0:$.horizontalScrollBarThumb,showScrollBar:x}))}var Pa=a.forwardRef(td);Pa.displayName="List";function nd(e,t){var r=a.useState(!1),n=oe(r,2),o=n[0],l=n[1];ot(function(){if(o)return e(),function(){t()}},[o]),ot(function(){return l(!0),function(){l(!1)}},[])}var rd=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],od=a.forwardRef(function(e,t){var r=e.className,n=e.style,o=e.motion,l=e.motionNodes,s=e.motionType,i=e.onMotionStart,d=e.onMotionEnd,c=e.active,p=e.treeNodeRequiredProps,u=bt(e,rd),v=a.useState(!0),f=oe(v,2),m=f[0],g=f[1],h=a.useContext($r),b=h.prefixCls,y=l&&s!=="hide";ot(function(){l&&y!==m&&g(y)},[l]);var S=function(){l&&i()},C=a.useRef(!1),w=function(){l&&!C.current&&(C.current=!0,d())};nd(S,w);var R=function(k){y===k&&w()};return l?a.createElement(Sl,Ee({ref:t,visible:m},o,{motionAppear:s==="show",onVisibleChanged:R}),function($,k){var x=$.className,T=$.style;return a.createElement("div",{ref:k,className:te("".concat(b,"-treenode-motion"),x),style:T},l.map(function(O){var D=Object.assign({},(Oa(O.data),O.data)),M=O.title,E=O.key,I=O.isStart,N=O.isEnd;delete D.children;var K=cn(E,p);return a.createElement(mn,Ee({},D,K,{title:M,active:c,data:O.data,key:E,isStart:I,isEnd:N}))}))}):a.createElement(mn,Ee({domRef:t,className:r,style:n},u,{active:c}))});function ad(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=e.length,n=t.length;if(Math.abs(r-n)!==1)return{add:!1,key:null};function o(l,s){var i=new Map;l.forEach(function(c){i.set(c,!0)});var d=s.filter(function(c){return!i.has(c)});return d.length===1?d[0]:null}return r<n?{add:!0,key:o(e,t)}:{add:!1,key:o(t,e)}}function wo(e,t,r){var n=e.findIndex(function(i){return i.key===r}),o=e[n+1],l=t.findIndex(function(i){return i.key===r});if(o){var s=t.findIndex(function(i){return i.key===o.key});return t.slice(l+1,s)}return t.slice(l+1)}var ld=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],$o={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},id=function(){},Xt="RC_TREE_MOTION_".concat(Math.random()),ar={key:Xt},Da={key:Xt,level:0,index:0,pos:"0",node:ar,nodes:[ar]},Eo={parent:null,children:[],pos:Da.pos,data:ar,title:null,key:Xt,isStart:[],isEnd:[]};function Ro(e,t,r,n){return t===!1||!r?e:e.slice(0,Math.ceil(r/n)+1)}function Io(e){var t=e.key,r=e.pos;return gn(t,r)}function sd(e){for(var t=String(e.data.key),r=e;r.parent;)r=r.parent,t="".concat(r.data.key," > ").concat(t);return t}var cd=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.data;e.selectable,e.checkable;var o=e.expandedKeys,l=e.selectedKeys,s=e.checkedKeys,i=e.loadedKeys,d=e.loadingKeys,c=e.halfCheckedKeys,p=e.keyEntities,u=e.disabled,v=e.dragging,f=e.dragOverNodeKey,m=e.dropPosition,g=e.motion,h=e.height,b=e.itemHeight,y=e.virtual,S=e.scrollWidth,C=e.focusable,w=e.activeItem,R=e.focused,$=e.tabIndex,k=e.onKeyDown,x=e.onFocus,T=e.onBlur,O=e.onActiveChange,D=e.onListChangeStart,M=e.onListChangeEnd,E=bt(e,ld),I=a.useRef(null),N=a.useRef(null);a.useImperativeHandle(t,function(){return{scrollTo:function(q){I.current.scrollTo(q)},getIndentWidth:function(){return N.current.offsetWidth}}});var K=a.useState(o),z=oe(K,2),B=z[0],V=z[1],Z=a.useState(n),ne=oe(Z,2),A=ne[0],se=ne[1],fe=a.useState(n),be=oe(fe,2),re=be[0],Q=be[1],ae=a.useState([]),ve=oe(ae,2),X=ve[0],F=ve[1],H=a.useState(null),U=oe(H,2),le=U[0],de=U[1],ie=a.useRef(n);ie.current=n;function Ce(){var W=ie.current;se(W),Q(W),F([]),de(null),M()}ot(function(){V(o);var W=ad(B,o);if(W.key!==null)if(W.add){var q=A.findIndex(function(ze){var me=ze.key;return me===W.key}),ue=Ro(wo(A,n,W.key),y,h,b),xe=A.slice();xe.splice(q+1,0,Eo),Q(xe),F(ue),de("show")}else{var he=n.findIndex(function(ze){var me=ze.key;return me===W.key}),Oe=Ro(wo(n,A,W.key),y,h,b),Re=n.slice();Re.splice(he+1,0,Eo),Q(Re),F(Oe),de("hide")}else A!==n&&(se(n),Q(n))},[o,n]),a.useEffect(function(){v||Ce()},[v]);var je=g?re:n,we={expandedKeys:o,selectedKeys:l,loadedKeys:i,loadingKeys:d,checkedKeys:s,halfCheckedKeys:c,dragOverNodeKey:f,dropPosition:m,keyEntities:p};return a.createElement(a.Fragment,null,R&&w&&a.createElement("span",{style:$o,"aria-live":"assertive"},sd(w)),a.createElement("div",null,a.createElement("input",{style:$o,disabled:C===!1||u,tabIndex:C!==!1?$:null,onKeyDown:k,onFocus:x,onBlur:T,value:"",onChange:id,"aria-label":"for screen reader"})),a.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},a.createElement("div",{className:"".concat(r,"-indent")},a.createElement("div",{ref:N,className:"".concat(r,"-indent-unit")}))),a.createElement(Pa,Ee({},E,{data:je,itemKey:Io,height:h,fullHeight:!1,virtual:y,itemHeight:b,scrollWidth:S,prefixCls:"".concat(r,"-list"),ref:I,role:"tree",onVisibleChange:function(q){q.every(function(ue){return Io(ue)!==Xt})&&Ce()}}),function(W){var q=W.pos,ue=Object.assign({},(Oa(W.data),W.data)),xe=W.title,he=W.key,Oe=W.isStart,Re=W.isEnd,ze=gn(he,q);delete ue.key,delete ue.children;var me=cn(ze,we);return a.createElement(od,Ee({},ue,me,{title:xe,active:!!w&&he===w.key,pos:q,data:W.data,isStart:Oe,isEnd:Re,motion:g,motionNodes:he===Xt?X:null,motionType:le,onMotionStart:D,onMotionEnd:Ce,treeNodeRequiredProps:we,onMouseMove:function(){O(null)}}))}))}),dd=10,Rr=function(e){xl(r,e);var t=Cl(r);function r(){var n;gr(this,r);for(var o=arguments.length,l=new Array(o),s=0;s<o;s++)l[s]=arguments[s];return n=t.call.apply(t,[this].concat(l)),P(ke(n),"destroyed",!1),P(ke(n),"delayedDragEnterLogic",void 0),P(ke(n),"loadingRetryTimes",{}),P(ke(n),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:Qt()}),P(ke(n),"dragStartMousePosition",null),P(ke(n),"dragNodeProps",null),P(ke(n),"currentMouseOverDroppableNodeKey",null),P(ke(n),"listRef",a.createRef()),P(ke(n),"onNodeDragStart",function(i,d){var c=n.state,p=c.expandedKeys,u=c.keyEntities,v=n.props.onDragStart,f=d.eventKey;n.dragNodeProps=d,n.dragStartMousePosition={x:i.clientX,y:i.clientY};var m=Rt(p,f);n.setState({draggingNodeKey:f,dragChildrenKeys:Ic(f,u),indent:n.listRef.current.getIndentWidth()}),n.setExpandedKeys(m),window.addEventListener("dragend",n.onWindowDragEnd),v==null||v({event:i,node:Qe(d)})}),P(ke(n),"onNodeDragEnter",function(i,d){var c=n.state,p=c.expandedKeys,u=c.keyEntities,v=c.dragChildrenKeys,f=c.flattenNodes,m=c.indent,g=n.props,h=g.onDragEnter,b=g.onExpand,y=g.allowDrop,S=g.direction,C=d.pos,w=d.eventKey;if(n.currentMouseOverDroppableNodeKey!==w&&(n.currentMouseOverDroppableNodeKey=w),!n.dragNodeProps){n.resetDragState();return}var R=mo(i,n.dragNodeProps,d,m,n.dragStartMousePosition,y,f,u,p,S),$=R.dropPosition,k=R.dropLevelOffset,x=R.dropTargetKey,T=R.dropContainerKey,O=R.dropTargetPos,D=R.dropAllowed,M=R.dragOverNodeKey;if(v.includes(x)||!D){n.resetDragState();return}if(n.delayedDragEnterLogic||(n.delayedDragEnterLogic={}),Object.keys(n.delayedDragEnterLogic).forEach(function(E){clearTimeout(n.delayedDragEnterLogic[E])}),n.dragNodeProps.eventKey!==d.eventKey&&(i.persist(),n.delayedDragEnterLogic[C]=window.setTimeout(function(){if(n.state.draggingNodeKey!==null){var E=Me(p),I=gt(u,d.eventKey);I&&(I.children||[]).length&&(E=kt(p,d.eventKey)),n.props.hasOwnProperty("expandedKeys")||n.setExpandedKeys(E),b==null||b(E,{node:Qe(d),expanded:!0,nativeEvent:i.nativeEvent})}},800)),n.dragNodeProps.eventKey===x&&k===0){n.resetDragState();return}n.setState({dragOverNodeKey:M,dropPosition:$,dropLevelOffset:k,dropTargetKey:x,dropContainerKey:T,dropTargetPos:O,dropAllowed:D}),h==null||h({event:i,node:Qe(d),expandedKeys:p})}),P(ke(n),"onNodeDragOver",function(i,d){var c=n.state,p=c.dragChildrenKeys,u=c.flattenNodes,v=c.keyEntities,f=c.expandedKeys,m=c.indent,g=n.props,h=g.onDragOver,b=g.allowDrop,y=g.direction;if(n.dragNodeProps){var S=mo(i,n.dragNodeProps,d,m,n.dragStartMousePosition,b,u,v,f,y),C=S.dropPosition,w=S.dropLevelOffset,R=S.dropTargetKey,$=S.dropContainerKey,k=S.dropTargetPos,x=S.dropAllowed,T=S.dragOverNodeKey;p.includes(R)||!x||(n.dragNodeProps.eventKey===R&&w===0?n.state.dropPosition===null&&n.state.dropLevelOffset===null&&n.state.dropTargetKey===null&&n.state.dropContainerKey===null&&n.state.dropTargetPos===null&&n.state.dropAllowed===!1&&n.state.dragOverNodeKey===null||n.resetDragState():C===n.state.dropPosition&&w===n.state.dropLevelOffset&&R===n.state.dropTargetKey&&$===n.state.dropContainerKey&&k===n.state.dropTargetPos&&x===n.state.dropAllowed&&T===n.state.dragOverNodeKey||n.setState({dropPosition:C,dropLevelOffset:w,dropTargetKey:R,dropContainerKey:$,dropTargetPos:k,dropAllowed:x,dragOverNodeKey:T}),h==null||h({event:i,node:Qe(d)}))}}),P(ke(n),"onNodeDragLeave",function(i,d){n.currentMouseOverDroppableNodeKey===d.eventKey&&!i.currentTarget.contains(i.relatedTarget)&&(n.resetDragState(),n.currentMouseOverDroppableNodeKey=null);var c=n.props.onDragLeave;c==null||c({event:i,node:Qe(d)})}),P(ke(n),"onWindowDragEnd",function(i){n.onNodeDragEnd(i,null,!0),window.removeEventListener("dragend",n.onWindowDragEnd)}),P(ke(n),"onNodeDragEnd",function(i,d){var c=n.props.onDragEnd;n.setState({dragOverNodeKey:null}),n.cleanDragState(),c==null||c({event:i,node:Qe(d)}),n.dragNodeProps=null,window.removeEventListener("dragend",n.onWindowDragEnd)}),P(ke(n),"onNodeDrop",function(i,d){var c,p=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=n.state,v=u.dragChildrenKeys,f=u.dropPosition,m=u.dropTargetKey,g=u.dropTargetPos,h=u.dropAllowed;if(h){var b=n.props.onDrop;if(n.setState({dragOverNodeKey:null}),n.cleanDragState(),m!==null){var y=j(j({},cn(m,n.getTreeNodeRequiredProps())),{},{active:((c=n.getActiveItem())===null||c===void 0?void 0:c.key)===m,data:gt(n.state.keyEntities,m).node}),S=v.includes(m);Bt(!S,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var C=Er(g),w={event:i,node:Qe(y),dragNode:n.dragNodeProps?Qe(n.dragNodeProps):null,dragNodesKeys:[n.dragNodeProps.eventKey].concat(v),dropToGap:f!==0,dropPosition:f+Number(C[C.length-1])};p||b==null||b(w),n.dragNodeProps=null}}}),P(ke(n),"cleanDragState",function(){var i=n.state.draggingNodeKey;i!==null&&n.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),n.dragStartMousePosition=null,n.currentMouseOverDroppableNodeKey=null}),P(ke(n),"triggerExpandActionExpand",function(i,d){var c=n.state,p=c.expandedKeys,u=c.flattenNodes,v=d.expanded,f=d.key,m=d.isLeaf;if(!(m||i.shiftKey||i.metaKey||i.ctrlKey)){var g=u.filter(function(b){return b.key===f})[0],h=Qe(j(j({},cn(f,n.getTreeNodeRequiredProps())),{},{data:g.data}));n.setExpandedKeys(v?Rt(p,f):kt(p,f)),n.onNodeExpand(i,h)}}),P(ke(n),"onNodeClick",function(i,d){var c=n.props,p=c.onClick,u=c.expandAction;u==="click"&&n.triggerExpandActionExpand(i,d),p==null||p(i,d)}),P(ke(n),"onNodeDoubleClick",function(i,d){var c=n.props,p=c.onDoubleClick,u=c.expandAction;u==="doubleClick"&&n.triggerExpandActionExpand(i,d),p==null||p(i,d)}),P(ke(n),"onNodeSelect",function(i,d){var c=n.state.selectedKeys,p=n.state,u=p.keyEntities,v=p.fieldNames,f=n.props,m=f.onSelect,g=f.multiple,h=d.selected,b=d[v.key],y=!h;y?g?c=kt(c,b):c=[b]:c=Rt(c,b);var S=c.map(function(C){var w=gt(u,C);return w?w.node:null}).filter(Boolean);n.setUncontrolledState({selectedKeys:c}),m==null||m(c,{event:"select",selected:y,node:d,selectedNodes:S,nativeEvent:i.nativeEvent})}),P(ke(n),"onNodeCheck",function(i,d,c){var p=n.state,u=p.keyEntities,v=p.checkedKeys,f=p.halfCheckedKeys,m=n.props,g=m.checkStrictly,h=m.onCheck,b=d.key,y,S={event:"check",node:d,checked:c,nativeEvent:i.nativeEvent};if(g){var C=c?kt(v,b):Rt(v,b),w=Rt(f,b);y={checked:C,halfChecked:w},S.checkedNodes=C.map(function(O){return gt(u,O)}).filter(Boolean).map(function(O){return O.node}),n.setUncontrolledState({checkedKeys:C})}else{var R=Gt([].concat(Me(v),[b]),!0,u),$=R.checkedKeys,k=R.halfCheckedKeys;if(!c){var x=new Set($);x.delete(b);var T=Gt(Array.from(x),{halfCheckedKeys:k},u);$=T.checkedKeys,k=T.halfCheckedKeys}y=$,S.checkedNodes=[],S.checkedNodesPositions=[],S.halfCheckedKeys=k,$.forEach(function(O){var D=gt(u,O);if(D){var M=D.node,E=D.pos;S.checkedNodes.push(M),S.checkedNodesPositions.push({node:M,pos:E})}}),n.setUncontrolledState({checkedKeys:$},!1,{halfCheckedKeys:k})}h==null||h(y,S)}),P(ke(n),"onNodeLoad",function(i){var d,c=i.key,p=n.state.keyEntities,u=gt(p,c);if(!(u!=null&&(d=u.children)!==null&&d!==void 0&&d.length)){var v=new Promise(function(f,m){n.setState(function(g){var h=g.loadedKeys,b=h===void 0?[]:h,y=g.loadingKeys,S=y===void 0?[]:y,C=n.props,w=C.loadData,R=C.onLoad;if(!w||b.includes(c)||S.includes(c))return null;var $=w(i);return $.then(function(){var k=n.state.loadedKeys,x=kt(k,c);R==null||R(x,{event:"load",node:i}),n.setUncontrolledState({loadedKeys:x}),n.setState(function(T){return{loadingKeys:Rt(T.loadingKeys,c)}}),f()}).catch(function(k){if(n.setState(function(T){return{loadingKeys:Rt(T.loadingKeys,c)}}),n.loadingRetryTimes[c]=(n.loadingRetryTimes[c]||0)+1,n.loadingRetryTimes[c]>=dd){var x=n.state.loadedKeys;Bt(!1,"Retry for `loadData` many times but still failed. No more retry."),n.setUncontrolledState({loadedKeys:kt(x,c)}),f()}m(k)}),{loadingKeys:kt(S,c)}})});return v.catch(function(){}),v}}),P(ke(n),"onNodeMouseEnter",function(i,d){var c=n.props.onMouseEnter;c==null||c({event:i,node:d})}),P(ke(n),"onNodeMouseLeave",function(i,d){var c=n.props.onMouseLeave;c==null||c({event:i,node:d})}),P(ke(n),"onNodeContextMenu",function(i,d){var c=n.props.onRightClick;c&&(i.preventDefault(),c({event:i,node:d}))}),P(ke(n),"onFocus",function(){var i=n.props.onFocus;n.setState({focused:!0});for(var d=arguments.length,c=new Array(d),p=0;p<d;p++)c[p]=arguments[p];i==null||i.apply(void 0,c)}),P(ke(n),"onBlur",function(){var i=n.props.onBlur;n.setState({focused:!1}),n.onActiveChange(null);for(var d=arguments.length,c=new Array(d),p=0;p<d;p++)c[p]=arguments[p];i==null||i.apply(void 0,c)}),P(ke(n),"getTreeNodeRequiredProps",function(){var i=n.state,d=i.expandedKeys,c=i.selectedKeys,p=i.loadedKeys,u=i.loadingKeys,v=i.checkedKeys,f=i.halfCheckedKeys,m=i.dragOverNodeKey,g=i.dropPosition,h=i.keyEntities;return{expandedKeys:d||[],selectedKeys:c||[],loadedKeys:p||[],loadingKeys:u||[],checkedKeys:v||[],halfCheckedKeys:f||[],dragOverNodeKey:m,dropPosition:g,keyEntities:h}}),P(ke(n),"setExpandedKeys",function(i){var d=n.state,c=d.treeData,p=d.fieldNames,u=_n(c,i,p);n.setUncontrolledState({expandedKeys:i,flattenNodes:u},!0)}),P(ke(n),"onNodeExpand",function(i,d){var c=n.state.expandedKeys,p=n.state,u=p.listChanging,v=p.fieldNames,f=n.props,m=f.onExpand,g=f.loadData,h=d.expanded,b=d[v.key];if(!u){var y=c.includes(b),S=!h;if(Bt(h&&y||!h&&!y,"Expand state not sync with index check"),c=S?kt(c,b):Rt(c,b),n.setExpandedKeys(c),m==null||m(c,{node:d,expanded:S,nativeEvent:i.nativeEvent}),S&&g){var C=n.onNodeLoad(d);C&&C.then(function(){var w=_n(n.state.treeData,c,v);n.setUncontrolledState({flattenNodes:w})}).catch(function(){var w=n.state.expandedKeys,R=Rt(w,b);n.setExpandedKeys(R)})}}}),P(ke(n),"onListChangeStart",function(){n.setUncontrolledState({listChanging:!0})}),P(ke(n),"onListChangeEnd",function(){setTimeout(function(){n.setUncontrolledState({listChanging:!1})})}),P(ke(n),"onActiveChange",function(i){var d=n.state.activeKey,c=n.props,p=c.onActiveChange,u=c.itemScrollOffset,v=u===void 0?0:u;d!==i&&(n.setState({activeKey:i}),i!==null&&n.scrollTo({key:i,offset:v}),p==null||p(i))}),P(ke(n),"getActiveItem",function(){var i=n.state,d=i.activeKey,c=i.flattenNodes;return d===null?null:c.find(function(p){var u=p.key;return u===d})||null}),P(ke(n),"offsetActiveKey",function(i){var d=n.state,c=d.flattenNodes,p=d.activeKey,u=c.findIndex(function(m){var g=m.key;return g===p});u===-1&&i<0&&(u=c.length),u=(u+i+c.length)%c.length;var v=c[u];if(v){var f=v.key;n.onActiveChange(f)}else n.onActiveChange(null)}),P(ke(n),"onKeyDown",function(i){var d=n.state,c=d.activeKey,p=d.expandedKeys,u=d.checkedKeys,v=d.fieldNames,f=n.props,m=f.onKeyDown,g=f.checkable,h=f.selectable;switch(i.which){case Kt.UP:{n.offsetActiveKey(-1),i.preventDefault();break}case Kt.DOWN:{n.offsetActiveKey(1),i.preventDefault();break}}var b=n.getActiveItem();if(b&&b.data){var y=n.getTreeNodeRequiredProps(),S=b.data.isLeaf===!1||!!(b.data[v.children]||[]).length,C=Qe(j(j({},cn(c,y)),{},{data:b.data,active:!0}));switch(i.which){case Kt.LEFT:{S&&p.includes(c)?n.onNodeExpand({},C):b.parent&&n.onActiveChange(b.parent.key),i.preventDefault();break}case Kt.RIGHT:{S&&!p.includes(c)?n.onNodeExpand({},C):b.children&&b.children.length&&n.onActiveChange(b.children[0].key),i.preventDefault();break}case Kt.ENTER:case Kt.SPACE:{g&&!C.disabled&&C.checkable!==!1&&!C.disableCheckbox?n.onNodeCheck({},C,!u.includes(c)):!g&&h&&!C.disabled&&C.selectable!==!1&&n.onNodeSelect({},C);break}}}m==null||m(i)}),P(ke(n),"setUncontrolledState",function(i){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!n.destroyed){var p=!1,u=!0,v={};Object.keys(i).forEach(function(f){if(n.props.hasOwnProperty(f)){u=!1;return}p=!0,v[f]=i[f]}),p&&(!d||u)&&n.setState(j(j({},v),c))}}),P(ke(n),"scrollTo",function(i){n.listRef.current.scrollTo(i)}),n}return pr(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,l=o.activeKey,s=o.itemScrollOffset,i=s===void 0?0:s;l!==void 0&&l!==this.state.activeKey&&(this.setState({activeKey:l}),l!==null&&this.scrollTo({key:l,offset:i}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o=this.state,l=o.focused,s=o.flattenNodes,i=o.keyEntities,d=o.draggingNodeKey,c=o.activeKey,p=o.dropLevelOffset,u=o.dropContainerKey,v=o.dropTargetKey,f=o.dropPosition,m=o.dragOverNodeKey,g=o.indent,h=this.props,b=h.prefixCls,y=h.className,S=h.style,C=h.showLine,w=h.focusable,R=h.tabIndex,$=R===void 0?0:R,k=h.selectable,x=h.showIcon,T=h.icon,O=h.switcherIcon,D=h.draggable,M=h.checkable,E=h.checkStrictly,I=h.disabled,N=h.motion,K=h.loadData,z=h.filterTreeNode,B=h.height,V=h.itemHeight,Z=h.scrollWidth,ne=h.virtual,A=h.titleRender,se=h.dropIndicatorRender,fe=h.onContextMenu,be=h.onScroll,re=h.direction,Q=h.rootClassName,ae=h.rootStyle,ve=Cn(this.props,{aria:!0,data:!0}),X;D&&(ut(D)==="object"?X=D:typeof D=="function"?X={nodeDraggable:D}:X={});var F={prefixCls:b,selectable:k,showIcon:x,icon:T,switcherIcon:O,draggable:X,draggingNodeKey:d,checkable:M,checkStrictly:E,disabled:I,keyEntities:i,dropLevelOffset:p,dropContainerKey:u,dropTargetKey:v,dropPosition:f,dragOverNodeKey:m,indent:g,direction:re,dropIndicatorRender:se,loadData:K,filterTreeNode:z,titleRender:A,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return a.createElement($r.Provider,{value:F},a.createElement("div",{className:te(b,y,Q,P(P(P({},"".concat(b,"-show-line"),C),"".concat(b,"-focused"),l),"".concat(b,"-active-focused"),c!==null)),style:ae},a.createElement(cd,Ee({ref:this.listRef,prefixCls:b,style:S,data:s,disabled:I,selectable:k,checkable:!!M,motion:N,dragging:d!==null,height:B,itemHeight:V,virtual:ne,focusable:w,focused:l,tabIndex:$,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:fe,onScroll:be,scrollWidth:Z},this.getTreeNodeRequiredProps(),ve))))}}],[{key:"getDerivedStateFromProps",value:function(o,l){var s=l.prevProps,i={prevProps:o};function d($){return!s&&o.hasOwnProperty($)||s&&s[$]!==o[$]}var c,p=l.fieldNames;if(d("fieldNames")&&(p=Qt(o.fieldNames),i.fieldNames=p),d("treeData")?c=o.treeData:d("children")&&(Bt(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),c=la(o.children)),c){i.treeData=c;var u=hr(c,{fieldNames:p});i.keyEntities=j(P({},Xt,Da),u.keyEntities)}var v=i.keyEntities||l.keyEntities;if(d("expandedKeys")||s&&d("autoExpandParent"))i.expandedKeys=o.autoExpandParent||!s&&o.defaultExpandParent?er(o.expandedKeys,v):o.expandedKeys;else if(!s&&o.defaultExpandAll){var f=j({},v);delete f[Xt];var m=[];Object.keys(f).forEach(function($){var k=f[$];k.children&&k.children.length&&m.push(k.key)}),i.expandedKeys=m}else!s&&o.defaultExpandedKeys&&(i.expandedKeys=o.autoExpandParent||o.defaultExpandParent?er(o.defaultExpandedKeys,v):o.defaultExpandedKeys);if(i.expandedKeys||delete i.expandedKeys,c||i.expandedKeys){var g=_n(c||l.treeData,i.expandedKeys||l.expandedKeys,p);i.flattenNodes=g}if(o.selectable&&(d("selectedKeys")?i.selectedKeys=po(o.selectedKeys,o):!s&&o.defaultSelectedKeys&&(i.selectedKeys=po(o.defaultSelectedKeys,o))),o.checkable){var h;if(d("checkedKeys")?h=jn(o.checkedKeys)||{}:!s&&o.defaultCheckedKeys?h=jn(o.defaultCheckedKeys)||{}:c&&(h=jn(o.checkedKeys)||{checkedKeys:l.checkedKeys,halfCheckedKeys:l.halfCheckedKeys}),h){var b=h,y=b.checkedKeys,S=y===void 0?[]:y,C=b.halfCheckedKeys,w=C===void 0?[]:C;if(!o.checkStrictly){var R=Gt(S,!0,v);S=R.checkedKeys,w=R.halfCheckedKeys}i.checkedKeys=S,i.halfCheckedKeys=w}}return d("loadedKeys")&&(i.loadedKeys=o.loadedKeys),i}}]),r}(a.Component);P(Rr,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:Hc,allowDrop:function(){return!0},expandAction:!1});P(Rr,"TreeNode",mn);const ud=({treeCls:e,treeNodeCls:t,directoryNodeSelectedBg:r,directoryNodeSelectedColor:n,motionDurationMid:o,borderRadius:l,controlItemBgHover:s})=>({[`${e}${e}-directory ${t}`]:{[`${e}-node-content-wrapper`]:{position:"static",[`> *:not(${e}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${o}`,content:'""',borderRadius:l},"&:hover:before":{background:s}},[`${e}-switcher, ${e}-checkbox, ${e}-draggable-icon`]:{zIndex:1},"&-selected":{[`${e}-switcher, ${e}-draggable-icon`]:{color:n},[`${e}-node-content-wrapper`]:{color:n,background:"transparent","&:before, &:hover:before":{background:r}}}}}),fd=new wl("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),vd=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),md=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${Y(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),pd=(e,t)=>{const{treeCls:r,treeNodeCls:n,treeNodePadding:o,titleHeight:l,indentSize:s,nodeSelectedBg:i,nodeHoverBg:d,colorTextQuaternary:c,controlItemBgActiveDisabled:p}=t;return{[r]:Object.assign(Object.assign({},pn(t)),{"--rc-virtual-list-scrollbar-bg":t.colorSplit,background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${r}-rtl ${r}-switcher_close ${r}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${r}-active-focused)`]:Object.assign({},jo(t)),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${n}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:fd,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[n]:{display:"flex",alignItems:"flex-start",marginBottom:o,lineHeight:Y(l),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:o},[`&-disabled ${r}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${r}-checkbox-disabled + ${r}-node-selected,&${n}-disabled${n}-selected ${r}-node-content-wrapper`]:{backgroundColor:p},[`${r}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${n}-disabled)`]:{[`${r}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${r}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${n}-disabled).filter-node ${r}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:l,textAlign:"center",visibility:"visible",color:c},[`&${n}-disabled ${r}-draggable-icon`]:{visibility:"hidden"}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:s}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher, ${r}-checkbox`]:{marginInlineEnd:t.calc(t.calc(l).sub(t.controlInteractiveSize)).div(2).equal()},[`${r}-switcher`]:Object.assign(Object.assign({},vd(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:l,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:l,height:l,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${r}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(l).div(2).equal()).mul(.8).equal(),height:t.calc(l).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${r}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:l,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},md(e,t)),{"&:hover":{backgroundColor:d},[`&${r}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:i},[`${r}-iconEle`]:{display:"inline-block",width:l,height:l,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${r}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${n}-leaf-last ${r}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${Y(t.calc(l).div(2).equal())} !important`}})}},gd=(e,t,r=!0)=>{const n=`.${e}`,o=`${n}-treenode`,l=t.calc(t.paddingXS).div(2).equal(),s=un(t,{treeCls:n,treeNodeCls:o,treeNodePadding:l});return[pd(e,s),r&&ud(s)].filter(Boolean)},hd=e=>{const{controlHeightSM:t,controlItemBgHover:r,controlItemBgActive:n}=e,o=t;return{titleHeight:o,indentSize:o,nodeHoverBg:r,nodeHoverColor:e.colorText,nodeSelectedBg:n,nodeSelectedColor:e.colorText}},bd=e=>{const{colorTextLightSolid:t,colorPrimary:r}=e;return Object.assign(Object.assign({},hd(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:r})},yd=In("Tree",(e,{prefixCls:t})=>[{[e.componentCls]:Wl(`${t}-checkbox`,e)},gd(t,e),Zo(e)],bd),No=4;function Sd(e){const{dropPosition:t,dropLevelOffset:r,prefixCls:n,indent:o,direction:l="ltr"}=e,s=l==="ltr"?"left":"right",i=l==="ltr"?"right":"left",d={[s]:-r*o+No,[i]:0};switch(t){case-1:d.top=-3;break;case 1:d.bottom=-3;break;default:d.bottom=-3,d[s]=o+No;break}return De.createElement("div",{style:d,className:`${n}-drop-indicator`})}const xd=e=>{const{prefixCls:t,switcherIcon:r,treeNodeProps:n,showLine:o,switcherLoadingIcon:l}=e,{isLeaf:s,expanded:i,loading:d}=n;if(d)return a.isValidElement(l)?l:a.createElement($l,{className:`${t}-switcher-loading-icon`});let c;if(o&&typeof o=="object"&&(c=o.showLeafIcon),s){if(!o)return null;if(typeof c!="boolean"&&c){const v=typeof c=="function"?c(n):c,f=`${t}-switcher-line-custom-icon`;return a.isValidElement(v)?Jt(v,{className:te(v.props.className||"",f)}):v}return c?a.createElement(ca,{className:`${t}-switcher-line-icon`}):a.createElement("span",{className:`${t}-switcher-leaf-line`})}const p=`${t}-switcher-icon`,u=typeof r=="function"?r(n):r;return a.isValidElement(u)?Jt(u,{className:te(u.props.className||"",p)}):u!==void 0?u:o?i?a.createElement(Vi,{className:`${t}-switcher-line-icon`}):a.createElement(Yi,{className:`${t}-switcher-line-icon`}):a.createElement(Ci,{className:p})},Ka=De.forwardRef((e,t)=>{var r;const{getPrefixCls:n,direction:o,virtual:l,tree:s}=De.useContext(Ht),{prefixCls:i,className:d,showIcon:c=!1,showLine:p,switcherIcon:u,switcherLoadingIcon:v,blockNode:f=!1,children:m,checkable:g=!1,selectable:h=!0,draggable:b,motion:y,style:S}=e,C=n("tree",i),w=n(),R=y??Object.assign(Object.assign({},Xo(w)),{motionAppear:!1}),$=Object.assign(Object.assign({},e),{checkable:g,selectable:h,showIcon:c,motion:R,blockNode:f,showLine:!!p,dropIndicatorRender:Sd}),[k,x,T]=yd(C),[,O]=fr(),D=O.paddingXS/2+(((r=O.Tree)===null||r===void 0?void 0:r.titleHeight)||O.controlHeightSM),M=De.useMemo(()=>{if(!b)return!1;let I={};switch(typeof b){case"function":I.nodeDraggable=b;break;case"object":I=Object.assign({},b);break}return I.icon!==!1&&(I.icon=I.icon||De.createElement(Ai,null)),I},[b]),E=I=>De.createElement(xd,{prefixCls:C,switcherIcon:u,switcherLoadingIcon:v,treeNodeProps:I,showLine:p});return k(De.createElement(Rr,Object.assign({itemHeight:D,ref:t,virtual:l},$,{style:Object.assign(Object.assign({},s==null?void 0:s.style),S),prefixCls:C,className:te({[`${C}-icon-hide`]:!c,[`${C}-block-node`]:f,[`${C}-unselectable`]:!h,[`${C}-rtl`]:o==="rtl"},s==null?void 0:s.className,d,x,T),direction:o,checkable:g&&De.createElement("span",{className:`${C}-checkbox-inner`}),selectable:h,switcherIcon:E,draggable:M}),m))}),ko=0,An=1,Oo=2;function Ir(e,t,r){const{key:n,children:o}=r;function l(s){const i=s[n],d=s[o];t(i,s)!==!1&&Ir(d||[],t,r)}e.forEach(l)}function Cd({treeData:e,expandedKeys:t,startKey:r,endKey:n,fieldNames:o}){const l=[];let s=ko;if(r&&r===n)return[r];if(!r||!n)return[];function i(d){return d===r||d===n}return Ir(e,d=>{if(s===Oo)return!1;if(i(d)){if(l.push(d),s===ko)s=An;else if(s===An)return s=Oo,!1}else s===An&&l.push(d);return t.includes(d)},Qt(o)),l}function Fn(e,t,r){const n=Me(t),o=[];return Ir(e,(l,s)=>{const i=n.indexOf(l);return i!==-1&&(o.push(s),n.splice(i,1)),!!n.length},Qt(r)),o}var To=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function wd(e){const{isLeaf:t,expanded:r}=e;return t?a.createElement(ca,null):r?a.createElement(Bi,null):a.createElement(zi,null)}function Mo({treeData:e,children:t}){return e||la(t)}const $d=(e,t)=>{var{defaultExpandAll:r,defaultExpandParent:n,defaultExpandedKeys:o}=e,l=To(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const s=a.useRef(null),i=a.useRef(null),d=()=>{const{keyEntities:k}=hr(Mo(l));let x;return r?x=Object.keys(k):n?x=er(l.expandedKeys||o||[],k):x=l.expandedKeys||o||[],x},[c,p]=a.useState(l.selectedKeys||l.defaultSelectedKeys||[]),[u,v]=a.useState(()=>d());a.useEffect(()=>{"selectedKeys"in l&&p(l.selectedKeys)},[l.selectedKeys]),a.useEffect(()=>{"expandedKeys"in l&&v(l.expandedKeys)},[l.expandedKeys]);const f=(k,x)=>{var T;return"expandedKeys"in l||v(k),(T=l.onExpand)===null||T===void 0?void 0:T.call(l,k,x)},m=(k,x)=>{var T;const{multiple:O,fieldNames:D}=l,{node:M,nativeEvent:E}=x,{key:I=""}=M,N=Mo(l),K=Object.assign(Object.assign({},x),{selected:!0}),z=(E==null?void 0:E.ctrlKey)||(E==null?void 0:E.metaKey),B=E==null?void 0:E.shiftKey;let V;O&&z?(V=k,s.current=I,i.current=V,K.selectedNodes=Fn(N,V,D)):O&&B?(V=Array.from(new Set([].concat(Me(i.current||[]),Me(Cd({treeData:N,expandedKeys:u,startKey:I,endKey:s.current,fieldNames:D}))))),K.selectedNodes=Fn(N,V,D)):(V=[I],s.current=I,i.current=V,K.selectedNodes=Fn(N,V,D)),(T=l.onSelect)===null||T===void 0||T.call(l,V,K),"selectedKeys"in l||p(V)},{getPrefixCls:g,direction:h}=a.useContext(Ht),{prefixCls:b,className:y,showIcon:S=!0,expandAction:C="click"}=l,w=To(l,["prefixCls","className","showIcon","expandAction"]),R=g("tree",b),$=te(`${R}-directory`,{[`${R}-directory-rtl`]:h==="rtl"},y);return a.createElement(Ka,Object.assign({icon:wd,ref:t,blockNode:!0},w,{showIcon:S,expandAction:C,prefixCls:R,className:$,expandedKeys:u,selectedKeys:c,onSelect:m,onExpand:f}))},Ed=a.forwardRef($d),Nr=Ka;Nr.DirectoryTree=Ed;Nr.TreeNode=mn;const Po=e=>{const{value:t,filterSearch:r,tablePrefixCls:n,locale:o,onChange:l}=e;return r?a.createElement("div",{className:`${n}-filter-dropdown-search`},a.createElement(Ll,{prefix:a.createElement(Kl,null),placeholder:o.filterSearchPlaceholder,onChange:l,value:t,htmlSize:1,className:`${n}-filter-dropdown-search-input`})):null},Rd=e=>{const{keyCode:t}=e;t===Kt.ENTER&&e.stopPropagation()},Id=a.forwardRef((e,t)=>a.createElement("div",{className:e.className,onClick:r=>r.stopPropagation(),onKeyDown:Rd,ref:t},e.children));function Zt(e){let t=[];return(e||[]).forEach(({value:r,children:n})=>{t.push(r),n&&(t=[].concat(Me(t),Me(Zt(n))))}),t}function Nd(e){return e.some(({children:t})=>t)}function Ba(e,t){return typeof t=="string"||typeof t=="number"?t==null?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()):!1}function La({filters:e,prefixCls:t,filteredKeys:r,filterMultiple:n,searchValue:o,filterSearch:l}){return e.map((s,i)=>{const d=String(s.value);if(s.children)return{key:d||i,label:s.text,popupClassName:`${t}-dropdown-submenu`,children:La({filters:s.children,prefixCls:t,filteredKeys:r,filterMultiple:n,searchValue:o,filterSearch:l})};const c=n?wn:Qo,p={key:s.value!==void 0?d:i,label:a.createElement(a.Fragment,null,a.createElement(c,{checked:r.includes(d)}),a.createElement("span",null,s.text))};return o.trim()?typeof l=="function"?l(o,s)?p:null:Ba(o,s.text)?p:null:p})}function Wn(e){return e||[]}const kd=e=>{var t,r,n,o;const{tablePrefixCls:l,prefixCls:s,column:i,dropdownPrefixCls:d,columnKey:c,filterOnClose:p,filterMultiple:u,filterMode:v="menu",filterSearch:f=!1,filterState:m,triggerFilter:g,locale:h,children:b,getPopupContainer:y,rootClassName:S}=e,{filterResetToDefaultFilteredValue:C,defaultFilteredValue:w,filterDropdownProps:R={},filterDropdownOpen:$,filterDropdownVisible:k,onFilterDropdownVisibleChange:x,onFilterDropdownOpenChange:T}=i,[O,D]=a.useState(!1),M=!!(m&&(!((t=m.filteredKeys)===null||t===void 0)&&t.length||m.forceFiltered)),E=W=>{var q;D(W),(q=R.onOpenChange)===null||q===void 0||q.call(R,W),T==null||T(W),x==null||x(W)},I=(o=(n=(r=R.open)!==null&&r!==void 0?r:$)!==null&&n!==void 0?n:k)!==null&&o!==void 0?o:O,N=m==null?void 0:m.filteredKeys,[K,z]=Lc(Wn(N)),B=({selectedKeys:W})=>{z(W)},V=(W,{node:q,checked:ue})=>{B(u?{selectedKeys:W}:{selectedKeys:ue&&q.key?[q.key]:[]})};a.useEffect(()=>{O&&B({selectedKeys:Wn(N)})},[N]);const[Z,ne]=a.useState([]),A=W=>{ne(W)},[se,fe]=a.useState(""),be=W=>{const{value:q}=W.target;fe(q)};a.useEffect(()=>{O||fe("")},[O]);const re=W=>{const q=W!=null&&W.length?W:null;if(q===null&&(!m||!m.filteredKeys)||vn(q,m==null?void 0:m.filteredKeys,!0))return null;g({column:i,key:c,filteredKeys:q})},Q=()=>{E(!1),re(K())},ae=({confirm:W,closeDropdown:q}={confirm:!1,closeDropdown:!1})=>{W&&re([]),q&&E(!1),fe(""),z(C?(w||[]).map(ue=>String(ue)):[])},ve=({closeDropdown:W}={closeDropdown:!0})=>{W&&E(!1),re(K())},X=(W,q)=>{q.source==="trigger"&&(W&&N!==void 0&&z(Wn(N)),E(W),!W&&!i.filterDropdown&&p&&Q())},F=te({[`${d}-menu-without-submenu`]:!Nd(i.filters||[])}),H=W=>{if(W.target.checked){const q=Zt(i==null?void 0:i.filters).map(ue=>String(ue));z(q)}else z([])},U=({filters:W})=>(W||[]).map((q,ue)=>{const xe=String(q.value),he={title:q.text,key:q.value!==void 0?xe:String(ue)};return q.children&&(he.children=U({filters:q.children})),he}),le=W=>{var q;return Object.assign(Object.assign({},W),{text:W.title,value:W.key,children:((q=W.children)===null||q===void 0?void 0:q.map(ue=>le(ue)))||[]})};let de;const{direction:ie,renderEmpty:Ce}=a.useContext(Ht);if(typeof i.filterDropdown=="function")de=i.filterDropdown({prefixCls:`${d}-custom`,setSelectedKeys:W=>B({selectedKeys:W}),selectedKeys:K(),confirm:ve,clearFilters:ae,filters:i.filters,visible:I,close:()=>{E(!1)}});else if(i.filterDropdown)de=i.filterDropdown;else{const W=K()||[],q=()=>{var xe,he;const Oe=(xe=Ce==null?void 0:Ce("Table.filter"))!==null&&xe!==void 0?xe:a.createElement(Vr,{image:Vr.PRESENTED_IMAGE_SIMPLE,description:h.filterEmptyText,styles:{image:{height:24}},style:{margin:0,padding:"16px 0"}});if((i.filters||[]).length===0)return Oe;if(v==="tree")return a.createElement(a.Fragment,null,a.createElement(Po,{filterSearch:f,value:se,onChange:be,tablePrefixCls:l,locale:h}),a.createElement("div",{className:`${l}-filter-dropdown-tree`},u?a.createElement(wn,{checked:W.length===Zt(i.filters).length,indeterminate:W.length>0&&W.length<Zt(i.filters).length,className:`${l}-filter-dropdown-checkall`,onChange:H},(he=h==null?void 0:h.filterCheckall)!==null&&he!==void 0?he:h==null?void 0:h.filterCheckAll):null,a.createElement(Nr,{checkable:!0,selectable:!1,blockNode:!0,multiple:u,checkStrictly:!u,className:`${d}-menu`,onCheck:V,checkedKeys:W,selectedKeys:W,showIcon:!1,treeData:U({filters:i.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:se.trim()?me=>typeof f=="function"?f(se,le(me)):Ba(se,me.title):void 0})));const Re=La({filters:i.filters||[],filterSearch:f,prefixCls:s,filteredKeys:K(),filterMultiple:u,searchValue:se}),ze=Re.every(me=>me===null);return a.createElement(a.Fragment,null,a.createElement(Po,{filterSearch:f,value:se,onChange:be,tablePrefixCls:l,locale:h}),ze?Oe:a.createElement(tn,{selectable:!0,multiple:u,prefixCls:`${d}-menu`,className:F,onSelect:B,onDeselect:B,selectedKeys:W,getPopupContainer:y,openKeys:Z,onOpenChange:A,items:Re}))},ue=()=>C?vn((w||[]).map(xe=>String(xe)),W,!0):W.length===0;de=a.createElement(a.Fragment,null,q(),a.createElement("div",{className:`${s}-dropdown-btns`},a.createElement(Tt,{type:"link",size:"small",disabled:ue(),onClick:()=>ae()},h.filterReset),a.createElement(Tt,{type:"primary",size:"small",onClick:Q},h.filterConfirm)))}i.filterDropdown&&(de=a.createElement(ra,{selectable:void 0},de)),de=a.createElement(Id,{className:`${s}-dropdown`},de);const we=Uo({trigger:["click"],placement:ie==="rtl"?"bottomLeft":"bottomRight",children:(()=>{let W;return typeof i.filterIcon=="function"?W=i.filterIcon(M):i.filterIcon?W=i.filterIcon:W=a.createElement(Pi,null),a.createElement("span",{role:"button",tabIndex:-1,className:te(`${s}-trigger`,{active:M}),onClick:q=>{q.stopPropagation()}},W)})(),getPopupContainer:y},Object.assign(Object.assign({},R),{rootClassName:te(S,R.rootClassName),open:I,onOpenChange:X,popupRender:()=>typeof(R==null?void 0:R.dropdownRender)=="function"?R.dropdownRender(de):de}));return a.createElement("div",{className:`${s}-column`},a.createElement("span",{className:`${l}-column-title`},b),a.createElement(br,Object.assign({},we)))},lr=(e,t,r)=>{let n=[];return(e||[]).forEach((o,l)=>{var s;const i=an(l,r),d=o.filterDropdown!==void 0;if(o.filters||d||"onFilter"in o)if("filteredValue"in o){let c=o.filteredValue;d||(c=(s=c==null?void 0:c.map(String))!==null&&s!==void 0?s:c),n.push({column:o,key:Lt(o,i),filteredKeys:c,forceFiltered:o.filtered})}else n.push({column:o,key:Lt(o,i),filteredKeys:t&&o.defaultFilteredValue?o.defaultFilteredValue:void 0,forceFiltered:o.filtered});"children"in o&&(n=[].concat(Me(n),Me(lr(o.children,t,i))))}),n};function Ha(e,t,r,n,o,l,s,i,d){return r.map((c,p)=>{const u=an(p,i),{filterOnClose:v=!0,filterMultiple:f=!0,filterMode:m,filterSearch:g}=c;let h=c;if(h.filters||h.filterDropdown){const b=Lt(h,u),y=n.find(({key:S})=>b===S);h=Object.assign(Object.assign({},h),{title:S=>a.createElement(kd,{tablePrefixCls:e,prefixCls:`${e}-filter`,dropdownPrefixCls:t,column:h,columnKey:b,filterState:y,filterOnClose:v,filterMultiple:f,filterMode:m,filterSearch:g,triggerFilter:l,locale:o,getPopupContainer:s,rootClassName:d},Mn(c.title,S))})}return"children"in h&&(h=Object.assign(Object.assign({},h),{children:Ha(e,t,h.children,n,o,l,s,u,d)})),h})}const Do=e=>{const t={};return e.forEach(({key:r,filteredKeys:n,column:o})=>{const l=r,{filters:s,filterDropdown:i}=o;if(i)t[l]=n||null;else if(Array.isArray(n)){const d=Zt(s);t[l]=d.filter(c=>n.includes(String(c)))}else t[l]=null}),t},ir=(e,t,r)=>t.reduce((o,l)=>{const{column:{onFilter:s,filters:i},filteredKeys:d}=l;return s&&d&&d.length?o.map(c=>Object.assign({},c)).filter(c=>d.some(p=>{const u=Zt(i),v=u.findIndex(m=>String(m)===String(p)),f=v!==-1?u[v]:p;return c[r]&&(c[r]=ir(c[r],t,r)),s(f,c)})):o},e),za=e=>e.flatMap(t=>"children"in t?[t].concat(Me(za(t.children||[]))):[t]),Od=e=>{const{prefixCls:t,dropdownPrefixCls:r,mergedColumns:n,onFilterChange:o,getPopupContainer:l,locale:s,rootClassName:i}=e;Nn();const d=a.useMemo(()=>za(n||[]),[n]),[c,p]=a.useState(()=>lr(d,!0)),u=a.useMemo(()=>{const g=lr(d,!1);if(g.length===0)return g;let h=!0;if(g.forEach(({filteredKeys:b})=>{b!==void 0&&(h=!1)}),h){const b=(d||[]).map((y,S)=>Lt(y,an(S)));return c.filter(({key:y})=>b.includes(y)).map(y=>{const S=d[b.findIndex(C=>C===y.key)];return Object.assign(Object.assign({},y),{column:Object.assign(Object.assign({},y.column),S),forceFiltered:S.filtered})})}return g},[d,c]),v=a.useMemo(()=>Do(u),[u]),f=g=>{const h=u.filter(({key:b})=>b!==g.key);h.push(g),p(h),o(Do(h),h)};return[g=>Ha(t,r,g,u,s,f,l,void 0,i),u,v]},Td=(e,t,r)=>{const n=a.useRef({});function o(l){var s;if(!n.current||n.current.data!==e||n.current.childrenColumnName!==t||n.current.getRowKey!==r){let d=function(c){c.forEach((p,u)=>{const v=r(p,u);i.set(v,p),p&&typeof p=="object"&&t in p&&d(p[t]||[])})};const i=new Map;d(e),n.current={data:e,childrenColumnName:t,kvMap:i,getRowKey:r}}return(s=n.current.kvMap)===null||s===void 0?void 0:s.get(l)}return[o]};var Md=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const _a=10;function Pd(e,t){const r={current:e.current,pageSize:e.pageSize};return Object.keys(t&&typeof t=="object"?t:{}).forEach(o=>{const l=e[o];typeof l!="function"&&(r[o]=l)}),r}function Dd(e,t,r){const n=r&&typeof r=="object"?r:{},{total:o=0}=n,l=Md(n,["total"]),[s,i]=a.useState(()=>({current:"defaultCurrent"in l?l.defaultCurrent:1,pageSize:"defaultPageSize"in l?l.defaultPageSize:_a})),d=Uo(s,l,{total:o>0?o:e}),c=Math.ceil((o||e)/d.pageSize);d.current>c&&(d.current=c||1);const p=(v,f)=>{i({current:v??1,pageSize:f||d.pageSize})},u=(v,f)=>{var m;r&&((m=r.onChange)===null||m===void 0||m.call(r,v,f)),p(v,f),t(v,f||(d==null?void 0:d.pageSize))};return r===!1?[{},()=>{}]:[Object.assign(Object.assign({},d),{onChange:u}),p]}const Sn="ascend",Vn="descend",Rn=e=>typeof e.sorter=="object"&&typeof e.sorter.multiple=="number"?e.sorter.multiple:!1,Ko=e=>typeof e=="function"?e:e&&typeof e=="object"&&e.compare?e.compare:!1,Kd=(e,t)=>t?e[e.indexOf(t)+1]:e[0],sr=(e,t,r)=>{let n=[];const o=(l,s)=>{n.push({column:l,key:Lt(l,s),multiplePriority:Rn(l),sortOrder:l.sortOrder})};return(e||[]).forEach((l,s)=>{const i=an(s,r);l.children?("sortOrder"in l&&o(l,i),n=[].concat(Me(n),Me(sr(l.children,t,i)))):l.sorter&&("sortOrder"in l?o(l,i):t&&l.defaultSortOrder&&n.push({column:l,key:Lt(l,i),multiplePriority:Rn(l),sortOrder:l.defaultSortOrder}))}),n},ja=(e,t,r,n,o,l,s,i)=>(t||[]).map((c,p)=>{const u=an(p,i);let v=c;if(v.sorter){const f=v.sortDirections||o,m=v.showSorterTooltip===void 0?s:v.showSorterTooltip,g=Lt(v,u),h=r.find(({key:x})=>x===g),b=h?h.sortOrder:null,y=Kd(f,b);let S;if(c.sortIcon)S=c.sortIcon({sortOrder:b});else{const x=f.includes(Sn)&&a.createElement(Ni,{className:te(`${e}-column-sorter-up`,{active:b===Sn})}),T=f.includes(Vn)&&a.createElement(Ei,{className:te(`${e}-column-sorter-down`,{active:b===Vn})});S=a.createElement("span",{className:te(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!!(x&&T)})},a.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},x,T))}const{cancelSort:C,triggerAsc:w,triggerDesc:R}=l||{};let $=C;y===Vn?$=R:y===Sn&&($=w);const k=typeof m=="object"?Object.assign({title:$},m):{title:$};v=Object.assign(Object.assign({},v),{className:te(v.className,{[`${e}-column-sort`]:b}),title:x=>{const T=`${e}-column-sorters`,O=a.createElement("span",{className:`${e}-column-title`},Mn(c.title,x)),D=a.createElement("div",{className:T},O,S);return m?typeof m!="boolean"&&(m==null?void 0:m.target)==="sorter-icon"?a.createElement("div",{className:`${T} ${e}-column-sorters-tooltip-target-sorter`},O,a.createElement(qn,Object.assign({},k),S)):a.createElement(qn,Object.assign({},k),D):D},onHeaderCell:x=>{var T;const O=((T=c.onHeaderCell)===null||T===void 0?void 0:T.call(c,x))||{},D=O.onClick,M=O.onKeyDown;O.onClick=N=>{n({column:c,key:g,sortOrder:y,multiplePriority:Rn(c)}),D==null||D(N)},O.onKeyDown=N=>{N.keyCode===Kt.ENTER&&(n({column:c,key:g,sortOrder:y,multiplePriority:Rn(c)}),M==null||M(N))};const E=Bc(c.title,{}),I=E==null?void 0:E.toString();return b&&(O["aria-sort"]=b==="ascend"?"ascending":"descending"),O["aria-label"]=I||"",O.className=te(O.className,`${e}-column-has-sorters`),O.tabIndex=0,c.ellipsis&&(O.title=(E??"").toString()),O}})}return"children"in v&&(v=Object.assign(Object.assign({},v),{children:ja(e,v.children,r,n,o,l,s,u)})),v}),Bo=e=>{const{column:t,sortOrder:r}=e;return{column:t,order:r,field:t.dataIndex,columnKey:t.key}},Lo=e=>{const t=e.filter(({sortOrder:r})=>r).map(Bo);if(t.length===0&&e.length){const r=e.length-1;return Object.assign(Object.assign({},Bo(e[r])),{column:void 0,order:void 0,field:void 0,columnKey:void 0})}return t.length<=1?t[0]||{}:t},cr=(e,t,r)=>{const n=t.slice().sort((s,i)=>i.multiplePriority-s.multiplePriority),o=e.slice(),l=n.filter(({column:{sorter:s},sortOrder:i})=>Ko(s)&&i);return l.length?o.sort((s,i)=>{for(let d=0;d<l.length;d+=1){const c=l[d],{column:{sorter:p},sortOrder:u}=c,v=Ko(p);if(v&&u){const f=v(s,i,u);if(f!==0)return u===Sn?f:-f}}return 0}).map(s=>{const i=s[r];return i?Object.assign(Object.assign({},s),{[r]:cr(i,t,r)}):s}):o},Bd=e=>{const{prefixCls:t,mergedColumns:r,sortDirections:n,tableLocale:o,showSorterTooltip:l,onSorterChange:s}=e,[i,d]=a.useState(()=>sr(r,!0)),c=(g,h)=>{const b=[];return g.forEach((y,S)=>{const C=an(S,h);if(b.push(Lt(y,C)),Array.isArray(y.children)){const w=c(y.children,C);b.push.apply(b,Me(w))}}),b},p=a.useMemo(()=>{let g=!0;const h=sr(r,!1);if(!h.length){const C=c(r);return i.filter(({key:w})=>C.includes(w))}const b=[];function y(C){g?b.push(C):b.push(Object.assign(Object.assign({},C),{sortOrder:null}))}let S=null;return h.forEach(C=>{S===null?(y(C),C.sortOrder&&(C.multiplePriority===!1?g=!1:S=!0)):(S&&C.multiplePriority!==!1||(g=!1),y(C))}),b},[r,i]),u=a.useMemo(()=>{var g,h;const b=p.map(({column:y,sortOrder:S})=>({column:y,order:S}));return{sortColumns:b,sortColumn:(g=b[0])===null||g===void 0?void 0:g.column,sortOrder:(h=b[0])===null||h===void 0?void 0:h.order}},[p]),v=g=>{let h;g.multiplePriority===!1||!p.length||p[0].multiplePriority===!1?h=[g]:h=[].concat(Me(p.filter(({key:b})=>b!==g.key)),[g]),d(h),s(Lo(h),h)};return[g=>ja(t,g,p,v,n,o,l),p,u,()=>Lo(p)]},Aa=(e,t)=>e.map(n=>{const o=Object.assign({},n);return o.title=Mn(n.title,t),"children"in o&&(o.children=Aa(o.children,t)),o}),Ld=e=>[a.useCallback(r=>Aa(r,e),[e])],Hd=wa((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),zd=Na((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),_d=e=>{const{componentCls:t,lineWidth:r,lineType:n,tableBorderColor:o,tableHeaderBg:l,tablePaddingVertical:s,tablePaddingHorizontal:i,calc:d}=e,c=`${Y(r)} ${n} ${o}`,p=(u,v,f)=>({[`&${t}-${u}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${Y(d(v).mul(-1).equal())}
              ${Y(d(d(f).add(r)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:c,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:c,borderTop:c,[`
            > ${t}-content,
            > ${t}-header,
            > ${t}-body,
            > ${t}-summary
          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:c},"> thead":{"> tr:not(:last-child) > th":{borderBottom:c},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:c}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${Y(d(s).mul(-1).equal())} ${Y(d(d(i).add(r)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:r,bottom:0,borderInlineEnd:c,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`
                > tr${t}-expanded-row,
                > tr${t}-placeholder
              `]:{"> th, > td":{borderInlineEnd:0}}}}}},p("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),p("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:c,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${Y(r)} 0 ${Y(r)} ${l}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:c}}}},jd=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},Ao),{wordBreak:"keep-all",[`
          &${t}-cell-fix-left-last,
          &${t}-cell-fix-right-first
        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},Ad=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:e.colorBgContainer}}}}},Fd=e=>{const{componentCls:t,antCls:r,motionDurationSlow:n,lineWidth:o,paddingXS:l,lineType:s,tableBorderColor:i,tableExpandIconBg:d,tableExpandColumnWidth:c,borderRadius:p,tablePaddingVertical:u,tablePaddingHorizontal:v,tableExpandedRowBg:f,paddingXXS:m,expandIconMarginTop:g,expandIconSize:h,expandIconHalfInner:b,expandIconScale:y,calc:S}=e,C=`${Y(o)} ${s} ${i}`,w=S(m).sub(o).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:c},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},El(e)),{position:"relative",float:"left",width:h,height:h,color:"inherit",lineHeight:Y(h),background:d,border:C,borderRadius:p,transform:`scale(${y})`,"&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${n} ease-out`,content:'""'},"&::before":{top:b,insetInlineEnd:w,insetInlineStart:w,height:o},"&::after":{top:w,bottom:w,insetInlineStart:b,width:o,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:g,marginInlineEnd:l},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:f}},[`${r}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${Y(S(u).mul(-1).equal())} ${Y(S(v).mul(-1).equal())}`,padding:`${Y(u)} ${Y(v)}`}}}},Wd=e=>{const{componentCls:t,antCls:r,iconCls:n,tableFilterDropdownWidth:o,tableFilterDropdownSearchWidth:l,paddingXXS:s,paddingXS:i,colorText:d,lineWidth:c,lineType:p,tableBorderColor:u,headerIconColor:v,fontSizeSM:f,tablePaddingHorizontal:m,borderRadius:g,motionDurationSlow:h,colorIcon:b,colorPrimary:y,tableHeaderFilterActiveBg:S,colorTextDisabled:C,tableFilterDropdownBg:w,tableFilterDropdownHeight:R,controlItemBgHover:$,controlItemBgActive:k,boxShadowSecondary:x,filterDropdownMenuBg:T,calc:O}=e,D=`${r}-dropdown`,M=`${t}-filter-dropdown`,E=`${r}-tree`,I=`${Y(c)} ${p} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:O(s).mul(-1).equal(),marginInline:`${Y(s)} ${Y(O(m).div(2).mul(-1).equal())}`,padding:`0 ${Y(s)}`,color:v,fontSize:f,borderRadius:g,cursor:"pointer",transition:`all ${h}`,"&:hover":{color:b,background:S},"&.active":{color:y}}}},{[`${r}-dropdown`]:{[M]:Object.assign(Object.assign({},pn(e)),{minWidth:o,backgroundColor:w,borderRadius:g,boxShadow:x,overflow:"hidden",[`${D}-menu`]:{maxHeight:R,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:T,"&:empty::after":{display:"block",padding:`${Y(i)} 0`,color:C,fontSize:f,textAlign:"center",content:'"Not Found"'}},[`${M}-tree`]:{paddingBlock:`${Y(i)} 0`,paddingInline:i,[E]:{padding:0},[`${E}-treenode ${E}-node-content-wrapper:hover`]:{backgroundColor:$},[`${E}-treenode-checkbox-checked ${E}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:k}}},[`${M}-search`]:{padding:i,borderBottom:I,"&-input":{input:{minWidth:l},[n]:{color:C}}},[`${M}-checkall`]:{width:"100%",marginBottom:s,marginInlineStart:s},[`${M}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${Y(O(i).sub(c).equal())} ${Y(i)}`,overflow:"hidden",borderTop:I}})}},{[`${r}-dropdown ${M}, ${M}-submenu`]:{[`${r}-checkbox-wrapper + span`]:{paddingInlineStart:i,color:d},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},Vd=e=>{const{componentCls:t,lineWidth:r,colorSplit:n,motionDurationSlow:o,zIndexTableFixed:l,tableBg:s,zIndexTableSticky:i,calc:d}=e,c=n;return{[`${t}-wrapper`]:{[`
        ${t}-cell-fix-left,
        ${t}-cell-fix-right
      `]:{position:"sticky !important",zIndex:l,background:s},[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after
      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:d(r).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{position:"absolute",top:0,bottom:d(r).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:d(i).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${c}`},[`
          ${t}-cell-fix-left-first::after,
          ${t}-cell-fix-left-last::after
        `]:{boxShadow:`inset 10px 0 8px -8px ${c}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${c}`},[`
          ${t}-cell-fix-right-first::after,
          ${t}-cell-fix-right-last::after
        `]:{boxShadow:`inset -10px 0 8px -8px ${c}`}},[`${t}-fixed-column-gapped`]:{[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after,
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{boxShadow:"none"}}}}},Xd=e=>{const{componentCls:t,antCls:r,margin:n}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${r}-pagination`]:{margin:`${Y(n)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},qd=e=>{const{componentCls:t,tableRadius:r}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${Y(r)} ${Y(r)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:r,borderStartEndRadius:r,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:r},"> *:last-child":{borderStartEndRadius:r}}},"&-footer":{borderRadius:`0 0 ${Y(r)} ${Y(r)}`}}}}},Yd=e=>{const{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},Ud=e=>{const{componentCls:t,antCls:r,iconCls:n,fontSizeIcon:o,padding:l,paddingXS:s,headerIconColor:i,headerIconHoverColor:d,tableSelectionColumnWidth:c,tableSelectedRowBg:p,tableSelectedRowHoverBg:u,tableRowHoverBg:v,tablePaddingHorizontal:f,calc:m}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:c,[`&${t}-selection-col-with-dropdown`]:{width:m(c).add(o).add(m(l).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:m(c).add(m(s).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:m(c).add(o).add(m(l).div(4)).add(m(s).mul(2)).equal()}},[`
        table tr th${t}-selection-column,
        table tr td${t}-selection-column,
        ${t}-selection-column
      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${r}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:m(e.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:Y(m(f).div(4).equal()),[n]:{color:i,fontSize:o,verticalAlign:"baseline","&:hover":{color:d}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:p,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:v}}}}}},Gd=e=>{const{componentCls:t,tableExpandColumnWidth:r,calc:n}=e,o=(l,s,i,d)=>({[`${t}${t}-${l}`]:{fontSize:d,[`
        ${t}-title,
        ${t}-footer,
        ${t}-cell,
        ${t}-thead > tr > th,
        ${t}-tbody > tr > th,
        ${t}-tbody > tr > td,
        tfoot > tr > th,
        tfoot > tr > td
      `]:{padding:`${Y(s)} ${Y(i)}`},[`${t}-filter-trigger`]:{marginInlineEnd:Y(n(i).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${Y(n(s).mul(-1).equal())} ${Y(n(i).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:Y(n(s).mul(-1).equal()),marginInline:`${Y(n(r).sub(i).equal())} ${Y(n(i).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:Y(n(i).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},o("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),o("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}},Zd=e=>{const{componentCls:t,marginXXS:r,fontSizeIcon:n,headerIconColor:o,headerIconHoverColor:l}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}, left 0s`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`
          &${t}-cell-fix-left:hover,
          &${t}-cell-fix-right:hover
        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1,minWidth:0},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:r,color:o,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:n,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:l}}}},Jd=e=>{const{componentCls:t,opacityLoading:r,tableScrollThumbBg:n,tableScrollThumbBgHover:o,tableScrollThumbSize:l,tableScrollBg:s,zIndexTableSticky:i,stickyScrollBarBorderRadius:d,lineWidth:c,lineType:p,tableBorderColor:u}=e,v=`${Y(c)} ${p} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:i,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${Y(l)} !important`,zIndex:i,display:"flex",alignItems:"center",background:s,borderTop:v,opacity:r,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:l,backgroundColor:n,borderRadius:d,transition:`all ${e.motionDurationSlow}, transform 0s`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:o}}}}}}},Ho=e=>{const{componentCls:t,lineWidth:r,tableBorderColor:n,calc:o}=e,l=`${Y(r)} ${e.lineType} ${n}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:l}}},[`div${t}-summary`]:{boxShadow:`0 ${Y(o(r).mul(-1).equal())} 0 ${n}`}}}},Qd=e=>{const{componentCls:t,motionDurationMid:r,lineWidth:n,lineType:o,tableBorderColor:l,calc:s}=e,i=`${Y(n)} ${o} ${l}`,d=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-tbody-virtual-holder-inner`]:{[`
            & > ${t}-row, 
            & > div:not(${t}-row) > ${t}-row
          `]:{display:"flex",boxSizing:"border-box",width:"100%"}},[`${t}-cell`]:{borderBottom:i,transition:`background ${r}`},[`${t}-expanded-row`]:{[`${d}${d}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${Y(n)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:i,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:i,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:s(n).mul(-1).equal(),borderInlineStart:i}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:i,borderBottom:i}}}}}},eu=e=>{const{componentCls:t,fontWeightStrong:r,tablePaddingVertical:n,tablePaddingHorizontal:o,tableExpandColumnWidth:l,lineWidth:s,lineType:i,tableBorderColor:d,tableFontSize:c,tableBg:p,tableRadius:u,tableHeaderTextColor:v,motionDurationMid:f,tableHeaderBg:m,tableHeaderCellSplitColor:g,tableFooterTextColor:h,tableFooterBg:b,calc:y}=e,S=`${Y(s)} ${i} ${d}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%","--rc-virtual-list-scrollbar-bg":e.tableScrollBg},Yn()),{[t]:Object.assign(Object.assign({},pn(e)),{fontSize:c,background:p,borderRadius:`${Y(u)} ${Y(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${Y(u)} ${Y(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`
          ${t}-cell,
          ${t}-thead > tr > th,
          ${t}-tbody > tr > th,
          ${t}-tbody > tr > td,
          tfoot > tr > th,
          tfoot > tr > td
        `]:{position:"relative",padding:`${Y(n)} ${Y(o)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${Y(n)} ${Y(o)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:v,fontWeight:r,textAlign:"start",background:m,borderBottom:S,transition:`background ${f} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:g,transform:"translateY(-50%)",transition:`background-color ${f}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${f}, border-color ${f}`,borderBottom:S,[`
              > ${t}-wrapper:only-child,
              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child
            `]:{[t]:{marginBlock:Y(y(n).mul(-1).equal()),marginInline:`${Y(y(l).sub(o).equal())}
                ${Y(y(o).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottomWidth:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:v,fontWeight:r,textAlign:"start",background:m,borderBottom:S,transition:`background ${f} ease`}}},[`${t}-footer`]:{padding:`${Y(n)} ${Y(o)}`,color:h,background:b}})}},tu=e=>{const{colorFillAlter:t,colorBgContainer:r,colorTextHeading:n,colorFillSecondary:o,colorFillContent:l,controlItemBgActive:s,controlItemBgActiveHover:i,padding:d,paddingSM:c,paddingXS:p,colorBorderSecondary:u,borderRadiusLG:v,controlHeight:f,colorTextPlaceholder:m,fontSize:g,fontSizeSM:h,lineHeight:b,lineWidth:y,colorIcon:S,colorIconHover:C,opacityLoading:w,controlInteractiveSize:R}=e,$=new Vt(o).onBackground(r).toHexString(),k=new Vt(l).onBackground(r).toHexString(),x=new Vt(t).onBackground(r).toHexString(),T=new Vt(S),O=new Vt(C),D=R/2-y,M=D*2+y*3;return{headerBg:x,headerColor:n,headerSortActiveBg:$,headerSortHoverBg:k,bodySortBg:x,rowHoverBg:x,rowSelectedBg:s,rowSelectedHoverBg:i,rowExpandedBg:t,cellPaddingBlock:d,cellPaddingInline:d,cellPaddingBlockMD:c,cellPaddingInlineMD:p,cellPaddingBlockSM:p,cellPaddingInlineSM:p,borderColor:u,headerBorderRadius:v,footerBg:x,footerColor:n,cellFontSize:g,cellFontSizeMD:g,cellFontSizeSM:g,headerSplitColor:u,fixedHeaderSortActiveBg:$,headerFilterHoverBg:l,filterDropdownMenuBg:r,filterDropdownBg:r,expandIconBg:r,selectionColumnWidth:f,stickyScrollBarBg:m,stickyScrollBarBorderRadius:100,expandIconMarginTop:(g*b-y*3)/2-Math.ceil((h*1.4-y*3)/2),headerIconColor:T.clone().setA(T.a*w).toRgbString(),headerIconHoverColor:O.clone().setA(O.a*w).toRgbString(),expandIconHalfInner:D,expandIconSize:M,expandIconScale:R/M}},zo=2,nu=In("Table",e=>{const{colorTextHeading:t,colorSplit:r,colorBgContainer:n,controlInteractiveSize:o,headerBg:l,headerColor:s,headerSortActiveBg:i,headerSortHoverBg:d,bodySortBg:c,rowHoverBg:p,rowSelectedBg:u,rowSelectedHoverBg:v,rowExpandedBg:f,cellPaddingBlock:m,cellPaddingInline:g,cellPaddingBlockMD:h,cellPaddingInlineMD:b,cellPaddingBlockSM:y,cellPaddingInlineSM:S,borderColor:C,footerBg:w,footerColor:R,headerBorderRadius:$,cellFontSize:k,cellFontSizeMD:x,cellFontSizeSM:T,headerSplitColor:O,fixedHeaderSortActiveBg:D,headerFilterHoverBg:M,filterDropdownBg:E,expandIconBg:I,selectionColumnWidth:N,stickyScrollBarBg:K,calc:z}=e,B=un(e,{tableFontSize:k,tableBg:n,tableRadius:$,tablePaddingVertical:m,tablePaddingHorizontal:g,tablePaddingVerticalMiddle:h,tablePaddingHorizontalMiddle:b,tablePaddingVerticalSmall:y,tablePaddingHorizontalSmall:S,tableBorderColor:C,tableHeaderTextColor:s,tableHeaderBg:l,tableFooterTextColor:R,tableFooterBg:w,tableHeaderCellSplitColor:O,tableHeaderSortBg:i,tableHeaderSortHoverBg:d,tableBodySortBg:c,tableFixedHeaderSortActiveBg:D,tableHeaderFilterActiveBg:M,tableFilterDropdownBg:E,tableRowHoverBg:p,tableSelectedRowBg:u,tableSelectedRowHoverBg:v,zIndexTableFixed:zo,zIndexTableSticky:z(zo).add(1).equal({unit:!1}),tableFontSizeMiddle:x,tableFontSizeSmall:T,tableSelectionColumnWidth:N,tableExpandIconBg:I,tableExpandColumnWidth:z(o).add(z(e.padding).mul(2)).equal(),tableExpandedRowBg:f,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:K,tableScrollThumbBgHover:t,tableScrollBg:r});return[eu(B),Xd(B),Ho(B),Zd(B),Wd(B),_d(B),qd(B),Fd(B),Ho(B),Ad(B),Ud(B),Vd(B),Jd(B),jd(B),Gd(B),Yd(B),Qd(B)]},tu,{unitless:{expandIconScale:!0}}),ru=[],ou=(e,t)=>{var r,n;const{prefixCls:o,className:l,rootClassName:s,style:i,size:d,bordered:c,dropdownPrefixCls:p,dataSource:u,pagination:v,rowSelection:f,rowKey:m="key",rowClassName:g,columns:h,children:b,childrenColumnName:y,onChange:S,getPopupContainer:C,loading:w,expandIcon:R,expandable:$,expandedRowRender:k,expandIconColumnIndex:x,indentSize:T,scroll:O,sortDirections:D,locale:M,showSorterTooltip:E={target:"full-header"},virtual:I}=e;Nn();const N=a.useMemo(()=>h||Cr(b),[h,b]),K=a.useMemo(()=>N.some(Se=>Se.responsive),[N]),z=Rl(K),B=a.useMemo(()=>{const Se=new Set(Object.keys(z).filter(ye=>z[ye]));return N.filter(ye=>!ye.responsive||ye.responsive.some(He=>Se.has(He)))},[N,z]),V=en(e,["className","style","columns"]),{locale:Z=Il,direction:ne,table:A,renderEmpty:se,getPrefixCls:fe,getPopupContainer:be}=a.useContext(Ht),re=Nl(d),Q=Object.assign(Object.assign({},Z.Table),M),ae=u||ru,ve=fe("table",o),X=fe("dropdown",p),[,F]=fr(),H=ur(ve),[U,le,de]=nu(ve,H),ie=Object.assign(Object.assign({childrenColumnName:y,expandIconColumnIndex:x},$),{expandIcon:(r=$==null?void 0:$.expandIcon)!==null&&r!==void 0?r:(n=A==null?void 0:A.expandable)===null||n===void 0?void 0:n.expandIcon}),{childrenColumnName:Ce="children"}=ie,je=a.useMemo(()=>ae.some(Se=>Se==null?void 0:Se[Ce])?"nest":k||$!=null&&$.expandedRowRender?"row":null,[ae]),we={body:a.useRef(null)},W=Kc(ve),q=a.useRef(null),ue=a.useRef(null);Pc(t,()=>Object.assign(Object.assign({},ue.current),{nativeElement:q.current}));const xe=a.useMemo(()=>typeof m=="function"?m:Se=>Se==null?void 0:Se[m],[m]),[he]=Td(ae,Ce,xe),Oe={},Re=(Se,ye,He=!1)=>{var We,Ye,at,dt;const Je=Object.assign(Object.assign({},Oe),Se);He&&((We=Oe.resetPagination)===null||We===void 0||We.call(Oe),!((Ye=Je.pagination)===null||Ye===void 0)&&Ye.current&&(Je.pagination.current=1),v&&((at=v.onChange)===null||at===void 0||at.call(v,1,(dt=Je.pagination)===null||dt===void 0?void 0:dt.pageSize))),O&&O.scrollToFirstRowOnChange!==!1&&we.body.current&&Yl(0,{getContainer:()=>we.body.current}),S==null||S(Je.pagination,Je.filters,Je.sorter,{currentDataSource:ir(cr(ae,Je.sorterStates,Ce),Je.filterStates,Ce),action:ye})},ze=(Se,ye)=>{Re({sorter:Se,sorterStates:ye},"sort",!1)},[me,pe,ee,G]=Bd({prefixCls:ve,mergedColumns:B,onSorterChange:ze,sortDirections:D||["ascend","descend"],tableLocale:Q,showSorterTooltip:E}),Pe=a.useMemo(()=>cr(ae,pe,Ce),[ae,pe]);Oe.sorter=G(),Oe.sorterStates=pe;const Le=(Se,ye)=>{Re({filters:Se,filterStates:ye},"filter",!0)},[ce,Ae,Ie]=Od({prefixCls:ve,locale:Q,dropdownPrefixCls:X,mergedColumns:B,onFilterChange:Le,getPopupContainer:C||be,rootClassName:te(s,H)}),_e=ir(Pe,Ae,Ce);Oe.filters=Ie,Oe.filterStates=Ae;const Ge=a.useMemo(()=>{const Se={};return Object.keys(Ie).forEach(ye=>{Ie[ye]!==null&&(Se[ye]=Ie[ye])}),Object.assign(Object.assign({},ee),{filters:Se})},[ee,Ie]),[et]=Ld(Ge),yt=(Se,ye)=>{Re({pagination:Object.assign(Object.assign({},Oe.pagination),{current:Se,pageSize:ye})},"paginate")},[Be,mt]=Dd(_e.length,yt,v);Oe.pagination=v===!1?{}:Pd(Be,v),Oe.resetPagination=mt;const wt=a.useMemo(()=>{if(v===!1||!Be.pageSize)return _e;const{current:Se=1,total:ye,pageSize:He=_a}=Be;return _e.length<ye?_e.length>He?_e.slice((Se-1)*He,Se*He):_e:_e.slice((Se-1)*He,Se*He)},[!!v,_e,Be==null?void 0:Be.current,Be==null?void 0:Be.pageSize,Be==null?void 0:Be.total]),[Fe,Xe]=Tc({prefixCls:ve,data:_e,pageData:wt,getRowKey:xe,getRecordByKey:he,expandType:je,childrenColumnName:Ce,locale:Q,getPopupContainer:C||be},f),tt=(Se,ye,He)=>{let We;return typeof g=="function"?We=te(g(Se,ye,He)):We=te(g),te({[`${ve}-row-selected`]:Xe.has(xe(Se,ye))},We)};ie.__PARENT_RENDER_ICON__=ie.expandIcon,ie.expandIcon=ie.expandIcon||R||Dc(Q),je==="nest"&&ie.expandIconColumnIndex===void 0?ie.expandIconColumnIndex=f?1:0:ie.expandIconColumnIndex>0&&f&&(ie.expandIconColumnIndex-=1),typeof ie.indentSize!="number"&&(ie.indentSize=typeof T=="number"?T:15);const Ue=a.useCallback(Se=>et(Fe(ce(me(Se)))),[me,ce,Fe]);let Ze,qe;if(v!==!1&&(Be!=null&&Be.total)){let Se;Be.size?Se=Be.size:Se=re==="small"||re==="middle"?"small":void 0;const ye=Ye=>a.createElement(jl,Object.assign({},Be,{className:te(`${ve}-pagination ${ve}-pagination-${Ye}`,Be.className),size:Se})),He=ne==="rtl"?"left":"right",{position:We}=Be;if(We!==null&&Array.isArray(We)){const Ye=We.find(Je=>Je.includes("top")),at=We.find(Je=>Je.includes("bottom")),dt=We.every(Je=>`${Je}`=="none");!Ye&&!at&&!dt&&(qe=ye(He)),Ye&&(Ze=ye(Ye.toLowerCase().replace("top",""))),at&&(qe=ye(at.toLowerCase().replace("bottom","")))}else qe=ye(He)}let ft;typeof w=="boolean"?ft={spinning:w}:typeof w=="object"&&(ft=Object.assign({spinning:!0},w));const nt=te(de,H,`${ve}-wrapper`,A==null?void 0:A.className,{[`${ve}-wrapper-rtl`]:ne==="rtl"},l,s,le),ct=Object.assign(Object.assign({},A==null?void 0:A.style),i),zt=typeof(M==null?void 0:M.emptyText)<"u"?M.emptyText:(se==null?void 0:se("Table"))||a.createElement(Bl,{componentName:"Table"}),xt=I?zd:Hd,Pt={},_t=a.useMemo(()=>{const{fontSize:Se,lineHeight:ye,lineWidth:He,padding:We,paddingXS:Ye,paddingSM:at}=F,dt=Math.floor(Se*ye);switch(re){case"middle":return at*2+dt+He;case"small":return Ye*2+dt+He;default:return We*2+dt+He}},[F,re]);return I&&(Pt.listItemHeight=_t),U(a.createElement("div",{ref:q,className:nt,style:ct},a.createElement(Jo,Object.assign({spinning:!1},ft),Ze,a.createElement(xt,Object.assign({},Pt,V,{ref:ue,columns:B,direction:ne,expandable:ie,prefixCls:ve,className:te({[`${ve}-middle`]:re==="middle",[`${ve}-small`]:re==="small",[`${ve}-bordered`]:c,[`${ve}-empty`]:ae.length===0},de,H,le),data:wt,rowKey:xe,rowClassName:tt,emptyText:zt,internalHooks:hn,internalRefs:we,transformColumns:Ue,getContainerWidth:W})),qe)))},au=a.forwardRef(ou),lu=(e,t)=>{const r=a.useRef(0);return r.current+=1,a.createElement(au,Object.assign({},e,{ref:t,_renderTimes:r.current}))},Mt=a.forwardRef(lu);Mt.SELECTION_COLUMN=Dt;Mt.EXPAND_COLUMN=Ot;Mt.SELECTION_ALL=tr;Mt.SELECTION_INVERT=nr;Mt.SELECTION_NONE=rr;Mt.Column=Sc;Mt.ColumnGroup=xc;Mt.Summary=va;const{Option:Xn}=Go;function iu(){const[e,t]=a.useState([]),[r,n]=a.useState(!1),[o,l]=a.useState(!1),[s,i]=a.useState(null),[d]=Wt.useForm();a.useEffect(()=>{c()},[]);const c=async()=>{n(!0);try{const h=await(await fetch("/api/orders")).json();t(h)}catch{sn.error("Failed to fetch orders")}finally{n(!1)}},p=()=>{i(null),d.resetFields(),l(!0)},u=g=>{i(g),d.setFieldsValue(g),l(!0)},v=async g=>{try{await fetch(`/api/orders/${g}`,{method:"DELETE",headers:{"X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")}}),sn.success("Order deleted successfully"),c()}catch{sn.error("Failed to delete order")}},f=async g=>{try{const h=s?"PUT":"POST",b=s?`/api/orders/${s.id}`:"/api/orders";await fetch(b,{method:h,headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify(g)}),sn.success(`Order ${s?"updated":"created"} successfully`),l(!1),c()}catch{sn.error("Failed to save order")}},m=[{title:"Order ID",dataIndex:"id",key:"id"},{title:"Customer",dataIndex:"customer_name",key:"customer_name"},{title:"Product",dataIndex:"product",key:"product"},{title:"Amount",dataIndex:"amount",key:"amount",render:g=>`$${g}`},{title:"Status",dataIndex:"status",key:"status",render:g=>{const h=g==="completed"?"green":g==="pending"?"orange":"red";return Ke.jsx(Tl,{color:h,children:g.toUpperCase()})}},{title:"Actions",key:"actions",render:(g,h)=>Ke.jsxs(Un,{children:[Ke.jsx(Tt,{type:"primary",size:"small",icon:Ke.jsx(Fl,{}),onClick:()=>u(h),children:"Edit"}),Ke.jsx(Tt,{danger:!0,size:"small",icon:Ke.jsx(Vl,{}),onClick:()=>v(h.id),children:"Delete"})]})}];return Ke.jsx("div",{className:"min-h-screen bg-gray-50 p-6",children:Ke.jsxs("div",{className:"max-w-7xl mx-auto",children:[Ke.jsx(kl,{title:Ke.jsxs("div",{className:"flex justify-between items-center",children:[Ke.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"Order Management"}),Ke.jsx(Tt,{type:"primary",icon:Ke.jsx(Ol,{}),onClick:p,className:"bg-blue-500 hover:bg-blue-600",children:"New Order"})]}),className:"shadow-lg",children:Ke.jsx(Jo,{spinning:r,children:Ke.jsx(Mt,{dataSource:e,columns:m,rowKey:"id",pagination:{pageSize:10},className:"bg-white"})})}),Ke.jsx(Hl,{title:s?"Edit Order":"Create New Order",open:o,onCancel:()=>l(!1),footer:null,width:600,children:Ke.jsxs(Wt,{form:d,layout:"vertical",onFinish:f,className:"mt-4",children:[Ke.jsx(Wt.Item,{name:"customer_name",label:"Customer Name",rules:[{required:!0,message:"Please enter customer name"}],children:Ke.jsx(Hn,{placeholder:"Enter customer name"})}),Ke.jsx(Wt.Item,{name:"product",label:"Product",rules:[{required:!0,message:"Please enter product"}],children:Ke.jsx(Hn,{placeholder:"Enter product name"})}),Ke.jsx(Wt.Item,{name:"amount",label:"Amount",rules:[{required:!0,message:"Please enter amount"}],children:Ke.jsx(Hn,{type:"number",placeholder:"Enter amount",prefix:"$"})}),Ke.jsx(Wt.Item,{name:"status",label:"Status",rules:[{required:!0,message:"Please select status"}],children:Ke.jsxs(Go,{placeholder:"Select status",children:[Ke.jsx(Xn,{value:"pending",children:"Pending"}),Ke.jsx(Xn,{value:"completed",children:"Completed"}),Ke.jsx(Xn,{value:"cancelled",children:"Cancelled"})]})}),Ke.jsx(Wt.Item,{className:"mb-0 text-right",children:Ke.jsxs(Un,{children:[Ke.jsx(Tt,{onClick:()=>l(!1),children:"Cancel"}),Ke.jsx(Tt,{type:"primary",htmlType:"submit",children:s?"Update":"Create"})]})})]})})]})})}const _o=document.getElementById("order-app");_o&&Ml.createRoot(_o).render(Ke.jsx(iu,{}));
