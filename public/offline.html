<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Offline - Restaurant App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        p {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }
        
        .feature-icon {
            margin-right: 10px;
            opacity: 0.7;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon pulse">📶</div>
        <h1>Bạn đang offline</h1>
        <p>Không thể kết nối internet. Vui lòng kiểm tra kết nối mạng và thử lại.</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            🔄 Thử lại
        </button>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">💾</span>
                <span>Giỏ hàng đã được lưu</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔄</span>
                <span>Tự động đồng bộ khi có mạng</span>
            </div>
            <div class="feature">
                <span class="feature-icon">📱</span>
                <span>Ứng dụng vẫn hoạt động offline</span>
            </div>
        </div>
    </div>

    <script>
        // Auto retry when online
        window.addEventListener('online', () => {
            window.location.reload();
        });
        
        // Show online/offline status
        function updateOnlineStatus() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        window.addEventListener('online', updateOnlineStatus);
        
        // Check connection every 5 seconds
        setInterval(() => {
            if (navigator.onLine) {
                fetch('/manifest.json', { method: 'HEAD' })
                    .then(() => {
                        window.location.reload();
                    })
                    .catch(() => {
                        // Still offline
                    });
            }
        }, 5000);
    </script>
</body>
</html>
